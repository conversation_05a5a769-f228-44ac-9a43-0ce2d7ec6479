<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SchoolRoleMenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * 为学校角色分配菜单，往role_has_menus添加数据
     * 只给学校的三个角色分配菜单：教务(type=2)、老师(type=3)、学生(type=1)
     * 分配的菜单以organization_has_menus为主，根据menus表crowd字段确认各角色默认菜单
     */
    public function run(): void
    {
        $startTime = microtime(true);
        Log::info("开始执行 SchoolRoleMenuSeeder");
        echo "开始执行 SchoolRoleMenuSeeder\n";

        // 设置内存和时间限制
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', 3600);

        try {
            DB::beginTransaction();

            $totalProcessed = 0;
            $totalInserted = 0;

            // 获取所有学校的机构ID
            $organizations = DB::table('organizations')
                ->where('model_type', 'school')
                ->select('id', 'model_id')
                ->get();

            echo "找到 " . $organizations->count() . " 个学校机构\n";

            foreach ($organizations as $organization) {
                $totalProcessed++;
                echo "正在处理第 {$totalProcessed} 个学校机构: organization_id={$organization->id}\n";

                // 获取该机构的学校角色
                $schoolRoles = $this->getSchoolRoles($organization->id);
                
                if ($schoolRoles->isEmpty()) {
                    echo "机构 {$organization->id} 没有找到学校角色\n";
                    continue;
                }

                echo "找到 " . $schoolRoles->count() . " 个学校角色\n";

                // 为每个角色分配菜单
                foreach ($schoolRoles as $role) {
                    echo "正在为角色 {$role->name} (type={$role->type}) 分配菜单...\n";
                    $insertedCount = $this->assignMenusToRole($role, $organization->id);
                    $totalInserted += $insertedCount;
                    echo "角色 {$role->name} 分配菜单数: {$insertedCount}\n";
                }
            }

            DB::commit();

            $endTime = microtime(true);
            $duration = $endTime - $startTime;

            Log::info("SchoolRoleMenuSeeder执行完成", [
                'total_organizations_processed' => $totalProcessed,
                'total_menus_inserted' => $totalInserted,
                'duration' => round($duration, 2) . '秒'
            ]);

            echo "SchoolRoleMenuSeeder执行完成\n";
            echo "处理机构数: {$totalProcessed}\n";
            echo "插入菜单数: {$totalInserted}\n";
            echo "耗时: " . round($duration, 2) . "秒\n";

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("SchoolRoleMenuSeeder执行失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 获取学校的三个角色：教务(type=2)、老师(type=3)、学生(type=1)
     */
    private function getSchoolRoles(int $organizationId)
    {
        return DB::table('roles')
            ->where('organization_id', $organizationId)
            ->where('status', 1)
            ->whereIn('type', [1, 2, 3]) // 1=学生, 2=教务, 3=老师
            ->whereIn('name', ['学生', '教务', '老师'])
            ->select('id', 'name', 'type', 'organization_id')
            ->get();
    }

    /**
     * 为指定角色分配菜单
     */
    private function assignMenusToRole($role, int $organizationId): int
    {
        $insertedCount = 0;
        $currentTime = Carbon::now();

        // 根据角色类型确定crowd值
        $crowdValue = (string)$role->type;

        // 获取该机构拥有的菜单，并且适用于该角色的菜单
        $availableMenus = $this->getAvailableMenusForRole($organizationId, $crowdValue);

        echo "找到 " . $availableMenus->count() . " 个适用于角色 {$role->name} 的菜单\n";

        foreach ($availableMenus as $menu) {
            // 检查是否已存在
            $exists = DB::table('role_has_menus')
                ->where('role_id', $role->id)
                ->where('organization_menu_id', $menu->organization_menu_id)
                ->exists();

            if (!$exists) {
                $insertData = [
                    'role_id' => $role->id,
                    'organization_menu_id' => $menu->organization_menu_id,
                    'menu_code' => $menu->menu_code,
                    'creator' => 'SchoolRoleMenuSeeder',
                    'updater' => 'SchoolRoleMenuSeeder',
                    'created_at' => $currentTime,
                    'updated_at' => $currentTime,
                ];

                DB::table('role_has_menus')->insert($insertData);
                $insertedCount++;
            }
        }

        return $insertedCount;
    }

    /**
     * 获取机构拥有的且适用于指定角色的菜单
     */
    private function getAvailableMenusForRole(int $organizationId, string $crowdValue)
    {
        return DB::table('organization_has_menus')
            ->join('menus', 'menus.id', '=', 'organization_has_menus.menu_id')
            ->where('organization_has_menus.organization_id', $organizationId)
            ->where('organization_has_menus.status', 1)
            ->where('menus.status', 1)
            ->where('menus.type', 3) // 学校类型菜单
            ->whereNotNull('menus.crowd')
            ->where('menus.crowd', '!=', '')
            ->whereRaw('JSON_VALID(menus.crowd)')
            ->whereRaw('JSON_CONTAINS(menus.crowd, JSON_QUOTE(?))', [$crowdValue])
            ->select(
                'organization_has_menus.id as organization_menu_id',
                'organization_has_menus.menu_id',
                'organization_has_menus.menu_code',
                'menus.menu_name',
                'menus.crowd'
            )
            ->get();
    }
}
