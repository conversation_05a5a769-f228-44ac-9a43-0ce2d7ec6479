# 班级数据同步功能指南

## 概述

班级数据同步功能已实现，包含年级自动创建和班级数据同步。支持单个创建、批量创建和修改操作。

## 功能特性

### 🎯 **核心功能**
1. **年级自动创建** - 根据班级年级自动创建 `ysy_grade` 表记录
2. **年份智能推算** - 根据当前时间和年级推算入学年份
3. **班级数据同步** - 同步到 `ysy_class` 表
4. **支持批量操作** - 支持单个和批量班级创建
5. **支持修改操作** - 支持班级数据更新同步

### 📊 **年级推算规则**

#### **年级映射**
- `7` → 初一
- `8` → 初二  
- `9` → 初三
- `10` → 高一
- `11` → 高二
- `12` → 高三

#### **年份推算逻辑**
**9月后（9-12月）：**
- 高一(10) → 当前年份（如2025）
- 高二(11) → 当前年份-1（如2024）
- 高三(12) → 当前年份-2（如2023）
- 初一(7) → 当前年份（如2025）
- 初二(8) → 当前年份-1（如2024）
- 初三(9) → 当前年份-2（如2023）

**9月前（1-8月）：**
- 高一(10) → 当前年份-1（如2024）
- 高二(11) → 当前年份-2（如2023）
- 高三(12) → 当前年份-3（如2022）
- 初一(7) → 当前年份-1（如2024）
- 初二(8) → 当前年份-2（如2023）
- 初三(9) → 当前年份-3（如2022）

## 数据库表结构

### **ysy_grade 表**
```sql
CREATE TABLE `ysy_grade` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) DEFAULT NULL COMMENT '年份',
    `grade_name` varchar(255) DEFAULT NULL COMMENT '年级名称',
    `grade_sort` int(11) DEFAULT NULL COMMENT '年级排序',
    `school_id` int(11) DEFAULT NULL COMMENT '学校ID',
    `school_district` int(11) DEFAULT NULL COMMENT '校区ID',
    `step` tinyint(4) DEFAULT 0 COMMENT '步骤',
    PRIMARY KEY (`id`)
);
```

### **ysy_class 表**
```sql
CREATE TABLE `ysy_class` (
    `id` int(11) NOT NULL,
    `name` varchar(255) DEFAULT NULL COMMENT '班级名称',
    `school_id` int(11) DEFAULT NULL COMMENT '学校ID',
    `school_district` int(11) DEFAULT NULL COMMENT '校区ID',
    `grade_id` int(11) DEFAULT NULL COMMENT '年级ID',
    `step` tinyint(4) DEFAULT 0 COMMENT '步骤',
    PRIMARY KEY (`id`)
);
```

## 使用方法

### **1. 单个班级创建**
```php
// 在 ClassController::store 中自动触发
POST /school/class
{
    "name": "高一(1)班",
    "grade": 10,
    "school_id": 1051
}
```

### **2. 批量班级创建**
```php
// 在 ClassController::batchStore 中自动触发
POST /school/class/batch
{
    "classes": [
        {"name": "高一(1)班", "grade": 10, "school_id": 1051},
        {"name": "高一(2)班", "grade": 10, "school_id": 1051},
        {"name": "高二(1)班", "grade": 11, "school_id": 1051}
    ]
}
```

### **3. 班级修改**
```php
// 在 ClassController::update 中自动触发
PUT /school/class/{id}
{
    "name": "高一(1)班（修改）",
    "grade": 10,
    "school_id": 1051
}
```

## 同步流程

### **单个班级同步流程**
1. **班级创建完成** - 原有业务逻辑创建班级
2. **检查同步开关** - 验证 `DATA_SYNC_ENABLED=true`
3. **年级处理** - 根据班级年级创建或获取年级记录
4. **年份推算** - 根据当前时间和年级推算入学年份
5. **班级同步** - 将班级数据同步到 `ysy_class` 表
6. **记录日志** - 记录同步过程和结果

### **批量班级同步流程**
1. **批量班级创建完成** - 原有业务逻辑批量创建班级
2. **逐个同步处理** - 对每个班级执行单个同步流程
3. **统计同步结果** - 记录成功和失败的数量
4. **记录批量日志** - 记录批量同步的整体结果

## 同步示例

### **原始班级数据**
```json
{
    "id": 2001,
    "name": "高一(1)班",
    "grade": 10,
    "school_id": 1051
}
```

### **自动创建的年级数据（ysy_grade）**
```json
{
    "id": 501,
    "name": "2025",
    "grade_name": "高一",
    "grade_sort": 10,
    "school_id": 1051,
    "school_district": 1364,
    "step": 0
}
```

### **同步后的班级数据（ysy_class）**
```json
{
    "id": 2001,
    "name": "高一(1)班",
    "school_id": 1051,
    "school_district": 1364,
    "grade_id": 501,
    "step": 0
}
```

## 测试方法

### **1. 运行测试命令**
```bash
php artisan test:datasync --type=school
```

### **2. 查看同步日志**
```bash
tail -f storage/logs/laravel.log | grep "班级数据同步"
```

### **3. 验证数据库**
```sql
-- 查看年级数据
SELECT * FROM ysy_grade WHERE school_id = 1051;

-- 查看班级数据
SELECT * FROM ysy_class WHERE school_id = 1051;

-- 关联查询
SELECT 
    c.id as class_id,
    c.name as class_name,
    g.name as grade_year,
    g.grade_name,
    g.grade_sort
FROM ysy_class c
LEFT JOIN ysy_grade g ON c.grade_id = g.id
WHERE c.school_id = 1051;
```

## 配置要求

### **环境变量**
```env
# 启用数据同步
DATA_SYNC_ENABLED=true
```

### **数据库连接**
确保同步数据库连接配置正确，能够访问目标数据库。

## 注意事项

### **1. 年级唯一性**
- 同一学校、同一校区、同一年级排序的年级记录是唯一的
- 如果年级已存在，会复用现有记录

### **2. 班级ID保持**
- 新表使用原表的 ID 作为主键
- 确保原表ID的唯一性

### **3. 校区依赖**
- 班级同步依赖校区数据
- 确保学校的校区数据已正确同步

### **4. 错误处理**
- 同步失败不影响原有业务流程
- 所有错误都会记录在日志中

### **5. 时间敏感性**
- 年份推算基于当前时间
- 9月是关键时间节点

## 故障排查

### **1. 年级创建失败**
- 检查年级字段是否正确（7-12）
- 检查学校和校区数据是否存在

### **2. 班级同步失败**
- 检查班级数据完整性
- 检查数据库连接和权限

### **3. 年份推算错误**
- 检查系统时间是否正确
- 验证年级推算逻辑

通过这个班级同步功能，您可以在创建班级时自动同步相关数据，并智能处理年级和年份的关系！🚀
