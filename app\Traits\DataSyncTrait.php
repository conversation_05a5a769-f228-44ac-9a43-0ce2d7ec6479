<?php

namespace App\Traits;

use App\Events\DataSync\SchoolCreated;
use App\Events\DataSync\StudentCreated;
use App\Events\DataSync\TeacherCreated;
use App\Events\DataSync\AdminCreated;
use Illuminate\Support\Facades\Log;

/**
 * 数据同步 Trait
 * 在模型中使用此 Trait 可以自动触发数据同步
 */
trait DataSyncTrait
{
    /**
     * 模型启动时注册事件监听
     */
    protected static function bootDataSyncTrait()
    {
        // 监听模型创建事件
        static::created(function ($model) {
            $model->triggerDataSync('created');
        });

        // 监听模型更新事件
        static::updated(function ($model) {
            $model->triggerDataSync('updated');
        });

        // 监听模型删除事件
        static::deleted(function ($model) {
            $model->triggerDataSync('deleted');
        });
    }

    /**
     * 触发数据同步
     * 
     * @param string $action 操作类型 (created, updated, deleted)
     */
    protected function triggerDataSync(string $action)
    {
        try {
            // 检查是否启用数据同步
            if (!config('datasync.enabled', false)) {
                return;
            }

            // 检查当前模型是否需要同步
            if (!$this->shouldSync()) {
                return;
            }

            $syncData = $this->prepareSyncData();
            
            if (empty($syncData)) {
                return;
            }

            // 根据模型类型和操作类型触发相应事件
            $this->dispatchSyncEvent($action, $syncData);

        } catch (\Exception $e) {
            Log::error('数据同步触发失败', [
                'model' => get_class($this),
                'model_id' => $this->id,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 判断是否需要同步
     * 
     * @return bool
     */
    protected function shouldSync(): bool
    {
        // 检查模型是否有同步配置
        if (!property_exists($this, 'syncEnabled') || !$this->syncEnabled) {
            return false;
        }

        // 检查是否在同步排除列表中
        $excludeIds = config('datasync.exclude_ids.' . $this->getSyncType(), []);
        if (in_array($this->id, $excludeIds)) {
            return false;
        }

        return true;
    }

    /**
     * 准备同步数据
     * 
     * @return array
     */
    protected function prepareSyncData(): array
    {
        $syncData = $this->toArray();

        // 如果模型定义了同步字段映射，使用映射后的数据
        if (property_exists($this, 'syncFieldMapping') && is_array($this->syncFieldMapping)) {
            $mappedData = [];
            foreach ($this->syncFieldMapping as $originalField => $syncField) {
                if (isset($syncData[$originalField])) {
                    $mappedData[$syncField] = $syncData[$originalField];
                }
            }
            $syncData = array_merge($syncData, $mappedData);
        }

        // 添加额外的同步数据
        $syncData = array_merge($syncData, $this->getAdditionalSyncData());

        return $syncData;
    }

    /**
     * 获取额外的同步数据
     * 子类可以重写此方法来添加额外的同步数据
     * 
     * @return array
     */
    protected function getAdditionalSyncData(): array
    {
        return [];
    }

    /**
     * 获取同步类型
     * 
     * @return string
     */
    protected function getSyncType(): string
    {
        // 如果模型定义了同步类型，使用定义的类型
        if (property_exists($this, 'syncType')) {
            return $this->syncType;
        }

        // 根据模型类名推断同步类型
        $className = class_basename($this);
        return match($className) {
            'School' => 'school',
            'Student', 'Member' => 'student',
            'Teacher' => 'teacher',
            'Admin', 'User' => 'admin',
            default => strtolower($className)
        };
    }

    /**
     * 分发同步事件
     * 
     * @param string $action 操作类型
     * @param array $syncData 同步数据
     */
    protected function dispatchSyncEvent(string $action, array $syncData)
    {
        $syncType = $this->getSyncType();

        // 只有创建操作才触发同步事件，更新和删除通过其他方式处理
        if ($action !== 'created') {
            return;
        }

        switch ($syncType) {
            case 'school':
                event(new SchoolCreated($syncData));
                break;
            case 'student':
                event(new StudentCreated($syncData));
                break;
            case 'teacher':
                event(new TeacherCreated($syncData));
                break;
            case 'admin':
                event(new AdminCreated($syncData));
                break;
            default:
                Log::warning('未知的同步类型', [
                    'sync_type' => $syncType,
                    'model' => get_class($this)
                ]);
        }
    }

    /**
     * 手动触发同步
     * 
     * @param string $action 操作类型
     * @return bool
     */
    public function manualSync(string $action = 'created'): bool
    {
        try {
            $this->triggerDataSync($action);
            return true;
        } catch (\Exception $e) {
            Log::error('手动同步失败', [
                'model' => get_class($this),
                'model_id' => $this->id,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 批量同步
     * 
     * @param \Illuminate\Database\Eloquent\Collection $models 模型集合
     * @param string $action 操作类型
     * @return array
     */
    public static function batchSync($models, string $action = 'created'): array
    {
        $results = [];
        $successCount = 0;
        $failCount = 0;

        foreach ($models as $model) {
            try {
                $success = $model->manualSync($action);
                if ($success) {
                    $successCount++;
                } else {
                    $failCount++;
                }
                
                $results[] = [
                    'id' => $model->id,
                    'success' => $success
                ];
            } catch (\Exception $e) {
                $failCount++;
                $results[] = [
                    'id' => $model->id,
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'total' => count($models),
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'results' => $results
        ];
    }
}
