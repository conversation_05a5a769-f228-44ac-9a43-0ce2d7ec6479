<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */
namespace app\psychassessment\logic;
use app\psychassessment\model\Focus as FocusModel;
use app\psychassessment\model\AppointmentTime as AppointmentTimeModel;

class Focus
{
    protected $FocusModel;
    protected $TimeModel;
    protected $user;

    public function __construct()
    {
        $this->FocusModel = new FocusModel();
        $this->TimeModel = new AppointmentTimeModel();
        $this->user = get_user();
    }

    public function add()
    {
        //多个student_id用逗号拼接
        $data = input('post.');
        $where['id'] = ['in',$data['student_ids']];
        $student_info = db('student')->where($where)->column('id,grade_id,class_id,member_id');
        $member_ids = array_column($student_info,'member_id');
        $has_exists = $this->FocusModel->where(['member_id'=>['in',$member_ids],'status'=>0])->group('member_id')->column('member_id');

        $student_ids_arr = explode(',',$data['student_ids']);
        foreach ($student_ids_arr as $k => $v){
            if(!in_array($student_info[$v]['member_id'],$has_exists)){
                $ins = [
                    'student_id'=>$v,
                    'school_id'=>$this->user['school_id'],
                    'grade_id'=>$student_info[$v]['grade_id'],
                    'class_id'=>$student_info[$v]['class_id'],
                    'member_id'=>$student_info[$v]['member_id'],
                    'reason'=>$data['reason'],
                    'teacher_member_id'=>$this->user['id'],
                ];
                $ins_arr[] = $ins;
            }
        }

        if(isset($ins_arr)){
            $res = $this->FocusModel->saveAll($ins_arr);
            apiReturn($res);
        }else{
            apiReturn([],'所选学生均已加入重点关注，请勿重复操作',-106);
        }

    }

    public function del()
    {
        $this->FocusModel->save([
            'status' => '-1',
            'remove_reason' => input('remove_reason'),
            'updated_at' => date('Y-m-d H:i:s'),
        ], ['id' => input('id')]);
        //软删除
        apiReturn(input('id'));
    }

    public function get_list()
    {
        $grade_id           = input('grade_id'); // 开始日期
        $class_id           = input('class_id'); // 开始日期
        $start_date         = input('start_date'); // 开始日期
        $end_date           = input('end_date'); // 结束日期
        $pageNumber         = input('page', 1); // 获取页码，默认为1
        $pageSize           = input('pagesize', 10); // 获取每页显示的记录数，默认为10
        $name               = input('name'); // 学生姓名

        //判断是教务还是老师
        switch ($this->user['role_source_id']){
            case 2:
                $where['a.school_id']  = $this->user['school_id'];
                break;
            case 3:
                $where['a.teacher_member_id'] = $this->user['id'];
                break;
        }

        $where['a.status'] = 0;
        if ($start_date && $end_date) $where['a.created_at'] = ['between', [$start_date.' 00:00:00', $end_date.' 23:59:59']];
        if ($grade_id) $where['a.grade_id'] = $grade_id;
        if ($class_id) $where['a.class_id'] = $class_id;
        if ($name) $where['student.name'] = ['like', '%' . $name . '%'];
        $list = $this->FocusModel->alias('a')
            ->join('student student','a.student_id = student.id')
            ->join('grade grade','a.grade_id = grade.id')
            ->join('class class','a.class_id = class.id')
            ->field('a.id,a.student_id,a.member_id,a.reason,a.created_at,grade.name as grade_year,grade.grade_name,class.name as class_name,student.student_no,student.name as student_name')
            ->where($where)
            ->group('a.member_id')
            ->order('a.id desc')
            ->select();
        $list = to_arr($list);
        return pageing($list,$pageSize,$pageNumber);
    }

}