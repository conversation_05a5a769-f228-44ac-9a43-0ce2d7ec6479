<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */

namespace app\evaluation\logic;
use app\evaluation\model\Student as StudentModel;
use app\backend\logic\Backend as BackendLogic;
use think\Config;

class TenthGradeReport
{
    private $studentModel;
    private $backend_logic;
    public function __construct()
    {
        $this->studentModel  = new StudentModel();
        $this->backend_logic = new BackendLogic();
        Config::load(APP_PATH.'evaluation/config_tenth.php');
    }

    public function personal_report($member_id,$distribution_id)
    {
        $member_info = $this->studentModel->alias('stu')
            ->join('school','stu.school_id = school.id')
            ->field('stu.name,case when stu.gender = 1 then "男" when stu.gender = 2 then "女" else "weizhi" end as gender,stu.student_no,school.name as school_name')
            ->where('member_id',$member_id)
            ->find();
        $member_info = to_arr($member_info);
        //查询做生涯测评时间
        $search_condition['a.time_error']  = 0;
        $search_condition['a.is_delete']   = 0;
        $search_condition['a.is_abnormal'] = 0;
        $search_condition['a.member_id']   = $member_id;
        $search_condition['b.survey_type'] = 29;
        $shengya_survey = db('survey_user_session a')
            ->join('survey b','a.survey_id = b.id')
            ->field('a.id,a.create_time,a.session_id,a.result,a.pdf_url')
            ->where($search_condition)
            ->order('a.id desc')
            ->find();

        $data['info'] = $member_info;
        $data['info']['survey_time'] = $shengya_survey['create_time'];

        $result = json_decode($shengya_survey['result'],true);

        $weak_motivation = $this->career_score_to_level($result['Learning_Motivation'][0],[5,10,11,19,20,25]);
        $strong_motivation = $this->career_score_to_level($result['Learning_Motivation'][1],[5,10,11,19,20,25]);
        $tenth_grade_motivation = Config::get('tenth_grade_motivation');

        $data['motivation'] = $tenth_grade_motivation[$this->motivation_type($weak_motivation,$strong_motivation)];
        $data['motivation']['score'] = $result['Learning_Motivation'];

        $total_efficacy = $this->career_score_to_level(round(array_sum($result['Academic_Efficacy'])),[0,33,34,44,45,55]);

        $tenth_grade_efficiency = Config::get('tenth_grade_efficacy');
        $data['efficacy'] = $tenth_grade_efficiency[$this->efficacy_type($total_efficacy,$result['Academic_Efficacy'])];
        $data['efficacy']['score'] = [
            round($result['Academic_Efficacy'][0] * 100 / 15),
            round($result['Academic_Efficacy'][1] * 100 / 20),
            round($result['Academic_Efficacy'][2] * 100 / 10),
            round($result['Academic_Efficacy'][3] * 100 / 10),
        ];

        $tenth_grade_ability = Config::get('tenth_grade_ability');
        $learning_ability_level = $this->learning_ability_level(array_sum($result['Learning_Ability']),[20,39,40,59,60,79,80,100]);
        $data['ability']['level'] = $tenth_grade_ability['level'][$learning_ability_level];
        $data['ability']['score'] = $result['Learning_Ability'];
        $data['ability']['norm'] = [16, 18, 18, 19];
        $data['ability']['advice'] = $this->ability_advice($result['Learning_Ability'],$tenth_grade_ability);

        $competition_goal = $this->career_score_to_level($result['Career_Goals'][0],[0,18,19,24,25,30]);
        $avoid_goal = $this->career_score_to_level($result['Career_Goals'][1],[0,18,19,24,25,30]);
        $master_goal = $this->career_score_to_level($result['Career_Goals'][2],[0,18,19,24,25,30]);

        $tenth_grade_goal = Config::get('tenth_grade_goal');
        $data['goal'] = $tenth_grade_goal[$this->goal_type($competition_goal,$avoid_goal,$master_goal)];
        $data['goal']['total'] = array_sum($result['Career_Goals']);
        $data['goal']['score'] = $result['Career_Goals'];

        $tenth_grade_strengths_interest = Config::get('tenth_grade_strengths_interest');
        list($strength_area,$potential_area,$development_area) = $this->strengths_interest_type($result['Career_Strengths'],$result['Interest'],$tenth_grade_strengths_interest);
        $data['strength_interest']['strengths'] = $result['Career_Strengths'];
        $data['strength_interest']['interests'] = $result['Interest'];
        $data['strength_interest']['advantage'] = $strength_area;
        $data['strength_interest']['potential'] = $potential_area;
        $data['strength_interest']['development'] = $development_area;

        //我的画像
        //画像学习
        $data['portrait'][] = [
            'name' => 'study',
            'options' => [
                ['label' => '学习动机','value' => $data['motivation']['type']],
                ['label' => '学业效能','value' => $data['efficacy']['type']],
                ['label' => '学习能力','value' => $data['ability']['level']['type']],
            ],
        ];

        if(!empty($strength_area)){
            $strength_interest = implode(',',array_column($strength_area,'type'));
            $strength_interest_description = implode(',',array_column($strength_area,'summarize'));
        }elseif(!empty($potential_area)){
            $strength_interest = implode(',',array_column($potential_area,'type'));
            $strength_interest_description = implode(',',array_column($potential_area,'summarize'));
        }elseif(!empty($development_area)){
            $strength_interest = '无突出优势和兴趣';
            $strength_interest_description = $tenth_grade_strengths_interest['low_advantages_low_interest']['summarize'];
        }
        //画像生涯
        $data['portrait'][] = [
            'name' => 'career',
            'options' => [
                ['label' => '生涯目标','value' => $data['goal']['type']],
                ['label' => '生涯优势','value' => $strength_interest],
            ],
        ];
        
        //阅读，数学，科学三门考试
        $where_exam['member_id'] = $member_id;
        if($distribution_id) $where_exam['distribution_id'] = $distribution_id;
        $exam = db('evaluation_exam')
            ->field('read,math,science,read_avg,math_avg,science_avg,
            language,thinking,appreciation,culture,reasoning,modeling,operation,imagine,analysis,abstract,concept,thought,explore,attitude,
            language_avg,thinking_avg,appreciation_avg,culture_avg,
            reasoning_avg,modeling_avg,operation_avg,imagine_avg,analysis_avg,abstract_avg,
            concept_avg,thought_avg,explore_avg,attitude_avg')
            ->where($where_exam)
            ->find();

        //画像科目
        $data['portrait'][] = [
            'name' => 'literacy',
            'options' => [
                ['label' => '数学素养','value' => $exam['math'].'%'],
                ['label' => '阅读素养','value' => $exam['read'].'%'],
                ['label' => '科学素养','value' => round($exam['science'] * 100 / 150, 2).'%'],
            ],
        ];

        //常模
        $math_mode = 51.23;
        $read_mode = 64.30;
        $science_mode = 43.33;//round(60.56 * 100 / 150,2)

        //初始规则：数学[0,39,40,59,60,74,75,89,90,100]，阅读[0,50,51,61,62,70,71,82,83,120]，科学[0,18,19,23,24,26,27,32,33,40]
        //初始规则不包括小数点，所以作了修改，只保留右边的数字
        $math_level = $this->total_score_to_level($exam['math'],[32,51,71,90,100]);
        $read_level = $this->total_score_to_level($exam['read'],[52,60,68,80,100]);
        $science_level = $this->total_score_to_level($exam['science'],[35,48.5,83,120,150]);

        //数学转换比例
        $exam_science = round($exam['science'] * 100 / 150,2);
        $data['subject_literacy']['table'] =
        [
            'math' => [
                $exam['math'],
                $math_mode,
                $math_level
            ],
            'read' => [
                $exam['read'],
                $read_mode,
                $read_level
            ],
            'science' => [
                $exam_science,
                $science_mode,
                $science_level
            ],
        ];

        $competence_shine = array_flip(competence_shine());//各科素养汉字与英文下标对应关系

        $reasoning_level = $this->score_to_level($exam['reasoning'],[0,7.5,7.6,12.9,13.0,17.8,17.9,21.7,21.8]);
        $modeling_level = $this->score_to_level($exam['modeling'],[0,7.7,7.8,12.5,12.6,19.7,19.8,28.1,28.2]);
        $operation_level = $this->score_to_level($exam['operation'],[0,10.4,10.5,16.2,16.3,23.0,23.1,29.4,29.5]);
        $imagine_level = $this->score_to_level($exam['imagine'],[0,4.2,4.3,8.5,8.6,10.7,10.8,12.7,12.8]);
        $data['competence']['math'] =[
            ['数学推理',$exam['reasoning'],$exam['reasoning_avg'],$reasoning_level],
            ['数学建模',$exam['modeling'],$exam['modeling_avg'],$modeling_level],
            ['数学运算',$exam['operation'],$exam['operation_avg'],$operation_level],
            ['直观想象',$exam['imagine'],$exam['imagine_avg'],$imagine_level],
        ];
        $math_level_arr = [
            'reasoning'=>$reasoning_level,
            'modeling'=>$modeling_level,
            'operation'=>$operation_level,
            'imagine'=>$imagine_level,
        ];
        $math_summary = $this->explain_and_advise('math',$math_level_arr,5,$competence_shine);

        $data['competence']['math_summary'] = [
            'high'=>$math_summary[0],
            'low'=>$math_summary[1],
            'high_word'=>$math_summary[2],
            'low_word'=>$math_summary[3],
        ];

        $language_level = $this->score_to_level($exam['language'],[0,17.0,17.1,19.7,19.8,22.4,22.5,26,26.1]);
        $thinking_level = $this->score_to_level($exam['thinking'],[0,12.1,12.2,15.2,15.3,18,18.1,21.9,22]);
        $appreciation_level = $this->score_to_level($exam['appreciation'],[0,4.3,4.4,5.3,5.4,6.8,6.9,8.8,8.9]);
        $culture_level = $this->score_to_level($exam['culture'],[0,16.5,16.6,18.1,18.2,20.2,20.3,23.2,23.3]);
        $data['competence']['read'] =[
            ['语言建构与运用',$exam['language'],$exam['language_avg'],$language_level],
            ['思维发展与提升',$exam['thinking'],$exam['thinking_avg'],$thinking_level],
            ['审美鉴赏与创造',$exam['appreciation'],$exam['appreciation_avg'],$appreciation_level],
            ['文化传承与理解',$exam['culture'],$exam['culture_avg'],$culture_level],
        ];
        $read_level_arr = [
            'language'=>$language_level,
            'thinking'=>$thinking_level,
            'appreciation'=>$appreciation_level,
            'culture'=>$culture_level,
        ];
        $read_summary = $this->explain_and_advise('read',$read_level_arr,3,$competence_shine);

        $data['competence']['read_summary'] = [
            'high'=>$read_summary[0],
            'low'=>$read_summary[1],
            'high_word'=>$read_summary[2],
            'low_word'=>$read_summary[3],
        ];

        $concept_level = $this->score_to_level($exam['concept'],[0,14.39,14.4,20.09,20.1,35.19,35.2,49.29,49.3]);
        $thought_level = $this->score_to_level($exam['thought'],[0,11.29,11.3,17.14,17.15,28.99,29,46.19,46.2]);
        $explore_level = $this->score_to_level($exam['explore'],[0,5.79,5.8,8.54,8.55,13.64,13.65,17.54,17.55]);
        $attitude_level = $this->score_to_level($exam['attitude'],[0,1.99,2,2.99,3,5.39,5.4,9.64,9.65]);
        $data['competence']['science'] =[
            ['科学观念',$exam['concept'],$exam['concept_avg'],$concept_level],
            ['科学思维',$exam['thought'],$exam['thought_avg'],$thought_level],
            ['探究实践',$exam['explore'],$exam['explore_avg'],$explore_level],
            ['态度责任',$exam['attitude'],$exam['attitude_avg'],$attitude_level],
        ];
        $science_level_arr = [
            'concept'=>$concept_level,
            'thought'=>$thought_level,
            'explore'=>$explore_level,
            'attitude'=>$attitude_level,
        ];
        $science_summary = $this->explain_and_advise('science',$science_level_arr,3,$competence_shine);

        $data['competence']['science_summary'] = [
            'high'=>$science_summary[0],
            'low'=>$science_summary[1],
            'high_word'=>$science_summary[2],
            'low_word'=>$science_summary[3],
        ];

        //总结与建议
        $data['summarize']['learn']['discription'] = $data['motivation']['summarize'] . $data['efficacy']['summarize'] . $data['ability']['level']['summarize'] . $data['goal']['summarize'] . $strength_interest_description;

        if($exam['math'] > $exam['read'] && $exam['math'] > $exam_science){
            $data['summarize']['subject']['max'] = [
                'subject'=>'数学',
                'high'=>$math_summary[0],
                'low'=>$math_summary[1],
            ];
        }elseif($exam['read'] > $exam['math'] && $exam['read'] > $exam_science){
            $data['summarize']['subject']['max'] = [
                'subject'=>'阅读',
                'high'=>$read_summary[0],
                'low'=>$read_summary[1],
            ];
        }else{
            $data['summarize']['subject']['max'] = [
                'subject'=>'科学',
                'high'=>$science_summary[0],
                'low'=>$science_summary[1],
            ];
        }

        if($exam['math'] < $exam['read'] && $exam['math'] < $exam_science){
            $data['summarize']['subject']['min'] = [
                'subject'=>'数学',
                'high'=>$math_summary[0],
                'low'=>$math_summary[1],
            ];
        }elseif($exam['read'] < $exam['math'] && $exam['read'] < $exam_science){
            $data['summarize']['subject']['min'] = [
                'subject'=>'阅读',
                'high'=>$read_summary[0],
                'low'=>$read_summary[1],
            ];
        }else{
            $data['summarize']['subject']['min'] = [
                'subject'=>'科学',
                'high'=>$science_summary[0],
                'low'=>$science_summary[1],
            ];
        }

        $school_id   = db('member')->where('id', $member_id)->value('school_id');
        $data['logo'] = $this->backend_logic->logo_school($school_id);
        return $data;
    }

    public function career_score_to_level($score,$norm){
        if($score >= $norm[0] && $score <= $norm[1]){
            $level = '低';
        }elseif($score >= $norm[2] && $score <= $norm[3]){
            $level = '中';
        }elseif($score >= $norm[4] && $score <= $norm[5]){
            $level = '高';
        }else{
            return '低';
        }
        return $level;
    }

    public function score_to_level($score,$norm){
        if($score <= $norm[1]){
            $level = 1;
        }elseif($score >= $norm[2] && $score <= $norm[3]){
            $level = 2;
        }elseif($score >= $norm[4] && $score <= $norm[5]){
            $level = 3;
        }elseif($score >= $norm[6] && $score <= $norm[7]){
            $level = 4;
        }elseif($score >= $norm[8]){
            $level = 5;
        }
        return $level;
    }

    public function learning_ability_level($score,$norm){
        if($score >= $norm[0] && $score <= $norm[1]){
            $level = 'novice';
        }elseif($score >= $norm[2] && $score <= $norm[3]){
            $level = 'improver';
        }elseif($score >= $norm[4] && $score <= $norm[5]){
            $level = 'proficient';
        }elseif($score >= $norm[6] && $score <= $norm[7]){
            $level = 'expert';
        }else{
            return 'novice';
        }
        return $level;
    }

    public function motivation_type($weak_motivation,$strong_motivation){
        switch (true){
            case $weak_motivation == '低' && $strong_motivation == '低':
                return 1;
            case $weak_motivation == '低' && $strong_motivation == '中':
                return 2;
            case $weak_motivation == '低' && $strong_motivation == '高':
                return 3;
            case $weak_motivation == '中' && $strong_motivation == '低':
                return 4;
            case $weak_motivation == '中' && $strong_motivation == '中':
                return 5;
            case $weak_motivation == '中' && $strong_motivation == '高':
                return 6;
            case $weak_motivation == '高' && $strong_motivation == '低':
                return 7;
            case $weak_motivation == '高' && $strong_motivation == '中':
                return 8;
            case $weak_motivation == '高' && $strong_motivation == '高':
                return 9;
            default:
                return 0; // 默认返回值，表示未匹配到任何类型
        }
        return $type;
    }

    public function efficacy_type($total, $academic)
    {
        $norm = [12, 14, 7, 7];//常模
        // 初始化高低标志
        $hasHigh = false;
        $hasLow = false;

        // 遍历学术效能感数据，判断是否存在高分或低分项，维度分>=阈值视为高分
        foreach ($academic as $index => $value) {
            if ($value >= $norm[$index]) {
                $hasHigh = true;
            }
            if ($value < $norm[$index]) {
                $hasLow = true;
            }
        }

        // 根据总效能感和具体效能感类型进行分类
        switch (true) {
            case $total == '高' && !$hasLow:
                return 1; // 高效能感，无低分项
            case $total == '高' && $hasLow:
                return 2; // 高效能感，存在低分项
            case $total == '中' && !$hasLow:
                return 3; // 中效能感，无低分项
            case $total == '中' && $hasHigh:
                return 4; // 中效能感，存在高分项
            case $total == '中' && $hasLow:
                return 5; // 中效能感，存在低分项
            case $total == '低' && $hasHigh:
                return 6; // 低效能感，存在高分项
            case $total == '低' && !$hasHigh:
                return 7; // 低效能感，无高分项，维度分>=阈值视为高分
            default:
                return 0; // 默认返回值，表示未匹配到任何类型
        }
    }

    public function goal_type($competition,$avoid,$master)
    {
        switch (true) {
            case $competition == '高' && $avoid != '高' && $master != '高':
                return 1; //卓越追求者：高竞争型，成就竞争目标 高+成就回避目标 中或低+成就掌握目标 中或低
            case $competition != '高' && $avoid == '高' && $master != '高':
                return 2; //稳健前行者：高回避型，成就竞争目标 中或低+成就回避目标 高+成就掌握目标 中或低
            case $competition != '高' && $avoid != '高' && $master == '高':
                return 3; //知识探索者：高掌握型，成就竞争目标 中或低+成就回避目标 中或低+成就掌握目标 高
            case $competition == '高' && $avoid != '高' && $master == '高':
                return 4; //全面发展之星：高竞争+掌握型，成就竞争目标 高+成就回避目标 中或低+成就掌握目标 高
            case $competition != '高' && $avoid == '高' && $master == '高':
                return 5;//谨慎学者之星：高掌握+回避型，成就竞争目标 中或低+成就回避目标 高+成就掌握目标 高
            case $competition == '高' && $avoid == '高' && $master != '高':
                return 6; //审慎挑战之星：高竞争+回避型，成就竞争目标 高+成就回避目标 高+成就掌握目标 中或低
            case $competition == '高' && $avoid == '高' && $master == '高':
                return 7; //全能战士：全面高型，成就竞争目标 高+成就回避目标 高+成就掌握目标 高
            case $competition == '中' && $avoid == '中' && $master == '中':
                return 8;//均衡发展：全面中等型，成就竞争目标 中+成就回避目标 中+成就掌握目标 中
            case $competition == '低' && $avoid == '低' && $master == '低':
                return 9; //蓄势待发：全面低型，成就竞争目标 低+成就回避目标 低+成就掌握目标 低
            default:
                return 9; // 默认蓄势待发
        }
    }

    public function strengths_interest_type($strengths,$interests,$tenth_grade_strengths_interest)
    {
        $strengths_norm = [0,22,23,29,30,40];//优势职业领域常模
        $interest_norm = [0,15,16,21,22,30];//兴趣常模

        $hobby_i = 0;
        $development_type = [];
        $tenth_grade_strengths_name = Config::get('tenth_grade_strengths_name');
        foreach ($strengths as $k => $strength){
            $advantage[$k] = $this->career_score_to_level($strength,$strengths_norm);
            if($advantage[$k] == '高'){
                $development_type[] = $tenth_grade_strengths_name[$k];//将优势领域名称保存下来
            }
        }
        foreach ($interests as $key => $interest){
            $hobby[$key] = $this->career_score_to_level($interest,$interest_norm);
            if($hobby[$key] == '低'){
                $hobby_i++;
            }
        }

        $strength_area = [];
        $potential_area = [];

        if($advantage[0] == '高' && $hobby['R'] == '高'){
            $strength_area[] = $tenth_grade_strengths_interest['high_engineering_career_advantages_high_realistic'];
        }

        if($advantage[1] == '高' && $hobby['I'] == '高') {
            $strength_area[] = $tenth_grade_strengths_interest['high_research_career_advantages_high_investigative'];
        }
        if($advantage[2] == '高' && $hobby['A'] == '高'){
            $strength_area[] = $tenth_grade_strengths_interest['high_arts_career_advantages_high_artistic'];
        }
        if($advantage[3] == '高' && $hobby['S'] == '高' && $interests['S'] > $interests['E']){
            $strength_area[] = $tenth_grade_strengths_interest['high_social_career_advantages_high_social'];
        }
        if($advantage[3] == '高' && $hobby['E'] == '高' && $interests['E'] > $interests['S']){
            $strength_area[] = $tenth_grade_strengths_interest['high_social_career_advantages_high_enterprising'];
        }
        if($advantage[3] == '高' && $hobby['S'] == '高' && $interests['S'] == $interests['E']){
            $strength_area[] = $tenth_grade_strengths_interest['high_social_career_advantages_high_social_high_enterprising'];
        }
        if($advantage[4] == '高' && $hobby['C'] == '高'){
            $strength_area[] = $tenth_grade_strengths_interest['high_administrative_career_advantages_high_conventional'];
        }
        if($advantage[0] != '高' && $hobby['R'] == '高'){
            $potential_area[] = $tenth_grade_strengths_interest['low_engineering_career_advantages_high_realistic'];
        }
        if($advantage[1] != '高' && $hobby['I'] == '高'){
            $potential_area[] = $tenth_grade_strengths_interest['low_research_career_advantages_high_investigative'];
        }
        if($advantage[2] != '高' && $hobby['A'] == '高'){
            $potential_area[] = $tenth_grade_strengths_interest['low_arts_career_advantages_high_artistic'];
        }
        if($advantage[3] != '高' && $hobby['S'] == '高' && $interests['S'] > $interests['E']){
            $potential_area[] = $tenth_grade_strengths_interest['low_social_career_advantages_high_social'];
        }
        if($advantage[3] != '高' && $hobby['E'] == '高' && $interests['E'] > $interests['S']){
            $potential_area[] = $tenth_grade_strengths_interest['low_social_career_advantages_high_enterprising'];
        }
        if($advantage[3] != '高' && $hobby['S'] == '高' && $interests['S'] == $interests['E']){
            $potential_area[] = $tenth_grade_strengths_interest['low_social_career_advantages_high_social_high_enterprising'];
        }
        if($advantage[4] != '高' && $hobby['C'] == '高'){
            $potential_area[] = $tenth_grade_strengths_interest['low_administrative_career_advantages_high_conventional'];
        }

        $development_area_description_central = '';
        if($hobby_i == 6 && !empty($development_type)){
            $development_area_description_central = sprintf($tenth_grade_strengths_interest['low_advantages_low_interest']['description_central'],implode('、',$development_type));
        }
        //此处加0是为了和优势和潜能结构一致，方便前端调用
        $development_area[0]['description'] = $tenth_grade_strengths_interest['low_advantages_low_interest']['description_front'].$development_area_description_central.$tenth_grade_strengths_interest['low_advantages_low_interest']['description_rear'];
        $development_area[0]['advice'] = $tenth_grade_strengths_interest['low_advantages_low_interest']['advice'];
        //只展示一个级别数据，优先级：优势>潜力>待发展
        if(!empty($strength_area)){
            return [$strength_area,[],[]];
        }elseif(!empty($potential_area)){
            return [[],$potential_area,[]];
        }else{
            return [[],[],$development_area];
        }

    }

    public function ability_advice($scores,$advices)
    {
        $norm = [16, 18, 18, 19];//常模

        //（1）就子维度得分在关键阈值以下的学生给出提升建议，
        //（2）如果4个子维度得分都>=关键阈值，就给3条精进建议。
        $low_norm_dimension = [];
        $low = 0;//$low等于0时代表4个子维度得分都>=关键阈值
        foreach ($scores as $k => $score){
            if($score < $norm[$k]){
                $low++;
                $low_norm_dimension[$k] = $advices['dimension'][$k]['advice'];
            }
        }
        if($low == 0){
            return array_slice($advices['all_excellent_advice'], 0, 3);
        }
        //最终选择建议个数
        $advice_count = [
            1 => 2, //1个子维度选2条，规则是在此维度相应的建议随机选2条；
            2 => 4, //2个子维度选4条，规则是在相应的建议里各随机选2条；
            3 => 4, //3个子维度选4条，规则是在相应的建议里各随机选1条，第四条在余下的建议里随机选一个；
            4 => 6  //4个子维度选6条，规则是在相应的建议里各随机选1条，剩下两条在余下的建议里随机选2个。
        ];
        $total_advice_needed = $advice_count[$low];
        $advice = [];
        $remaining_advice = [];
        foreach ($low_norm_dimension as $v){
            shuffle($v);
            if($low == 1 || $low == 2){
                $advice = array_merge($advice,array_slice($v, 0, 2));
            }
            elseif($low == 3 || $low == 4){
                $advice = array_merge($advice,array_slice($v, 0, 1));
                $remaining_advice = array_merge($remaining_advice,array_slice($v, 1, 2));
            }
        }
        //个数不足的从余下的建议中选择不足的个数补足
        if($low == 3 || $low == 4){
            shuffle($remaining_advice);
            $advice = array_merge($advice,array_slice($remaining_advice, 0, $total_advice_needed - count($advice)));
        }

        return $advice;
    }

    public function total_score_to_level($score,$norm){
        if($score < $norm[0]){
            $level = 1;
        }elseif($score >= $norm[0] && $score < $norm[1]){
            $level = 2;
        }elseif($score >= $norm[1] && $score < $norm[2]){
            $level = 3;
        }elseif($score >= $norm[2] && $score < $norm[3]){
            $level = 4;
        }elseif($score >= $norm[3]){
            $level = 5;
        }
        return $level;
    }

    public function explain_and_advise($subject = 'math',$subject_level_arr,$num = 5,$competence_shine){
        $subject_setting = Config::get($subject);
        arsort($subject_level_arr);//根据value排序
        $math_rank = array_values($subject_level_arr);//第0个是排名最高的，第5个是排名最低的
        $youshi_text=$lieshi_text=$youshi_word=$lieshi_word=[];
        foreach ($subject_level_arr as $key => $value){
            if($value == $math_rank[0] && $value >= 3){
                $youshi[] = $key;//只要和第0个相等且大于等级3的都是优势素养
                $youshi_text[] = $competence_shine[$key];
                $subject_setting[$key]['high']['advice'] = array_slice($subject_setting[$key]['high']['advice'],0,4);//只显示前四个，数组长度为3并不会报错
                $youshi_word[] = $subject_setting[$key]['high'];
            }elseif($value < 3){
                $lieshi[] = $key;//只要和第5个相等且小于等级3的都是劣势素养
                $lieshi_text[] = $competence_shine[$key];
                $subject_setting[$key]['low']['advice'] = array_slice($subject_setting[$key]['low']['advice'],0,4);
                $lieshi_word[] = $subject_setting[$key]['low'];
            }
        }
        return [$youshi_text,$lieshi_text,$youshi_word,$lieshi_word];
    }


}