<?php

namespace App\Listeners\DataSync;

use App\Events\DataSync\StudentCreated;
use App\Services\DataSync\DataSyncService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * 同步学生数据监听器
 */
class SyncStudentData implements ShouldQueue
{
    use InteractsWithQueue;

    protected $dataSyncService;

    /**
     * Create the event listener.
     */
    public function __construct(DataSyncService $dataSyncService)
    {
        $this->dataSyncService = $dataSyncService;
    }

    /**
     * Handle the event.
     */
    public function handle(StudentCreated $event): void
    {
        try {
            $result = $this->dataSyncService->syncStudent($event->studentData);
            
            if (!$result['success']) {
                Log::error('学生数据同步失败', [
                    'student_data' => $event->studentData,
                    'error' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('学生数据同步监听器异常', [
                'student_data' => $event->studentData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(StudentCreated $event, \Throwable $exception): void
    {
        Log::error('学生数据同步队列任务失败', [
            'student_data' => $event->studentData,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
