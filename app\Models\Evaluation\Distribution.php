<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * 分发模型
 */
class Distribution extends Model
{
    protected $table = 'evaluation_distributions';

    protected $fillable = [
        'title',
        'paper_ids',
        'grade_id',
        'class_ids',
        'member_ids',
        'distribution_by',
        'role_id',
        'from_id',
        'school_ids',
        'status',
        'start_time',
        'end_time',
        'time_limit',
        'description',
        'creator_id',
        'school_id',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'grade_id' => 'integer',
        'distribution_by' => 'integer',
        'role_id' => 'integer',
        'from_id' => 'integer',
        'status' => 'integer',
        'time_limit' => 'integer',
        'creator_id' => 'integer',
        'school_id' => 'integer',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    /**
     * 分发详情关联
     */
    public function details(): HasMany
    {
        return $this->hasMany(DistributionDetail::class, 'distribution_id')->where('status', 0);
    }

    /**
     * 老师学生分配关联
     */
    public function teacherStudents(): HasMany
    {
        return $this->hasMany(DistributionTeachersStudents::class, 'distribution_id')->where('status', 0);
    }

    /**
     * 答题日志关联
     */
    public function logs(): HasMany
    {
        return $this->hasMany(EvaluationLog::class, 'distribution_id');
    }

    /**
     * 答题记录关联
     */
    public function answers(): HasMany
    {
        return $this->hasMany(EvaluationAnswer::class, 'distribution_id');
    }

    /**
     * 试卷关联（多对多）
     */
    public function papers(): BelongsToMany
    {
        return $this->belongsToMany(Papers::class, 'evaluation_distribution_details', 'distribution_id', 'paper_id')
            ->wherePivot('status', 0)
            ->withTimestamps();
    }

    /**
     * 创建者关联
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'distribution_by');
    }

    /**
     * 年级关联
     */
    public function grade(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School\System\Grade::class, 'grade_id');
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 作用域：按学校筛选
     */
    public function scopeBySchool($query, $schoolId)
    {
        return $query->where('school_ids', 'like', '%' . $schoolId . '%');
    }

    /**
     * 作用域：按年级筛选
     */
    public function scopeByGrade($query, $gradeId)
    {
        return $query->where('grade_id', $gradeId);
    }

    /**
     * 作用域：按创建者筛选
     */
    public function scopeByCreator($query, $creatorId)
    {
        return $query->where('distribution_by', $creatorId);
    }

    /**
     * 获取试卷ID数组
     */
    public function getPaperIdsArray(): array
    {
        if (empty($this->paper_ids)) {
            return [];
        }
        return explode(',', $this->paper_ids);
    }

    /**
     * 设置试卷ID数组
     */
    public function setPaperIdsArray(array $paperIds): void
    {
        $this->paper_ids = implode(',', $paperIds);
    }

    /**
     * 获取班级ID数组
     */
    public function getClassIdsArray(): array
    {
        if (empty($this->class_ids)) {
            return [];
        }
        return explode(',', $this->class_ids);
    }

    /**
     * 设置班级ID数组
     */
    public function setClassIdsArray(array $classIds): void
    {
        $this->class_ids = implode(',', $classIds);
    }

    /**
     * 获取学生ID数组
     */
    public function getMemberIdsArray(): array
    {
        if (empty($this->member_ids)) {
            return [];
        }
        return explode(',', $this->member_ids);
    }

    /**
     * 设置学生ID数组
     */
    public function setMemberIdsArray(array $memberIds): void
    {
        $this->member_ids = implode(',', $memberIds);
    }

    /**
     * 获取学校ID数组
     */
    public function getSchoolIdsArray(): array
    {
        if (empty($this->school_ids)) {
            return [];
        }
        return explode(',', $this->school_ids);
    }

    /**
     * 设置学校ID数组
     */
    public function setSchoolIdsArray(array $schoolIds): void
    {
        $this->school_ids = implode(',', $schoolIds);
    }

    /**
     * 检查分发是否已开始
     */
    public function isStarted(): bool
    {
        if (!$this->start_time) {
            return true; // 没有设置开始时间，认为已开始
        }
        return now() >= $this->start_time;
    }

    /**
     * 检查分发是否已结束
     */
    public function isEnded(): bool
    {
        if (!$this->end_time) {
            return false; // 没有设置结束时间，认为未结束
        }
        return now() > $this->end_time;
    }

    /**
     * 检查分发是否进行中
     */
    public function isActive(): bool
    {
        return $this->isStarted() && !$this->isEnded() && $this->status == 0;
    }

    /**
     * 获取分发状态文本
     */
    public function getStatusTextAttribute(): string
    {
        if ($this->status == -1) {
            return '已删除';
        }

        if ($this->status == 0) {
            if (!$this->isStarted()) {
                return '未开始';
            } elseif ($this->isEnded()) {
                return '已结束';
            } else {
                return '进行中';
            }
        }

        return '未知状态';
    }

    /**
     * 获取参与学生数量
     */
    public function getStudentCount(): int
    {
        return count($this->getMemberIdsArray());
    }

    /**
     * 获取试卷数量
     */
    public function getPaperCount(): int
    {
        return count($this->getPaperIdsArray());
    }

    /**
     * 获取完成情况统计
     */
    public function getCompletionStats(): array
    {
        $totalStudents = $this->getStudentCount();
        $completedCount = $this->logs()->where('check_status', 1)->count();
        $pendingCount = $this->logs()->where('check_status', 0)->count();

        return [
            'total_students' => $totalStudents,
            'completed_count' => $completedCount,
            'pending_count' => $pendingCount,
            'completion_rate' => $totalStudents > 0 ? round(($completedCount / $totalStudents) * 100, 2) : 0
        ];
    }

    /**
     * 检查是否可以删除
     */
    public function canDelete(): bool
    {
        // 检查是否有答题记录
        if ($this->answers()->exists()) {
            return false;
        }

        // 检查是否有答题日志
        if ($this->logs()->exists()) {
            return false;
        }

        return true;
    }

    /**
     * 软删除
     */
    public function softDelete(): bool
    {
        return $this->update(['status' => -1]);
    }

    /**
     * 恢复软删除
     */
    public function restore(): bool
    {
        return $this->update(['status' => 0]);
    }
}
