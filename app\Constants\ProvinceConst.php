<?php

namespace App\Constants;

/*/*
|--------------------------------------------------------------------------
| 公用常量库
|--------------------------------------------------------------------------
|
| This script returns the application instance. The instance is given to
| the calling script we can separate the building of the instances
| from the actual running of the application and sending responses.
|
*/

class ProvinceConst
{
    // 省份 ID、拼音、中文名称的映射
    public static $sfMapById = [];
    //sfMapByName
    public static $sfMapByName = [];
    // 静态初始化
    public static function initialize()
    {
        $sfId = ["0", "-1", "-2", "-3", "-4", "-5", "-6", "32", "33", "34", "-10",
            "1", "2", "3", "4", "5", "-16", "-17", "-18", "-19", "-20",
            "6", "7", "8", "-24", "-25", "-26", "-27", "-28", "-29", "-30",
            "9", "10", "11", "12", "13", "14", "15", "-38", "-39", "-40",
            "16", "17", "18", "19", "20", "21", "-47", "-48", "-49", "22",
            "23", "24", "25", "26", "-55", "-56", "-57", "-58", "-59", "-60",
            "27", "28", "29", "30", "31"];

        $sfPy = ["0", "1", "2", "3", "4", "5", "6", "taiwan", "xianggang", "aomen", "10",
            "beijing", "tianjin", "hebei", "shanxi", "neimenggu", "16", "17", "18", "19", "20",
            "liaoning", "jilin", "heilongjiang", "24", "25", "26", "27", "28", "29", "30",
            "shanghai", "jiangsu", "zhejiang", "anhui", "fujian", "jiangxi", "shandong", "38", "39", "40",
            "henan", "hubei", "hunan", "guangdong", "guangxi", "hainan", "47", "48", "49", "chongqing",
            "sichuan", "guizhou", "yunnan", "xizang", "55", "56", "57", "58", "59", "60",
            "shaanxi", "gansu", "qinghai", "ningxia", "xinjiang"];

        $sfCn = ["0", "1", "2", "3", "4", "5", "6", "台湾", "香港", "澳门", "10",
            "北京", "天津", "河北", "山西", "内蒙古", "16", "17", "18", "19", "20",
            "辽宁", "吉林", "黑龙江", "24", "25", "26", "27", "28", "29", "30",
            "上海", "江苏", "浙江", "安徽", "福建", "江西", "山东", "38", "39", "40",
            "河南", "湖北", "湖南", "广东", "广西", "海南", "47", "48", "49", "重庆",
            "四川", "贵州", "云南", "西藏", "55", "56", "57", "58", "59", "60",
            "陕西", "甘肃", "青海", "宁夏", "新疆"];

        for ($i = 0; $i < count($sfId); $i++) {
            if (intval($sfId[$i]) > 0) {
                self::$sfMapById[intval($sfId[$i])] = [
                    'id' => intval($sfId[$i]),
                    'code' => $i,
                    'province_name' => $sfCn[$i],
                    'province_spell' => $sfPy[$i],
                ];
                self::$sfMapByName[$sfCn[$i]] = [
                    'id' => intval($sfId[$i]),
                    'code' => $i,
                    'province_name' => $sfCn[$i],
                    'province_spell' => $sfPy[$i],
                ];
            }
        }
    }
}
// 初始化省份常量
ProvinceConst::initialize();




