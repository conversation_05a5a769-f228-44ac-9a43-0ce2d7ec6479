<?php

namespace App\Enums;

enum SystemRoleTypeEnum: int
{
    case STUDENT = 1;
    case ACADEMIC_AFFAIRS = 2;
    case TEACHER = 3;
    case EDUCATION_BUREAU = 4;
    case PARENT = 5;
    case SYSTEM_ADMIN = 999;

    /**
     * 获取角色类型名称
     *
     * @return string
     */
    public function getName(): string
    {
        return match($this) {
            self::STUDENT => '学生',
            self::ACADEMIC_AFFAIRS => '教务',
            self::TEACHER => '教师',
            self::EDUCATION_BUREAU => '教育局',
            self::PARENT => '家长',
            self::SYSTEM_ADMIN => '系统管理员',// 只有后端管理人员配这个角色类型，学校、教育局都不会配这个角色类型
        };
    }

    /**
     * 获取所有角色类型
     *
     * @return array<int, string>
     */
    public static function getAll(): array
    {
        return array_combine(
            array_column(self::cases(), 'value'),
            array_map(fn($case) => $case->getName(), self::cases())
        );
    }
}