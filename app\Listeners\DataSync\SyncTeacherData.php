<?php

namespace App\Listeners\DataSync;

use App\Events\DataSync\TeacherCreated;
use App\Services\DataSync\DataSyncService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * 同步教师数据监听器
 */
class SyncTeacherData implements ShouldQueue
{
    use InteractsWithQueue;

    protected $dataSyncService;

    /**
     * Create the event listener.
     */
    public function __construct(DataSyncService $dataSyncService)
    {
        $this->dataSyncService = $dataSyncService;
    }

    /**
     * Handle the event.
     */
    public function handle(TeacherCreated $event): void
    {
        try {
            $result = $this->dataSyncService->syncTeacher($event->teacherData);
            
            if (!$result['success']) {
                Log::error('教师数据同步失败', [
                    'teacher_data' => $event->teacherData,
                    'error' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('教师数据同步监听器异常', [
                'teacher_data' => $event->teacherData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(TeacherCreated $event, \Throwable $exception): void
    {
        Log::error('教师数据同步队列任务失败', [
            'teacher_data' => $event->teacherData,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
