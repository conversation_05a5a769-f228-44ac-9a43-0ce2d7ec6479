<?php

namespace App\Http\Requests;


class UserRequest extends BaseRequest
{
        public function rules(): array
    {
        $method = $this->route()->getActionMethod();

        return match ($method) {
            'resetPasswordNoLogin' => $this->resetPasswordRules(),
            default => []
        };
    }

    private function resetPasswordRules(): array
    {
        return [
            'username' => 'required|string|min:4',
            'old_password' => 'required|string',
            'new_password' => 'required|string|min:8|regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/',
            'new_password_again' => 'required|string|same:new_password',
        ];
    }
    
    public function messages(): array
    {
        return [
            'username.required' => '用户名不能为空',
            'old_password.required' => '旧密码不能为空',
            'new_password.required' => '新密码不能为空',
            'new_password.min' => '新密码至少8个字符',
            'new_password.regex' => '密码至少8位,必须包含大写字母、小写字母、数字',
            'new_password_again.required' => '确认密码不能为空',
            'new_password_again.same' => '两次密码不一致',
        ];
    }
}
