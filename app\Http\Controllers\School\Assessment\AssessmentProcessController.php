<?php

namespace App\Http\Controllers\School\Assessment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests\School\Assessment\AssessmentRequest;

class AssessmentProcessController extends Controller
{

    // 构造函数注入服务
    public function __construct(
        protected Request $request,
        protected $dynamicService
    ) {
    }

    /**
     * 保存测评答案
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveAnswer(AssessmentRequest $request)
    {
        $result = $this->dynamicService->submitAnswer($request->validated(), $request->user());
        return $this->success($result);
    }

    /**
     * 教师端提交测评答案并直接生成报告（不保存数据）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function teacherSubmitAndReport(AssessmentRequest $request)
    {
        // 直接生成报告，不保存数据
        $report = $this->dynamicService->generateReportFromAnswers($request->validated(), $request->user());
        return $this->success($report);
    }

    /**
     * 个人报告
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function report(AssessmentRequest $request)
    {
        //生成报告，根据不同测评类型进行不同的处理 工厂模式
        $report = $this->dynamicService->generateReport($request->validated());
        return $this->success($report);
    }

    /**
     * 团体报告
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function groupReport(AssessmentRequest $request)
    {
        try {
            //生成报告，根据不同测评类型进行不同的处理 工厂模式
            $school_id = $request->user()->organization->model_id;
            $report = $this->dynamicService->generateReport($request->validated(), $school_id);
            return $this->success($report);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

}
