<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use  \Illuminate\Support\Facades\Hash;

class CreateDemoUser extends Command
{
    protected $signature = 'create:demouser {--gender=} {--pwd=} {--username=} {--real_name=} {--role_id=}';

    protected $description = '创建测试用户';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        //$param1 = $this->argument('param1');
        $gender   = $this->option('gender');
        $pwd      = $this->option('pwd');
        $username = $this->option('username');
        $real_name     = $this->option('real_name');
        $role_id  = $this->option('role_id');
        if (empty($gender)) {
            echo "gender参数不能为空";
            exit;
        }
        if (empty($pwd)) {
            echo "pwd参数不能为空";
            exit;
        }
        if (empty($username)) {
            echo "username参数不能为空";
            exit;
        }
        if (empty($real_name)) {
            echo "name参数不能为空";
            exit;
        }
        $info = DB::table("users")->where("username", $username)->first();
        if (!empty($info)) {
            echo $username . "用户名存在";
            exit;
        }
        $data = [
            'username' => $username,
            'real_name' => $real_name,
            'phone' => '13800000000',
            'gender' => $gender,
            'password' => Hash::make($pwd),
            'role_id' => $role_id,
        ];
        DB::table("users")->insert($data);
        echo "添加成功";
    }
}
