<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_schedules', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('school_id')->comment('学校ID');
            $table->unsignedInteger('school_year')->comment('学年');
            $table->string('name')->comment('计划名称');
            $table->tinyInteger('type')->default(1)->comment('计划类型：1基础测评计划 2心理测评计划');
            $table->timestamp('open_time')->nullable()->comment('计划开放时间');
            $table->timestamp('close_time')->nullable()->comment('计划关闭时间，普测2099');
            $table->tinyInteger('is_report_visible')->default(1)->comment('测评报告是否对学生显示,1显示0不显示');
            $table->string('creator', 20)->nullable()->comment('添加人');
            $table->string('updater', 20)->nullable()->comment('更新人');
            $table->softDeletes();
            $table->timestamps();
        });
        DB::statement("ALTER TABLE `assessment_schedules` comment '测评计划表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_schedules');
    }
};
