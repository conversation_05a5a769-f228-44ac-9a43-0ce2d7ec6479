<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use function Laravel\Prompts\select;

/**
 * 科目数据填充
 */
class CourseSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school_id = $this->school_id;
        // 获取所有年级数据
        $grade_list = DB::connection($this->connect)->table('grade')
            ->where('name','>=',2021)// 2021年以后
            ->where('school_id', $school_id)
            ->where('step', 0)
            ->groupBy('grade_sort','school_district')
            ->selectRaw('GROUP_CONCAT(id) as id, grade_sort,school_district')
            ->get()
            ->each(function ($item) {
                // 年级ID组合
                $grade_ids = $item->id;

                // 查询科目
                $course_list = DB::connection($this->connect)->table('course')
                    ->where('step', 0)
                    ->whereIn('grade_id', explode(',', $grade_ids))
                    ->select('id', 'type', 'code','name','grade_id','school_id','school_district')
                    ->get()->toArray();
                if(!empty($course_list)){
                    $course_arr = self::uniqueCourse($course_list);
                    // 年级ID
                    $grade_sort = intval($item->grade_sort);
                    $insert_data = [];
                    foreach ($course_arr as $type=>$value){
                        foreach ($value as $course){
                            $insert_data [] = [
                                'course_name' => $course['name'],
                                'code'=> $course['code'],
                                'type' => $type,
                                'school_id' => $course['school_id'],
                                'school_campus_id' => $course['school_district_id'],
                                'grade_id' => intval($grade_sort),
                                'created_at' => date('Y-m-d H:i:s',time()),
                                'updated_at' => date('Y-m-d H:i:s',time()),
                            ];
                        }
                    }
                    // 保存科目数据到数据表
                    DB::table('courses')->insert($insert_data);
                    dump(count($insert_data)."条科目数据保存完成，年级：".intval($grade_sort));
                }
            });
//        dd('执行完成！');
    }

    /**
     * 对科目数据进行去重，保留相同名称的一个科目信息
     */
    public function uniqueCourse($course_list): array
    {
        // 先按科目类型进行分组
        $course_arr = [];
        foreach ($course_list as $value){
            $type = $value->type;
            $course_arr[$type][] = [
                'name' => $value->name,
                'code' => $value->code,
                'school_id' => $value->school_id,
                'school_district_id' => $value->school_district,
            ];
        }
        // 对科目进行去重
        $distinctByKey = function (array $array, string $key){
            $result = [];
            foreach ($array as $item){
                if(!isset($result[$item[$key]])){
                    $result[$item[$key]] = $item;
                }
{}            }
            return array_values($result);
        };
        // 对科目进行去重
        foreach ($course_arr as $type => $element){
           $course_arr[$type] = $distinctByKey($element, 'name');
        }

        return $course_arr;
    }
}
