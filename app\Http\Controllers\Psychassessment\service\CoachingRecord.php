<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON>
 * Date: 2024/01/19
 * Time: 17:52
 */
namespace app\psychassessment\service;

class CoachingRecord{
    protected $CoachingRecord;
    public function __construct()
    {
          $this->CoachingRecordlogic = new \app\psychassessment\logic\CoachingRecord();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://查询

                return $this->CoachingRecordlogic->get_list();
                break;
            case 'post'://增加

                return $this->CoachingRecordlogic->add();
                break;
            case 'put'://修改

                return $this->CoachingRecordlogic->edit();
                break;
            case 'delete'://删除
                return $this->CoachingRecordlogic->del();
                break;
            default:
                return false;
        }
    }


}