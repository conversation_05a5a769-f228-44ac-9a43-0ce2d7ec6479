<?php

namespace App\Http\Requests\School\Assessment;

use App\Http\Requests\BaseRequest;

class PdfHtmlRequest extends BaseRequest
{
    public function rules(): array
    {
        return [
            'assessment_task_assignment_id' => 'required|integer',
            'assessment_id' => 'required|integer',
        ];
    }

    public function messages(): array
    {
        return [
            'assessment_task_assignment_id.required' => '分发ID不能为空',
            'assessment_task_assignment_id.integer' => '分发ID必须为整数',
            'assessment_id.required' => '测评ID不能为空',
            'assessment_id.integer' => '测评ID必须为整数',
        ];
    }
}
