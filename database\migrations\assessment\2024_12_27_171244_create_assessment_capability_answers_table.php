<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_capability_answers', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('assessment_task_assignment_id')->comment('测评任务指定人表id');
            $table->unsignedInteger('student_id')->comment('学生ID');
            $table->unsignedInteger('assessment_capability_question_id')->comment('五力测评问题表的ID');
            $table->string('answer',255)->comment('选项内容');
            $table->unsignedInteger('school_id')->comment('冗余,便于统计');
            $table->unsignedInteger('assessment_id')->comment('冗余,便于查询或分表分区');
            $table->timestamps();
        });
        DB::statement("ALTER TABLE `assessment_capability_answers` comment '五力测评答题原始数据表'"); //
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_capability_answers');
    }
};
