<?php

namespace App\Http\Requests\Evaluation;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 分发请求验证类
 */
class DistributionRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        $rules = [
            'title' => 'required|string|max:200',
            'paper_ids' => 'required|string',
        ];

        // 根据请求方法添加不同的规则
        if ($this->isMethod('post')) {
            // 创建时的额外规则
            $rules = array_merge($rules, [
                'grade_id' => 'sometimes|integer|min:0',
                'class_ids' => 'sometimes|string',
                'member_ids' => 'sometimes|string',
                'from_id' => 'sometimes|integer|min:0',
                'school_ids' => 'sometimes|string',
                'start_time' => 'sometimes|date',
                'end_time' => 'sometimes|date|after:start_time',
                'time_limit' => 'sometimes|integer|min:0',
                'description' => 'sometimes|string|max:2000',
                'distribution_arr' => 'sometimes|string', // JSON字符串
            ]);
        } elseif ($this->isMethod('put') || $this->isMethod('patch')) {
            // 更新时的规则（大部分字段变为可选）
            $rules = [
                'title' => 'sometimes|string|max:200',
                'paper_ids' => 'sometimes|string',
                'grade_id' => 'sometimes|integer|min:0',
                'class_ids' => 'sometimes|string',
                'member_ids' => 'sometimes|string',
                'from_id' => 'sometimes|integer|min:0',
                'school_ids' => 'sometimes|string',
                'start_time' => 'sometimes|date',
                'end_time' => 'sometimes|date|after:start_time',
                'time_limit' => 'sometimes|integer|min:0',
                'description' => 'sometimes|string|max:2000',
                'distribution_arr' => 'sometimes|string',
            ];
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性名称
     */
    public function attributes(): array
    {
        return [
            'title' => '分发标题',
            'paper_ids' => '试卷',
            'grade_id' => '年级',
            'class_ids' => '班级',
            'member_ids' => '学生',
            'from_id' => '来源',
            'school_ids' => '学校',
            'start_time' => '开始时间',
            'end_time' => '结束时间',
            'time_limit' => '时间限制',
            'description' => '分发描述',
            'distribution_arr' => '分发配置',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            'title.required' => '分发标题不能为空',
            'title.string' => '分发标题必须是字符串',
            'title.max' => '分发标题不能超过200个字符',
            'paper_ids.required' => '试卷不能为空',
            'paper_ids.string' => '试卷必须是字符串',
            'grade_id.integer' => '年级必须是整数',
            'grade_id.min' => '年级不能小于0',
            'class_ids.string' => '班级必须是字符串',
            'member_ids.string' => '学生必须是字符串',
            'from_id.integer' => '来源必须是整数',
            'from_id.min' => '来源不能小于0',
            'school_ids.string' => '学校必须是字符串',
            'start_time.date' => '开始时间格式不正确',
            'end_time.date' => '结束时间格式不正确',
            'end_time.after' => '结束时间必须晚于开始时间',
            'time_limit.integer' => '时间限制必须是整数',
            'time_limit.min' => '时间限制不能小于0',
            'description.string' => '分发描述必须是字符串',
            'description.max' => '分发描述不能超过2000个字符',
            'distribution_arr.string' => '分发配置必须是字符串',
        ];
    }

    /**
     * 配置验证实例
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 自定义验证逻辑
            $this->validatePaperIds($validator);
            $this->validateDistributionArr($validator);
            $this->validateSchoolPermission($validator);
        });
    }

    /**
     * 验证试卷ID
     */
    private function validatePaperIds($validator): void
    {
        $paperIds = $this->input('paper_ids');
        
        if ($paperIds) {
            $paperIdArray = explode(',', $paperIds);
            $paperIdArray = array_filter(array_map('intval', $paperIdArray));
            
            if (empty($paperIdArray)) {
                $validator->errors()->add('paper_ids', '试卷ID格式不正确');
                return;
            }
            
            // 检查试卷是否存在
            $existingCount = \App\Models\Evaluation\Papers::whereIn('id', $paperIdArray)
                ->where('status', 0)
                ->count();
                
            if ($existingCount != count($paperIdArray)) {
                $validator->errors()->add('paper_ids', '部分试卷不存在或已被删除');
            }
        }
    }

    /**
     * 验证分发配置
     */
    private function validateDistributionArr($validator): void
    {
        $distributionArr = $this->input('distribution_arr');
        
        if ($distributionArr) {
            $decoded = json_decode($distributionArr, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $validator->errors()->add('distribution_arr', '分发配置格式不正确');
                return;
            }
            
            if (!is_array($decoded)) {
                $validator->errors()->add('distribution_arr', '分发配置必须是数组');
                return;
            }
            
            // 验证每个分发项的必要字段
            foreach ($decoded as $index => $item) {
                if (!isset($item['paper_id']) || !is_numeric($item['paper_id'])) {
                    $validator->errors()->add('distribution_arr', "分发配置第" . ($index + 1) . "项缺少有效的试卷ID");
                }
                
                if (!isset($item['teacher_ids']) || empty($item['teacher_ids'])) {
                    $validator->errors()->add('distribution_arr', "分发配置第" . ($index + 1) . "项缺少老师ID");
                }
                
                if (isset($item['start_time']) && !strtotime($item['start_time'])) {
                    $validator->errors()->add('distribution_arr', "分发配置第" . ($index + 1) . "项开始时间格式不正确");
                }
                
                if (isset($item['exam_duration']) && (!is_numeric($item['exam_duration']) || $item['exam_duration'] < 0)) {
                    $validator->errors()->add('distribution_arr', "分发配置第" . ($index + 1) . "项考试时长必须是非负数");
                }
            }
        }
    }

    /**
     * 验证学校权限
     */
    private function validateSchoolPermission($validator): void
    {
        $user = auth()->user();
        $schoolIds = $this->input('school_ids');
        
        // 如果不是超级管理员，检查学校权限
        if ($user && $user->role_id != 999 && $user->role_source_id != 1) {
            if ($schoolIds && $user->school_id) {
                $schoolIdArray = explode(',', $schoolIds);
                if (!in_array($user->school_id, $schoolIdArray)) {
                    $validator->errors()->add('school_ids', '您没有权限操作指定的学校');
                }
            }
        }
        
        // 超级管理员创建分发时必须指定学校
        if ($user && ($user->role_id == 999 || $user->role_source_id == 1)) {
            if ($this->isMethod('post') && empty($schoolIds)) {
                $validator->errors()->add('school_ids', '请指定分发的学校');
            }
        }
    }
}
