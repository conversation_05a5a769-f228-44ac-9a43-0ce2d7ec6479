<?php

namespace App\Http\Requests\School\System;

use App\Http\Requests\BaseRequest;

class StudentRequest extends BaseRequest
{
    public function rules(): array
    {
        $method = $this->route()->getActionMethod();

        return match ($method) {
            'store' => $this->storeRules(),
            'batchStore' => $this->batchStoreRules(),
            'batchUpgrade' => $this->batchUpgradeRules(),
            'update' => $this->updateRules(),
            default => []
        };
    }

    private function storeRules(): array
    {
        return [
            'student_name' => 'required|string|max:10',
            'gender' => 'required|integer|in:0,1,2',
            'school_campus_id' => 'required|integer|exists:school_campuses,id',
            'class_id' => 'required|integer|exists:classes,id',
            'school_year' => 'required|integer',
            'username' => 'required|string|unique:users,username',
            'password' => 'required|string|min:6',
            'roles' => 'required|array'
        ];
    }

    private function batchStoreRules(): array
    {
        return [
            'grade_id' => 'required|integer|exists:grades,id',
            'school_campus_id' => 'required|integer|exists:school_campuses,id',
            'students' => 'required|array',
            'students.*.student_name' => 'required|string|max:10',
            'students.*.school_no' => 'required|string|distinct',
            'students.*.class_name' => 'required|string',
            'students.*.username' => 'required|string|unique:users,username',
            'students.*.role_name' => 'required|string',
            'students.*.school_year' => 'required|integer',
        ];
    }

    private function batchUpgradeRules(): array
    {
        return [
            'school_campus_id' => 'required|integer|exists:school_campuses,id',
            'grade_id' => 'required|integer|exists:grades,id',
            'students' => 'required|array',
            'students.*.username' => 'required|string|unique:users,username',
            'students.*.class_name' => 'required|string',
            'students.*.school_year' => 'required|integer',
        ];
    }

    private function updateRules(): array
    {
        return [
            'student_name' => 'required|string|max:10',
            'gender' => 'required|integer|in:0,1,2',
            'class_id' => 'required|integer|exists:classes,id',
            'roles' => 'required|array'
        ];
    }

    public function messages(): array
    {
        return [
            'student_name.required' => '学生姓名不能为空',
            'student_name.max' => '学生姓名最多10个字符',
            'gender.required' => '性别不能为空',
            'gender.integer' => '性别必须为整数',
            'gender.in' => '性别值无效',
            'school_campus_id.required' => '校区ID不能为空',
            'school_campus_id.integer' => '校区ID必须为整数',
            'school_campus_id.exists' => '校区不存在',
            'class_id.required' => '班级ID不能为空',
            'class_id.integer' => '班级ID必须为整数',
            'class_id.exists' => '班级不存在',
            'school_year.required' => '学年不能为空',
            'school_year.integer' => '学年必须为整数',
            'username.required' => '用户名不能为空',
            'username.unique' => '用户名已存在',
            'password.required' => '密码不能为空',
            'password.min' => '密码至少6个字符',
            'roles.required' => '角色不能为空',
            'roles.array' => '角色必须为数组',
            'grade_id.required' => '年级ID不能为空',
            'grade_id.integer' => '年级ID必须为整数',
            'grade_id.exists' => '年级不存在',
            'students.required' => '学生数据不能为空',
            'students.array' => '学生数据必须为数组',
            'students.*.student_name.required' => '学生姓名不能为空',
            'students.*.student_name.max' => '学生姓名最多10个字符',
            'students.*.school_no.required' => '学号不能为空',
            'students.*.school_no.distinct' => '学号不能重复',
            'students.*.class_name.required' => '班级名称不能为空',
            'students.*.username.required' => '用户名不能为空',
            'students.*.username.unique' => '用户名已存在',
            'students.*.role_name.required' => '角色名称不能为空',
            'students.*.school_year.required' => '学年不能为空',
            'students.*.school_year.integer' => '学年必须为整数',
        ];
    }
}
