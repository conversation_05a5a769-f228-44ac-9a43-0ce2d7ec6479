<?php

namespace Database\Seeders\assessment\psychology;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class QuestionsSeeder extends Seeder
{
    protected string $connect = 'mysql_prod';
    public function run(): void
    {
        // 心理测评
        // 使用数据库事务
        DB::transaction(function () {
            // 自信心评估
            $this->processSurveyQuestions('471', 21);
            // 自我意识评估
            $this->processSurveyQuestions('472', 22);
            // 焦虑评估
            $this->processSurveyQuestions('473', 23);

        });
    }

    function processSurveyQuestions($surveyId, $assessmentsId) {
        // 获取问题数据
        $surveyQuestionData = DB::connection($this->connect)
            ->table('survey_question')
            ->where('survey_id', $surveyId)
            ->orderBy('show_id', 'asc')
            ->get()
            ->toArray();

        $assessmentQuestionData = [];
        foreach ($surveyQuestionData as $item) {
            // 获取每个问题的选项
            $optionsData = DB::connection($this->connect)
                ->table('survey_question_answer')
                ->where('question_id', $item->id)
                ->orderBy('question_id', 'asc') // 使用适当的列来排序选项
                ->get()
                ->toArray();

            // 为选项分配字母
            $options = array_map(function ($option, $index) {
                $letter = chr(65 + $index); // ASCII 码 A=65, B=66, C=67, ...
                return [
                    "option" => $letter,
                    "name" => $option->answer,
                    "order_number" => $option->sort,
                    "score" => $option->sort
                ];
            }, $optionsData, array_keys($optionsData));

            $Question_item_Data = [
                'question_content' => $item->name,
                'old_question_id' => $item->id,
                'is_normal' => $item->is_normal_type,
                'question_number' => $item->show_id,
                'assessment_id' => $assessmentsId,
                'options' => json_encode($options),
                'created_at' => now()->toDateTimeString(),
            ];
            $assessmentQuestionData[] = $Question_item_Data;
        }

        DB::table('assessment_psychology_questions')->insert($assessmentQuestionData);
    }
}
