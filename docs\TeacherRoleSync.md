# 教师角色同步功能说明

## 概述

教师同步功能现在支持根据角色类型自动查询同步数据库中的角色信息，并使用正确的角色ID进行同步。

## 角色类型映射

### 主系统角色类型
- `2` = 教务
- `3` = 老师/教师

### 角色判断逻辑
- 如果roles数组包含2，则判定为**教务**
- 如果roles数组只有3（不包含2），则判定为**老师**

### 同步数据库角色名称
- `教务` = 对应主系统的角色类型 2
- `老师` = 对应主系统的角色类型 3

## 功能实现

### 1. 角色查询逻辑

```php
// 根据角色类型查询同步数据库ysy_role表获取角色ID
$role_ids = [];
$role_names = [];

// 如果包含2就是教务，如果只有3就是老师
if (in_array(2, $roles)) {
    // 教务角色
    $role = $this->syncConnection->table('ysy_role')
        ->where('name', '教务')
        ->first();
    if ($role) {
        $role_ids[] = $role->id;
        $role_names[] = '教务';
    }
} elseif (in_array(3, $roles) && !in_array(2, $roles)) {
    // 只有教师角色（没有教务）
    $role = $this->syncConnection->table('ysy_role')
        ->where('name', '老师')
        ->first();
    if ($role) {
        $role_ids[] = $role->id;
        $role_names[] = '老师';
    }
}
```

### 2. 数据同步

#### ysy_member表同步
```php
$member_data = [
    'id' => $teacherData['user_id'],
    'name' => $teacherData['teacher_name'],
    'username' => $teacherData['username'],
    'password' => $teacherData['password'],
    'mobile' => $teacherData['phone'] ?? '',
    'gender' => $teacherData['gender'] == 1 ? 1 : 2,
    'school_id' => $teacherData['school_id'],
    'role_id' => '0,' . implode(',', $syncRoleIds) . ',0', // 使用同步数据库中的角色ID
    'step' => 0,
    'created_at' => now(),
    'updated_at' => now()
];
```

#### ysy_teacher表同步
只有当角色类型包含教师(type=3)时，才会同步到ysy_teacher表。

## 使用示例

### 请求参数示例

**示例1：教务角色**
```php
$request = [
    'teacher_name' => '张老师',
    'username' => 'teacher001',
    'gender' => 1,
    'roles' => [2, 3] // 包含2，判定为教务
];
```

**示例2：教师角色**
```php
$request = [
    'teacher_name' => '李老师',
    'username' => 'teacher002',
    'gender' => 1,
    'roles' => [3] // 只有3，判定为老师
];
```

### 处理流程

**示例1处理流程（教务）：**
1. 解析roles数组：[2, 3]
2. 因为包含2，判定为教务角色
3. 查询同步数据库ysy_role表：name='教务' → 获取角色ID（如：5）
4. 同步到ysy_member表，role_id字段值为：'0,5,0'
5. 因为包含角色类型3，同步到ysy_teacher表

**示例2处理流程（老师）：**
1. 解析roles数组：[3]
2. 只有3且不包含2，判定为老师角色
3. 查询同步数据库ysy_role表：name='老师' → 获取角色ID（如：6）
4. 同步到ysy_member表，role_id字段值为：'0,6,0'
5. 因为包含角色类型3，同步到ysy_teacher表

### 返回结果

**示例1返回结果（教务）：**
```php
[
    'success' => true,
    'teacher_id' => 1,
    'synced_to_member' => true,
    'synced_to_teacher' => true,
    'role_types' => [2, 3],
    'sync_role_ids' => [5],
    'sync_role_names' => ['教务'],
    'message' => '教师数据同步成功'
]
```

**示例2返回结果（老师）：**
```php
[
    'success' => true,
    'teacher_id' => 2,
    'synced_to_member' => true,
    'synced_to_teacher' => true,
    'role_types' => [3],
    'sync_role_ids' => [6],
    'sync_role_names' => ['老师'],
    'message' => '教师数据同步成功'
]
```

## 数据库表结构

### ysy_role表
```sql
CREATE TABLE ysy_role (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    school_id INT COMMENT '学校ID',
    type INT COMMENT '角色类型',
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### ysy_member表
```sql
CREATE TABLE ysy_member (
    id INT PRIMARY KEY COMMENT '用户ID',
    name VARCHAR(50) COMMENT '姓名',
    username VARCHAR(50) COMMENT '用户名',
    password VARCHAR(255) COMMENT '密码',
    mobile VARCHAR(20) COMMENT '手机号',
    gender TINYINT COMMENT '性别1男2女',
    school_id INT COMMENT '学校ID',
    role_id VARCHAR(50) COMMENT '角色ID，格式：0,角色ID1,角色ID2,0',
    step TINYINT DEFAULT 0 COMMENT '步骤状态',
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### ysy_teacher表
```sql
CREATE TABLE ysy_teacher (
    id INT PRIMARY KEY COMMENT '教师ID',
    member_id INT COMMENT '关联ysy_member表的ID',
    name VARCHAR(50) COMMENT '教师姓名',
    school_id INT COMMENT '学校ID',
    school_district INT COMMENT '校区ID',
    is_psych TINYINT DEFAULT 0 COMMENT '是否心理教师',
    step TINYINT DEFAULT 0 COMMENT '步骤状态',
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## 注意事项

1. **角色查询失败处理**：如果在ysy_role表中找不到对应的角色名称，该角色将被跳过，不会影响其他角色的同步。

2. **角色ID格式**：ysy_member表的role_id字段使用特定格式：'0,角色ID1,角色ID2,0'，前后都有0作为分隔符。

3. **教师表同步条件**：只有当角色类型包含3（老师）时，才会同步到ysy_teacher表。

4. **事务处理**：整个同步过程使用数据库事务，确保数据一致性。

5. **日志记录**：同步过程会记录详细的日志信息，包括角色查询结果和同步状态。
