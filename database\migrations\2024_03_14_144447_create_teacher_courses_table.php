<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('teacher_courses', function (Blueprint $table) {
            $table->id();
            $table->integer('teacher_id')->comment('教师ID');
            $table->integer('course_id')->comment('课程ID：教学科目ID');
            $table->year('school_year')->comment('学年：指老师的教学年份9月');
            $table->softDeletes();
            $table->timestamps();
            $table->string('creator',20)->nullable()->comment('创建人');
            $table->string('updater',20)->nullable()->comment('最后更新人');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('teacher_courses');
    }
};
