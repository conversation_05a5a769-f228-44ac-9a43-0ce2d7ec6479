<?php
/**
 * Created by PhpStorm.
 * User: Dosion
 * Date: 2019/7/5
 * Time: 13:30
 */
namespace app\psychassessment\controller;

use think\Controller;

class Loadpdf extends Controller {
    protected $ReportLogic;
    protected $loadpdf_service;
    public function __construct() {
        parent::__construct();
        $this->ReportLogic = new \app\psychassessment\logic\Report();
        $this->loadpdf_service = new \app\psychassessment\service\Loadpdf();
    }

    public function personal_pdf() {
        $plan_id     = input('plan_id');
        $survey_type = input('survey_type');
        $member_id   = input('member_id');
        $data = $this->ReportLogic->report($plan_id,$survey_type,$member_id);
        $this->view->engine->layout(false);
        return $this->view->fetch('page/index_pdf' , ['data' => $data]);
    }

    /**
     * 模块：心理评估-个人报告下载
     * @SWG\Get(path="/psychassessment/individual",
     *  tags={"心理评估-个人报告下载:loadpdf"},
     *  summary="个人报告下载",
     *  description="生成个人报告pdf",
     *  produces={"application/json"},
     *  @SWG\Parameter(
     *     in="query",
     *     name="member_id",
     *     type="integer",
     *     description="成员id",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="query",
     *     name="plan_id",
     *     type="integer",
     *     description="测评计划id",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="query",
     *     name="survey_type",
     *     type="integer",
     *     description="心理测评类型",
     *     required=true,
     *   ),
     *  @SWG\Response(response="0", description="操作成功")
     * )
     */
    public function individual() {
        $data=$this->loadpdf_service->individual();
        if($data[1]==1){
            return json(['data'=>$data[0], 'code'=>0, 'message'=>'生成成功！']);
        }else{
            return json(['data'=>[], 'code'=>-1, 'message'=>'生成失败！']);
        }
    }
}