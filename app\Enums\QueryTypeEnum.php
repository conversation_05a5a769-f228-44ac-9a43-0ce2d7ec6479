<?php

namespace App\Enums;

/**
 * 查询类型枚举
 * 
 * 定义系统中使用的各种查询类型
 */
enum QueryTypeEnum: int
{
    /**
     * 空查询类型
     */
    case EMPTY = 0;
    
    /**
     * 院校分数查询
     */
    case COLLEGE_SCORE = 1;
    
    /**
     * 专业分数查询
     */
    case MAJOR_SCORE = 2;
    
    /**
     * 专业计划查询
     */
    case MAJOR_PLAN = 3;
    
    /**
     * 获取查询类型对应的表名前缀
     *
     * @return string 表名前缀
     */
    public function getTablePrefix(): string
    {
        return match($this) {
            self::EMPTY => '',
            self::COLLEGE_SCORE => 'CollegeScore',
            self::MAJOR_SCORE => 'MajorScore',
            self::MAJOR_PLAN => 'MajorPlan',
        };
    }
    
    /**
     * 根据查询类型和省份拼音获取完整表名
     *
     * @param string $provinceSpell 省份拼音
     * @return string 完整表名
     */
    public function getTableName(string $provinceSpell): string
    {
        $prefix = $this->getTablePrefix();
        return $prefix ? "{$prefix}_{$provinceSpell}" : '';
    }
    
    /**
     * 从整数值获取枚举实例
     *
     * @param int $value 整数值
     * @return self 对应的枚举实例，如果不存在则返回 EMPTY
     */
    public static function fromInt(int $value): self
    {
        return self::tryFrom($value) ?? self::EMPTY;
    }
}
