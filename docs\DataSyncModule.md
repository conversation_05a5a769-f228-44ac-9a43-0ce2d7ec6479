# 数据同步模块使用文档

## 概述

数据同步模块用于在主数据库和同步数据库之间自动同步基础数据，包括学校、校区、角色、学生、教师和教务人员等数据。

## 功能特性

- ✅ **自动同步**: 当主数据库中的数据发生变化时，自动同步到目标数据库
- ✅ **手动同步**: 提供API接口支持手动触发同步
- ✅ **批量同步**: 支持批量同步历史数据
- ✅ **事件驱动**: 基于Laravel事件系统，支持异步处理
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **日志记录**: 详细的同步日志记录
- ✅ **配置灵活**: 支持多种配置选项

## 配置说明

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# 数据同步开关
DATA_SYNC_ENABLED=true

# 同步数据库连接配置
SYNC_DB_HOST=127.0.0.1
SYNC_DB_PORT=3306
SYNC_DB_DATABASE=sync_database
SYNC_DB_USERNAME=sync_user
SYNC_DB_PASSWORD=sync_password
SYNC_DB_PREFIX=

# 同步模式 (sync: 同步, async: 异步)
DATA_SYNC_MODE=async

# 队列配置
DATA_SYNC_QUEUE_CONNECTION=redis
DATA_SYNC_QUEUE_NAME=data-sync

# 重试配置
DATA_SYNC_MAX_ATTEMPTS=3
DATA_SYNC_RETRY_DELAY=60

# 日志配置
DATA_SYNC_LOGGING_ENABLED=true
DATA_SYNC_LOG_CHANNEL=daily
DATA_SYNC_LOG_LEVEL=info
```

### 2. 数据库配置

同步数据库连接已在 `config/database.php` 中配置为 `sync_mysql`。

### 3. 队列配置

如果使用异步模式，需要启动队列处理器：

```bash
php artisan queue:work --queue=data-sync
```

## 使用方法

### 1. 在模型中启用自动同步

在需要同步的模型中使用 `DataSyncTrait`：

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Traits\DataSyncTrait;

class School extends Model
{
    use DataSyncTrait;

    // 启用数据同步
    protected $syncEnabled = true;
    
    // 指定同步类型
    protected $syncType = 'school';
    
    // 字段映射（可选）
    protected $syncFieldMapping = [
        'original_field' => 'sync_field'
    ];

    // 重写方法添加额外同步数据（可选）
    protected function getAdditionalSyncData(): array
    {
        return [
            'campuses' => $this->campuses()->get()->toArray(),
            'admin_user' => $this->getDefaultAdminUser()
        ];
    }
}
```

### 2. 手动触发同步

#### 通过API接口

```bash
# 同步学校数据
POST /admin/datasync/sync-school
{
    "id": 1,
    "name": "示例学校",
    "code": "DEMO001",
    "type": 1,
    "level": 1,
    "address": "示例地址",
    "phone": "123456789",
    "email": "<EMAIL>",
    "campuses": [
        {
            "id": 1,
            "name": "主校区",
            "code": "MAIN",
            "address": "主校区地址"
        }
    ],
    "admin_user": {
        "name": "管理员",
        "username": "admin",
        "password": "hashed_password",
        "phone": "123456789",
        "email": "<EMAIL>"
    }
}

# 同步学生数据
POST /admin/datasync/sync-student
{
    "id": 1,
    "name": "张三",
    "student_number": "2024001",
    "gender": 1,
    "phone": "123456789",
    "email": "<EMAIL>",
    "school_id": 1,
    "campus_id": 1,
    "grade_id": 1,
    "class_id": 1,
    "status": 1
}

# 批量同步
POST /admin/datasync/batch-sync
{
    "type": "student",
    "data": [
        {
            "id": 1,
            "name": "张三",
            // ... 其他字段
        },
        {
            "id": 2,
            "name": "李四",
            // ... 其他字段
        }
    ]
}
```

#### 通过模型方法

```php
// 手动触发单个模型同步
$school = School::find(1);
$school->manualSync('created');

// 批量同步
$schools = School::where('status', 1)->get();
$results = School::batchSync($schools, 'created');
```

#### 通过命令行

```bash
# 同步所有数据
php artisan datasync:sync all

# 同步指定类型的数据
php artisan datasync:sync school
php artisan datasync:sync student
php artisan datasync:sync teacher
php artisan datasync:sync admin

# 同步指定ID的数据
php artisan datasync:sync school --id=1

# 强制同步（忽略已同步的数据）
php artisan datasync:sync school --force

# 指定批量大小
php artisan datasync:sync student --batch-size=50
```

### 3. 查询同步状态

```bash
GET /admin/datasync/sync-status?type=school&original_id=1
```

### 4. 管理同步数据

```bash
# 删除同步数据
DELETE /admin/datasync/delete-sync-data
{
    "type": "school",
    "original_id": 1
}

# 更新同步数据
PUT /admin/datasync/update-sync-data
{
    "type": "school",
    "original_id": 1,
    "update_data": {
        "name": "新学校名称",
        "status": 1
    }
}
```

## 数据结构

### 同步数据库表结构

同步数据库需要包含以下表：

#### schools 表
```sql
CREATE TABLE `schools` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL COMMENT '学校名称',
    `code` varchar(50) DEFAULT NULL COMMENT '学校代码',
    `type` tinyint DEFAULT 1 COMMENT '学校类型',
    `level` tinyint DEFAULT 1 COMMENT '学校等级',
    `address` text COMMENT '学校地址',
    `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `website` varchar(255) DEFAULT NULL COMMENT '官网',
    `description` text COMMENT '学校描述',
    `status` tinyint DEFAULT 1 COMMENT '状态',
    `original_id` bigint unsigned NOT NULL COMMENT '原始数据ID',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_original_id` (`original_id`),
    KEY `idx_status` (`status`)
);
```

#### students 表
```sql
CREATE TABLE `students` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL COMMENT '学生姓名',
    `student_number` varchar(50) DEFAULT NULL COMMENT '学号',
    `gender` tinyint DEFAULT 1 COMMENT '性别',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `school_id` bigint unsigned NOT NULL COMMENT '学校ID',
    `campus_id` bigint unsigned DEFAULT NULL COMMENT '校区ID',
    `grade_id` bigint unsigned DEFAULT NULL COMMENT '年级ID',
    `class_id` bigint unsigned DEFAULT NULL COMMENT '班级ID',
    `status` tinyint DEFAULT 1 COMMENT '状态',
    `original_id` bigint unsigned NOT NULL COMMENT '原始数据ID',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_original_id` (`original_id`),
    KEY `idx_school_id` (`school_id`),
    KEY `idx_status` (`status`)
);
```

其他表结构类似，都包含 `original_id` 字段用于关联原始数据。

## 事件系统

模块使用Laravel事件系统来处理数据同步：

### 事件类
- `SchoolCreated` - 学校创建事件
- `StudentCreated` - 学生创建事件
- `TeacherCreated` - 教师创建事件
- `AdminCreated` - 教务人员创建事件

### 监听器类
- `SyncSchoolData` - 同步学校数据
- `SyncStudentData` - 同步学生数据
- `SyncTeacherData` - 同步教师数据
- `SyncAdminData` - 同步教务人员数据

## 错误处理

### 日志记录
所有同步操作都会记录详细的日志，包括：
- 成功同步的记录
- 失败的同步操作及错误原因
- 异常信息和堆栈跟踪

### 重试机制
- 支持配置最大重试次数
- 支持配置重试延迟时间
- 失败的任务会自动重试

### 监控告警
- 支持配置Webhook URL进行告警通知
- 可配置是否在失败时发送告警

## 性能优化

### 批量处理
- 支持配置批量处理大小
- 避免一次性处理大量数据导致内存溢出

### 事务处理
- 使用数据库事务确保数据一致性
- 支持配置是否启用事务

### 连接池
- 支持配置数据库连接池大小
- 优化数据库连接使用

## 安全考虑

### 数据加密
- 支持对敏感字段进行加密
- 可配置敏感字段列表

### 审计日志
- 记录所有同步操作的审计日志
- 包含操作用户、时间、操作类型等信息

## 故障排查

### 常见问题

1. **同步失败**
   - 检查同步数据库连接配置
   - 查看错误日志确定具体原因
   - 检查数据格式是否正确

2. **队列任务不执行**
   - 确认队列处理器是否启动
   - 检查队列配置是否正确
   - 查看队列失败任务

3. **数据不一致**
   - 检查字段映射配置
   - 确认事务是否正确提交
   - 手动触发重新同步

### 调试命令

```bash
# 查看队列状态
php artisan queue:work --queue=data-sync --verbose

# 查看失败的队列任务
php artisan queue:failed

# 重试失败的任务
php artisan queue:retry all

# 测试数据同步
php artisan datasync:sync school --id=1 --force
```

## API接口文档

### 测试接口
```
GET /datasync/test
```

### 同步接口
```
POST /admin/datasync/sync-school      # 同步学校
POST /admin/datasync/sync-student     # 同步学生
POST /admin/datasync/sync-teacher     # 同步教师
POST /admin/datasync/sync-admin       # 同步教务人员
POST /admin/datasync/batch-sync       # 批量同步
```

### 管理接口
```
DELETE /admin/datasync/delete-sync-data  # 删除同步数据
PUT /admin/datasync/update-sync-data     # 更新同步数据
GET /admin/datasync/sync-status          # 查询同步状态
```

所有接口都需要认证，返回统一的JSON格式响应。
