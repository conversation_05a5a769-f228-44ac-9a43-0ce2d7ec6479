# 批量学生同步功能说明

## 概述

批量学生同步功能与单个学生同步保持一致的架构，支持通过班级名称查询同步数据库中的班级ID，实现批量学生数据同步到ysy_member和ysy_student表。

## 功能特点

### 1. 请求数据格式

```json
{
    "school_campus_id": 2515,
    "grade_id": 10,
    "init_grade_id": 10,
    "grade_year": 2024,
    "date_start": "2024-09-01",
    "date_due": "2027-06-30",
    "students": [
        {
            "username": "ce1s1331ab1121",
            "role_name": "学生1",
            "class_name": "11班",
            "student_name": "测试11",
            "gender": "男",
            "school_no": "ce1s133123111",
            "school_year": 2024
        },
        {
            "username": "ce1s2331ab2222",
            "role_name": "学生",
            "class_name": "11班",
            "student_name": "测试11",
            "gender": "男",
            "school_no": "ce1s223312322",
            "school_year": 2024
        }
    ]
}
```

### 2. 与单个学生同步的区别

| 特性 | 单个学生同步 | 批量学生同步 |
|------|-------------|-------------|
| 班级ID获取 | 通过class_id查询班级名称 | 直接使用class_name |
| 年级ID获取 | 通过class对象获取 | 通过grade_year和init_grade_id查询 |
| 调用方式 | syncSingleStudent($request) | syncBatchStudents($request) |
| 数据来源 | 单个学生记录 | students数组 |

## 实现细节

### 1. StudentService修改

**简化调用方式：**
```php
// 调用批量学生同步功能
try {
    $dataSyncService = app(\App\Services\DataSync\DataSyncService::class);
    $sync_result = $dataSyncService->syncBatchStudents($request);
    \Log::info('批量学生同步结果', $sync_result);
} catch (\Exception $e) {
    // 同步失败不影响主流程，只记录日志
    \Log::warning('批量学生创建后同步失败', [
        'error' => $e->getMessage(),
        'student_count' => count($student_result_list)
    ]);
}
```

### 2. DataSyncService修改

**方法签名变更：**
```php
// 修改前
public function syncBatchStudents(array $request_data): array

// 修改后
public function syncBatchStudents($request): array
```

**年级ID查询：**
```php
// 通过校区ID、grade_year和init_grade_id查询ysy_grade表获取grade_id
$ysy_grade = $this->syncConnection->table('ysy_grade')
    ->where('school_id', $syncSchoolId)
    ->where('name', $grade_year)
    ->where('grade_sort', $init_grade_id)
    ->first();

$sync_grade_id = $ysy_grade->id;
```

**班级ID查询：**
```php
// 通过班级名称和年级ID查询同步数据库中的班级ID
$ysy_class = $this->syncConnection->table('ysy_class')
    ->where('school_id', $syncSchoolId)
    ->where('grade_id', $sync_grade_id)
    ->where('name', $class_name)
    ->first();

$sync_class_id = $ysy_class->id;
```

## 查询流程

### 批量学生同步查询链路

```
请求参数
├── school_campus_id (校区ID)
├── grade_year (年级名称)
├── init_grade_id (年级排序)
└── students[] (学生数组)
    └── class_name (班级名称)
    ↓
获取校区信息 → syncSchoolId
    ↓
查询ysy_grade表 → sync_grade_id
    ↓
循环处理每个学生：
    ├── 通过class_name查询ysy_class表 → sync_class_id
    ├── 检查用户名重复
    ├── 查询学生角色ID
    ├── 准备学生数据
    └── 调用syncStudent方法
```

## 关键字段映射

### 请求参数到同步数据映射

| 请求字段 | 查询表 | 查询条件 | 结果字段 | 用途 |
|---------|--------|----------|----------|------|
| school_campus_id | - | - | syncSchoolId | 学校ID |
| grade_year | ysy_grade | name | sync_grade_id | 年级ID |
| init_grade_id | ysy_grade | grade_sort | sync_grade_id | 年级ID |
| class_name | ysy_class | name + grade_id | sync_class_id | 班级ID |
| student_name | - | - | name | 学生姓名 |
| username | - | - | username | 用户名 |
| school_no | - | - | school_no | 学籍号 |
| gender | - | - | gender | 性别 |

### 最终同步数据结构

```php
$student_data = [
    'name' => $student_name,
    'username' => $username,
    'password' => '827ccb0eea8a706c4c34a16891f84e7b',
    'school_no' => $school_no,
    'role_id' => $role->id,
    'school_id' => $syncSchoolId,
    'school_district' => $school_campus_id,
    'class_id' => $sync_class_id, // 同步数据库中的班级ID
    'grade_id' => $sync_grade_id, // 同步数据库中的年级ID
    'role_source_id' => '4',
    'step' => '0',
    'gender' => $gender
];
```

## 错误处理

### 1. 年级查询失败
```php
if (!$ysy_grade) {
    return [
        'success' => false,
        'message' => '未找到对应的年级信息'
    ];
}
```

### 2. 班级查询失败
```php
if (!$ysy_class) {
    $sync_results[] = [
        'student_name' => $student_name,
        'username' => $username,
        'success' => false,
        'message' => "未找到班级: {$class_name}"
    ];
    continue;
}
```

### 3. 用户名重复
```php
if ($existingMember) {
    $sync_results[] = [
        'student_name' => $student_name,
        'username' => $username,
        'success' => false,
        'message' => '用户名已存在，跳过插入',
        'skipped' => true
    ];
    $skipped_count++;
    continue;
}
```

## 返回结果

### 成功返回格式
```json
{
    "success": true,
    "sync_results": [
        {
            "student_name": "测试11",
            "username": "ce1s1331ab1121",
            "success": true,
            "member_id": 1001,
            "class_id": 3001,
            "grade_id": 2001
        },
        {
            "student_name": "测试11",
            "username": "ce1s2331ab2222",
            "success": false,
            "message": "用户名已存在，跳过插入",
            "skipped": true
        }
    ],
    "total_count": 2,
    "success_count": 1,
    "skipped_count": 1,
    "message": "批量学生数据同步完成"
}
```

## 优势

1. **架构一致性**：与单个学生同步保持相同的调用方式和处理逻辑
2. **灵活查询**：支持通过班级名称动态查询班级ID，适应不同的数据结构
3. **完善错误处理**：每个查询步骤都有详细的错误处理和反馈
4. **批量效率**：一次性处理多个学生，提高同步效率
5. **详细反馈**：提供每个学生的详细同步结果和统计信息

## 注意事项

1. **班级名称准确性**：确保请求中的class_name与同步数据库中的班级名称完全匹配
2. **年级信息一致性**：grade_year和init_grade_id必须与同步数据库中的年级信息对应
3. **用户名唯一性**：系统会自动检查用户名重复，重复的会跳过处理
4. **事务独立性**：每个学生的同步都是独立的，一个失败不影响其他学生
5. **日志记录**：所有同步结果都会记录到日志中，便于问题排查
