<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('site_configs', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('站点名称');
            $table->string('title',20)->comment('标题');
            $table->string('icon',200)->comment('图标');
            $table->json('special_config')->nullable()->comment('特殊定制style、logo等信息');
            $table->json('third_config')->nullable()->comment('第三方配置信息');
            $table->json('logout_config')->nullable()->comment('登出配置');
            $table->timestamps();
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('site_configs');
    }
};
