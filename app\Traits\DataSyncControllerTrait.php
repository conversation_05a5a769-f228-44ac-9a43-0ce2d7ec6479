<?php

namespace App\Traits;

use App\Events\DataSync\SchoolCreated;
use App\Events\DataSync\StudentCreated;
use App\Events\DataSync\TeacherCreated;
use App\Events\DataSync\AdminCreated;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 数据同步控制器 Trait
 * 用于在控制器中快速添加数据同步功能
 */
trait DataSyncControllerTrait
{
    /**
     * 触发学校数据同步
     * 
     * @param mixed $school 学校模型或数据
     * @param Request $request 请求对象
     * @param array $additionalData 额外数据
     */
    protected function triggerSchoolSync($school, Request $request, array $additionalData = [])
    {
        if (!$this->shouldSync()) {
            return;
        }

        try {
            $syncData = $this->prepareSchoolSyncData($school, $request, $additionalData);
            event(new SchoolCreated($syncData));
            
            $this->logSyncEvent('school', $school->id ?? 'unknown', $school->name ?? '未知');
        } catch (\Exception $e) {
            $this->logSyncError('school', $e);
        }
    }

    /**
     * 触发学生数据同步
     * 
     * @param mixed $student 学生模型或数据
     * @param Request $request 请求对象
     * @param array $additionalData 额外数据
     */
    protected function triggerStudentSync($student, Request $request, array $additionalData = [])
    {
        if (!$this->shouldSync()) {
            return;
        }

        try {
            $syncData = $this->prepareStudentSyncData($student, $request, $additionalData);
            event(new StudentCreated($syncData));
            
            $this->logSyncEvent('student', $student->id ?? 'unknown', $student->name ?? '未知');
        } catch (\Exception $e) {
            $this->logSyncError('student', $e);
        }
    }

    /**
     * 触发教师数据同步
     * 
     * @param mixed $teacher 教师模型或数据
     * @param Request $request 请求对象
     * @param array $additionalData 额外数据
     */
    protected function triggerTeacherSync($teacher, Request $request, array $additionalData = [])
    {
        if (!$this->shouldSync()) {
            return;
        }

        try {
            $syncData = $this->prepareTeacherSyncData($teacher, $request, $additionalData);
            event(new TeacherCreated($syncData));
            
            $this->logSyncEvent('teacher', $teacher->id ?? 'unknown', $teacher->name ?? '未知');
        } catch (\Exception $e) {
            $this->logSyncError('teacher', $e);
        }
    }

    /**
     * 触发管理员数据同步
     * 
     * @param mixed $admin 管理员模型或数据
     * @param Request $request 请求对象
     * @param array $additionalData 额外数据
     */
    protected function triggerAdminSync($admin, Request $request, array $additionalData = [])
    {
        if (!$this->shouldSync()) {
            return;
        }

        try {
            $syncData = $this->prepareAdminSyncData($admin, $request, $additionalData);
            event(new AdminCreated($syncData));
            
            $this->logSyncEvent('admin', $admin->id ?? 'unknown', $admin->name ?? '未知');
        } catch (\Exception $e) {
            $this->logSyncError('admin', $e);
        }
    }

    /**
     * 检查是否应该同步
     * 
     * @return bool
     */
    protected function shouldSync(): bool
    {
        return config('datasync.enabled', false);
    }

    /**
     * 准备学校同步数据
     * 
     * @param mixed $school 学校模型
     * @param Request $request 请求对象
     * @param array $additionalData 额外数据
     * @return array
     */
    protected function prepareSchoolSyncData($school, Request $request, array $additionalData = []): array
    {
        $schoolArray = is_array($school) ? $school : $school->toArray();
        $requestData = $request->all();
        
        $syncData = array_merge($schoolArray, $additionalData, [
            'add_time' => $requestData['add_time'] ?? now()->format('Y-m-d H:i:s'),
            'date_due' => $requestData['date_due'] ?? null,
            'province' => $requestData['province'] ?? '',
            'city' => $requestData['city'] ?? '',
            'district' => $requestData['district'] ?? '',
            'address' => $requestData['address'] ?? '',
            'buy_modules' => $requestData['buy_modules'] ?? '',
            'location' => $requestData['location'] ?? '',
        ]);

        // 获取校区信息
        if (isset($school->id)) {
            $syncData['campuses'] = $this->getCampusesForSync($school->id);
        }

        // 添加默认管理员用户信息
        $syncData['admin_user'] = $this->getDefaultAdminUser($school, $request);

        return $syncData;
    }

    /**
     * 准备学生同步数据
     * 
     * @param mixed $student 学生模型
     * @param Request $request 请求对象
     * @param array $additionalData 额外数据
     * @return array
     */
    protected function prepareStudentSyncData($student, Request $request, array $additionalData = []): array
    {
        $studentArray = is_array($student) ? $student : $student->toArray();
        $requestData = $request->all();
        
        return array_merge($studentArray, $additionalData, [
            'student_name' => $studentArray['name'] ?? $requestData['name'] ?? '',
            'student_no' => $studentArray['student_number'] ?? $requestData['student_number'] ?? '',
            'sex' => $studentArray['gender'] ?? $requestData['gender'] ?? 1,
            'mobile' => $studentArray['phone'] ?? $requestData['phone'] ?? '',
            'email' => $studentArray['email'] ?? $requestData['email'] ?? '',
            'student_status' => $studentArray['status'] ?? $requestData['status'] ?? 1,
        ]);
    }

    /**
     * 准备教师同步数据
     * 
     * @param mixed $teacher 教师模型
     * @param Request $request 请求对象
     * @param array $additionalData 额外数据
     * @return array
     */
    protected function prepareTeacherSyncData($teacher, Request $request, array $additionalData = []): array
    {
        $teacherArray = is_array($teacher) ? $teacher : $teacher->toArray();
        $requestData = $request->all();
        
        return array_merge($teacherArray, $additionalData, [
            'teacher_name' => $teacherArray['name'] ?? $requestData['name'] ?? '',
            'teacher_no' => $teacherArray['teacher_number'] ?? $requestData['teacher_number'] ?? '',
            'sex' => $teacherArray['gender'] ?? $requestData['gender'] ?? 1,
            'mobile' => $teacherArray['phone'] ?? $requestData['phone'] ?? '',
            'email' => $teacherArray['email'] ?? $requestData['email'] ?? '',
            'dept' => $teacherArray['department'] ?? $requestData['department'] ?? '',
            'job_title' => $teacherArray['position'] ?? $requestData['position'] ?? '',
            'subject' => $teacherArray['subject'] ?? $requestData['subject'] ?? '',
            'teacher_status' => $teacherArray['status'] ?? $requestData['status'] ?? 1,
        ]);
    }

    /**
     * 准备管理员同步数据
     * 
     * @param mixed $admin 管理员模型
     * @param Request $request 请求对象
     * @param array $additionalData 额外数据
     * @return array
     */
    protected function prepareAdminSyncData($admin, Request $request, array $additionalData = []): array
    {
        $adminArray = is_array($admin) ? $admin : $admin->toArray();
        $requestData = $request->all();
        
        return array_merge($adminArray, $additionalData, [
            'admin_name' => $adminArray['name'] ?? $requestData['name'] ?? '',
            'user_name' => $adminArray['username'] ?? $requestData['username'] ?? '',
            'sex' => $adminArray['gender'] ?? $requestData['gender'] ?? 1,
            'mobile' => $adminArray['phone'] ?? $requestData['phone'] ?? '',
            'email' => $adminArray['email'] ?? $requestData['email'] ?? '',
            'dept' => $adminArray['department'] ?? $requestData['department'] ?? '',
            'job_title' => $adminArray['position'] ?? $requestData['position'] ?? '',
            'user_role' => $adminArray['role'] ?? $requestData['role'] ?? 'admin',
            'admin_status' => $adminArray['status'] ?? $requestData['status'] ?? 1,
        ]);
    }

    /**
     * 获取校区信息用于同步
     * 
     * @param int $schoolId
     * @return array
     */
    protected function getCampusesForSync(int $schoolId): array
    {
        try {
            return \DB::table('school_campuses')
                ->where('school_id', $schoolId)
                ->where('status', 1)
                ->select('id', 'campus_name as name', 'school_id', 'type', 'is_main', 'status')
                ->get()
                ->map(function ($campus) {
                    return [
                        'id' => $campus->id,
                        'name' => $campus->name,
                        'code' => 'CAMPUS_' . $campus->id,
                        'school_id' => $campus->school_id,
                        'type' => $campus->type,
                        'is_main' => $campus->is_main,
                        'status' => $campus->status,
                        'address' => '',
                        'phone' => '',
                    ];
                })
                ->toArray();
        } catch (\Exception $e) {
            Log::warning('获取校区信息失败', [
                'school_id' => $schoolId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 获取默认管理员用户信息
     * 
     * @param mixed $school
     * @param Request $request
     * @return array
     */
    protected function getDefaultAdminUser($school, Request $request): array
    {
        $schoolName = is_array($school) ? ($school['name'] ?? '学校') : ($school->name ?? '学校');
        $schoolCode = is_array($school) ? ($school['code'] ?? '') : ($school->code ?? '');
        
        return [
            'name' => $schoolName . '管理员',
            'username' => 'admin_' . ($schoolCode ?: 'school_' . time()),
            'password' => bcrypt($request->input('admin_password', '123456')),
            'phone' => $request->input('contact_phone', ''),
            'email' => $request->input('contact_email', ''),
            'role' => 'admin',
            'status' => 1
        ];
    }

    /**
     * 记录同步事件日志
     * 
     * @param string $type
     * @param mixed $id
     * @param string $name
     */
    protected function logSyncEvent(string $type, $id, string $name)
    {
        Log::info("{$type}数据同步事件已触发", [
            "{$type}_id" => $id,
            "{$type}_name" => $name
        ]);
    }

    /**
     * 记录同步错误日志
     * 
     * @param string $type
     * @param \Exception $e
     */
    protected function logSyncError(string $type, \Exception $e)
    {
        Log::error("触发{$type}数据同步失败", [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
}
