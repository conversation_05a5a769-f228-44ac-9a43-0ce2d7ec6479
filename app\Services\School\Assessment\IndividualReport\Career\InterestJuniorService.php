<?php
namespace App\Services\School\Assessment\IndividualReport\Career;

use App\Services\School\Assessment\IndividualReport\AbstractIndividualReportService;

class InterestJuniorService extends AbstractIndividualReportService
{
    /**
     * 解析初中兴趣评估结果
     */
    private const DIFFERENTIATION_THRESHOLDS = [
        ['threshold' => 1.5, 'percentage' => 25],
        ['threshold' => 2.5, 'percentage' => 45],
        ['threshold' => 4.0, 'percentage' => 65],
        ['threshold' => PHP_FLOAT_MAX, 'percentage' => 85]
    ];

    public function generateReport(array $params): array
    {
        $assignmentId = $params['assessment_task_assignment_id'];
        // 获取分发信息
        $assignmentInfo = $this->getAssignmentInfo($assignmentId);
        
        // 生成报告
        $results = json_decode($assignmentInfo['results'], true);
        $assessmentInfo = $this->interestInfoJunior($results);
        // 合并结果
        return array_merge($assignmentInfo, $assessmentInfo);
    }

    private function interestInfoJunior($result): array
    {
        $configs = $this->loadJuniorConfigs();
        $survey = $this->parseJuniorSurveyData($result[0]);
        $content = $this->processTopThreeInterests($survey, $configs['dimensionsJunior']);
        $differentiation = $this->calculateJuniorDifferentiation(
            array_column($survey, 'score'),
            $configs['differentiation']
        );

        return [
            'scores' => $survey,
            'code_num' => $result[1],
            'differentiation' => $differentiation,
            'content' => $content
        ];
    }

    private function loadJuniorConfigs(): array
    {
        return [
            'dimensionsJunior' => config('assessment.career.interest_dimensions_junior'),
            'differentiation' => config('assessment.career.interest_differentiation')
        ];
    }

    private function parseJuniorSurveyData($data): array
    {
        $survey = [];
        foreach ($data as $key => $value) {
            $survey[$key] = [
                'name' => $value[2],
                'type' => $value[1],
                'score' => $value[0]
            ];
        }
        return $survey;
    }

    private function processTopThreeInterests($survey, $dimensionsJunior): array
    {
        $content = [];
        foreach (array_slice($survey, 0, 3) as $key => $value) {
            $content[$key] = array_merge(
                $dimensionsJunior[$value['type']],
                [
                    'name' => $value['name'],
                    'type' => $value['type']
                ]
            );

            // 转换字符串为数组
            $content[$key]['characteristic'] = explode(',', $content[$key]['characteristic']);
            $content[$key]['occupation'] = explode(',', $content[$key]['occupation']);
        }
        return $content;
    }

    private function calculateJuniorDifferentiation($scores, $differentiation): array
    {
        rsort($scores);
        $score = ($scores[0] - ($scores[1] + $scores[3]) / 2) / 2;

        $data = [];
        foreach (self::DIFFERENTIATION_THRESHOLDS as $index => $threshold) {
            if ($score < $threshold['threshold']) {
                $data = [
                    'content' => $differentiation[$index],
                    'percentage' => $threshold['percentage'],
                    'score' => $score
                ];
            }
        }
        return $data;
    }
}
