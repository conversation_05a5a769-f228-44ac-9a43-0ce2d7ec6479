<?php

namespace App\Services\DataSync;

use App\Services\BaseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StudentSyncService extends BaseService
{
    protected $syncConnection;
    protected $schoolSyncService;
    protected $classSyncService;

    public function __construct(SchoolSyncService $schoolSyncService, ClassSyncService $classSyncService)
    {
        $this->syncConnection = DB::connection('sync_mysql');
        $this->schoolSyncService = $schoolSyncService;
        $this->classSyncService = $classSyncService;
    }

    /**
     * 同步单个学生数据（保持原有逻辑）
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function syncSingleStudent($request): array
    {
        try {
            // 获取请求参数
            $name = $request->input('student_name');
            $username = $request->input('username');
            $gender = $request->input('gender', 1);
            $class_id = $request->input('class_id'); // 单个学生传递class_id
            $class_name = $request->input('class_name'); // 批量学生传递class_name
            $grade_year = $request->input('grade_year');
            $init_grade_id = $request->input('init_grade_id');
            $school_campus_id = $request->input('school_campus_id');
            $school_no = $request->input('school_no');

            // 获取校区信息
            $syncSchoolId = $this->schoolSyncService->getSyncDistrictId($school_campus_id);

            // 通过校区ID、grade_year和init_grade_id查询ysy_grade表获取grade_id
            $ysy_grade = $this->syncConnection->table('ysy_grade')
                ->where('school_id', $syncSchoolId)
                ->where('name', $grade_year)
                ->where('grade_sort', $init_grade_id)
                ->first();
            if (!$ysy_grade) {
                return [
                    'success' => false,
                    'message' => '未找到对应的年级信息'
                ];
            }

            $sync_grade_id = $ysy_grade->id;

            // 根据传入参数决定如何获取class_name
            if ($class_id) {
                // 单个学生同步：通过class_id查询原表获取class_name
                $class = \DB::table('classes')->where('id', $class_id)->first();
                if (!$class) {
                    return [
                        'success' => false,
                        'message' => '未找到班级信息'
                    ];
                }
                $class_name = $class->class_name;
            }

            $ysy_class = $this->syncConnection->table('ysy_class')
                ->where('school_id', $syncSchoolId)
                ->where('grade_id', $sync_grade_id)
                ->where('name', $class_name)
                ->first();

            if (!$ysy_class) {
                return [
                    'success' => false,
                    'message' => '未找到对应的班级信息'
                ];
            }

            $sync_class_id = $ysy_class->id;
            // 查询学生角色ID
            $role = $this->syncConnection->table('ysy_role')
                ->where('name', '学生')
                ->where('school_id', $syncSchoolId)
                ->first();

            if (!$role) {
                return [
                    'success' => false,
                    'message' => '未找到学生角色信息'
                ];
            }

            // 准备学生数据
            $student_data = [
                'name' => $name,
                'username' => $username,
                'password' => '827ccb0eea8a706c4c34a16891f84e7b',
                'role_id' => $role->id,
                'school_no' => $school_no,
                'school_id' => $syncSchoolId,
                'school_district' => $school_campus_id,
                'class_id' => $sync_class_id, // 使用同步数据库中的班级ID
                'grade_id' => $sync_grade_id, // 使用同步数据库中的年级ID
                'role_source_id' => '4', // 学生角色类型
                'step' => '0',
                'gender' => $gender
            ];

            // 调用syncStudent方法
            return $this->syncStudent($student_data);

        } catch (\Exception $e) {
            $this->throwBusinessException('同步单个学生数据失败');
        }
    }

    /**
     * 同步学生数据到ysy_member和ysy_student表
     * 
     * @param array $studentData 学生数据
     * @return array
     */
    public function syncStudent(array $studentData): array
    {
        try {
            $this->syncConnection->beginTransaction();

            // 检查ysy_member表中是否已存在相同用户名的记录
            $existingMember = $this->syncConnection->table('ysy_member')
                ->where('username', $studentData['username'])
                ->where('school_id', $studentData['school_id'])
                ->first();

            if ($existingMember) {
                // 如果member已存在，检查是否需要同步到student表
                $member_id = $existingMember->id;

                // 检查ysy_student表中是否已存在相同的记录
                $existingStudent = $this->syncConnection->table('ysy_student')
                    ->where('member_id', $member_id)
                    ->where('school_id', $studentData['school_id'])
                    ->first();

                if ($existingStudent) {
                    $this->syncConnection->rollBack();
                    return [
                        'success' => true,
                        'message' => '学生数据已存在，跳过同步',
                        'skipped' => true
                    ];
                }
            } else {
                // 准备同步到ysy_member表的数据
                $member_data = [
                    'name' => $studentData['name'],
                    'username' => $studentData['username'],
                    'password' => $studentData['password'],
                    'gender' => isset($studentData['gender']) ? ($studentData['gender'] == 1 ? 1 : 2) : 1,
                    'school_id' => $studentData['school_id'],
                    'role_id' => '0,' . $studentData['role_id'] . ',0',
                    'step' => 0,
                    'school_district' => $studentData['school_district'],
                    'role_source_id' => '1',
                    'create_time' => now(),
                ];

                // 同步到ysy_member表并获取生成的ID
                $member_id = $this->syncConnection->table('ysy_member')->insertGetId($member_data);
            }

            // 同步到ysy_student表
            $student_data = [
                'member_id' => $member_id,
                'name' => $studentData['name'],
                'username' => $studentData['username'],
                'student_no' => $studentData['school_no'] ?? '',
                'gender' => isset($studentData['gender']) ? ($studentData['gender'] == 1 ? 1 : 2) : 1,
                'school_id' => $studentData['school_id'],
                'school_district' => $studentData['school_district'],
                'class_id' => $studentData['class_id'],
                'grade_id' => $studentData['grade_id'],
                'step' => 0,
                'school_no' => $studentData['school_no'] ?? ''
            ];

            // 同步到ysy_student表
            $student_id = $this->syncConnection->table('ysy_student')->insertGetId($student_data);

            $this->syncConnection->commit();

            Log::info('学生数据同步成功', [
                'student_name' => $studentData['name'],
                'username' => $studentData['username'],
                'member_id' => $member_id,
                'class_id' => $studentData['class_id'],
                'grade_id' => $studentData['grade_id']
            ]);

            return [
                'success' => true,
                'member_id' => $member_id,
                'synced_to_member' => true,
                'synced_to_student' => true,
                'class_id' => $studentData['class_id'],
                'grade_id' => $studentData['grade_id'],
                'message' => '学生数据同步成功'
            ];

        } catch (\Exception $e) {
            $this->syncConnection->rollBack();
            $this->throwBusinessException('学生数据同步失败');
        }
    }

    /**
     * 批量同步学生数据（通过请求参数）
     *
     * @param \Illuminate\Http\Request $request 请求对象
     * @return array
     */
    public function syncBatchStudents($request_data): array
    {
        // 如果传入的是Request对象，转换为数组
        if (is_object($request_data) && method_exists($request_data, 'input')) {
            $students = $request_data->input('students', []);
            $school_campus_id = $request_data->input('school_campus_id');
            $grade_year = $request_data->input('grade_year');
            $init_grade_id = $request_data->input('init_grade_id');
        } else {
            // 如果传入的是数组
            $students = $request_data['students'] ?? [];
            $school_campus_id = $request_data['school_campus_id'] ?? null;
            $grade_year = $request_data['grade_year'] ?? null;
            $init_grade_id = $request_data['init_grade_id'] ?? null;
        }

        if (empty($students)) {
            return [
                'success' => false,
                'message' => '学生数据为空'
            ];
        }

        $sync_results = [];
        $success_count = 0;
        $failed_count = 0;
        $skipped_count = 0;

        foreach ($students as $student_info) {
            // 构造单个学生的请求数据
            $single_request_data = [
                'school_campus_id' => $school_campus_id,
                'student_name' => $student_info['student_name'],
                'username' => $student_info['username'],
                'gender' => $this->convertGenderToNumber($student_info['gender'] ?? '男'),
                'class_name' => $student_info['class_name'], // 批量同步：直接传递class_name，不传class_id
                'grade_year' => $grade_year,
                'init_grade_id' => $init_grade_id,
                'school_no' => $student_info['school_no'] ?? ''
            ];

            // 创建模拟的Request对象
            $mock_request = new \Illuminate\Http\Request($single_request_data);

            // 调用单个学生同步方法
            try {
                $result = $this->syncSingleStudent($mock_request);
                $sync_results[] = array_merge($result, [
                    'student_name' => $student_info['student_name'],
                    'username' => $student_info['username']
                ]);

                if ($result['success']) {
                    if (isset($result['skipped']) && $result['skipped']) {
                        $skipped_count++;
                    } else {
                        $success_count++;
                    }
                } else {
                    $failed_count++;
                }
            } catch (\Exception $e) {
                $sync_results[] = [
                    'student_name' => $student_info['student_name'],
                    'username' => $student_info['username'],
                    'success' => false,
                    'message' => '同步失败: ' . $e->getMessage()
                ];
                $failed_count++;

                Log::warning('批量同步学生失败', [
                    'student_name' => $student_info['student_name'],
                    'username' => $student_info['username'],
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'success' => true,
            'sync_results' => $sync_results,
            'total_count' => count($students),
            'success_count' => $success_count,
            'failed_count' => $failed_count,
            'skipped_count' => $skipped_count,
            'message' => "批量学生数据同步完成 - 成功: {$success_count}, 跳过: {$skipped_count}, 失败: {$failed_count}"
        ];
    }

    /**
     * 转换性别字符串为数字
     *
     * @param string|int $gender 性别（男/女 或 1/2）
     * @return int 1=男，2=女
     */
    private function convertGenderToNumber($gender): int
    {
        if (is_numeric($gender)) {
            return (int)$gender;
        }

        return $gender === '女' ? 2 : 1;
    }
}
