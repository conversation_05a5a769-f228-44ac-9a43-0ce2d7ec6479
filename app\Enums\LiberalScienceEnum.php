<?php

namespace App\Enums;

/**
 * 文理科类型枚举
 * 
 * 定义系统中使用的文理科类型
 */
enum LiberalScienceEnum: int
{
    /**
     * 文科
     */
    case LIBERAL_ARTS = 0;
    
    /**
     * 理科
     */
    case SCIENCE = 1;
    
    /**
     * 文理不分
     */
    case COMBINED = 2;
    
    /**
     * 获取文科映射数据
     *
     * @param bool $isNew 是否使用新高考名称
     * @return array 包含 liberalScience 和 name 的关联数组
     */
    public static function getLiberalArtsMap(bool $isNew = false): array
    {
        return [
            'liberalScience' => self::LIBERAL_ARTS->value,
            'name' => $isNew ? '历史' : '文科'
        ];
    }
    
    /**
     * 获取理科映射数据
     *
     * @param bool $isNew 是否使用新高考名称
     * @return array 包含 liberalScience 和 name 的关联数组
     */
    public static function getScienceMap(bool $isNew = false): array
    {
        return [
            'liberalScience' => self::SCIENCE->value,
            'name' => $isNew ? '物理' : '理科'
        ];
    }
    
    /**
     * 获取文理不分映射数据
     *
     * @return array 包含 liberalScience 和 name 的关联数组
     */
    public static function getCombinedMap(): array
    {
        return [
            'liberalScience' => self::COMBINED->value,
            'name' => '文理不分'
        ];
    }
}
