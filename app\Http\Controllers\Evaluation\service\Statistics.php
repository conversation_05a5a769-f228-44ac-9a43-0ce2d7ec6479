<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 16:01
 */
namespace app\evaluation\service;

class Statistics{
    protected $Statistics;
    public function __construct()
    {
          $this->StatisticsLogic = new \app\evaluation\logic\Statistics();
    }

    public function get_student_report(){
        $data = $this->StatisticsLogic->get_student_report();
        return $data;
    }

    public function integrated_statistics_student(){
        $data = $this->StatisticsLogic->integrated_statistics_student();
        return $data;
    }

    public function integrated_statistics_head(){
        $data = $this->StatisticsLogic->integrated_statistics_head();
        return $data;
    }

    public function statistics_career(){
        $data = $this->StatisticsLogic->statistics_career();
        return $data;
    }

    public function batch_create_evaluation_pdf(){
        $data = $this->StatisticsLogic->batch_create_evaluation_pdf();
        apiReturn($data);
    }

    public function batch_download_evaluation_pdf(){
        $data = $this->StatisticsLogic->batch_download_evaluation_pdf();
        apiReturn($data);
    }
}