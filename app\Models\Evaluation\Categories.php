<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 素养类别模型
 */
class Categories extends Model
{
    protected $table = 'evaluation_categories';

    // 自定义时间戳字段名
    const CREATED_AT = 'create_at';
    const UPDATED_AT = 'update_at';

    protected $fillable = [
        'category_name',
        'description',
        'course_id',
        'parent_id',
        'status',
        'content',
        'create_at',
        'update_at'
    ];

    protected $casts = [
        'course_id' => 'integer',
        'parent_id' => 'integer',
        'status' => 'integer',
    ];

    /**
     * 子类别关联
     */
    public function children(): HasMany
    {
        return $this->hasMany(Categories::class, 'parent_id')->where('status', 0)->orderBy('id');
    }

    /**
     * 父类别关联
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Categories::class, 'parent_id');
    }

    /**
     * 素养占比关联
     */
    public function categoryPortions(): HasMany
    {
        return $this->hasMany(CategoryPortion::class, 'category_id');
    }

    /**
     * 学科关联
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School\System\Course::class, 'course_id');
    }

    /**
     * 作用域：按学科筛选
     */
    public function scopeByCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    /**
     * 作用域：按父级筛选
     */
    public function scopeByParent($query, $parentId)
    {
        return $query->where('parent_id', $parentId);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 作用域：顶级类别
     */
    public function scopeTopLevel($query)
    {
        return $query->where('parent_id', 0);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('id');
    }

    /**
     * 获取所有子类别ID（递归）
     */
    public function getAllChildrenIds(): array
    {
        $childrenIds = [];
        $this->collectChildrenIds($this->id, $childrenIds);
        return $childrenIds;
    }

    /**
     * 递归收集子类别ID
     */
    private function collectChildrenIds(int $parentId, array &$childrenIds): void
    {
        $children = static::where('parent_id', $parentId)->where('status', 0)->get();
        
        foreach ($children as $child) {
            $childrenIds[] = $child->id;
            $this->collectChildrenIds($child->id, $childrenIds);
        }
    }

    /**
     * 获取类别路径
     */
    public function getPath(): array
    {
        $path = [];
        $current = $this;
        
        while ($current) {
            array_unshift($path, [
                'id' => $current->id,
                'name' => $current->category_name
            ]);
            $current = $current->parent;
        }
        
        return $path;
    }

    /**
     * 获取类别层级
     */
    public function getLevel(): int
    {
        $level = 0;
        $current = $this;
        
        while ($current->parent_id > 0) {
            $level++;
            $current = $current->parent;
            if (!$current) break;
        }
        
        return $level;
    }

    /**
     * 检查是否为叶子节点
     */
    public function isLeaf(): bool
    {
        return $this->children()->count() === 0;
    }

    /**
     * 检查是否为根节点
     */
    public function isRoot(): bool
    {
        return $this->parent_id === 0;
    }

    /**
     * 检查是否可以删除
     */
    public function canDelete(): bool
    {
        // 检查是否有子类别
        if ($this->children()->exists()) {
            return false;
        }

        // 检查是否被题目使用
        if ($this->categoryPortions()->exists()) {
            return false;
        }

        return true;
    }

    /**
     * 获取使用该类别的题目数量
     */
    public function getQuestionCount(): int
    {
        return $this->categoryPortions()->distinct('question_id')->count();
    }

    /**
     * 软删除
     */
    public function softDelete(): bool
    {
        return $this->update(['status' => -1]);
    }

    /**
     * 恢复软删除
     */
    public function restore(): bool
    {
        return $this->update(['status' => 0]);
    }
}
