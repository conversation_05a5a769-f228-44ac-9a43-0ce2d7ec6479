<?php

namespace App\Services\School\System;

use App\Exceptions\BusinessException;
use App\Models\School\System\Claass;
use App\Models\School\System\Teacher;
use App\Models\School\System\TeacherViewClass;
use App\Repositories\TeacherViewClassRepository;
use App\Services\BaseService;
use App\Services\DataSync\DataSyncService;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TeacherViewService extends BaseService
{
    use ApiResponse;

    protected DataSyncService $dataSyncService;

    public function __construct(
        protected ClassService $classService,
        protected TeacherViewClassRepository $teacherViewClassRepository,
        DataSyncService $dataSyncService
    ) {
        $this->dataSyncService = $dataSyncService;
    }

    /**
     * 获取教师可查看班级数据
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getViewClasses(Request $request)
    {
        $teacher_id = $request->input('teacher_id');
        $school_year = $request->input('school_year');
        // $school_year = $this->getCurrentSchoolYear(); // 获取当前学年

        // 获取教师所在校区信息
        $teacher = Teacher::find($teacher_id);
        
        // 查询当前老师所在校区的所有班级
        // 开启SQL查询日志
        \DB::enableQueryLog();

        $query = Claass::where('school_id',$teacher->school_id)
            ->where('school_campus_id', $teacher->school_campus_id)
            ->with('grade:id,grade_name');

        // 获取SQL语句（不执行查询）
        $sql = $query->toSql();
        $bindings = $query->getBindings();

        // 执行查询
        $classes = $query->get(['id', 'class_name', 'grade_id']);

        // 获取执行的SQL日志
        $queryLog = \DB::getQueryLog();

        

        // 查询教师已有权限的班级ID列表
        $viewClassIds = $this->teacherViewClassRepository->getViewClassIds($teacher_id, $school_year);

//        // 构建返回数据，标记教师已有权限的班级
//        $result = [];
//        foreach ($classes as $class) {
//            $result[] = [
//                'school_year' => $school_year,
//                'class_id' => $class->id,
//                'class_name' => $class->class_name,
//                'grade_id' => $class->grade_id,
//                'grade_name' => $class->grade->grade_name ?? '',
//                'is_checked' => in_array($class->id, $viewClassIds)
//            ];
//        }
//
//        return $this->success([
//            'list' => $result,
//            'cnt' => count($result)
//        ]);

        // 按年级分组
        $groupedClasses = [];
        foreach ($classes as $class) {
            $gradeId = $class->grade_id;
            $gradeName = $class->grade->grade_name ?? '';

            $groupedClasses[$gradeId]['grade_id'] = $gradeId;
            $groupedClasses[$gradeId]['grade_name'] = $gradeName;
            $groupedClasses[$gradeId]['classes'][] = [
                'class_id' => $class->id,
                'class_name' => $class->class_name,
                'is_checked' => in_array($class->id, $viewClassIds)
            ];
        }

        // 转换为索引数组
        $groupedClasses = array_values($groupedClasses);
        return $this->success($groupedClasses);
    }

    /**
     * 设置教师可查看班级数据
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function setViewClasses(Request $request)
    {
        $teacher_id = $request->input('teacher_id');
        $school_year = $request->input('school_year');
        $class_ids = $request->input('class_ids', []);
        $user_name = Auth::user()->real_name ?? 'system';

        // 开始事务
        DB::beginTransaction();
        try {
            // 先删除教师当前学年的所有查看班级权限
            $this->teacherViewClassRepository->deleteTeacherViewClass($teacher_id, $school_year);

            // 添加新的查看班级权限
            $data = [];
            foreach ($class_ids as $class_id) {
                $data[] = [
                    'teacher_id' => $teacher_id,
                    'class_id' => $class_id,
                    'school_year' => $school_year,
                    'creator' => $user_name,
                    'updater' => $user_name,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            if (!empty($data)) {
                TeacherViewClass::insert($data);
            }

            // 同步到 ysy_teacher 表（同步失败不影响主要业务）
            try {
                $syncResult = $this->dataSyncService->syncTeacherClasses([
                    'teacher_id' => $teacher_id,
                    'class_ids' => $class_ids,
                    'school_year' => $school_year
                ]);

                // 记录同步结果
                Log::info('教师带班信息同步结果', $syncResult);
            } catch (\Exception $e) {
                // 同步失败只记录日志，不影响主要业务
                Log::error('教师带班信息同步异常', [
                    'teacher_id' => $teacher_id,
                    'class_ids' => $class_ids,
                    'school_year' => $school_year,
                    'error' => $e->getMessage()
                ]);
            }

            DB::commit();
            return $this->message('设置成功');
        } catch (\Exception $e) {
            DB::rollBack();

            // 记录详细错误信息
            Log::error('设置教师可查看班级数据失败', [
                'teacher_id' => $teacher_id,
                'school_year' => $school_year,
                'class_ids' => $class_ids,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new BusinessException("设置教师可查看班级数据设置失败: " . $e->getMessage(), 500);
        }
    }


}