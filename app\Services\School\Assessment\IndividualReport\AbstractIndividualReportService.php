<?php

namespace App\Services\School\Assessment\IndividualReport;

use App\Models\School\Assessment\AssessmentTaskAssignment;
use App\Services\BaseService;

abstract class AbstractIndividualReportService extends BaseService
{
    /**
     * 处理具体的报告生成逻辑
     */
    abstract protected function generateReport(array $params): array;

    // 查询分发测评信息
    protected function getAssignmentInfo(int $assessmentTaskAssignmentId): array
    {
        $assignmentInfo = AssessmentTaskAssignment::where('assessment_task_assignments.id', $assessmentTaskAssignmentId)
            ->join('assessments', 'assessment_task_assignments.assessment_id', '=', 'assessments.id')
            ->join('users', 'assessment_task_assignments.user_id', '=', 'users.id')
            ->join('schools', 'assessment_task_assignments.school_id', '=', 'schools.id')
            ->select(
                'assessment_task_assignments.assessment_id',
                'assessments.name as assessment_name',
                'standard_results',
                'pdf_url',
                'user_id',
                'assessment_task_assignments.student_id',
                'assessment_task_assignments.school_id',
                'assessment_task_assignments.updated_at as create_time',
                'users.real_name',
                'gender',
                'schools.name as school_name'
            )
            ->first();
            
        if (!$assignmentInfo) {
            throw new \Exception('未找到测评任务');
        }
        
        $assignmentData = $assignmentInfo->toArray();
        
        if (!$assignmentData['standard_results']) {
            throw new \Exception('测评结果为空');
        }
        
        return $assignmentData;
    }

}