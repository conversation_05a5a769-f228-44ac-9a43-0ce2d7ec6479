<?php
namespace App\Services\School\Assessment\IndividualReport\Competency;

use App\Services\School\Assessment\IndividualReport\AbstractIndividualReportService;

class LearningAbilityService extends AbstractCompetencyReportService
{
    /**
     * 改进指数限制
     * 
     * @var int
     */
    private const IMPROVEMENT_INDEX_LIMIT = 10;

    /**
     * 获取配置键名
     * 
     * @return string 配置键名
     */
    protected function getConfigKey(): string
    {
        return 'learning_ability';
    }
    
    /**
     * 获取改进指数限制
     * 
     * @return int 改进指数限制
     */
    protected function getImprovementIndexLimit(): int
    {
        return self::IMPROVEMENT_INDEX_LIMIT;
    }

    /**
     * 从标准结果中提取维度分数,问题解决能力和学习力需要重写此方法
     * 
     * @param array|string $standard_results 标准结果数据
     * @return array 维度分数数组
     */
    protected function extractDimensionScores($standard_results): array
    {
        // 如果是字符串，则解析为数组
        if (is_string($standard_results)) {
            $standard_results = json_decode($standard_results, true);
        }

        $dimensions = $standard_results['dimensions'] ?? [];
        $dimension_scores = [];

        foreach ($dimensions as $dimension) {
            if(!empty($dimension['children'])){
                foreach ($dimension['children'] as $sub_dimension) {
                    $dimension_scores[] = $sub_dimension['score'];
                }
            }
            $dimension_scores[] = $dimension['score'];
        }

        $dimension_scores[] = $standard_results['total_score'];
        
        return $dimension_scores;
    }
}
