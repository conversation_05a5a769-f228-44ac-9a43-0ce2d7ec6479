<?php

namespace App\Http\Requests;


class CreateThirdPartyUserRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'student_id' => 'required|string',
            'openid' => 'required|string|unique:users,openid',
            'real_name' => 'required|string|max:50',
            'gender' => 'required|in:1,2',
            'phone' => 'required|string|max:20',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'student_id.required' => '学生student_id不能为空',
            'openid.required' => '学生openid不能为空',
            'openid.unique' => '学生openid已存在',
            'real_name.required' => '真实姓名不能为空',
            'real_name.max' => '真实姓名不能超过50个字符',
            'gender.required' => '性别不能为空',
            'gender.in' => '性别值必须为1（男）或2（女）',
            'phone.required' => '手机号不能为空',
            'phone.max' => '手机号不能超过20个字符',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'student_id' => '学生ID',
            'phone' => '手机号',
            'openid' => 'OpenID',
            'real_name' => '真实姓名',
            'gender' => '性别',
        ];
    }
}
