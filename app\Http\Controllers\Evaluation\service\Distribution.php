<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 16:01
 */
namespace app\evaluation\service;
use think\Loader;
class Distribution{
    protected $DistributionLogic;
    public function __construct()
    {
          $this->DistributionLogic = new \app\evaluation\logic\Distribution();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://查询

                return $this->DistributionLogic->get_list();
                break;
            case 'post'://增加

                return $this->DistributionLogic->add();
                break;
            case 'put'://修改

                return $this->DistributionLogic->edit();
                break;
            case 'delete'://删除
                return $this->DistributionLogic->del();
                break;
            default:
                return false;
        }
    }

    public function set_status()
    {
        return $this->DistributionLogic->set_status();
    }

    public function take_district_grade_class_student_linkage(){
        return $this->DistributionLogic->take_district_grade_class_student_linkage();
    }

    public function take_course_grade_paper_linkage(){
        return $this->DistributionLogic->take_course_grade_paper_linkage();
    }

    public function take_schools(){
        return $this->DistributionLogic->take_schools();
    }

    public function take_teachers(){
        return $this->DistributionLogic->take_teachers();
    }

    public function distribution_name_list(){
        return $this->DistributionLogic->distribution_name_list();
    }

}