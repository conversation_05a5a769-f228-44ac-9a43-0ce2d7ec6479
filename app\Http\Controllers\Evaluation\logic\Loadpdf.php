<?php
namespace app\evaluation\logic;

class Loadpdf{
    /**
     * 根据member_id生成个人创新人才测评的整体报告
     * @return array
     * 注意此处pdf生成是新方式
     */
    public function evaluation_create($url,$member_id,$distribution_id,$logo){
//        $url_post = 'http://112.124.200.64:8380/htmltopdf-1.0.0/htmlToPdf/commonPy';//本地
        $url_post = 'http://172.16.15.126:8380/htmltopdf-1.0.0/htmlToPdf/commonPy';//线上
        #1.url生成地址
        $saveDir = '/evaluation/pdf/'.date('Ymd');
        $str = '{"path":"/data/ysy/uploads_cdn'.$saveDir.'/evaluation_'.$member_id.'_'.$distribution_id.'.pdf","format":"A4","printBackground":"true","displayHeaderFooter": True,"headerTemplate":"<style>#header { padding: 0;}</style><div style=\"display: flex; justify-content: space-between; align-items: center; width: 100%; height: 20.23px; padding: 39.87px 53.55px 29.75px;\"><img src = \"'.$logo.'\" style=\"width: auto; height: 20.23px;\"><div style=\"font-weight: normal; font-size: 10px; line-height: 1; color: #C2C2C2;\">学科素养测评综合报告</div></div>","footerTemplate": "<div class=\"pdf_footer\"></div>","start_page":2,"preferCSSPageSize":True,"margin": {"top": "31.6mm","bottom": "21mm","left":"0","right":"0"}}';

        $post_data['customDict'] = base64_encode($str);//不能传json对象，只能传字符串
        $post_data['url'] = base64_encode($url);
        #2.发起请求
        $report =request_post($url_post,json_encode($post_data));
        $pdf = objectToArray(json_decode($report));
        $pdf_url='https://s.yishengya.cn/saas_upload'.$saveDir.'/evaluation_'.$member_id.'_'.$distribution_id.'.pdf';

        //返回结果
        return array($pdf_url,$pdf['statue']);
    }

}