<?php
/**
 * Created by PhpStorm.
 * User: zhao
 * Date: 2024/01/19
 * Time: 17:52
 */
namespace app\psychassessment\controller;
use app\login\controller\ApiAuth;


/**
 * Class Questions
 * @package app\psychassessment\controller
 */
class Survey extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->Survey = new \app\psychassessment\service\Survey();
    }

    /**
     * 模块：心理评估-测评计划
     * @SWG\Post(path="/psychassessment/survey",
     *   tags={"心理评估-测评计划:Survey"},
     *   summary="测评计划添加",
     *  @SWG\Parameter(
     *     in="formData",
     *     name="name",
     *     type="string",
     *     description="测评计划名称",
     *     required=false,
     *   ),
     *  @SWG\Parameter(
     *     in="formData",
     *     name="class_ids",
     *     type="string",
     *     description="多个班级class_ids",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="formData",
     *     name="member_ids",
     *     type="string",
     *     description="学生member_id,多个用逗号拼接",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="formData",
     *     name="survey_ids",
     *     type="string",
     *     description="测评id,心里测评3选N",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="formData",
     *     name="start_time",
     *     type="string",
     *     description="测评开始时间",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="formData",
     *     name="end_time",
     *     type="string",
     *     description="测评结束时间",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="formData",
     *     name="show_report",
     *     type="integer",
     *     description="是否对学生开放，1是2否",
     *     required=true,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：心理评估-测评计划
     * @SWG\Put(path="/psychassessment/survey/{id}",
     *   tags={"心理评估-测评计划:Survey"},
     *   summary="测评计划修改",
     *  @SWG\Parameter(
     *     in="path",
     *     name="id",
     *     type="integer",
     *     description="id",
     *     required=true,
     *  ),
     *   @SWG\Parameter(
     *     in="body",
     *     name="data",
     *     description="更新的数据",
     *     required=true,
     *     @SWG\Schema(
     *       type="object",
     *       @SWG\Property(property="name", type="string", description="名称"),
     *       @SWG\Property(property="class_ids", type="string", description="班级"),
     *       @SWG\Property(property="member_ids", type="string", description="member_id"),
     *       @SWG\Property(property="survey_ids", type="string", description="测评id"),
     *       @SWG\Property(property="start_time", type="string", description="测评开始时间"),
     *       @SWG\Property(property="end_time", type="string", description="测评结束时间"),
     *       @SWG\Property(property="show_report", type="integer", description="是否对学生开放，1是2否")
     *     )
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：心理评估-测评计划
     * @SWG\Get(path="/psychassessment/survey",
     *   tags={"心理评估-测评计划:Survey"},
     *   summary="测评计划列表",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     * @SWG\Parameter(
     *     in="query",
     *     name="keyword",
     *     type="string",
     *     description="关键字查询",
     *     required=false,
     *   ),
     * @SWG\Parameter(
     *     in="query",
     *     name="pagesize",
     *     type="integer",
     *     description="每页显示记录数",
     *     required=false,
     *   ),
     * @SWG\Parameter(
     *     in="query",
     *     name="page",
     *     type="integer",
     *     description="页码",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：心理评估-测评计划
     * @SWG\DELETE(path="/psychassessment/survey/{id}",
     *   tags={"心理评估-测评计划:Survey"},
     *   summary="测评计划删除",
     *   description="数据说明：",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function survey()
    {
        $data = $this->Survey->hand_out();
        apiReturn($data);
    }

    /**
     * 模块：心理评估-列表
     * @SWG\Get(path="/psychassessment/assess_list",
     *   tags={"心理评估-测评计划:Survey"},
     *   summary="心理评估列表",
     *   description="数据说明：",
     *   produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function assess_list()
    {
        $data = $this->Survey->assess_list();
        apiReturn($data);
    }

    /**
     * 模块：心理评估-获取老师辅导的学生列表
     * @SWG\Get(path="/psychassessment/get_psychassessment_class_member",
     *   tags={"心理评估-测评计划:Survey"},
     *   summary="获取老师辅导的学生列表",
     *   description="数据说明：",
     *   produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function get_psychassessment_class_member()
    {
        $data = $this->Survey->get_psychassessment_class_member();
        apiReturn($data);
    }

    /**
     * 模块：心理评估-获取未完成学生列表
     * @SWG\Get(path="/psychassessment/get_member_complete_status",
     *   tags={"心理评估-测评计划:Survey"},
     *   summary="获取未完成的学生列表",
     *   description="数据说明：",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="计划id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="status",
     *     type="integer",
     *     description="状态，1已完成，2未完成，不传返回全部",
     *     required=false,
     *   ),
     *   produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function get_member_complete_status()
    {
        $data = $this->Survey->get_member_complete_status();
        apiReturn($data);
    }

    /**
     * 模块：心理评估-测评计划
     * @SWG\Get(path="/psychassessment/get_student_psychassess",
     *   tags={"心理评估-测评计划:Survey"},
     *   summary="学生端测评计划列表",
     *   description="数据说明：",
     *   produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function get_student_psychassess()
    {
        $data = $this->Survey->get_student_psychassess();
        apiReturn($data);
    }

}