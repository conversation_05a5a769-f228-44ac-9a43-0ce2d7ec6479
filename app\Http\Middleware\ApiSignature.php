<?php

namespace App\Http\Middleware;

use App\Traits\ApiResponse;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ApiSignature
{
    use ApiResponse;

    /**
     * 处理传入的请求，验证API签名
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
       // 获取请求头中的签名信息
       // 时间戳
       $timestamp = $request->header('X-Timestamp');
       // 随机数
       $nonce = $request->header('X-Nonce');
       // 签名
       $signature = $request->header('X-Signature');

       // 验证必要参数
       if (!$timestamp || !$nonce || !$signature) {
           return $this->error('缺少必要的安全参数', 401);
       }

       // 验证时间戳是否在有效期内（例如5分钟）
       if (abs(time() - intval($timestamp)) > 300) {
           return $this->error('请求已过期', 401);
       }

       // 获取应用密钥（实际应用中应从数据库或配置中获取）
       $appSecret = $this->getAppSecret();

       // 计算签名
       $params = $request->all();
       $params['timestamp'] = $timestamp;
       $params['nonce'] = $nonce;
       ksort($params);
       $paramString = http_build_query($params);
       $calculatedSignature = hash_hmac('sha256', $paramString, $appSecret);

       // 验证签名
       if ($calculatedSignature !== $signature) {
           return $this->error('签名验证失败', 401);
       }
       
        return $next($request);
    }
    
    /**
     * 获取应用密钥
     *
     * @param string $appKey
     * @return string|null
     */
    private function getAppSecret(): ?string
    {
        // 从环境变量中获取应用密钥
        return env('APP_API_SECRET_KEY');
    }
}