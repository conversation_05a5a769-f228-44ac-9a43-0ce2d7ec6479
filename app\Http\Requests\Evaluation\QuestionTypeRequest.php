<?php

namespace App\Http\Requests\Evaluation;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 题目类型请求验证类
 */
class QuestionTypeRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        $rules = [
            'type_name' => 'required|string|max:50',
            'is_subjective' => 'required|integer|in:0,1',
        ];

        // 根据请求方法添加不同的规则
        if ($this->isMethod('post')) {
            // 创建时的额外规则
            $rules = array_merge($rules, [
                'description' => 'sometimes|string|max:500',
                'code' => 'sometimes|string|max:20|unique:evaluation_question_types,code,NULL,id,status,0',
                'sort' => 'sometimes|integer|min:0',
            ]);
        } elseif ($this->isMethod('put') || $this->isMethod('patch')) {
            // 更新时的规则（大部分字段变为可选）
            $currentId = $this->route('id');
            $rules = [
                'type_name' => 'sometimes|string|max:50',
                'is_subjective' => 'sometimes|integer|in:0,1',
                'description' => 'sometimes|string|max:500',
                'code' => "sometimes|string|max:20|unique:evaluation_question_types,code,{$currentId},id,status,0",
                'sort' => 'sometimes|integer|min:0',
            ];
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性名称
     */
    public function attributes(): array
    {
        return [
            'type_name' => '类型名称',
            'is_subjective' => '题目性质',
            'description' => '类型描述',
            'code' => '类型代码',
            'sort' => '排序',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            'type_name.required' => '类型名称不能为空',
            'type_name.string' => '类型名称必须是字符串',
            'type_name.max' => '类型名称不能超过50个字符',
            'is_subjective.required' => '题目性质不能为空',
            'is_subjective.integer' => '题目性质必须是整数',
            'is_subjective.in' => '题目性质只能是0（客观题）或1（主观题）',
            'description.string' => '类型描述必须是字符串',
            'description.max' => '类型描述不能超过500个字符',
            'code.string' => '类型代码必须是字符串',
            'code.max' => '类型代码不能超过20个字符',
            'code.unique' => '类型代码已存在',
            'sort.integer' => '排序必须是整数',
            'sort.min' => '排序不能小于0',
        ];
    }

    /**
     * 配置验证实例
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 自定义验证逻辑
            $this->validateTypeName($validator);
        });
    }

    /**
     * 验证类型名称唯一性
     */
    private function validateTypeName($validator): void
    {
        $typeName = $this->input('type_name');
        
        if ($typeName) {
            $query = \App\Models\Evaluation\QuestionType::where('type_name', $typeName)
                ->where('status', 0);
                
            // 更新时排除当前记录
            if ($this->isMethod('put') || $this->isMethod('patch')) {
                $currentId = $this->route('id');
                if ($currentId) {
                    $query->where('id', '!=', $currentId);
                }
            }
            
            if ($query->exists()) {
                $validator->errors()->add('type_name', '类型名称已存在');
            }
        }
    }
}
