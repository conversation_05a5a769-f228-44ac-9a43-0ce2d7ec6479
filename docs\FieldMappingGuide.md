# 数据同步字段映射指南

## 概述

数据同步模块支持灵活的字段映射功能，可以将原始数据库的字段名转换为同步数据库的字段名。这对于不同数据库结构之间的数据同步非常有用。

## 字段映射配置

### 1. 配置文件位置

字段映射配置位于 `config/datasync.php` 文件中的 `field_mapping` 部分：

```php
'field_mapping' => [
    'school' => [
        'school_name' => 'name',           // 学校名称
        'school_code' => 'code',           // 学校代码
        'contact_phone' => 'phone',        // 联系电话
        // ... 更多映射
    ],
    'student' => [
        'student_name' => 'name',          // 学生姓名
        'student_no' => 'student_number',  // 学号
        'sex' => 'gender',                 // 性别
        // ... 更多映射
    ],
    // ... 其他类型
],
```

### 2. 支持的数据类型

- `school` - 学校数据
- `student` - 学生数据
- `teacher` - 教师数据
- `admin` - 管理员数据

## 字段映射规则

### 学校字段映射

| 原始字段名 | 同步字段名 | 说明 |
|-----------|-----------|------|
| `school_name` | `name` | 学校名称 |
| `school_code` | `code` | 学校代码 |
| `school_type` | `type` | 学校类型 |
| `school_level` | `level` | 学校等级 |
| `contact_phone` | `phone` | 联系电话 |
| `contact_email` | `email` | 联系邮箱 |
| `official_website` | `website` | 官方网站 |
| `school_desc` | `description` | 学校描述 |
| `school_status` | `status` | 学校状态 |

### 学生字段映射

| 原始字段名 | 同步字段名 | 说明 |
|-----------|-----------|------|
| `student_name` | `name` | 学生姓名 |
| `student_no` | `student_number` | 学号 |
| `sex` | `gender` | 性别 |
| `mobile` | `phone` | 手机号 |
| `student_status` | `status` | 状态 |

### 教师字段映射

| 原始字段名 | 同步字段名 | 说明 |
|-----------|-----------|------|
| `teacher_name` | `name` | 教师姓名 |
| `teacher_no` | `teacher_number` | 教师编号 |
| `sex` | `gender` | 性别 |
| `mobile` | `phone` | 手机号 |
| `dept` | `department` | 部门 |
| `job_title` | `position` | 职位 |
| `teacher_status` | `status` | 状态 |

### 管理员字段映射

| 原始字段名 | 同步字段名 | 说明 |
|-----------|-----------|------|
| `admin_name` | `name` | 管理员姓名 |
| `user_name` | `username` | 用户名 |
| `sex` | `gender` | 性别 |
| `mobile` | `phone` | 手机号 |
| `dept` | `department` | 部门 |
| `job_title` | `position` | 职位 |
| `user_role` | `role` | 角色 |
| `admin_status` | `status` | 状态 |

## API 接口使用

### 1. 获取字段映射配置

```bash
GET /admin/datasync/field-mapping

# 获取特定类型的字段映射
GET /admin/datasync/field-mapping?type=school
```

**响应示例：**
```json
{
    "status": "success",
    "code": 200,
    "message": "获取字段映射成功",
    "data": {
        "field_mapping": {
            "school_name": "name",
            "school_code": "code",
            "contact_phone": "phone"
        },
        "available_types": ["school", "student", "teacher", "admin"]
    }
}
```

### 2. 测试字段映射

```bash
POST /admin/datasync/test-field-mapping
Content-Type: application/json

{
    "type": "school",
    "test_data": {
        "school_name": "示例学校",
        "school_code": "DEMO001",
        "contact_phone": "123456789",
        "address": "示例地址"
    },
    "custom_mapping": {
        "school_name": "name",
        "school_code": "code",
        "contact_phone": "phone"
    }
}
```

**响应示例：**
```json
{
    "status": "success",
    "code": 200,
    "message": "字段映射测试成功",
    "data": {
        "original_data": {
            "school_name": "示例学校",
            "school_code": "DEMO001",
            "contact_phone": "123456789",
            "address": "示例地址"
        },
        "mapped_data": {
            "name": "示例学校",
            "code": "DEMO001",
            "phone": "123456789",
            "address": "示例地址"
        },
        "mapping_rules": {
            "school_name": "name",
            "school_code": "code",
            "contact_phone": "phone"
        }
    }
}
```

### 3. 获取完整配置

```bash
GET /admin/datasync/config
```

## 使用示例

### 1. 同步学校数据示例

**原始数据：**
```json
{
    "id": 1,
    "school_name": "北京第一中学",
    "school_code": "BJ001",
    "school_type": 2,
    "contact_phone": "010-12345678",
    "contact_email": "<EMAIL>",
    "address": "北京市朝阳区示例路123号",
    "school_status": 1
}
```

**映射后的数据：**
```json
{
    "id": 1,
    "name": "北京第一中学",
    "code": "BJ001",
    "type": 2,
    "phone": "010-12345678",
    "email": "<EMAIL>",
    "address": "北京市朝阳区示例路123号",
    "status": 1,
    "original_id": 1
}
```

### 2. 同步学生数据示例

**原始数据：**
```json
{
    "id": 1,
    "student_name": "张三",
    "student_no": "2024001",
    "sex": 1,
    "mobile": "13800138000",
    "school_id": 1,
    "student_status": 1
}
```

**映射后的数据：**
```json
{
    "id": 1,
    "name": "张三",
    "student_number": "2024001",
    "gender": 1,
    "phone": "13800138000",
    "school_id": 1,
    "status": 1,
    "original_id": 1
}
```

## 自定义字段映射

### 1. 修改配置文件

直接编辑 `config/datasync.php` 文件中的字段映射配置：

```php
'field_mapping' => [
    'school' => [
        // 添加自定义映射
        'custom_field' => 'target_field',
        'another_field' => 'mapped_field',
    ],
],
```

### 2. 环境变量配置

虽然字段映射主要通过配置文件管理，但您也可以通过环境变量来控制某些行为：

```env
# 是否启用字段映射
DATA_SYNC_FIELD_MAPPING_ENABLED=true

# 是否使用严格模式（只映射配置中的字段）
DATA_SYNC_FIELD_MAPPING_STRICT=false
```

## 注意事项

### 1. 映射优先级

1. **配置文件映射** - 优先使用 `config/datasync.php` 中的映射
2. **默认映射** - 如果配置文件中没有，使用代码中的默认映射
3. **保持原字段** - 如果都没有映射规则，保持原字段名

### 2. 数据类型处理

- 字段映射只处理字段名，不处理数据类型转换
- 确保映射后的字段在目标数据库中存在
- 注意字段长度和约束限制

### 3. 特殊字段

- `id` 字段会被映射为 `original_id`，用于关联原始数据
- `created_at` 和 `updated_at` 会自动添加
- 某些系统字段可能需要特殊处理

### 4. 性能考虑

- 字段映射会增加少量处理时间
- 建议在配置文件中预定义常用映射
- 避免在运行时频繁修改映射规则

## 故障排查

### 1. 字段映射不生效

- 检查配置文件语法是否正确
- 确认字段名大小写匹配
- 查看日志中的映射过程

### 2. 数据丢失

- 确认目标字段在同步数据库中存在
- 检查字段长度和类型限制
- 查看同步日志中的错误信息

### 3. 测试映射

使用测试接口验证映射规则：

```bash
curl -X POST http://your-domain/admin/datasync/test-field-mapping \
  -H "Content-Type: application/json" \
  -d '{
    "type": "school",
    "test_data": {"school_name": "测试学校"},
    "custom_mapping": {"school_name": "name"}
  }'
```

通过这个字段映射功能，您可以灵活地处理不同数据库结构之间的字段名差异，确保数据同步的准确性和完整性。
