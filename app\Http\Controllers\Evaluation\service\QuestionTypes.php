<?php
/**
 * Created by PhpStorm.
 * User: zuochen
 * Date: 2023/8/30
 * Time: 16:01
 */
namespace app\evaluation\service;
use think\Loader;
class QuestionTypes{
    protected $QuestionTypesLogic;
    public function __construct()
    {
          $this->QuestionTypesLogic = new \app\evaluation\logic\QuestionTypes();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://获取
                return $this->QuestionTypesLogic->get_list();
                break;
            case 'post'://增加
                return $this->QuestionTypesLogic->add();
                break;
            case 'put'://修改

                return $this->QuestionTypesLogic->edit();
                break;
            case 'delete'://删除
                return $this->QuestionTypesLogic->del();
                break;
            default:
                return false;
        }
    }



}