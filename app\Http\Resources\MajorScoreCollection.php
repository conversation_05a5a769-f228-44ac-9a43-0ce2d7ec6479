<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MajorScoreCollection extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->ID,
            'college_id' => $this->CollegeID,
            'college' => $this->College,
            'college_code' => $this->CollegeCode,
            'major' => $this->Major,
            'major_code' => $this->MajorCode,
            'highest_score' => $this->HighestScore,
            'avg_score' => $this->AvgScore,
            'lowest_score' => $this->LowestScore,
            'lowest_score_rank' => $this->LowestScoreRank,
            'people_count' => $this->PeopleCount,
            'major_remark' => $this->MajorRemark,
            'year' => $this->Year,
            'liberalScience' => $this->LiberalScience,
            'phase' => $this->Phase,
            'phase_name' => $this->PhaseName,
            'province_id' => $this->ProvinceId,
            'course' => (isset($this->Course) ? ($this->Course) : '') . (isset($this->CourseSecond) ? ('+' . $this->CourseSecond) : ''),
        ];
    }
}
