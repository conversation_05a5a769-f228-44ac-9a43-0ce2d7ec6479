<?php
namespace App\Services\School\Assessment\IndividualReport\Capability;

use App\Services\School\Assessment\IndividualReport\AbstractIndividualReportService;
use App\Models\School\Assessment\Question\AssessmentCapabilityQuestion;
use App\Models\School\Assessment\Answer\AssessmentCapabilityAnswer;

/**
 * 能力评估个人报告抽象服务类
 * 
 * 该类为各种能力评估个人报告提供基础功能，包括报告生成、维度分析和等级计算
 */
abstract class AbstractCapabilityReportService extends AbstractIndividualReportService
{
    /**
     * 能力配置
     * 
     * @var array
     */
    protected array $config;
    
    /**
     * 分数阈值
     * 
     * @var int
     */
    protected const SCORE_THRESHOLD = 3;
    
    /**
     * 默认低分阈值
     * 
     * @var float
     */
    protected const DEFAULT_LOW_THRESHOLD = 6;
    
    /**
     * 默认高分阈值
     * 
     * @var float
     */
    protected const DEFAULT_HIGH_THRESHOLD = 8;
    
    /**
     * 低等级列表
     * 
     * @var array
     */
    protected const LOW_LEVELS = ['L1', 'L2'];

    /**
     * 构造函数
     * 
     * 初始化能力配置
     */
    public function __construct()
    {
        $this->config = config('assessment.capability.' . $this->getConfigKey());
    }

    /**
     * 获取配置键名
     * 
     * @return string 配置键名
     */
    abstract protected function getConfigKey(): string;

    /**
     * 获取改进指数限制
     * 
     * 子类必须实现此方法以提供其特定的改进指数限制。
     * 
     * @return int
     */
    abstract protected function getImprovementIndexLimit(): int;

    /**
     * 生成能力个人报告
     * 
     * @param array $params 包含assessment_task_assignment_id和assessment_id的参数数组
     * @return array 生成的报告数据
     */
    public function generateReport(array $params): array
    {
        $assignment_id = $params['assessment_task_assignment_id'];
        // 获取分发信息
        $assignment_info = $this->getAssignmentInfo($assignment_id);
        
        // 生成报告
        $assessment_info = $this->generateCapabilityReport($assignment_info['standard_results'], $params);

        // 合并结果
        return array_merge($assignment_info, $assessment_info);
    }

    /**
     * 生成能力报告内容
     * 
     * @param array $standard_results 标准化的评估结果
     * @param array $params 包含assessment_id和assessment_task_assignment_id的参数数组
     * @return array 报告内容
     */
    protected function generateCapabilityReport(array $standard_results, array $params): array
    {
        $questions = $this->getQuestions($params['assessment_id']);
        $answers = $this->getAnswers($params);
        $answer_scores = $this->calculateScores($questions, $answers);
        
        return [
            'content' => $this->processReportContent($standard_results, $answer_scores)
        ];
    }

    /**
     * 处理报告内容
     * 
     * @param array $standard_results 标准化的评估结果
     * @param array $answer_scores 答案得分数组
     * @return array 处理后的报告内容
     */
    protected function processReportContent(array $standard_results, array $answer_scores): array
    {
        $content = [];
        $improvement_areas = [];
        $categories = $this->config['category'];
        
        // 将维度数据转换为以索引为键的数组，方便处理
        $dimension_scores = [];
        foreach ($standard_results['dimensions'] as $dimension) {
            $index = intval($dimension['code']) - 1; // 将代码转换为索引
            $dimension_scores[$index] = $dimension['score'];
        }
        
        // 添加总分
        $dimension_scores[count($dimension_scores)] = $standard_results['total_score'];

        foreach($categories as $index => $category){
            [$high_score_questions, $low_score_questions] = $this->getDimensionQuestionsByScore($answer_scores, $category['name']);
            $level_info = $this->calculateLevel($dimension_scores[$index], $index, $category['low'], $category['high']);
            
            if($this->shouldImprove($index, $level_info[0])) {
                $improvement_areas[] = $category['name'];
            }

            $content[$index] = $this->formatCategoryContent(
                $category,
                $dimension_scores[$index],
                $level_info,
                $high_score_questions,
                $low_score_questions,
                $improvement_areas,
                $index
            );
        }

        return $content;
    }

    /**
     * 获取维度的高分题和低分题
     * 
     * @param array $answer_scores 答案得分数组
     * @param string $dimension_name 维度名称
     * @return array 包含高分题和低分题的数组
     */
    protected function getDimensionQuestionsByScore(array $answer_scores, string $dimension_name): array
    {
        // 子类可以重写此方法以处理特殊情况
        $high_score_questions = [];
        $low_score_questions = [];

        foreach ($answer_scores as $answer) {
            if ($dimension_name === $answer['dimension_name']) {
                if($answer['score'] > self::SCORE_THRESHOLD){
                    $high_score_questions[] = $answer['content'];
                } else {
                    $low_score_questions[] = $answer['content'];
                }
            }
        }

        return [$high_score_questions, $low_score_questions];
    }

    /**
     * 计算维度等级
     * 
     * @param float $score 分数
     * @param int $level_type 等级类型
     * @param float $low 低分阈值，默认为6
     * @param float $high 高分阈值，默认为8
     * @return array 等级信息数组
     */
    protected function calculateLevel(float $score, int $level_type, float $low = self::DEFAULT_LOW_THRESHOLD, float $high = self::DEFAULT_HIGH_THRESHOLD): array
    {
        if($score < $low){
            return $this->config[$level_type][0];
        }
        if($score < $high){
            return $this->config[$level_type][1];
        }
        return $this->config[$level_type][2];
    }

    /**
     * 判断是否需要改进
     * 
     * @param int $index 维度索引
     * @param string $level 等级
     * @return bool 是否需要改进
     */
    protected function shouldImprove(int $index, string $level): bool
    {
        return $index < $this->getImprovementIndexLimit() && in_array($level, self::LOW_LEVELS);
    }

    /**
     * 格式化分类内容
     * 
     * @param array $category 分类信息
     * @param float $score 分数
     * @param array $level_info 等级信息
     * @param array $high_scores 高分题目
     * @param array $low_scores 低分题目
     * @param array $improvement_areas 需要改进的领域
     * @param int $index 维度索引
     * @return array 格式化后的分类内容
     */
    protected function formatCategoryContent(
        array $category,
        float $score,
        array $level_info,
        array $high_scores,
        array $low_scores,
        array $improvement_areas,
        int $index
    ): array {
        return [
            'name' => $category['name'],
            'score' => $score,
            'level' => $level_info[0],
            'level_desc' => $level_info[1],
            'level_content' => $this->formatLevelContent($level_info[2], $improvement_areas, $index, $level_info[0]),
            'high_problem' => $high_scores,
            'low_problem' => $low_scores,
        ];
    }

    /**
     * 格式化等级内容
     * 
     * @param string $content 内容
     * @param array $improvement_areas 需要改进的领域
     * @param int $index 维度索引
     * @param string $level 等级
     * @return string 格式化后的内容
     */
    protected function formatLevelContent(string $content, array $improvementAreas, int $index, string $level): string
    {
        if ($improvementAreas && $index == $this->getImprovementIndexLimit() && in_array($level, self::LOW_LEVELS)) {
            return sprintf($content, implode(',', $improvementAreas));
        }
        return $content;
    }

    /**
     * 获取问题列表
     * 
     * @param int $assessment_id 评估ID
     * @return array 问题列表
     */
    protected function getQuestions(int $assessment_id): array
    {
        $questions = AssessmentCapabilityQuestion::where('assessment_id', $assessment_id)
            ->select('id', 'content', 'options', 'dimension_code', 'dimension_name', 'correct')
            ->get()
            ->keyBy('id')
            ->toArray();
        return $questions;
    }

    /**
     * 获取答案列表
     * 
     * @param array $params 参数数组
     * @return array 答案列表
     */
    protected function getAnswers(array $params): array
    {
        return AssessmentCapabilityAnswer::where('assessment_id', $params['assessment_id'])
            ->where('assessment_task_assignment_id', $params['assessment_task_assignment_id'])
            ->orderBy('assessment_capability_question_id')
            ->select('answer', 'assessment_capability_question_id')
            ->get()
            ->toArray();
    }

    /**
     * 计算得分
     * 
     * @param array $questions 问题列表
     * @param array $answers 答案列表
     * @return array 得分数组
     */
    protected function calculateScores(array $questions, array $answers): array
    {
        $result = [];
        
        // 计算每个答案的得分
        foreach ($answers as $answer) {
            $question_id = $answer['assessment_capability_question_id'];
            if (isset($questions[$question_id])) {
                $question = $questions[$question_id];
                $user_answer = $answer['answer'];
                
                // 判断是否有正确答案
                if (!empty($question['correct'])) {
                    $correct_strlen = strlen($question['correct']);
                    // 单选题或者密码锁题全等得1分
                    if ($correct_strlen == 1 && $user_answer == $question['correct']) {
                        $score = 1;
                    } else {
                        $score = 0;
                    }
                } else {
                    // 量表题，根据选项分数计算
                    $options = $question['options'];
                    $score = $options[$user_answer]['score'];
                }
                
                $result[] = [
                    'assessment_capability_question_id' => $question_id,
                    'content' => $question['content'],
                    'dimension_name' => $question['dimension_name'],
                    'score' => $score,
                    'answer' => $user_answer
                ];
            }
        }
        
        return $result;
    }
}