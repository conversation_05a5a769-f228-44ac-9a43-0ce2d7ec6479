<?php

namespace App\Http\Requests\School\System;

use App\Http\Requests\BaseRequest;

class ClassRequest extends BaseRequest
{

    public function rules(): array
    {
        $rules = [
            'school_campus_id' => 'required|integer',
            'grade_id' => 'required|integer'
        ];

        if ($this->has('class_names')) {
            // 批量提交
            $rules['class_names'] = 'required|array';
            $rules['class_names.*'] = 'required|string|max:255';
        }
        if ($this->has('class_name')) {
            $rules['class_name'] = 'required|string|max:255';
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'school_campus_id.integer' => 'school_campus_id 必须为整数',
            'school_campus_id.required' => 'school_campus_id 必须为整数',
            'grade_id.required' => 'grade_id 必须为整数',
            'grade_id.integer' => 'grade_id 必须为整数',
            'class_names.array' => 'class_names 必须为数组'
        ];
    }
}
