<?php

namespace App\Http\Controllers\School\System;

use App\Http\Controllers\Controller;
use App\Models\School\System\SchoolCampus;
use App\Services\School\System\ClassService;
use App\Services\School\System\SchoolService;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;

class SchoolCampusController extends Controller
{
    use CrudOperations;

    protected string $model = SchoolCampus::class;
    protected  $schoolService;
    public function __construct(SchoolService $schoolService )
    {
        $this->schoolService = $schoolService;
    }

    //all 所有学校的所有校区 关联school表 获取学校名称
    public function all(Request $request)
    {
        $schoolCampus = $this->schoolService->getAllSchoolCampus($request);
        return $this->success($schoolCampus);
    }


    public function index(Request $request, ClassService $classService)
    {
        // 获取学校id
        $school_id = $classService->getSchoolId($request);
        $schoolCampus = $this->schoolService->getSchoolCampus($school_id);
        return $this->success($schoolCampus);
    }


    public function store(Request $request, ClassService $service)
    {
        // 获取学校id
//        $school_id = $service->getSchoolId($request);
        $school_id = $request->input('school_id');

        // 判断校区是否已存在
        $is_exist = $this->model::where('school_id', $school_id)
            ->where('campus_name', $request->campus_name)
            ->first();
        if ($is_exist) {
            return $this->error('校区已存在');
        }

        $data = filterRequestData($this->getTableName());
        $data['school_id'] = $school_id;
        $data['status'] = 1;
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $record = $this->model::forceCreate($data);
        // return $this->success($record);
        return $this->message('添加成功');
    }


    public function update(Request $request, $id)
    {
        $record = $this->model::find($id);
        if (!$record) {
            return $this->notFound('更新对象不存在');
        }
        // 获取当前登录用户所属学校id
        $school_id = $request->user()->organization->model_id;
        // 判断校区是否已存在
        $is_exist = $this->model::where('school_id', $school_id)
            ->where('campus_name', $request->campus_name)
            ->where('id', '!=', $id)
            ->first();
        if ($is_exist) {
            return $this->error('校区已存在');
        }

        $data = filterRequestData($this->getTableName());
        $data['updater'] = $request->user()->real_name;
        $record->fill($data)->save();
        // return $this->success($record);
        return $this->message('修改成功');
    }

}
