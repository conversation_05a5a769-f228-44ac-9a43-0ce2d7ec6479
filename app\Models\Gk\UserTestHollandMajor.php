<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserTestHollandMajor extends BaseModel
{
    use HasFactory;

    protected $table = 'UserTestHollandMajor';

    // 指定连接
    protected $connection = 'sqlsrv_gk';
    
    // 主键
    protected $primaryKey = 'ID';
    
    // 是否使用时间戳
    public $timestamps = false;
    
    // 可批量赋值的字段
    protected $fillable = [
        'HollandCode',
        'Gender',
        'MajorID',
        'MajorName',
        'MajorCode',
        'SubjectID',
        'CategoryID',
        'Remark',
        'Phase'
    ];
    
    /**
     * 获取关联的专业信息
     */
    public function major()
    {
        // 根据Phase判断关联本科或专科专业
        if ($this->Phase == 1) {
            return $this->belongsTo(MajorBK::class, 'MajorID', 'Id');
        } else {
            return $this->belongsTo(MajorZK::class, 'MajorID', 'Id');
        }
    }
    
    /**
     * 获取关联的学科信息
     */
    public function subject()
    {
        // 根据Phase判断关联本科或专科学科
        if ($this->Phase == 1) {
            return $this->belongsTo(MajorSubjectBK::class, 'SubjectID', 'Id');
        } else {
            return $this->belongsTo(MajorSubjectZK::class, 'SubjectID', 'Id');
        }
    }
    
    /**
     * 获取关联的类别信息
     */
    public function category()
    {
        // 根据Phase判断关联本科或专科类别
        if ($this->Phase == 1) {
            return $this->belongsTo(MajorCategoryBK::class, 'CategoryID', 'Id');
        } else {
            return $this->belongsTo(MajorCategoryZK::class, 'CategoryID', 'Id');
        }
    }
}
