<?php

namespace App\Models\Partner;

use App\Models\Admin\Organization;
use App\Models\BaseModel;
use App\Models\Role;
use App\Models\SiteConfig;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 *
 *
 * @property int $id
 * @property string $partner_name 伙伴名称
 * @property string $province 省
 * @property string|null $city 市
 * @property string|null $district 区县
 * @property int $type 伙伴类型1普代2区代3省代4区局5市局6省局
 * @property string|null $address 详细地址
 * @property string $add_date 添加日期
 * @property string $due_date 到期日期
 * @property int $site_config_id 站点配置ID
 * @property int $state 状态0未激活 1激活 2冻结
 * @property int $agent_id 原表ID,用完可删
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $creator 添加人
 * @property string $updater 更新人
 * @method static \Illuminate\Database\Eloquent\Builder|Partner newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Partner newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Partner query()
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereAddDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereAgentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereDistrict($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereDueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner wherePartnerName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereProvince($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereSiteConfigId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner whereUpdater($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Partner pagination()
 * @mixin \Eloquent
 */
class Partner extends BaseModel
{
    use SoftDeletes;
    protected $hidden = [
        'created_at',
        'updated_at',
        'creator',
        'updater',
    ];

    public function organization()
    {
        return $this->morphOne(Organization::class, 'model');
    }

    public function role()
    {
        return $this->morphOne(Role::class, 'model');
    }

    public function config()
    {
        return $this->belongsTo(SiteConfig::class, 'site_config_id', 'id');
    }
}
