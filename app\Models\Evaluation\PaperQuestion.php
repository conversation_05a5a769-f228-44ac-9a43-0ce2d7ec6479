<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 试卷题目关联模型
 */
class PaperQuestion extends Model
{
    protected $table = 'evaluation_paper_questions';

    protected $fillable = [
        'paper_id',
        'question_id',
        'sort',
        'score',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'paper_id' => 'integer',
        'question_id' => 'integer',
        'sort' => 'integer',
        'score' => 'integer',
    ];

    /**
     * 所属试卷关联
     */
    public function paper(): BelongsTo
    {
        return $this->belongsTo(Papers::class, 'paper_id');
    }

    /**
     * 所属题目关联
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class, 'question_id');
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort');
    }

    /**
     * 作用域：按试卷筛选
     */
    public function scopeByPaper($query, $paperId)
    {
        return $query->where('paper_id', $paperId);
    }

    /**
     * 作用域：按题目筛选
     */
    public function scopeByQuestion($query, $questionId)
    {
        return $query->where('question_id', $questionId);
    }
}
