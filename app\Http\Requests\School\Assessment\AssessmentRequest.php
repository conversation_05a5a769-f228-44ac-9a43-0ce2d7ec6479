<?php

namespace App\Http\Requests\School\Assessment;

use App\Http\Requests\BaseRequest;

class AssessmentRequest extends BaseRequest
{
    public function rules(): array
    {
        $method = $this->route()->getActionMethod();

        return match ($method) {
            'questionList' => $this->questionListRules(),
            'saveAnswer' => $this->saveAnswerRules(),
            'teacherSubmitAndReport' => $this->teacherSubmitAndReportRules(),
            'report' => $this->reportRules(),
            'groupReport' => $this->groupReportRules(),
            default => []
        };
    }

    private function questionListRules(): array
    {
        return [
            'assessment_id' => 'required|integer',
            'type' => 'required|string',
        ];
    }

    private function saveAnswerRules(): array
    {
        return [
            'duration' => 'required|integer|min:0',
            'assessment_id' => 'required|integer',
            'assessment_task_assignment_id' => 'nullable|integer',
            'answer' => 'required|array|min:1',
            'answer.*' => 'required|array|size:2',
            'answer.*.question_id' => 'required|integer',
            'answer.*.option' => 'required|string',
        ];
    }

    private function reportRules(): array
    {
        return [
            'assessment_task_assignment_id' => 'required|integer',
            'assessment_id' => 'required|integer',
        ];
    }

    private function teacherSubmitAndReportRules(): array
    {
        return [
            'duration' => 'required|integer|min:0',
            'assessment_id' => 'required|integer',
            'answer' => 'required|array|min:1',
            'answer.*' => 'required|array|size:2',
            'answer.*.question_id' => 'required|integer',
            'answer.*.option' => 'required|string',
        ];
    }

    private function groupReportRules(): array
    {
        return [
            'assessment_schedule_id' => 'required|integer',
            'assessment_id' => 'required|integer',
        ];
    }

    public function messages(): array
    {
        return [
            'assessment_id.required' => '测评ID不能为空',
            'assessment_id.integer' => '测评ID必须为整数',
            'duration.required' => '答题时长不能为空',
            'duration.integer' => '答题时长必须为整数',
            'duration.min' => '答题时长不能小于0',
            'assessment_task_assignment_id.required' => '任务分配ID不能为空',
            'assessment_task_assignment_id.integer' => '分发ID必须为整数',
            'answer.required' => '答案不能为空',
            'answer.array' => '答案格式错误',
            'answer.min' => '至少需要提交一个答案',
            'answer.*.array' => '答案项格式错误',
            'answer.*.size' => '答案项格式错误',
            'answer.*.question_id.required' => '题目ID不能为空',
            'answer.*.question_id.integer' => '题目ID必须为整数',
            'answer.*.option.required' => '答案选项不能为空',
            'answer.*.option.string' => '答案选项必须为字符串',

            // 添加报告相关的验证消息
            'assessment_schedule_id.required' => '计划ID不能为空',
            'assessment_schedule_id.integer' => '计划ID必须为整数',
        ];
    }
}
