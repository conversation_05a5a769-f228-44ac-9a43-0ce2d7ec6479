<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON>
 * Date: 2024/01/19
 * Time: 17:52
 */
namespace app\psychassessment\service;

class Survey{
    protected $SurveyLogic;
    public function __construct()
    {
          $this->SurveyLogic = new \app\psychassessment\logic\Survey();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://查询

                return $this->SurveyLogic->get_list();
                break;
            case 'post'://增加

                return $this->SurveyLogic->add();
                break;
            case 'put'://修改

                return $this->SurveyLogic->edit();
                break;
            case 'delete'://删除
                return $this->SurveyLogic->del();
                break;
            default:
                return false;
        }
    }

    /**
     * @return \app\psychassessment\logic\psychassessment_survey
     */
    public function assess_list()
    {
        return $this->SurveyLogic->assess_list();
    }

    public function get_psychassessment_class_member()
    {
        $data = $this->SurveyLogic->get_psychassessment_class_member();
        apiReturn($data);
    }

    public function get_member_complete_status()
    {
        $data = $this->SurveyLogic->get_member_complete_status();
        apiReturn($data);
    }

    public function get_student_psychassess()
    {
        $data = $this->SurveyLogic->get_student_psychassess();
        apiReturn($data);
    }

}