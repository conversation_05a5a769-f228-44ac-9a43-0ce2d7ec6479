<?php

namespace App\Events\DataSync;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 学校创建事件
 */
class SchoolCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $schoolData;

    /**
     * Create a new event instance.
     */
    public function __construct(array $schoolData)
    {
        $this->schoolData = $schoolData;
    }
}
