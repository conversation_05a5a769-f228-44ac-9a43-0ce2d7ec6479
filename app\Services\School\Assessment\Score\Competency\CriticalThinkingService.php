<?php

namespace App\Services\School\Assessment\Score\Competency;

use App\Services\School\Assessment\Score\Competency\AbstractScoreService;

/**
 * 批判性思维能力评估服务类
 * 
 * 该类用于计算学生的批判性思维能力评估结果，包括维度分数计算和总分计算
 */
class CriticalThinkingService extends AbstractScoreService
{
    /**
     * 计算批判性思维能力评估结果
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 评估结果数组，包含维度分数和总分
     */
    public function calculate(array $params): array
    {
        $dimension_scores = $this->calculateScores($params);

        $total_questions = 0;
        $total_score = 0;
        foreach ($dimension_scores as $key => $dimension){
            $total_score += $dimension['score'];
            $dimension_scores[$key]['score'] = round($dimension['score'] * 10 / $dimension['question_count'], 1);//展示每个维度的分数=每个维度总分*10/每个维度的题数
            $total_questions += $dimension['question_count'];
            unset($dimension_scores[$key]['question_count']);
        }
        $average_score = round($total_score * 10 / $total_questions, 1);//展示总分=所有题目总分*10/所有题数

        return ['dimensions'=>$dimension_scores,'total_score'=>$average_score];
    }
    
}