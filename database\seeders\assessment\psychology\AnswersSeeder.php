<?php

namespace Database\Seeders\assessment\psychology;

use Database\Seeders\BaseIncrementalSeeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
ini_set('memory_limit', '1024M');

class AnswersSeeder extends BaseIncrementalSeeder
{
    protected string $assessment_type = 'psychologyAnswer';

    public function __construct($schoolId)
    {
        parent::__construct($schoolId);
    }

    protected function getSurveyIds(): array
    {
        return [471, 472, 473];
    }

    protected function getAssessmentIds(): array
    {
        return [21, 22, 23];
    }

    /**
     * Run the database seeds.
     */
    protected function executeSeeder(): void
    {
        $lastProcessedId = $this->getLastProcessedId();
        
        // 限制测评类型
        $surveyToAssessment = [
            '471'=>21,
            '472'=>22,
            '473'=>23,
        ];
        
        // 查询新老问题id的关系
        $assessmentPsychologyQuestionIds = DB::table('assessment_psychology_questions')
            ->whereNotNull('old_question_id')
            ->select(['id', 'old_question_id'])
            ->pluck('id', 'old_question_id')
            ->toArray();

        // 查询assessment_task_assignment_id和session_id的关系
        $assessmentTaskAssignmentIds = DB::table('assessment_schedules as schedules')
            ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
            ->join('assessment_task_assignments as assignments', 'tasks.id', '=', 'assignments.assessment_task_id')
            ->where('schedules.school_id', $this->school_id)
            ->select(['assignments.id', 'assignments.old_session_id'])
            ->pluck('assignments.id', 'assignments.old_session_id')
            ->toArray();

        $studentAnswerList = DB::connection($this->connect)
            ->table('assessment_schedules as schedules')
            ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
            ->join('survey_user_session as session', function($join) {
                $join->on('session.survey_id', '=', 'tasks.old_survey_id')
                    ->on('session.grade_id', '=', 'tasks.old_grade_id')
                    ->on('session.times', '=', 'tasks.old_times');
            })
            ->join('survey_user_answer as answer', 'session.session_id', '=', 'answer.session')
            ->whereIn('tasks.assessment_id', $this->getAssessmentIds())
            ->where('schedules.school_id', $this->school_id)
            ->where('session.school_id', $this->school_id)
            ->whereIn('answer.survey_id', $this->getSurveyIds())
            ->where('session.is_delete', 0)
            ->where('session.is_abnormal', 0)
            ->where('session.time_error', 0)
            ->where('session.result', '!=', '')
            ->whereNotNull('session.result')
            ->whereNotNull('answer.input')
            ->where('session.id', '>', $lastProcessedId) // 只处理新记录
            ->select([
                'answer.input',
                'session.session_id',
                'session.member_id',
                'session.school_id',
                'session.survey_id',
                'session.id as survey_session_id' // 添加survey_session_id用于更新执行日志
            ])
            ->get();

        $answerData = [];
        $batchSize = 3000;
        $processedCount = 0;
        $maxSurveySessionId = $lastProcessedId;

        foreach ($studentAnswerList as $item) {

            $optionstoletterForSurvey = self::getOptionLettersForSurveyId(); // 调用静态方法获取当前select_num对应的选项字母映射

            $input = json_decode($item->input, true);
            foreach ($input as $key => $value) {
                if(in_array($key,[2391,2382,2387,2388,2389,2374,2372,2368,2364,2360])){// 反向题
                    $answerOption = $optionstoletterForSurvey[6 - $value['select_num']] ?? null;
                }else{
                    $answerOption = $optionstoletterForSurvey[$value['select_num']] ?? null;
                }
                
                // 检查 $key 是否在 $assessmentsubjectQuestionIds 数组中
                if ($answerOption !== null && $answerOption !== 0) {
                    if (array_key_exists($key, $assessmentPsychologyQuestionIds)) {
                        $answerData[] = [
                            'assessment_task_assignment_id' => $assessmentTaskAssignmentIds[$item->session_id] ?? 0,
                            'student_id' => $item->member_id,
                            'assessment_id' => $surveyToAssessment[$item->survey_id],
                            'school_id' => $item->school_id,
                            'assessment_psychology_question_id' => $assessmentPsychologyQuestionIds[$key],
                            'answer' => $answerOption,
                        ];
                        $processedCount++;
                    }
                }

                // 如果达到批次大小，则插入数据库并清空数组
                if (count($answerData) >= $batchSize) {
                    DB::transaction(function () use ($answerData) {
                        DB::table('assessment_psychology_answers')->insert($answerData);
                    });
                    $answerData = []; // 清空数组以释放内存
                }
            }

            // 更新最大的survey_session_id
            if ($item->survey_session_id > $maxSurveySessionId) {
                $maxSurveySessionId = $item->survey_session_id;
            }
        }
        
        // 循环结束时answerData可能会有小于批次大小的数据没有insert
        if (!empty($answerData)) {
            DB::transaction(function () use ($answerData) {
                DB::table('assessment_psychology_answers')->insert($answerData);
            });
        }
        
        // 更新执行日志
        if ($maxSurveySessionId > $lastProcessedId) {
            $this->updateExecutionLog($maxSurveySessionId, $processedCount);
            
            Log::info('psychology answers seeding completed', [
                'school_id' => $this->school_id,
                'psychology_total_records' => $processedCount,
                'last_processed_survey_session_id' => $maxSurveySessionId,
            ]);
        } else {
            Log::info('没有新的psychology answers数据需要处理', [
                'school_id' => $this->school_id,
                'last_processed_id' => $lastProcessedId
            ]);
        }
    }

    /**
     * 获取给定 surveyId 的选项字母映射
     *
     * @return array
     */
    public static function getOptionLettersForSurveyId(): array
    {
        return [
            1 => 'A',
            2 => 'B',
            3 => 'C',
            4 => 'D',
            5 => 'E',
        ];
    }
}
