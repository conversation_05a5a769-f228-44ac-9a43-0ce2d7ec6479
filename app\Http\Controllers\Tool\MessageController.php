<?php

namespace App\Http\Controllers\Tool;

use App\Http\Controllers\Controller;
use App\Services\Tool\MessageService;
use Illuminate\Http\Request;

class MessageController extends Controller
{
    protected $messageService;

    // 通过依赖注入注入 User 模型
    public function __construct(MessageService $messageService)
    {
        $this->messageService = $messageService;
    }
    
    // 消息列表
    public function index(Request $request)
    {
        $list = $this->messageService->getMessageList($request);
        return $this->success($list);
    }

    // 消息详情
    public function getUnreadMessageNum()
    {
        $data = $this->messageService->getUnreadMessageNum();
        return $this->success(['unreadMessageNum' => $data]);
    }

    // 消息详情
    public function show($id)
    {
        $data = $this->messageService->getMessageDetail($id);
        return $this->success($data);
    }

    /**
     * 标记消息为已读
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(Request $request)
    {
        $ids = $request->input('ids');
        if (empty($ids)) {
            return $this->error('消息ID不能为空', 400);
        }

        $result = $this->messageService->markAsRead($ids);
        return $this->success(['result' => $result], '标记已读成功');
    }
    
    /**
     * 标记所有消息为已读
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAllAsRead()
    {
        $result = $this->messageService->markAllAsRead();
        return $this->success(['result' => $result], '标记全部已读成功');
    }
    
    /**
     * 删除消息
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request)
    {
        $ids = $request->input('ids');
        if (empty($ids)) {
            return $this->error('消息ID不能为空', 400);
        }

        $result = $this->messageService->deleteMessage($ids);
        return $this->success(['result' => $result], '删除消息成功');
    }
}
