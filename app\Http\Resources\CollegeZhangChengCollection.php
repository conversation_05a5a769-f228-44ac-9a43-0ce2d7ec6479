<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CollegeZhangChengCollection extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->ID,
            'zhangcheng_title' => $this->ZhangchengTitle,
            'year' => $this->Year,
            'remark' => $this->Remark,
            'type' => $this->Type,
        ];
    }
}
