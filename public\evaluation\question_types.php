<?php
/**
 * 题目类型接口 - 临时独立实现
 * 访问路径: /evaluation/question_types.php
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理 OPTIONS 请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 模拟数据库连接和数据
    $questionTypes = [
        [
            'id' => 1,
            'type_name' => '单选题',
            'description' => '单项选择题，只能选择一个答案',
            'code' => 'single_choice',
            'is_subjective' => 0,
            'sort' => 1,
            'status' => 0,
            'created_at' => '2024-01-01 00:00:00',
            'updated_at' => '2024-01-01 00:00:00'
        ],
        [
            'id' => 2,
            'type_name' => '多选题',
            'description' => '多项选择题，可以选择多个答案',
            'code' => 'multiple_choice',
            'is_subjective' => 0,
            'sort' => 2,
            'status' => 0,
            'created_at' => '2024-01-01 00:00:00',
            'updated_at' => '2024-01-01 00:00:00'
        ],
        [
            'id' => 3,
            'type_name' => '判断题',
            'description' => '判断题，选择对或错',
            'code' => 'true_false',
            'is_subjective' => 0,
            'sort' => 3,
            'status' => 0,
            'created_at' => '2024-01-01 00:00:00',
            'updated_at' => '2024-01-01 00:00:00'
        ],
        [
            'id' => 4,
            'type_name' => '填空题',
            'description' => '填空题，需要填写答案',
            'code' => 'fill_blank',
            'is_subjective' => 1,
            'sort' => 4,
            'status' => 0,
            'created_at' => '2024-01-01 00:00:00',
            'updated_at' => '2024-01-01 00:00:00'
        ],
        [
            'id' => 5,
            'type_name' => '简答题',
            'description' => '简答题，需要简要回答',
            'code' => 'short_answer',
            'is_subjective' => 1,
            'sort' => 5,
            'status' => 0,
            'created_at' => '2024-01-01 00:00:00',
            'updated_at' => '2024-01-01 00:00:00'
        ],
        [
            'id' => 6,
            'type_name' => '论述题',
            'description' => '论述题，需要详细论述',
            'code' => 'essay',
            'is_subjective' => 1,
            'sort' => 6,
            'status' => 0,
            'created_at' => '2024-01-01 00:00:00',
            'updated_at' => '2024-01-01 00:00:00'
        ]
    ];

    // 根据请求方法处理
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            // 获取题目类型列表
            $id = $_GET['id'] ?? null;
            
            if ($id) {
                // 获取单个题目类型
                $questionType = null;
                foreach ($questionTypes as $type) {
                    if ($type['id'] == $id) {
                        $questionType = $type;
                        break;
                    }
                }
                
                if ($questionType) {
                    $response = [
                        'status' => 'success',
                        'code' => 200,
                        'message' => '操作成功',
                        'data' => $questionType
                    ];
                } else {
                    $response = [
                        'status' => 'error',
                        'code' => 404,
                        'message' => '题目类型不存在'
                    ];
                }
            } else {
                // 获取题目类型列表
                $page = $_GET['page'] ?? 1;
                $pagesize = $_GET['pagesize'] ?? 15;
                
                // 简单分页
                $total = count($questionTypes);
                $offset = ($page - 1) * $pagesize;
                $list = array_slice($questionTypes, $offset, $pagesize);
                
                $response = [
                    'status' => 'success',
                    'code' => 200,
                    'message' => '操作成功',
                    'data' => [
                        'list' => $list,
                        'total' => $total,
                        'page' => (int)$page,
                        'pagesize' => (int)$pagesize
                    ]
                ];
            }
            break;
            
        case 'POST':
            // 创建题目类型
            $input = json_decode(file_get_contents('php://input'), true);
            
            $newType = [
                'id' => count($questionTypes) + 1,
                'type_name' => $input['type_name'] ?? '',
                'description' => $input['description'] ?? '',
                'code' => $input['code'] ?? '',
                'is_subjective' => $input['is_subjective'] ?? 0,
                'sort' => $input['sort'] ?? 0,
                'status' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $response = [
                'status' => 'success',
                'code' => 200,
                'message' => '题目类型创建成功',
                'data' => $newType
            ];
            break;
            
        default:
            $response = [
                'status' => 'error',
                'code' => 405,
                'message' => '不支持的请求方法'
            ];
            break;
    }
    
    // 输出响应
    http_response_code($response['code']);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $response = [
        'status' => 'error',
        'code' => 500,
        'message' => '服务器内部错误: ' . $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ];
    
    http_response_code(500);
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
