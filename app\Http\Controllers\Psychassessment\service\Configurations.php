<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON>
 * Date: 2024/01/19
 * Time: 17:52
 */
namespace app\psychassessment\service;

class Configurations{
    protected $AppointmentRecordLogic;
    public function __construct()
    {
        $this->ConfigurationsLogic = new \app\psychassessment\logic\Configurations();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://查询

                return $this->ConfigurationsLogic->get_list();
                break;
            case 'post'://增加

                return $this->ConfigurationsLogic->add();
                break;
            case 'put'://修改

                return $this->ConfigurationsLogic->edit();
                break;
            case 'delete'://删除

                return $this->ConfigurationsLogic->del();
                break;
            default:
                return false;
        }
    }

    public function dates(){
                return $this->ConfigurationsLogic->dates();
    }
}