<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\DataSync\SchoolSyncService;
use App\Services\DataSync\ClassSyncService;
use App\Services\DataSync\TeacherSyncService;
use App\Services\DataSync\StudentSyncService;
use App\Services\DataSync\TeacherClassSyncService;
use App\Services\School\System\ClassService;
use App\Services\DataSync\DataSyncServiceNew;

class DataSyncServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // 注册学校同步服务
        $this->app->singleton(SchoolSyncService::class, function ($app) {
            return new SchoolSyncService();
        });

        // 注册班级同步服务
        $this->app->singleton(ClassSyncService::class, function ($app) {
            return new ClassSyncService($app->make(SchoolSyncService::class));
        });

        // 注册教师同步服务
        $this->app->singleton(TeacherSyncService::class, function ($app) {
            return new TeacherSyncService(
                $app->make(SchoolSyncService::class),
                $app->make(ClassService::class)
            );
        });

        // 注册学生同步服务
        $this->app->singleton(StudentSyncService::class, function ($app) {
            return new StudentSyncService(
                $app->make(SchoolSyncService::class),
                $app->make(ClassSyncService::class)
            );
        });

        // 注册教师带班同步服务
        $this->app->singleton(TeacherClassSyncService::class, function ($app) {
            return new TeacherClassSyncService();
        });

        // 注册统一入口服务
        $this->app->singleton(DataSyncServiceNew::class, function ($app) {
            return new DataSyncServiceNew(
                $app->make(SchoolSyncService::class),
                $app->make(ClassSyncService::class),
                $app->make(TeacherSyncService::class),
                $app->make(StudentSyncService::class),
                $app->make(TeacherClassSyncService::class)
            );
        });

        // 为了向后兼容，可以将新服务绑定到原来的类名
        // $this->app->bind(DataSyncService::class, DataSyncServiceNew::class);
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
