<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessments', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique()->comment('测评名称');
            $table->tinyInteger('survey_type')->comment('老表测评类型');
            $table->tinyInteger('stage')->comment('学段（小初高）小学1，初中2，高中3');
            $table->string('category_name',50)->comment('测评类型中文名称');
            $table->string('category_code',50)->comment('测评类型英文名称');
            $table->string('official_name',50)->comment('测评名字');
            $table->string('icon')->comment('图标');
            $table->string('introduction_pc')->comment('引言pc端');
            $table->string('introduction_mobile')->comment('引言移动端');
            $table->string('prompt')->comment('测评提示语');
            $table->string('note')->comment('注意事项');
            $table->integer('min_duration')->comment('最短时间,单位s');
            $table->integer('max_duration')->comment('最长时间,单位s');
            $table->integer('suggest_duration')->comment('建议时间,单位minute');
            $table->tinyInteger('status')->default(0)->comment('状态1正常2禁用');
            $table->tinyInteger('assessment_sort',4)->comment('测评排序');
            $table->string('description',1000)->comment('备注信息');
            $table->timestamps();
        });
        DB::statement("ALTER TABLE `assessments` comment '测评类型总表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessments');
    }
};
