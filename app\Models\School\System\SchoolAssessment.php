<?php

namespace App\Models\School\System;

use App\Models\BaseModel;
use App\Models\School\Assessment\Assessment;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SchoolAssessment extends BaseModel
{
    use HasFactory;

    //对应多个学校
    public function school()
    {
        return $this->belongsTo(School::class, 'school_id', 'id');
    }

    //对应多个测评
    public function assessment()
    {
        return $this->belongsTo(Assessment::class, 'assessment_id', 'id');
    }

    public static function batchInsertIfNotExists(array $data): int
    {
        if (empty($data)) {
            return 0;
        }

        // 提取 partner_id, school_id组合用于查重
        $assessmentIds = array_column($data, 'assessment_id');
        $schoolIds = array_column($data, 'school_id');

        // 查询已存在的记录
        $existingRecords = self::whereIn('assessment_id', $assessmentIds)
            ->whereIn('school_id', $schoolIds)
            ->get(['assessment_id', 'school_id'])
            ->keyBy(fn($item) => "{$item->assessment_id}-{$item->school_id}");

        // 过滤出不存在的记录
        $filteredData = array_filter($data, function ($item) use ($existingRecords) {
            $key = "{$item['assessment_id']}-{$item['school_id']}";
            return !isset($existingRecords[$key]);
        });

        // 批量插入新数据
        if (!empty($filteredData)) {
            self::insert($filteredData);
        }

        return count($filteredData);
    }
}
