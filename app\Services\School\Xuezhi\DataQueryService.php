<?php

namespace App\Services\School\Xuezhi;

use App\Constants\BaseConstants;
use App\Constants\QueryConstants;
use App\Enums\LiberalScienceEnum;
use App\Enums\ProvinceEnum;
use App\Enums\QueryTypeEnum;
use App\Helpers\ProvinceHelper;
use App\Http\Requests\School\Xuezhi\DataQueryRequest;
use App\Services\BaseService;
use Illuminate\Support\Facades\DB;

class DataQueryService extends BaseService
{
    // 指定连接
    protected $connection = 'sqlsrv_gk';

    // 年份下拉
    public function yearList($provinceId, $query_type)
    {
        // 获取省份拼音
        $provinceSpell = ProvinceEnum::fromInt($provinceId)->getPinyin();
        $provinceName = ProvinceEnum::fromInt($provinceId)->getDisplayName();

        // 判断是否包含港澳台
        if (BaseConstants::hasNoScoreDataTable($provinceName)) {
            return $this->getDefaultYears();
        } else {
            $queryTable = QueryConstants::getQueryTable($query_type, $provinceSpell) . " as cs";

            return $this->getYearsFromTable($queryTable);
        }
    }

    // 默认年份下拉
    private function getDefaultYears()
    {
        return $this->getYearsFromTable("SiteYear");
    }

    // 从指定表获取年份列表
    private function getYearsFromTable($tableName)
    {
        return DB::connection($this->connection)->table($tableName)
            ->select('Year')
            ->groupBy('Year')
            ->orderBy('Year', 'desc')
            ->limit(QueryConstants::DEFAULT_YEARS_LIMIT)
            ->pluck('Year')
            ->toArray();
    }

    function getLiberalSciencesNew($provinceId, $queryType, $year) {
        $mapList = [];

        // 定义文理科映射
        $map0 = LiberalScienceEnum::getLiberalArtsMap(false);
        $map0New = LiberalScienceEnum::getLiberalArtsMap(true);
        $map1 = LiberalScienceEnum::getScienceMap(false);
        $map1New = LiberalScienceEnum::getScienceMap(true);
        $map2 = LiberalScienceEnum::getCombinedMap();

        $scoreFormType = ProvinceHelper::getScoreFormType2($provinceId);

        // 院校录取数据
        if ($queryType == QueryTypeEnum::COLLEGE_SCORE->value) {
            if ($provinceId == ProvinceEnum::SHANGHAI->value || $provinceId == ProvinceEnum::TIANJIN->value) {
                $mapList[] = $map2;
            } else {
                $mapList = $this->getLiberalSciencesByScoreFormType($scoreFormType, $year, $map0, $map0New, $map1, $map1New);
            }
        }

        // 专业录取/计划数据
        if ($queryType == QueryTypeEnum::MAJOR_SCORE->value || $queryType == QueryTypeEnum::MAJOR_PLAN->value) {
            if ($provinceId == ProvinceEnum::SHANGHAI->value ||
                $provinceId == ProvinceEnum::ZHEJIANG->value ||
                $provinceId == ProvinceEnum::TIANJIN->value) {
                $mapList[] = $map2;
            } elseif ($provinceId == ProvinceEnum::SHANDONG->value) {
                if ($year !== null && $year < 2020) {
                    $mapList[] = $map1;
                    $mapList[] = $map0;
                } else {
                    $mapList[] = $map2;
                }
            } else {
                $mapList = $this->getLiberalSciencesByScoreFormType($scoreFormType, $year, $map0, $map0New, $map1, $map1New);
            }
        }

        return $mapList;
    }

    // 根据分数形式类型获取文理科列表
    private function getLiberalSciencesByScoreFormType($scoreFormType, $year, $map0, $map0New, $map1, $map1New)
    {
        $mapList = [];

        if ($scoreFormType == "312-2021") {
            $this->addLiberalSciencesByYear($mapList, $year, 2021, $map0, $map0New, $map1, $map1New);
        } elseif ($scoreFormType == "312-2024") {
            $this->addLiberalSciencesByYear($mapList, $year, 2024, $map0, $map0New, $map1, $map1New);
        } elseif ($scoreFormType == "312-2025") {
            $this->addLiberalSciencesByYear($mapList, $year, 2025, $map0, $map0New, $map1, $map1New);
        } else {
            $mapList[] = $map1;
            $mapList[] = $map0;
        }

        return $mapList;
    }

    // 根据年份添加文理科
    private function addLiberalSciencesByYear(&$mapList, $year, $thresholdYear, $map0, $map0New, $map1, $map1New)
    {
        if ($year !== null && $year >= $thresholdYear) {
            $mapList[] = $map1New;
            $mapList[] = $map0New;
        } elseif ($year !== null) {
            $mapList[] = $map1;
            $mapList[] = $map0;
        } else {
            if ($thresholdYear == 2025) {
                $mapList[] = $map1;
                $mapList[] = $map0;
            } else {
                $mapList[] = $map1New;
                $mapList[] = $map0New;
            }
        }
    }

    public function collegeScoreListBuilder(DataQueryRequest $request)
    {
        $collegeId = $request['college_id'];
        $provinceId = $request['province_id'];
        $liberalScience = $request['liberalScience'];
        $year = $request['year'];

        // 获取省份拼音
        $provinceSpell = ProvinceEnum::fromInt($provinceId)->getPinyin();
        $queryTable = "CollegeScore_$provinceSpell";

        // 获取最新的4年
        $latestYears = DB::connection($this->connection)->table($queryTable)
            ->select('Year')
            ->groupBy('Year')
            ->orderBy('Year', 'desc')
            ->limit(QueryConstants::LATEST_YEARS_LIMIT)
            ->pluck('Year')
            ->toArray();

        $query = DB::connection($this->connection)->table("$queryTable as cs")
            ->leftJoin('College as c', 'c.ID', '=', 'cs.CollegeID')
            ->select('cs.ID', 'cs.CollegeID', 'cs.College', 'cs.CollegeCode', 'cs.Year', 'cs.LiberalScience', 'cs.Phase', 'cs.PhaseName',
                'cs.Score', 'cs.Rank', 'cs.HighestScore', 'cs.AvgScore', 'cs.LowestScore', 'cs.RealCount', 'cs.CollegeRemark', 'c.ProvinceId')
            ->where('cs.CollegeID', $collegeId)
            ->where('cs.LiberalScience', $liberalScience)
            ->orderBy('cs.Year', 'desc')
            ->orderBy('cs.Phase');

        if ($year !== null) {
            $query->where('cs.Year', $year);
        } else {
            $query->whereIn('cs.Year', $latestYears);
        }

        $this->addCourseSelectFields($query, 'cs', ProvinceHelper::collegeQueryHasCourseProvinceDefault($provinceId));

        return $query;
    }

    /**
     * majorScoreListBuilder
     */
    public function majorScoreListBuilder(DataQueryRequest $request)
    {
        $collegeId = $request['college_id'];
        $provinceId = $request['province_id'];
        $year = $request['year'];
        $liberalScience = $request['liberalScience'];
        $phase = $request['phase'];
        $sort = $request->input('sort', QueryConstants::DEFAULT_SORT);

        // 获取省份拼音
        $provinceSpell = ProvinceEnum::fromInt($provinceId)->getPinyin();
        // DB连接查询
        $query = DB::connection($this->connection)->table("MajorScore_$provinceSpell as m")
            ->leftJoin('College as c', 'm.CollegeID', '=', 'c.ID')
            ->select(
                'm.ID',
                'm.CollegeID',
                'm.College',
                'm.CollegeCode',
                'm.Major',
                'm.MajorCode',
                'm.HighestScore',
                'm.AvgScore',
                'm.LowestScore',
                'm.LowestScoreRank',
                'm.PeopleCount',
                'm.MajorRemark',
                'm.Year',
                'm.LiberalScience',
                'm.Phase',
                'm.PhaseName',
                'c.ProvinceId'
            )
            ->where('m.CollegeID', $collegeId)
            ->where('m.Year', $year)
            ->where('m.LiberalScience', $liberalScience);

        $this->addCourseSelectFields($query, 'm', ProvinceHelper::majorQueryHasCourseProvince($provinceId, $year));

        if ($phase !== null) {
            $query->where('m.Phase', $phase);
        }

        $this->applySortingToMajorScoreQuery($query, $sort);

        return $query;
    }

    // 添加课程选择字段
    private function addCourseSelectFields($query, $tableAlias, $hasCourse)
    {
        if ($hasCourse === 'one') {
            $query->addSelect("$tableAlias.Course");
        } elseif ($hasCourse === 'two') {
            $query->addSelect("$tableAlias.Course", "$tableAlias.CourseSecond");
        }
    }

    // 应用排序到专业分数查询
    private function applySortingToMajorScoreQuery($query, $sort)
    {
        if ($sort === null || $sort == 0) {
            $query->orderByDesc('m.LowestScore')
                ->orderByDesc('m.LowestScoreRank')
                ->orderBy('m.Phase');
        } elseif ($sort == 1) {
            $query->orderByDesc('m.LowestScore')
                ->orderBy('m.CollegeCode')
                ->orderBy('m.Phase')
                ->orderBy('m.MajorCode');
        } elseif ($sort == 2) {
            $query->orderBy('m.LowestScore')
                ->orderBy('m.CollegeCode')
                ->orderBy('m.Phase')
                ->orderBy('m.MajorCode');
        }
    }

    /**
     * majorPlanListBuilder
     */
    public function majorPlanListBuilder(DataQueryRequest $request)
    {
        $collegeId = $request['college_id'];
        $provinceId = $request['province_id'];
        $year = $request['year'];
        $liberalScience = $request['liberalScience'];

        // 获取省份拼音
        $provinceSpell = ProvinceEnum::fromInt($provinceId)->getPinyin();
        // 基础查询
        $query = DB::connection($this->connection)->table("MajorPlan_$provinceSpell as mp")
            ->select(
                'mp.CollegeID',
                'mp.College',
                'mp.CollegeCode',
                'mp.MajorCode',
                'mp.Major',
                'mp.PlanType',
                'mp.Phase',
                'mp.PhaseName',
                'mp.LiberalScience',
                'mp.Xuezhi',
                'mp.Xuefei',
                'mp.PlanCount',
                'mp.Year',
                'mp.MajorRemark'
            )
            ->where('mp.collegeId', $collegeId)
            ->where('mp.year', $year)
            ->where('mp.LiberalScience', $liberalScience);

        $this->addCourseSelectFields($query, 'mp', ProvinceHelper::majorQueryHasCourseProvince($provinceId, $year));

        // 排序逻辑
        $query->orderBy('mp.Phase')
            ->orderBy('mp.CollegeCode')
            ->orderBy('mp.MajorCode')
            ->orderByRaw("charindex('非定向', mp.PlanType) DESC");
        return $query;
    }
}
