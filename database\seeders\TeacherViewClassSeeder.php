<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class TeacherViewClassSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';
    protected int $batchSize = 500; // 增加批量插入大小
    protected array $classCache = []; // 班级缓存
    protected array $newClassCache = []; // 新班级ID缓存

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $startTime = microtime(true);
        Log::info("开始执行 TeacherViewClassSeeder，学校ID: {$this->school_id}");

        // 设置内存和时间限制
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', 3600);

        try {
            DB::beginTransaction();

            // 获取教师ID映射关系
            $teacherIdMap = $this->getTeacherIdMap();
            if (empty($teacherIdMap)) {
                Log::warning("未找到学校ID {$this->school_id} 的教师数据");
                DB::rollBack();
                return;
            }

            // 获取教师查看班级数据
            $viewClassData = $this->getTeacherViewClassData($teacherIdMap);
            if (empty($viewClassData)) {
                Log::warning("未找到教师查看班级数据");
                DB::rollBack();
                return;
            }

            // 处理并批量插入数据
            $this->processAndInsertData($viewClassData);

            DB::commit();

            // 清理缓存释放内存
            $this->clearCache();

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            Log::info("TeacherViewClassSeeder 执行完成，耗时: {$executionTime}秒");

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("TeacherViewClassSeeder 执行失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取教师ID映射关系
     */
    protected function getTeacherIdMap(): array
    {
        return DB::table('teachers')
            ->where('school_id', $this->school_id)
            ->pluck('teacher_id', 'id')
            ->toArray();
    }

    /**
     * 获取教师查看班级数据
     */
    protected function getTeacherViewClassData(array $teacherIdMap): array
    {
        return DB::connection($this->connect)
            ->table('teacher')
            ->whereIn('id', array_values($teacherIdMap))
            ->whereNotNull('view_class_ids')
            ->where('view_class_ids', '!=', '')
            ->select('member_id', 'view_class_ids')
            ->get()
            ->toArray();
    }
    /**
     * 处理并批量插入数据（优化版本）
     */
    protected function processAndInsertData(array $viewClassData): void
    {
        $insertData = [];
        $processedCount = 0;
        $now = Carbon::now();

        // 1. 收集所有需要查询的班级ID
        $allClassIds = [];
        foreach ($viewClassData as $item) {
            $viewClassIds = array_filter(explode(',', $item->view_class_ids));
            $allClassIds = array_merge($allClassIds, $viewClassIds);
        }
        $allClassIds = array_unique($allClassIds);

        Log::info("开始预加载 " . count($allClassIds) . " 个班级数据");

        // 2. 批量预加载所有班级数据
        $this->preloadClassData($allClassIds);

        // 3. 批量预加载新班级映射
        $this->preloadNewClassMapping();

        Log::info("预加载完成，开始处理教师数据");

        // 4. 处理每个教师的数据
        foreach ($viewClassData as $item) {
            $teacherId = $item->member_id;
            $viewClassIds = array_filter(explode(',', $item->view_class_ids));

            if (empty($viewClassIds)) {
                continue;
            }

            // 使用缓存快速转换班级数据
            foreach ($viewClassIds as $classId) {
                $classInfo = $this->getCachedClassInfo($classId);
                if ($classInfo && !empty($classInfo['class_id'])) {
                    $insertData[] = [
                        'teacher_id' => $teacherId,
                        'class_id' => $classInfo['class_id'],
                        'school_year' => $classInfo['school_year'],
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];

                    // 批量插入
                    if (count($insertData) >= $this->batchSize) {
                        $this->batchInsert($insertData);
                        $processedCount += count($insertData);
                        $insertData = [];
                        Log::info("已处理 {$processedCount} 条教师查看班级记录");
                    }
                }
            }
        }

        // 插入剩余数据
        if (!empty($insertData)) {
            $this->batchInsert($insertData);
            $processedCount += count($insertData);
        }

        Log::info("总共处理了 {$processedCount} 条教师查看班级记录");
    }

    /**
     * 批量插入数据（优化版本）
     */
    protected function batchInsert(array $data): void
    {
        if (empty($data)) {
            return;
        }

        try {
            $startTime = microtime(true);

            // 使用 INSERT IGNORE 避免重复数据错误
            $sql = "INSERT IGNORE INTO teacher_view_classes (teacher_id, class_id, school_year, created_at, updated_at) VALUES ";
            $values = [];
            $bindings = [];

            foreach ($data as $row) {
                $values[] = "(?, ?, ?, ?, ?)";
                $bindings[] = $row['teacher_id'];
                $bindings[] = $row['class_id'];
                $bindings[] = $row['school_year'];
                $bindings[] = $row['created_at'];
                $bindings[] = $row['updated_at'];
            }

            $sql .= implode(', ', $values);
            DB::statement($sql, $bindings);

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);

            Log::debug("批量插入 " . count($data) . " 条记录，耗时: {$executionTime}ms");

        } catch (\Exception $e) {
            Log::error("批量插入教师查看班级数据失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 预加载班级数据到缓存
     */
    protected function preloadClassData(array $classIds): void
    {
        if (empty($classIds)) {
            return;
        }

        // 分批查询，避免 IN 子句过长
        $chunks = array_chunk($classIds, 1000);

        foreach ($chunks as $chunk) {
            $classList = DB::connection($this->connect)
                ->table('class')
                ->join('grade', 'grade.id', '=', 'class.grade_id')
                ->whereIn('class.id', $chunk)
                ->where('class.school_id', $this->school_id)
                ->where('class.step', 0)
                ->where('grade.step', 0)
                ->selectRaw('ysy_class.id,ysy_class.name,ysy_class.school_district,ysy_grade.grade_sort as grade_id,ysy_grade.name as grade_name')
                ->get();

            foreach ($classList as $item) {
                $this->classCache[$item->id] = $item;
            }
        }

        Log::info("预加载了 " . count($this->classCache) . " 个班级数据到缓存");
    }

    /**
     * 预加载新班级映射关系
     */
    protected function preloadNewClassMapping(): void
    {
        // 获取所有新班级数据，建立映射关系
        $newClasses = DB::table('classes')
            ->where('school_id', $this->school_id)
            ->select('id', 'class_name', 'school_campus_id', 'grade_id')
            ->get();

        foreach ($newClasses as $class) {
            $key = $class->class_name . '_' . $class->school_campus_id . '_' . $class->grade_id;
            $this->newClassCache[$key] = $class->id;
        }

        Log::info("预加载了 " . count($this->newClassCache) . " 个新班级映射关系");
    }

    /**
     * 从缓存获取班级信息
     */
    protected function getCachedClassInfo($classId): ?array
    {
        if (!isset($this->classCache[$classId])) {
            return null;
        }

        $classInfo = $this->classCache[$classId];

        // 生成新班级名称
        $newClassName = $this->matchClassName($classInfo->name);

        // 从缓存中查找新班级ID
        $key = $newClassName . '_' . $classInfo->school_district . '_' . $classInfo->grade_id;
        $newClassId = $this->newClassCache[$key] ?? null;

        if (!$newClassId) {
            return null;
        }

        // 计算学年
        $school_year = $this->matchSchoolYear($classInfo->grade_id, $classInfo->grade_name);

        return [
            'id' => $classInfo->id,
            'class_id' => $newClassId,
            'school_year' => $school_year,
        ];
    }

    /**
     * 清理缓存释放内存
     */
    protected function clearCache(): void
    {
        $this->classCache = [];
        $this->newClassCache = [];

        // 强制垃圾回收
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        Log::info("缓存已清理，内存已释放");
    }



    /**
     * 匹配班级名称（优化版本）
     */
    protected function matchClassName(string $original_class_name): string
    {
        // 优先匹配括号里的数字
        if (preg_match('/\((\d+)\)/', $original_class_name, $matches)) {
            return "{$matches[1]}班";
        }

        // 匹配任意数字
        if (preg_match('/(\d+)/', $original_class_name, $matches)) {
            return "{$matches[1]}班";
        }

        // 如果都没匹配到，返回原名称
        return $original_class_name;
    }

    /**
     * 根据年级排序和年级名称推算学年（优化版本）
     * 1-5为小学 6-9为初中 10-12为高中
     */
    protected function matchSchoolYear(int $grade_sort, string $grade_year): int
    {
        // 将年级名称转换为数字（如果是数字字符串）
        $year = is_numeric($grade_year) ? intval($grade_year) : $this->extractYearFromGradeName($grade_year);

        // 根据年级阶段计算学年
        if ($grade_sort <= 5) {
            // 小学阶段：1-5年级
            return $year + $grade_sort - 1;
        } elseif ($grade_sort <= 9) {
            // 初中阶段：6-9年级
            return $year + $grade_sort - 6;
        } else {
            // 高中阶段：10-12年级
            return $year + $grade_sort - 10;
        }
    }

    /**
     * 从年级名称中提取年份
     */
    protected function extractYearFromGradeName(string $grade_name): int
    {
        // 尝试从年级名称中提取4位数年份
        if (preg_match('/(\d{4})/', $grade_name, $matches)) {
            return intval($matches[1]);
        }

        // 如果无法提取，返回当前年份作为默认值
        Log::warning("无法从年级名称中提取年份: {$grade_name}，使用当前年份");
        return intval(date('Y'));
    }
}

