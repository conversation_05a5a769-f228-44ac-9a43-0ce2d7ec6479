<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('school_assessments', function (Blueprint $table) {
            $table->id();
            $table->bigIncrements('school_id')->nullable(false)->comment('学校ID');
            $table->bigIncrements('assessment_id')->nullable(false)->comment('测评ID');
            $table->timestamps();
            $table->string('creator',20)->nullable()->comment('创建人');
            $table->string('updater',20)->nullable()->comment('最后更新人');
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `school_assessments` comment '学校测评关系表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('school_assessments');
    }
};
