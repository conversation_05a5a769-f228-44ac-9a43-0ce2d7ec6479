<?php

namespace Database\Seeders\assessment\competency;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class QuestionsSeeder extends Seeder
{
    protected string $connect = 'mysql_prod';

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 使用数据库事务
        DB::transaction(function () {
            // 创造性思维倾向
            $this->processSurveyQuestions('426', 14);
            // 批判性思维能力
            $this->processSurveyQuestions('427', 15);
            // 沟通与合作能力
            $this->processSurveyQuestions('428', 16);
            // 问题解决能力
            $this->processSurveyQuestions('429', 17);
            // 学习力
            $this->processSurveyQuestions('430', 18);
            // 问题解决能力（初中版）
            $this->processSurveyQuestions('431', 19);
            // 学习力（初中版)
            $this->processSurveyQuestions('432', 20);
        });
    }

    public function processSurveyQuestions($surveyId,$assessmentId)
    {
        $surveyQuestionData = DB::connection($this->connect)
            ->table('survey_question as question')
            ->leftJoin('survey_dimension as dim','question.dim_id','=','dim.id')
            ->select('question.id','question.name','question.show_id','question.is_forward','question.question_stem','dim.order','dim.name as dimension_name')
            ->where('question.survey_id', $surveyId)
            ->orderBy('show_id', 'asc')
            ->get()->toArray();

        // 遍历数据，将数据插入到测评表中
        $assessmentQuestionData = [];
        foreach ($surveyQuestionData as $item) {
            $parentId = 0;//每题循环之前将parent_id置为0
            // 获取每个问题的选项
            $answersData = DB::connection($this->connect)
                ->table('survey_question_answer')
                ->where('question_id', $item->id)
                ->orderBy('id') // 使用适当的列来排序选项
                ->get()
                ->toArray();

            //录入沟通与合作的题冒1990，1991，1992，1993，1994，1995，1996，1997，1998，1999，2000，2001，2002共十三题用一个题冒
            if($item->id == 1990) $stemId1990 = $this->inputStemId($item->question_stem,$assessmentId);
            if(in_array($item->id,[1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002])) $parentId = $stemId1990;

            //录入问题解决能力(高中)的题冒，2024,2025两题共用一个题冒，其他题parent_id = 0
            if($item->id == 2024) $stemId2024 = $this->inputStemId($item->question_stem,$assessmentId);
            if(in_array($item->id,[2024,2025])) $parentId = $stemId2024;

            //录入问题解决能力(初中)的题冒，2098,2099,2100三题共用一个题冒，其他题parent_id = 0
            if($item->id == 2098) $stemId2098 = $this->inputStemId($item->question_stem,$assessmentId);
            if(in_array($item->id,[2098,2099,2100])) $parentId = $stemId2098;

            //录入学习力(初中)的题冒，2123,2124两题共用一个题冒
            if($item->id == 2123) $stemId2123 = $this->inputStemId($item->question_stem,$assessmentId);
            if(in_array($item->id,[2123,2124])) $parentId = $stemId2123;

            //录入学习力(初中)的题冒，2125,2126,2127,2128,2129五题共用一个题冒
            if($item->id == 2125) $stemId2125 = $this->inputStemId($item->question_stem,$assessmentId);
            if(in_array($item->id,[2125,2126,2127,2128,2129])) $parentId = $stemId2125;

            //录入学习力(初中)的题冒，2131,2132,2133三题共用一个题冒
            if($item->id == 2131) $stemId2131 = $this->inputStemId($item->question_stem,$assessmentId);
            if(in_array($item->id,[2131,2132,2133])) $parentId = $stemId2131;

            $optionsData = [];
            switch ($surveyId){
                case 426:// 创造性思维倾向
                    $correct = null;
                    foreach ($answersData as $k => $v){
                        $optionsData[$v->option] = [
                            "name" => $v->answer,
                            "score" => $item->is_forward == 1 ? $v->sort : 6 - $v->sort,
                        ];
                    }
                    break;
                case 427://批判性思维能力，只有一题多选题，也就是密码题
                    foreach ($answersData as $k => $v){
                        if($item->id == 1985){//密码题特殊处理
                            $correct = 'AEC';
                        }elseif($v->is_correct_answer == 1){
                            $correct = $v->option;
                        }
                        $optionsData[$v->option] = $v->answer;
                    }
                    break;
                case 428:// 沟通与合作能力
                    foreach ($answersData as $k => $v){
                        if($v->is_correct_answer == 1) $correct = $v->option;
                        $optionsData[$v->option] = $v->answer;
                    }
                    break;
                case 429://问题解决能力，只有一题多选题
                    if($item->id <= 2023){//前21题是主观题
                        $correct = null;
                        foreach ($answersData as $k => $v){
                            $optionsData[$v->option] = [
                                "name" => $v->answer,
                                "score" => $item->is_forward == 1 ? $v->sort : 6 - $v->sort,
                            ];
                        }
                    }else{
                        $correct = '';
                        foreach ($answersData as $k => $v){
                            if($v->is_correct_answer == 1) $correct .= $v->option;//多选题选项拼起来
                            $optionsData[$v->option] = $v->answer;
                        }
                    }
                    break;
                case 430://学习力，只有一题多选题
                    if($item->id <= 2033){//前7题是主观题
                        $correct = null;
                        foreach ($answersData as $k => $v){
                            $optionsData[$v->option] = [
                                "name" => $v->answer,
                                "score" => $item->is_forward == 1 ? $v->sort : 6 - $v->sort,
                            ];
                        }
                    }elseif($item->id >= 2034 && $item->id <= 2052){
                        $correct = '';
                        foreach ($answersData as $k => $v){
                            if($v->is_correct_answer == 1) $correct .= $v->option;//多选题选项拼起来
                            $optionsData[$v->option] = $v->answer;
                        }
                    }elseif($item->id >= 2053){//后23题也是主观题，注意题目顺序
                        $correct = null;
                        foreach ($answersData as $k => $v){
                            $optionsData[$v->option] = [
                                "name" => $v->answer,
                                "score" => $item->is_forward == 1 ? $v->sort : 6 - $v->sort,
                            ];
                        }
                    }
                    break;
                case 431://问题解决能力（初中版），只有一题多选题
                    if($item->id <= 2096){//前21题是主观题
                        $correct = null;
                        foreach ($answersData as $k => $v){
                            $optionsData[$v->option] = [
                                "name" => $v->answer,
                                "score" => $item->is_forward == 1 ? $v->sort : 6 - $v->sort,
                            ];
                        }
                    }else{
                        $correct = '';
                        foreach ($answersData as $k => $v){
                            if($v->is_correct_answer == 1) $correct .= $v->option;//多选题选项拼起来
                            $optionsData[$v->option] = $v->answer;
                        }
                    }
                    break;
                case 432://学习力（初中版），只有一题多选题
                    if($item->id <= 2107){//前7题是主观题
                        $correct = null;
                        foreach ($answersData as $k => $v){
                            $optionsData[$v->option] = [
                                "name" => $v->answer,
                                "score" => $item->is_forward == 1 ? $v->sort : 6 - $v->sort,
                            ];
                        }
                    }elseif($item->id >= 2108 && $item->id <= 2133){
                        $correct = '';
                        foreach ($answersData as $k => $v){
                            if($v->is_correct_answer == 1) $correct .= $v->option;//多选题选项拼起来
                            $optionsData[$v->option] = $v->answer;
                        }
                    }elseif($item->id >= 2134){//后23题也是主观题，注意题目顺序
                        $correct = null;
                        foreach ($answersData as $k => $v){
                            $optionsData[$v->option] = [
                                "name" => $v->answer,
                                "score" => $item->is_forward == 1 ? $v->sort : 6 - $v->sort,
                            ];
                        }
                    }
                    break;
            }

            $Question_item_Data = [
                'content' => $item->name,
                'old_question_id' => $item->id,
                'assessment_id' => $assessmentId,
                'correct' => $correct,
                'options' => json_encode($optionsData),
                'number' => $item->show_id,
                'parent_id' => $parentId,
                'dimension_code' => $item->order,
                'dimension_name' => $item->dimension_name,
            ];
            $assessmentQuestionData[] = $Question_item_Data;
        }
        DB::table('assessment_competency_questions')->insert($assessmentQuestionData);

        return '测评'.$surveyId.'问题导入完毕';
    }

    public function inputStemId($stem,$assessmentId)
    {
        $stemData = [
            'content' => $stem,
            'assessment_id' => $assessmentId,
        ];
        $stemId = DB::table('assessment_competency_questions')->insertGetId($stemData);
        return $stemId;
    }

}
