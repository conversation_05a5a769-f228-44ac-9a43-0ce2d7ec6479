<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use App\Http\Requests\Evaluation\AnswerRequest;
use App\Services\Evaluation\AnswerService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * 答题管理控制器 - 基于原 ThinkPHP Answer 控制器重新实现
 */
class EvaluationAnswerController extends Controller
{
    protected $answerService;

    public function __construct(AnswerService $answerService)
    {
        $this->answerService = $answerService;
    }

    /**
     * 获取阅卷管理列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->getAnswerList($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取阅卷管理列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 提交答案
     * 
     * @param AnswerRequest $request
     * @return JsonResponse
     */
    public function answer(AnswerRequest $request): JsonResponse
    {
        try {
            $data = $this->answerService->submitAnswer($request->validated());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('答题失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取答题记录
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getAnswerRecord(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->getAnswerRecord($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取答题记录失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取批量答题记录
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getBatchAnswerRecord(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->getBatchAnswerRecord($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取批量答题记录失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取题目分数
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getQuestionScore(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->getQuestionScore($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取题目分数失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取题目分数和素养分数
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getQuestionScoreWithCompetence(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->getQuestionScoreWithCompetence($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取题目分数和素养分数失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取学生考试信息
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudentExam(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->getStudentExam($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取学生考试信息失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取学生Excel数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudentExcel(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->getStudentExcel($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取学生Excel数据失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取十年级学生Excel数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudentExcelTenth(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->getStudentExcelTenth($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取十年级学生Excel数据失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取十年级学生等级Excel数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudentExcelLevelTenth(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->getStudentExcelLevelTenth($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取十年级学生等级Excel数据失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 插入单选题答案
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function insertAnswerSingle(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->insertAnswerSingle($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('插入单选题答案失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 插入多选题答案
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function insertAnswerMult(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->insertAnswerMult($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('插入多选题答案失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 插入主观题答案
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function insertAnswerSub(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->insertAnswerSub($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('插入主观题答案失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 插入日志
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function insertLog(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->insertLog($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('插入日志失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新日志素养分数
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateLogCompetence(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->updateLogCompetence($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('更新日志素养分数失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新答案素养分数
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateAnswerCompetence(Request $request): JsonResponse
    {
        try {
            $data = $this->answerService->updateAnswerCompetence($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('更新答案素养分数失败: ' . $e->getMessage());
        }
    }
}
