<?php
namespace app\psychassessment\service;

class Loadpdf{
    protected $ReportLogic;
    protected $LoadpdfLogic;

    public function __construct()
    {
        $this->ReportLogic = new \app\psychassessment\logic\Report();
        $this->LoadpdfLogic = new \app\psychassessment\logic\Loadpdf();
    }

    public function individual()
    {
        $member_id   = input('member_id');
        $plan_id     = input('plan_id');
        $survey_type = input('survey_type');
        $logo        = $this->ReportLogic->get_logo($member_id);
        $data = $this->LoadpdfLogic->psychassessment_create($member_id,$plan_id,$survey_type,$logo['logo_base64']);
        return $data;
    }
}