<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use App\Services\Evaluation\ReportService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * 报告管理控制器
 */
class EvaluationReportController extends Controller
{
    protected $reportService;

    public function __construct(ReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    /**
     * 报告查询
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function report(Request $request): JsonResponse
    {
        try {
            $data = $this->reportService->getReportList($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取报告列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取学生报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudentReport(Request $request): JsonResponse
    {
        try {
            $data = $this->reportService->getStudentReport($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取学生报告失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取班级报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getClassReport(Request $request): JsonResponse
    {
        try {
            $data = $this->reportService->getClassReport($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取班级报告失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取学校报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getSchoolReport(Request $request): JsonResponse
    {
        try {
            $data = $this->reportService->getSchoolReport($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取学校报告失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function generateReport(Request $request): JsonResponse
    {
        try {
            $data = $this->reportService->generateReport($request->all());
            return $this->success($data, '报告生成成功');
        } catch (\Exception $e) {
            return $this->error('报告生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 下载报告
     * 
     * @param int $id
     * @return mixed
     */
    public function downloadReport(int $id)
    {
        try {
            return $this->reportService->downloadReport($id);
        } catch (\Exception $e) {
            return $this->error('报告下载失败: ' . $e->getMessage());
        }
    }

    /**
     * 个人报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function personalReport(Request $request): JsonResponse
    {
        try {
            $memberId = $request->input('member_id');
            $distributionId = $request->input('distribution_id');
            
            $data = $this->reportService->personalReport($memberId, $distributionId);
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取个人报告失败: ' . $e->getMessage());
        }
    }

    /**
     * 结算报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function settlement(Request $request): JsonResponse
    {
        try {
            $data = $this->reportService->settlement($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取结算报告失败: ' . $e->getMessage());
        }
    }

    /**
     * 全部结算
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function allSettlement(Request $request): JsonResponse
    {
        try {
            $data = $this->reportService->allSettlement($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取全部结算报告失败: ' . $e->getMessage());
        }
    }

    /**
     * 报告统计概览
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function overview(Request $request): JsonResponse
    {
        try {
            $schoolId = $request->input('school_id');
            $distributionId = $request->input('distribution_id');
            
            $data = $this->reportService->settlement([
                'school_id' => $schoolId,
                'distribution_id' => $distributionId
            ]);
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取报告概览失败: ' . $e->getMessage());
        }
    }

    /**
     * 报告趋势分析
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function trends(Request $request): JsonResponse
    {
        try {
            $params = $request->all();
            $data = $this->reportService->allSettlement($params);
            
            return $this->success([
                'trends' => $data['trends'] ?? [],
                'overview' => $data['overview'] ?? []
            ]);
        } catch (\Exception $e) {
            return $this->error('获取趋势分析失败: ' . $e->getMessage());
        }
    }

    /**
     * 报告对比分析
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function comparison(Request $request): JsonResponse
    {
        try {
            $distributionIds = $request->input('distribution_ids', []);
            $schoolIds = $request->input('school_ids', []);
            
            if (empty($distributionIds) && empty($schoolIds)) {
                return $this->error('请选择要对比的分发或学校');
            }
            
            $comparisonData = [];
            
            // 按分发对比
            if (!empty($distributionIds)) {
                foreach ($distributionIds as $distributionId) {
                    $comparisonData['distributions'][$distributionId] = $this->reportService->settlement([
                        'distribution_id' => $distributionId
                    ]);
                }
            }
            
            // 按学校对比
            if (!empty($schoolIds)) {
                foreach ($schoolIds as $schoolId) {
                    $comparisonData['schools'][$schoolId] = $this->reportService->settlement([
                        'school_id' => $schoolId
                    ]);
                }
            }
            
            return $this->success($comparisonData);
        } catch (\Exception $e) {
            return $this->error('获取对比分析失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出报告
     *
     * @param Request $request
     * @return mixed
     */
    public function export(Request $request)
    {
        try {
            $reportType = $request->input('report_type', 'student');
            $format = $request->input('format', 'excel'); // excel, pdf, csv
            $params = $request->all();

            switch ($reportType) {
                case 'student':
                    $data = $this->reportService->getStudentReport($params);
                    break;
                case 'class':
                    $data = $this->reportService->getClassReport($params);
                    break;
                case 'school':
                    $data = $this->reportService->getSchoolReport($params);
                    break;
                default:
                    throw new \Exception('不支持的报告类型');
            }

            // 这里需要实现具体的导出逻辑
            // 可以使用 Laravel Excel 等包来实现

            return $this->success([
                'download_url' => '',
                'file_name' => "report_{$reportType}_" . date('YmdHis') . ".{$format}"
            ], '报告导出成功');
        } catch (\Exception $e) {
            return $this->error('报告导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成PDF报告
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generatePdf(Request $request): JsonResponse
    {
        try {
            $memberId = $request->input('member_id');
            $distributionId = $request->input('distribution_id');

            if (!$memberId || !$distributionId) {
                return $this->error('缺少必要参数');
            }

            // 生成个人报告PDF
            $data = $this->reportService->generateReport([
                'report_type' => 'student',
                'target_id' => $memberId,
                'distribution_id' => $distributionId,
                'format' => 'pdf'
            ]);

            return $this->success($data, 'PDF生成成功');
        } catch (\Exception $e) {
            return $this->error('PDF生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 十年级个人报告
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function tenthGradePersonalReport(Request $request): JsonResponse
    {
        try {
            $memberId = $request->input('member_id');
            $distributionId = $request->input('distribution_id');

            if (!$distributionId) {
                return $this->error('缺少分发ID参数');
            }

            // 如果没有指定member_id，使用当前用户ID
            if (!$memberId) {
                $memberId = auth()->id();
            }

            $data = $this->reportService->getStudentReport([
                'member_id' => $memberId,
                'distribution_id' => $distributionId,
                'grade_type' => 10 // 十年级特殊标识
            ]);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取十年级个人报告失败: ' . $e->getMessage());
        }
    }
}
