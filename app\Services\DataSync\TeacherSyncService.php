<?php

namespace App\Services\DataSync;

use App\Models\User;
use App\Models\School\System\SchoolCampus;
use App\Services\BaseService;
use App\Services\School\System\ClassService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TeacherSyncService extends BaseService
{
    protected $syncConnection;
    protected $schoolSyncService;
    protected $classService;

    public function __construct(SchoolSyncService $schoolSyncService, ClassService $classService)
    {
        $this->syncConnection = DB::connection('sync_mysql');
        $this->schoolSyncService = $schoolSyncService;
        $this->classService = $classService;
    }

    /**
     * 同步单个教师数据（保持原有逻辑）
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function syncSingleTeacher($request): array
    {
        $name = $request->input('teacher_name');
        $username = $request->input('username');
        $password = '827ccb0eea8a706c4c34a16891f84e7b';
        $gender = $request->input('gender', 1); // 默认为1（男）
        $school_district = $request->input('school_campus_id'); // [2,3]这样的数组
        $roles = $request->input('role_type');
 
        // 获取同步数据库中的学校ID
            $syncSchoolId = $this->schoolSyncService->getSyncDistrictId($school_district);
    

        // 如果包含2就是教务，如果只有3就是老师
        if (in_array(2, $roles)) {
            // 教务角色
            $role = $this->syncConnection->table('ysy_role')
                ->where('name', '教务')
                ->where('school_id', $syncSchoolId)
                ->first();
            $role_id = $role->id;
            $role_source_id = '2';
        } elseif (in_array(3, $roles) && !in_array(2, $roles)) {
            // 只有教师角色（没有教务）
            $role = $this->syncConnection->table('ysy_role')
                ->where('name', '老师')
                ->where('school_id', $syncSchoolId)
                ->first();
            $role_id = $role->id;
            $role_names = '老师';
            $role_source_id = '3';
        }

        // 准备教师教务账号数据
        $teacherData = [
            'name' => $name,
            'username' => $username,
            'password' => '827ccb0eea8a706c4c34a16891f84e7b',
            'role_id' => $role_id,
            'school_id' => $syncSchoolId,
            'school_district' => $school_district,
            'role_source_id' => $role_source_id,
            'step' => '0',
            'roles' => $roles,
            'gender' => $gender
        ];

        // 调用syncTeacher方法
        return $this->syncTeacher($teacherData);
    }

    /**
     * 同步教师数据到ysy_member和ysy_teacher表（保持原有逻辑）
     *
     * @param array $teacherData 教师数据
     * @return array
     */
    public function syncTeacher(array $teacherData): array
    {
        try {
            $this->syncConnection->beginTransaction();

            // 检查ysy_member表中是否已存在相同用户名的记录
            $existingMember = $this->syncConnection->table('ysy_member')
                ->where('username', $teacherData['username'])
                ->where('school_id', $teacherData['school_id'])
                ->first();

            if ($existingMember) {
                // 如果member已存在，检查是否需要同步到teacher表
                $member_id = $existingMember->id;

                // 检查ysy_teacher表中是否已存在相同的记录
                if (in_array(3, $teacherData['roles'])) {
                    $existingTeacher = $this->syncConnection->table('ysy_teacher')
                        ->where('member_id', $member_id)
                        ->where('school_id', $teacherData['school_id'])
                        ->first();

                    if ($existingTeacher) {
                        $this->syncConnection->rollBack();
                        return [
                            'success' => true,
                            'message' => '教师数据已存在，跳过同步',
                            'skipped' => true
                        ];
                    }
                } else {
                    $this->syncConnection->rollBack();
                    return [
                        'success' => true,
                        'message' => '用户数据已存在，跳过同步',
                        'skipped' => true
                    ];
                }
            } else {
                // 准备同步到ysy_member表的数据
                $member_data = [
                    'name' => $teacherData['teacher_name'] ?? $teacherData['name'],
                    'username' => $teacherData['username'],
                    'password' => $teacherData['password'],
                    'gender' => isset($teacherData['gender']) ? ($teacherData['gender'] == 1 ? 1 : 2) : 1,
                    'school_id' => $teacherData['school_id'],
                    'school_district' => $teacherData['school_district'],                    'role_id' => '0,' . $teacherData['role_id'] . ',0', // 使用同步数据库中的角色ID
                    'step' => 0,
                    'role_source_id' => $teacherData['role_source_id'],
                    'create_time' => now(),
                ];

                // 同步到ysy_member表并获取生成的ID
                $member_id = $this->syncConnection->table('ysy_member')->insertGetId($member_data);
            }
            $synced_to_teacher = false;

            // 如果角色类型包含教师(type=3)，同步到ysy_teacher表
            if (in_array(3, $teacherData['roles'])) {
                // 生成教师ID
                $teacher_data = [
                    'member_id' => $member_id,
                    'name' => $teacherData['name'],
                    'username' => $teacherData['username'],
                    'gender' => isset($teacherData['gender']) ? ($teacherData['gender'] == 1 ? 1 : 2) : 1,
                    'school_id' => $teacherData['school_id'],
                    'school_district' => $teacherData['school_district'],
                    'step' => 0,
                    'type' => '老师',
                    //是否是心理老师
                    'is_psych' => '0',
                ];

                // 同步到ysy_teacher表
                $this->syncConnection->table('ysy_teacher')->insertGetId($teacher_data);
                $synced_to_teacher = true;
            }

            $this->syncConnection->commit();

            return [
                'success' => true,
                'message' => '教师数据同步成功'
            ];
        } catch (\Exception $e) {
            $this->syncConnection->rollBack();
            $this->throwBusinessException('教师数据同步失败');
        }
    }

    /**
     * 批量同步教师数据（保持原有逻辑，支持role_name和重复检查）
     *
     * @param array $request_data 包含school_campus_id和teachers数组的请求数据
     * @return array
     */
    public function syncBatchTeachers(array $request_data): array
    {
        if (empty($request_data['teachers'])) {
            return [
                'success' => false,
                'message' => '教师数据为空'
            ];
        }

        $school_campus_id = $request_data['school_campus_id'];
        $teachers = $request_data['teachers'];
        $sync_results = [];
        $success_count = 0;
        $failed_count = 0;
        $skipped_count = 0;

        foreach ($teachers as $teacher_info) {
            // 构造单个教师的请求数据
            $single_request_data = [
                'school_campus_id' => $school_campus_id,
                'teacher_name' => $teacher_info['teacher_name'],
                'username' => $teacher_info['username'],
                'password' => '123456', // 默认密码
                'gender' => $this->convertGenderToNumber($teacher_info['gender'] ?? '男'),
                'role_name' => $teacher_info['role_name']
            ];

            // 转换role_name为roles数组
            if ($teacher_info['role_name'] == '教务') {
                $single_request_data['role_type'] = [2];
            } elseif ($teacher_info['role_name'] == '老师') {
                $single_request_data['role_type'] = [3];
            } else {
                $single_request_data['role_type'] = [3]; // 默认为老师
            }

            // 创建模拟的Request对象
            $mock_request = new \Illuminate\Http\Request($single_request_data);

            // 调用单个教师同步方法
            try {
                $result = $this->syncSingleTeacher($mock_request);
                $sync_results[] = array_merge($result, [
                    'teacher_name' => $teacher_info['teacher_name'],
                    'username' => $teacher_info['username']
                ]);

                if ($result['success']) {
                    if (isset($result['skipped']) && $result['skipped']) {
                        $skipped_count++;
                    } else {
                        $success_count++;
                    }
                } else {
                    $failed_count++;
                }
            } catch (\Exception $e) {
                $sync_results[] = [
                    'teacher_name' => $teacher_info['teacher_name'],
                    'username' => $teacher_info['username'],
                    'success' => false,
                    'message' => '同步失败: ' . $e->getMessage()
                ];
                $failed_count++;

                Log::warning('批量同步教师失败', [
                    'teacher_name' => $teacher_info['teacher_name'],
                    'username' => $teacher_info['username'],
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'success' => true,
            'sync_results' => $sync_results,
            'total_count' => count($teachers),
            'success_count' => $success_count,
            'failed_count' => $failed_count,
            'skipped_count' => $skipped_count,
            'message' => "批量教师数据同步完成 - 成功: {$success_count}, 跳过: {$skipped_count}, 失败: {$failed_count}"
        ];
    }

    /**
     * 批量同步教师更新
     *
     * @param array $teachers
     * @return array
     */
    public function syncBatchTeachersUpdate(array $teachers): array
    {
        try {
            if (empty($teachers)) {
                return [
                    'success' => false,
                    'message' => '教师数据为空'
                ];
            }

            $sync_results = [];
            $success_count = 0;
            $failed_count = 0;

            foreach ($teachers as $teacher) {
                // 调用单个教师更新同步方法
                $result = $this->syncSingleTeacherUpdate($teacher);

                $sync_results[] = array_merge($result, [
                    'teacher_id' => $teacher->id,
                    'teacher_name' => $teacher->teacher_name ?? 'unknown'
                ]);

                if ($result['success']) {
                    $success_count++;
                } else {
                    $failed_count++;
                }
            }

            Log::info('批量教师更新同步完成', [
                'total_count' => count($teachers),
                'success_count' => $success_count,
                'failed_count' => $failed_count
            ]);

            return [
                'success' => true,
                'sync_results' => $sync_results,
                'total_count' => count($teachers),
                'success_count' => $success_count,
                'failed_count' => $failed_count,
                'message' => '批量教师更新同步完成'
            ];

        } catch (\Exception $e) {
            $this->throwBusinessException('批量教师更新同步失败');
        }
    }

    /**
     * 同步单个教师更新（保持原有逻辑）
     *
     * @param \App\Models\School\System\Teacher $teacher
     * @return array
     */
    public function syncSingleTeacherUpdate(\App\Models\School\System\Teacher $teacher): array
    {
        try {
            $this->syncConnection->beginTransaction();

            // 重新加载教师数据，确保包含最新的关联关系
            $teacher = \App\Models\School\System\Teacher::with(['user.roles', 'school', 'schoolCampus'])
                ->find($teacher->id);

            if (!$teacher || !$teacher->user) {
                return [
                    'success' => false,
                    'message' => '教师或用户信息不存在'
                ];
            }

            // 获取角色类型
            $roles = $teacher->user->roles->filter(function ($item) {
                return $item->status == 1;
            })->pluck('type')->unique()->toArray();

            // 查询角色ID
            $role_id = null;
            $role_source_id = null;

            if (in_array(2, $roles)) {
                // 教务角色
                $role = $this->syncConnection->table('ysy_role')
                    ->where('name', '教务')
                    ->where('school_id', $teacher->school_id)
                    ->first();
                if ($role) {
                    $role_id = $role->id;
                    $role_source_id = '2';
                }
            } elseif (in_array(3, $roles) && !in_array(2, $roles)) {
                // 只有教师角色（没有教务）
                $role = $this->syncConnection->table('ysy_role')
                    ->where('name', '老师')
                    ->where('school_id', $teacher->school_id)
                    ->first();
                if ($role) {
                    $role_id = $role->id;
                    $role_source_id = '3';
                }
            }

            if (!$role_id) {
                return [
                    'success' => false,
                    'message' => '未找到对应的角色信息'
                ];
            }

            // 根据学校ID+用户名查找现有的ysy_member记录
            $existing_member = $this->syncConnection->table('ysy_member')
                ->where('username', $teacher->user->username)
                ->where('school_id', $teacher->school_id)
                ->first();

            if (!$existing_member) {
                return [
                    'success' => false,
                    'message' => '未找到要更新的教师记录'
                ];
            }

            // 更新ysy_member表
            $member_update_data = [
                'name' => $teacher->teacher_name,
                'gender' => $teacher->user->gender == 1 ? 1 : 2,
                'role_id' => '0,' . $role_id . ',0',
                'role_source_id' => $role_source_id,
            ];

            $this->syncConnection->table('ysy_member')
                ->where('id', $existing_member->id)
                ->update($member_update_data);

            $synced_to_teacher = false;

            // 如果角色类型包含教师(type=3)，更新ysy_teacher表
            if (in_array(3, $roles)) {
                $existing_teacher = $this->syncConnection->table('ysy_teacher')
                    ->where('member_id', $existing_member->id)
                    ->first();

                if ($existing_teacher) {
                    // 更新现有教师记录
                    $teacher_update_data = [
                        'name' => $teacher->teacher_name,
                        'username' => $teacher->user->username,
                        'gender' => $teacher->user->gender == 1 ? 1 : 2,
                        'school_id' => $teacher->school_id,
                        'school_district' => $teacher->school_campus_id,
                        'step' => 0,
                        'type' => '老师',
                        'is_psych' => '0'
                    ];

                    $this->syncConnection->table('ysy_teacher')
                        ->where('id', $existing_teacher->id)
                        ->update($teacher_update_data);

                    $synced_to_teacher = true;
                } else {
                    // 如果ysy_teacher表中没有记录，则插入新记录
                    $teacher_insert_data = [
                        'member_id' => $existing_member->id,
                        'name' => $teacher->teacher_name,
                        'username' => $teacher->user->username,
                        'gender' => $teacher->user->gender == 1 ? 1 : 2,
                        'school_id' => $teacher->school_id,
                        'school_district' => $teacher->school_campus_id,
                        'step' => 0,
                        'type' => '老师',
                        'is_psych' => '0'
                    ];

                    $this->syncConnection->table('ysy_teacher')->insert($teacher_insert_data);
                    $synced_to_teacher = true;
                }
            }

            $this->syncConnection->commit();

            Log::info('教师更新同步成功', [
                'teacher_id' => $teacher->id,
                'member_id' => $existing_member->id,
                'username' => $teacher->user->username,
                'synced_to_teacher' => $synced_to_teacher
            ]);

            return [
                'success' => true,
                'teacher_id' => $teacher->id,
                'member_id' => $existing_member->id,
                'synced_to_member' => true,
                'synced_to_teacher' => $synced_to_teacher,
                'message' => '教师更新同步成功'
            ];

        } catch (\Exception $e) {
            $this->syncConnection->rollBack();
            $this->throwBusinessException('单个教师更新同步失败');
        }
    }

    /**
     * 同步教务用户到老师接口
     * 通过机构ID获取学校ID，再获取校区ID进行同步
     *
     * @param User $user 教务用户
     * @param Request $request 请求对象（可选）
     * @return array
     */
    public function syncAdminUserIfNeeded($user, $request = null): array
    {
        try {
            // 教务用户同步，角色固定为教务（type=2）
            // 这里不需要检查角色，因为调用此方法的前提就是教务用户

            // 通过机构ID获取学校ID，再获取校区ID
            $schoolCampusId = null;

            try {
                // 通过用户的机构ID获取学校ID
                $organization_id = $user->organization_id;
                $school_id = $this->classService->getSchoolId(new \Illuminate\Http\Request(['organization_id' => $organization_id]));

                // 通过学校ID获取第一个可用的校区ID
                $schoolCampus = SchoolCampus::where('school_id', $school_id)
                    ->where('status', 1)
                    ->first();

                if ($schoolCampus) {
                    $schoolCampusId = $schoolCampus->id;
                }
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::warning('获取校区ID失败', [
                    'user_id' => $user->id,
                    'organization_id' => $user->organization_id,
                    'error' => $e->getMessage()
                ]);
            }

            // 如果没有校区ID，跳过同步
            if (!$schoolCampusId) {
                \Illuminate\Support\Facades\Log::warning('教务用户同步跳过：缺少校区ID', [
                    'user_id' => $user->id,
                    'username' => $user->username
                ]);
                return [
                    'success' => false,
                    'message' => '缺少校区ID，无法同步',
                    'skipped' => true
                ];
            }
           
            // 准备同步到老师接口的数据
            $teacherSyncData = [
                'teacher_name' => $user->real_name,
                'username' => $user->username,
                'gender' => $user->gender ?? 1,
                'school_campus_id' => $schoolCampusId,
                'role_type' => [2], // 教务角色
            ];

            // 创建模拟的Request对象
            $mockRequest = new \Illuminate\Http\Request($teacherSyncData);

            // 调用单个教师同步方法
            $result = $this->syncSingleTeacher($mockRequest);

            // 记录同步结果
            \Illuminate\Support\Facades\Log::info('教务用户同步到老师接口', [
                'user_id' => $user->id,
                'username' => $user->username,
                'school_campus_id' => $schoolCampusId,
                'sync_result' => $result
            ]);

            return array_merge($result, [
                'message' => '教务用户同步成功'
            ]);

        } catch (\Exception $e) {
            $this->throwBusinessException('教务同步失败');
        }
    }

    /**
     * 转换性别字符串为数字
     *
     * @param string|int $gender 性别（男/女 或 1/2）
     * @return int 1=男，2=女
     */
    private function convertGenderToNumber($gender): int
    {
        if (is_numeric($gender)) {
            return (int)$gender;
        }

        return $gender === '女' ? 2 : 1;
    }
}
