<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */

namespace app\evaluation\logic;

use app\evaluation\model\Knowledges as KnowledgesModel;
use think\Db;
use think\Loader;

class Knowledges
{
    protected $user;

    public function __construct()
    {

        $this->user = get_user();
    }

    public function add()
    {

        $model = new KnowledgesModel();
        $model->data([
            'name' => input('name'),
            'parent_id' => input('parent_id'),
            'is_high' => input('is_high'),
            'course_id' => input('course_id'),
            'create_at' => date('Y-m-d H:i:s'),
        ]);

        $model->save();
        $id = $model->id;
        apiReturn($id);
    }

    public function edit()
    {

        $model = new KnowledgesModel();
        $model->save([
            'name' => input('name'),
            'parent_id' => input('parent_id'),
            'is_high' => input('is_high'),
            'course_id' => input('course_id'),
            'update_at' => date('Y-m-d H:i:s'),
        ], ['id' => input('id')]);
        apiReturn(input('id'));
    }

    public function del()
    {
        $model = new KnowledgesModel();
        $model->save([
            'status' => '-1',
        ], ['id' => input('id')]);
        //软删除
        apiReturn(input('id'));
    }

    public function get_list()
    {

        $model = new KnowledgesModel();
        $course_id = input('course_id');
        $is_high = input('is_high');
        $name = input('name');
        if (!empty(input('id'))) {
            $result = $model->where('id', (int)input('id'))->find();
        } else {
            $data = $model->alias('a')
                ->field('a.id, a.name, a.parent_id, a.course_id, a.is_high, b.id as child_id, b.name as child_name, b.parent_id as child_parent_id, b.course_id as child_course_id, b.is_high as child_is_high, c.id as grandchild_id, c.name as grandchild_name, c.parent_id as grandchild_parent_id, c.course_id as grandchild_course_id, c.is_high as grandchild_is_high')
                ->join('evaluation_knowledges b', 'a.id = b.parent_id  AND b.status = 0 ', 'LEFT')
                ->join('evaluation_knowledges c', 'b.id = c.parent_id  AND c.status = 0', 'LEFT')
                ->where('a.status', 0)
                ->where('a.parent_id', 0);
            if (!empty($is_high)) {
                $data->where('a.is_high', $is_high);
            }
            if (!empty($course_id)) {
                $data->where('a.course_id', $course_id);
            }
            if (!empty($name)) {
                $data->where(function ($query) use ($name) {
                    $query->where('a.name', 'like', '%' . $name . '%')
                        ->whereOr('b.name', 'like', '%' . $name . '%')
                        ->whereOr('c.name', 'like', '%' . $name . '%');
                });
            }
            $data = $data->select();

            $result = [];
            foreach ($data as $item) {
                $resultItem = [
                    "id" => $item['id'],
                    "name" => $item['name'],
                    "parent_id" => $item['parent_id'],
                    "course_id" => $item['course_id'],
                    "is_high" => $item['is_high'],
                    "children" => [] // Initialize children as an empty array
                ];

                if (!isset($result[$item['id']])) {
                    $result[$item['id']] = $resultItem;
                }

                if (!empty($item['child_id'])) {
                    $childItem = [
                        "id" => $item['child_id'],
                        "name" => $item['child_name'],
                        "parent_id" => $item['child_parent_id'],
                        "course_id" => $item['child_course_id'],
                        "is_high" => $item['child_is_high'],
                        "children" => [] // Initialize children as an empty array
                    ];

                    if (!isset($result[$item['id']]['children'])) {
                        $result[$item['id']]['children'] = [];
                    }

                    // Check if child item with the same ID already exists
                    $childExists = false;
                    foreach ($result[$item['id']]['children'] as &$existingChild) {
                        if ($existingChild['id'] === $childItem['id']) {
                            $childExists = true;
                            $existingChild['children'] = array_merge($existingChild['children'], $childItem['children']);
                            break;
                        }
                    }

                    if (!$childExists) {
                        $result[$item['id']]['children'][] = $childItem;
                    }

                    if (!empty($item['grandchild_id'])) {
                        $grandchildItem = [
                            "id" => $item['grandchild_id'],
                            "name" => $item['grandchild_name'],
                            "parent_id" => $item['grandchild_parent_id'],
                            "course_id" => $item['grandchild_course_id'],
                            "is_high" => $item['grandchild_is_high']
                        ];

                        $lastChildIndex = count($result[$item['id']]['children']) - 1;
                        $result[$item['id']]['children'][$lastChildIndex]['children'][] = $grandchildItem;
                    }
                }
            }



            foreach ($result as &$item) {
                if (!isset($item['children'])) {
                    $item['children'] = [];
                }
            }

            foreach ($result as &$item) {
                foreach ($item['children'] as &$child) {
                    if (!isset($child['children'])) {
                        $child['children'] = [];
                    }
                }
            }
            $result = array_values($this->tree1($result));
        }
        apiReturn($result);
    }

    /**
     * 格式化数组键名
     * @param $data
     * @return array
     */
    public function tree1($data)
    {
        foreach ($data as $k => $v) {
            if (isset($v['children'])) {
                $data[$k]['children'] = array_values($this->tree1($v['children']));
            }
        }
        return array_values($data);
    }

}