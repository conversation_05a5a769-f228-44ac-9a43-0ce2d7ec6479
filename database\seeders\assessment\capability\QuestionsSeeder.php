<?php

namespace Database\Seeders\assessment\capability;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class QuestionsSeeder extends Seeder
{
    protected string $connect = 'mysql_prod';
    
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 使用数据库事务
        DB::transaction(function () {
            // 学习力测评
            $this->processSurveyQuestions('234', 9);
            // 批判性思维能力
            $this->processSurveyQuestions('258', 10);
            // 问题解决能力
            $this->processSurveyQuestions('257', 11);
            // 创造思维倾向
            $this->processSurveyQuestions('256', 12);
            // 沟通与合作
            $this->processSurveyQuestions('255', 13);

        });
    }

    function processSurveyQuestions($surveyId, $assessmentsId) {
        // 获取问题数据
        $surveyQuestionData = DB::connection($this->connect)
            ->table('survey_question')
            ->where('survey_id', $surveyId)
            ->orderBy('show_id', 'asc')
            ->get()
            ->toArray();

        $forwardArr=[];
        $dimensionCodeArr=[];
        $dimensionNameArr=[];
        list($forwardArr,$dimensionCodeArr,$dimensionNameArr) = $this->formatArr($surveyId);
        // dd($forwardArr);
        $assessmentQuestionData = [];
        foreach ($surveyQuestionData as $item) {
            // 获取每个问题的选项
            $optionsData = DB::connection($this->connect)
                ->table('survey_question_answer')
                ->where('question_id', $item->id)
                ->orderBy('question_id', 'asc') // 使用适当的列来排序选项
                ->get()
                ->toArray();

            // 为选项分配字母
            $correct = null;
            foreach($optionsData as $value){

                if(!empty($forwardArr) && in_array($item->show_id,$forwardArr)){
                    $score = 6-$value->sort;
                }else{
                    $score = $value->sort;
                }
                
                //批判性思维能力和沟通与合作部分题目
                if( $surveyId == 258 || ( $surveyId == 255 && in_array($item->id,[1408,1409,1410,1411,1412,1413,1414,1415,1416,1417]) ) ){
                    if($value->is_correct_answer == 1) $correct = $value->option;
                    $options[$value->option] = $value->answer;
                }else{
                    $options[$value->option] = [
                        "name" => $value->answer,
                        "score" => $score,
                    ];
                }
                
            }

            $Question_item_Data = [
                'assessment_id' => $assessmentsId,
                'old_question_id' => $item->id,
                'is_normal' => $item->is_normal_type,
                'content' => $item->name,
                'number' => $item->show_id,
                'dimension_name' => $dimensionNameArr[$item->show_id] ?? null,
                'dimension_code' => $dimensionCodeArr[$item->show_id] ?? null,
                'options' => json_encode($options),
                'correct' => $correct,
            ];
            $assessmentQuestionData[] = $Question_item_Data;
        }

        DB::table('assessment_capability_questions')->insert($assessmentQuestionData);
    }

    public function formatArr($surveyId){
        switch($surveyId){
            case 234://学习力
                //下标为0 为正向题 下标为1 是反向题
                $arr = [
                    [
                        [41,44,52,59],[40],'1','记忆学习策略'
                    ],
                    [
                        [12,21,56,58,64],[31],'2','精加工学习策略'
                    ],
                    [
                        [2,7,10,23,27,30,34,37,43,51,61,63],[4],'3','学习内容组织策略'
                    ],
                    [
                        [1,8,9,24,47,55,62],[3,15],'4','学习坚毅力'
                    ],
                    [
                        [5,29,53,54],[36,39],'5','成长型学习思维'
                    ],
                    [
                        [6,13,16,19,20,26,28,32,33,38,42,45,48,49,65,66],[14],'6','元认知学习策略'
                    ],
                    [
                        [18,25,46,50,60],[],'7','学习求助能力'
                    ],
                    [
                        [22,35],[11,17,57],'8','学习习惯'
                    ],
                ];
                break;
            case 258://批判性思维能力
                $arr = [
                    [
                        [1,2,3,4,5],[],'1','分析能力'
                    ],
                    [
                        [6,7,8,9,10,11],[],'2','推理能力'
                    ],
                    [
                        [12,13,14,15,16,17],[],'3','评价能力'
                    ],
                ];
                break;
            case 257://问题解决能力
                $arr = [
                    [
                        [2,4,5,6,7,8],[1,3],'1','问题解决自信心'
                    ],
                    [
                        [9,10,11,13,15,16,17,18,19,22,23,24,25,26,27],[12,14,20,21,28],'2','问题解决能力'
                    ],
                ];
                break;
            case 256:// 创造思维倾向
                $arr = [
                    [
                        [2,8,11,19,27,33,34,37,38,39,47,49],[12,48],'1','好奇性'
                    ],
                    [
                        [1,5,24,25,28,36,43,44],[29,35],'2','冒险性'
                    ],
                    [
                        [3,7,10,18,26,41,42],[4,9,17],'3','挑战性'
                    ],
                    [
                        [6,13,14,16,22,23,30,31,32,40,46],[45],'4','想象力'
                    ],
                    [
                        [55,56,57,58,59],[51,52,53,54],'5','意志力'
                    ],
                    [
                        [15,21,50,60,61,62,63,64,66],[20,65],'6','自信心'
                    ],
        
                ];
                break;
            case 255:// 沟通与合作
                $arr = [
                    [
                        [1,2,3,4,5,6,7,8,9,10],[],'1','沟通与合作基本能力'
                    ],
                    [
                        [12,17,36,41,47,51,54,59,64,82,83],[31,52,70,76,77],'2','倾听能力'
                    ],
                    [
                        [20,22,37,42,50,55,63,79],[44,62],'3','表达能力'
                    ],
                    [
                        [11,26,34,39,60,65,73,75,78,84],[21,61],'4','同理反应能力'
                    ],
                    [
                        [13,15,23,30,69,71],[72,80],'5','社交互动能力'
                    ],
                    [
                        [25,28,32,33,45,53,66,67,68,81],[29],'6','参与与配合'
                    ],
                    [
                        [14,19,24,35,38,49,56,58],[],'7','领导力'
                    ],
                    [
                        [16,18,27,40,43,57,74,85],[46,48],'8','协调能力'
                    ],
                ];
                break;
        }
        $dimensionCodeArr = [];
        $dimensionNameArr = [];
        $forwardArr = [];
        foreach($arr as $v){
            foreach($v[0] as $v0){
                $directionArr[] = $v0;
                $dimensionCodeArr[$v0] = $v[2];
                $dimensionNameArr[$v0] = $v[3];
            }
            if(!empty($v[1])){
                foreach($v[1] as $v1){
                    $forwardArr[] = $v1;
                    $dimensionCodeArr[$v1] = $v[2];
                    $dimensionNameArr[$v1] = $v[3];
                }
            }
        }
        // ksort($dimensionCodeArr);
        // dd($dimensionCodeArr);
        return [$forwardArr,$dimensionCodeArr,$dimensionNameArr];
    }
}
