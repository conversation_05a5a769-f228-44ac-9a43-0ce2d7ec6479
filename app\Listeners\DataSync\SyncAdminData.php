<?php

namespace App\Listeners\DataSync;

use App\Events\DataSync\AdminCreated;
use App\Services\DataSync\DataSyncService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * 同步教务人员数据监听器
 */
class SyncAdminData implements ShouldQueue
{
    use InteractsWithQueue;

    protected $dataSyncService;

    /**
     * Create the event listener.
     */
    public function __construct(DataSyncService $dataSyncService)
    {
        $this->dataSyncService = $dataSyncService;
    }

    /**
     * Handle the event.
     */
    public function handle(AdminCreated $event): void
    {
        try {
            $result = $this->dataSyncService->syncAdmin($event->adminData);
            
            if (!$result['success']) {
                Log::error('教务人员数据同步失败', [
                    'admin_data' => $event->adminData,
                    'error' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('教务人员数据同步监听器异常', [
                'admin_data' => $event->adminData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(AdminCreated $event, \Throwable $exception): void
    {
        Log::error('教务人员数据同步队列任务失败', [
            'admin_data' => $event->adminData,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
