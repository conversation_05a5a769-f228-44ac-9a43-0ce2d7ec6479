<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ClearUserLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:clear-user-logs {--dry-run : 仅显示将要删除的记录数量，不实际删除} {--force : 强制执行，跳过确认提示}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '清空用户登录日志和访问日志表（每月1号执行）';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        $isForced = $this->option('force');

        try {
            // 获取当前时间
            $currentTime = Carbon::now();
            
            if ($isDryRun) {
                $this->info('=== 干运行模式 - 仅显示统计信息，不会实际删除数据 ===');
            }
            
            // 统计要清理的记录数量
            $loginLogsCount = DB::table('user_login_logs')->count();
            $accessLogsCount = DB::table('user_access_logs')->count();
            
            $this->info("当前 user_login_logs 表记录数: {$loginLogsCount}");
            $this->info("当前 user_access_logs 表记录数: {$accessLogsCount}");
            
            if ($isDryRun) {
                $this->warn('干运行模式结束 - 没有删除任何数据');
                return 0;
            }
            
            // 确认是否继续执行（除非使用 --force 参数）
            if (!$isForced && !$this->confirm('确定要清空这些日志表吗？此操作不可逆！')) {
                $this->info('操作已取消');
                return 0;
            }
            
            // 开始清理操作
            $this->info('开始清理用户日志表...');
            
            // 清空 user_login_logs 表
            $this->info('正在清空 user_login_logs 表...');
            DB::table('user_login_logs')->truncate();
            $this->info('✓ user_login_logs 表已清空');
            
            // 清空 user_access_logs 表
            $this->info('正在清空 user_access_logs 表...');
            DB::table('user_access_logs')->truncate();
            $this->info('✓ user_access_logs 表已清空');
            
            // 记录日志
            Log::info('用户日志表清理完成', [
                'cleared_login_logs' => $loginLogsCount,
                'cleared_access_logs' => $accessLogsCount,
                'executed_at' => $currentTime->toDateTimeString(),
                'command' => 'logs:clear-user-logs'
            ]);
            
            $this->info("✅ 日志清理完成！");
            $this->info("已清理登录日志: {$loginLogsCount} 条");
            $this->info("已清理访问日志: {$accessLogsCount} 条");
            $this->info("执行时间: {$currentTime->toDateTimeString()}");
            
        } catch (\Exception $e) {
            $this->error('清理日志时发生错误: ' . $e->getMessage());
            
            // 记录错误日志
            Log::error('用户日志表清理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'executed_at' => Carbon::now()->toDateTimeString(),
                'command' => 'logs:clear-user-logs'
            ]);
            
            return 1;
        }
        
        return 0;
    }
}
