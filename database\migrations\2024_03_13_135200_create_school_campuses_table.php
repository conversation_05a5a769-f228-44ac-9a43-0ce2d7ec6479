<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('school_campuses', function (Blueprint $table) {
            $table->id();
            $table->string('campus_name',20)->comment('校区名称');
            $table->integer('school_id')->comment('学校ID');
            $table->tinyInteger('type')->comment('类型1:小学2:初中3:高中');
            $table->tinyInteger('is_main')->default(0)->comment('是否主校区0否1是');
            $table->tinyInteger('status')->default(1)->comment('状态1正常2禁用');
            $table->timestamps();
            $table->string('creator',20)->nullable()->comment('创建人');
            $table->string('updater',20)->nullable()->comment('最后更新人');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('school_campuses');
    }
};
