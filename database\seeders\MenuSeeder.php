<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * 菜单数据同步
 */
class MenuSeeder extends Seeder
{
    protected string $connect = 'mysql_demo';
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 使用预加载来获取所有相关数据，减少查询次数,共196条数据
        $menuData = DB::connection($this->connect)->table('module')
            ->where('is_delete', 0)
            ->orderBy('module_id', 'asc')
            ->get()->toArray();
        // 遍历数据，将数据插入到菜单表中
        $menuInsertData = [];
        foreach ($menuData as $item) {
            // 准备每个菜单项的数据结构
            $menu_item_data = [
                'menu_name' => $item->menu_name,
                'code' => $item->module_id,
                'description' => $item->description,
                'url' => $item->url,
                'sort' => $item->module_order,
                'parent_code' => $item->pid,
                'created_at' => date('Y-m-d H:i:s',time()),
                'updated_at' => date('Y-m-d H:i:s',time()),
            ];
            $menuInsertData[] = $menu_item_data;
        }
        // 保存这些菜单数据
        DB::table('menus')->insert($menuInsertData);
        die('end');
    }

    // 辅助函数：递归查找子菜单
    function findChildren($moduleId, $moduleCode, $menuData, &$menuInsertData): array
    {
        // 筛选符合条件的数据
        $children = array_filter($menuData, function ($item) use ($moduleCode) {
            return $item->pid == $moduleCode;
        });
        foreach ($children as $child) {
            // 准备每个菜单项的数据结构
            $menu_item_data = [
                'menu_name' => $child->menu_name,
                'code' => $child->module_id,
                'module_id' => $moduleId,
                'description' => $child->description,
                'url' => $child->url,
                'sort' => $child->module_order,
                'parent_code' => $child->pid,
                'created_at' => date('Y-m-d H:i:s',time()),
                'updated_at' => date('Y-m-d H:i:s',time()),
            ];
            $menuInsertData[] = $menu_item_data; // 累积数据
            // 递归查找更深层次的子菜单
            self::findChildren($moduleId, $child->module_id, $menuData, $menuInsertData);
        }
        return $menuInsertData;
    }
}
