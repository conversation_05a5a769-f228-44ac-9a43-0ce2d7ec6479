<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MajorAcademicGroupBK extends BaseModel
{
    use HasFactory;

    protected $table = 'MajorAcademicGroup_BK';

    // 指定连接
    protected $connection = 'sqlsrv_gk';

    //主键
    protected $primaryKey = 'Id';

    // 隐藏字段
    protected $hidden = [];
    // 是否使用时间戳
    public $timestamps = false;

    // 可批量赋值的字段
    protected $fillable = [
        'AcademicGroupName',
        'Introduction',
        'Ability',
        'HighSchoolSubject',
        'Major',
        'Theory',
        'YingYong',
        'ShiCao',
        'Picture',
        'Explain',
    ];
}
