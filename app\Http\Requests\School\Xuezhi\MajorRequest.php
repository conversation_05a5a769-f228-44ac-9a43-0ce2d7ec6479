<?php

namespace App\Http\Requests\School\Xuezhi;

use App\Http\Requests\BaseRequest;

class MajorRequest extends BaseRequest
{
    public function rules(): array
    {
        $method = $this->route()->getActionMethod();

        return match ($method) {
            'colleges', 'xuankeDistributed' => $this->collegesRules(),
            'academic' => $this->academicRules(),
            'employment', 'info', 'top5MajorCollegeRank' , 'analysis' => $this->employmentRules(),
            'aiIntroduction' => $this->aiIntroductionRules(),
            'search' => $this->searchRules(),
            default => []
        };
    }

    private function collegesRules(): array
    {
        return [
            'province_id' => 'required|integer|exists_province',
            'major_name' => 'required|string',
        ];
    }

    private function academicRules(): array
    {
        return [
            'major_code' => 'required|string',
            'major_id' => 'required|integer',
        ];
    }

    private function employmentRules(): array
    {
        return [
            'major_code' => 'required|string',
        ];
    }

    private function aiIntroductionRules(): array
    {
        return [
            'code' => 'required|string',
        ];
    }

    private function searchRules(): array
    {
        return [
            'major_name' => 'required|string|min:1',
        ];
    }

    public function messages(): array
    {
        return [
            'province_id.required' => '省份ID不能为空',
            'province_id.integer' => '省份ID必须为整数',
            'major_name.required' => '专业名称不能为空',
            'major_name.string' => '专业名称必须为字符串',
            'major_name.min' => '专业名称至少需要1个字符',
            'major_code.required' => '专业代码不能为空',
            'major_code.string' => '专业代码必须为字符串',
            'major_id.required' => '专业ID不能为空',
            'major_id.integer' => '专业ID必须为整数',
            'code.required' => '代码不能为空',
            'code.string' => '代码必须为字符串',
        ];
    }
}