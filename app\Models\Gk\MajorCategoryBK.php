<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MajorCategoryBK extends BaseModel
{
    use HasFactory;

    protected $table = 'MajorCategory_BK';

    // 指定连接
    protected $connection = 'sqlsrv_gk';

    //主键
    protected $primaryKey = 'Id';

    // 隐藏字段
    protected $hidden = [];
    // 是否使用时间戳
    public $timestamps = false;

    // 可批量赋值的字段
    protected $fillable = [
        'MajorSubjectID',
        'MajorCategoryCode',
        'MajorCategoryName',
    ];

    // 与 MajorSubjectBK 的多对一关系
    public function majorSubject()
    {
        return $this->belongsTo(MajorSubjectBK::class, 'MajorSubjectID', 'ID');
    }

    // 与 MajorBK 的一对多关系
    public function majors()
    {
        return $this->hasMany(MajorBK::class, 'MajorCategoryID', 'ID');
    }
}
