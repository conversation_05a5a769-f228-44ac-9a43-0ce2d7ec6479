<?php

namespace Database\Seeders\assessment;

use Database\Seeders\BaseIncrementalSeeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SchedulesSeeder extends BaseIncrementalSeeder
{
    protected string $assessment_type = 'schedules';

    // 测评ID映射
    protected array $survey_ids = [
        1, 18, 21, 28, 45, 32, 53, 72,
        234, 258, 257, 256, 255, 426, 427, 428,
        429, 430, 431, 432, 433, 471, 472, 473
    ];

    // 新老测评ID转换
    protected array $surveytoassessment_id = [
        1  => 1,
        18 => 2,
        21 => 3,
        28 => 4,
        45 => 5,
        32 => 6,
        53 => 7,
        72 => 8,
        234 => 9,
        258 => 10,
        257 => 11,
        256 => 12,
        255 => 13,
        426 => 14,
        427 => 15,
        428 => 16,
        429 => 17,
        430 => 18,
        431 => 19,
        432 => 20,
        471 => 21,
        472 => 22,
        473 => 23,
        433 => 24,
    ];
    /**
     * 获取测评ID列表
     */
    protected function getSurveyIds(): array
    {
        return $this->survey_ids;
    }

    /**
     * 获取评估ID列表
     */
    protected function getAssessmentIds(): array
    {
        return array_values($this->surveytoassessment_id);
    }

    /**
     * 执行具体的seeder逻辑
     */
    protected function executeSeeder(): void
    {
        // 获取最后处理的ID
        $lastProcessedId = $this->getLastProcessedId();

        Log::info("开始执行SchedulesSeeder", [
            'school_id' => $this->school_id,
            'last_processed_id' => $lastProcessedId
        ]);

        // 构建子查询，按 grade_id 和 times survey_id 分组，只查询大于最后处理ID的记录
        $subQuery = DB::connection($this->connect)->table('survey_user_session')
            ->join('survey', 'survey.id', '=', 'survey_user_session.survey_id')
            ->select('grade_id','survey_id','title','times')
            ->where('survey_user_session.school_id', $this->school_id)
            ->whereIn('survey_id', $this->survey_ids)
            ->where('times', '>', 0)
            ->where('survey_user_session.id', '>', $lastProcessedId) // 增量查询条件
            ->groupBy('grade_id','survey_id', 'times');

        $result = DB::connection($this->connect)->table('grade as g')
            ->select(
                'g.id',
                'g.name',
                'g.grade_name',
                'sub.grade_id',
                'sub.survey_id',
                'sub.title',
                'sub.times',
            )
            ->joinSub($subQuery, 'sub', function ($join) {
                $join->on('sub.grade_id', '=', 'g.id');
            })
            ->get()->toArray();

        if (empty($result)) {
            Log::info("没有新的数据需要处理", [
                'school_id' => $this->school_id,
                'last_processed_id' => $lastProcessedId
            ]);
            return;
        }

        Log::info("找到新数据", [
            'school_id' => $this->school_id,
            'count' => count($result)
        ]);

        $insertedSchedules = [];
        $processedCount = 0;
        $maxSessionId = 0;

        foreach ($result as $item) {
            $scheduleData = [
                'school_id' => $this->school_id,
                'old_times' => $item->times,
                'name' => $item->name . $item->grade_name . $item->title . $item->survey_id . '测评计划' . $item->times,
                'open_time' => now()->toDateTimeString(),
                'close_time' => date('Y-m-d H:i:s', strtotime('+10 years')),
                'creator' => '管理员',
            ];

            $uniqueKey = $item->grade_id . '_' . $item->survey_id . '_' . $item->times;

            if (!isset($insertedSchedules[$uniqueKey])) {
                // 检查是否已存在相同的计划
                $existingSchedule = DB::table('assessment_schedules')
                    ->where('school_id', $this->school_id)
                    ->where('old_times', $item->times)
                    ->where('name', $scheduleData['name'])
                    ->first();

                if (!$existingSchedule) {
                    // 插入到 assessment_schedules 表并获取 ID
                    $scheduleId = DB::table('assessment_schedules')->insertGetId($scheduleData);
                    $insertedSchedules[$uniqueKey] = $scheduleId;
                } else {
                    $scheduleId = $existingSchedule->id;
                    $insertedSchedules[$uniqueKey] = $scheduleId;
                }
            } else {
                $scheduleId = $insertedSchedules[$uniqueKey];
            }

            // 检查是否已存在相同的任务
            $existingTask = DB::table('assessment_tasks')
                ->where('assessment_schedule_id', $scheduleId)
                ->where('assessment_id', $this->surveytoassessment_id[$item->survey_id])
                ->where('old_survey_id', $item->survey_id)
                ->where('old_times', $item->times)
                ->where('old_grade_id', $item->grade_id)
                ->first();

            if (!$existingTask) {
                // 构建 assessment_tasks 数据
                $tasksData = [
                    'assessment_schedule_id' => $scheduleId,
                    'assessment_id' => $this->surveytoassessment_id[$item->survey_id],
                    'old_survey_id' => $item->survey_id,
                    'old_times' => $item->times,
                    'old_grade_id' => $item->grade_id,
                ];

                // 插入到 assessment_tasks 表
                DB::table('assessment_tasks')->insert($tasksData);
            }

            $processedCount++;
        }

        // 获取当前最大的session ID用于更新执行日志
        $maxSessionId = $this->getMaxSurveySessionId();

        // 更新执行日志
        $this->updateExecutionLog($maxSessionId, $processedCount);

        Log::info("SchedulesSeeder执行完成", [
            'school_id' => $this->school_id,
            'processed_count' => $processedCount,
            'max_session_id' => $maxSessionId
        ]);
    }
}
