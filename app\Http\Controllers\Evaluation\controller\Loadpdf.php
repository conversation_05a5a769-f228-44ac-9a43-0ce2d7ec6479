<?php
/**
 * Created by PhpStorm.
 * User: Dosion
 * Date: 2019/7/5
 * Time: 13:30
 */
namespace app\evaluation\controller;

use think\Controller;

class Loadpdf extends Controller {
    protected $ReportLogic;
    protected $loadpdf_service;
    public function __construct() {
        parent::__construct();
        $this->ReportLogic = new \app\evaluation\logic\Report();
        $this->TenthGradeReportLogic = new \app\evaluation\logic\TenthGradeReport();
        $this->loadpdf_service = new \app\evaluation\service\Loadpdf();
    }

    public function sixth_grade_personal_pdf() {
        $member_id       = input('member_id');
        $distribution_id = input('distribution_id');
        $data = $this->ReportLogic->personal_report($member_id,$distribution_id);
        $this->view->engine->layout(false);
        return $this->view->fetch('page/question_pdf' , ['data' => $data]);
    }

    public function tenth_grade_personal_pdf() {
        $member_id       = input('member_id');
        $distribution_id = input('distribution_id');
        $data = $this->TenthGradeReportLogic->personal_report($member_id,$distribution_id);
        $this->view->engine->layout(false);
        return $this->view->fetch('page/question_pdf' , ['data' => $data]);
    }

    /**
     * 模块：素养测评-个人报告下载
     * @SWG\Get(path="/evaluation/loadpdf/individual",
     *  tags={"素养测评-个人报告下载:loadpdf"},
     *  summary="个人报告下载",
     *  description="生成个人报告pdf",
     *  produces={"application/json"},
     *  @SWG\Parameter(
     *     in="query",
     *     name="member_id",
     *     type="integer",
     *     description="成员id",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="query",
     *     name="distribution_id",
     *     type="integer",
     *     description="分发id",
     *     required=true,
     *   ),
     *  @SWG\Response(response="0", description="操作成功")
     * )
     */
    public function individual() {
        $data=$this->loadpdf_service->individual();
        if($data[1]==1){
            return json(['data'=>$data[0], 'code'=>0, 'message'=>'生成成功！']);
        }else{
            return json(['data'=>[], 'code'=>-1, 'message'=>'生成失败！']);
        }
    }
}