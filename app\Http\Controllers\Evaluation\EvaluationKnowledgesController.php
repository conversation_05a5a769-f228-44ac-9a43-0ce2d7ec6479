<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use App\Services\Evaluation\KnowledgesService;
use App\Http\Requests\Evaluation\KnowledgeRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * 知识点管理控制器
 */
class EvaluationKnowledgesController extends Controller
{
    protected $knowledgesService;

    public function __construct(KnowledgesService $knowledgesService)
    {
        $this->knowledgesService = $knowledgesService;
    }

    /**
     * 知识点查询
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function knowledges(Request $request): JsonResponse
    {
        try {
            $data = $this->knowledgesService->getKnowledgesList($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取知识点列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 知识点添加
     * 
     * @param KnowledgeRequest $request
     * @return JsonResponse
     */
    public function store(KnowledgeRequest $request): JsonResponse
    {
        try {
            $data = $this->knowledgesService->createKnowledge($request->validated());
            return $this->success($data, '知识点添加成功');
        } catch (\Exception $e) {
            return $this->error('知识点添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 知识点修改
     * 
     * @param KnowledgeRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(KnowledgeRequest $request, int $id): JsonResponse
    {
        try {
            $data = $this->knowledgesService->updateKnowledge($id, $request->validated());
            return $this->success($data, '知识点修改成功');
        } catch (\Exception $e) {
            return $this->error('知识点修改失败: ' . $e->getMessage());
        }
    }

    /**
     * 知识点删除
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->knowledgesService->deleteKnowledge($id);
            return $this->success(null, '知识点删除成功');
        } catch (\Exception $e) {
            return $this->error('知识点删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取知识点树形结构
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function tree(Request $request): JsonResponse
    {
        try {
            $data = $this->knowledgesService->getKnowledgeTree($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取知识点树失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取子知识点
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function children(Request $request): JsonResponse
    {
        try {
            $parentId = $request->input('parent_id', 0);
            $courseId = $request->input('course_id');
            $isHigh = $request->input('is_high');
            $data = $this->knowledgesService->getChildrenKnowledges($parentId, $courseId, $isHigh);
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取子知识点失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新排序
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function sort(Request $request): JsonResponse
    {
        try {
            $this->knowledgesService->updateSort($request->all());
            return $this->success(null, '排序更新成功');
        } catch (\Exception $e) {
            return $this->error('排序更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 移动知识点
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function move(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $parentId = $request->input('parent_id', 0);
            $this->knowledgesService->moveKnowledge($id, $parentId);
            return $this->success(null, '知识点移动成功');
        } catch (\Exception $e) {
            return $this->error('知识点移动失败: ' . $e->getMessage());
        }
    }

    /**
     * 搜索知识点
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $data = $this->knowledgesService->searchKnowledges($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('搜索知识点失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取知识点统计
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $data = $this->knowledgesService->getKnowledgeStatistics($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取知识点统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量操作
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchOperation(Request $request): JsonResponse
    {
        try {
            $action = $request->input('action');
            $ids = $request->input('ids', []);
            
            if (empty($ids)) {
                return $this->error('请选择要操作的知识点');
            }

            $successCount = 0;
            $errors = [];

            foreach ($ids as $id) {
                try {
                    switch ($action) {
                        case 'delete':
                            $this->knowledgesService->deleteKnowledge($id);
                            break;
                        default:
                            throw new \Exception('不支持的操作类型');
                    }
                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "ID {$id}: " . $e->getMessage();
                }
            }

            $message = "成功处理 {$successCount} 个知识点";
            if (!empty($errors)) {
                $message .= "，失败: " . implode('; ', $errors);
            }

            return $this->success([
                'success_count' => $successCount,
                'error_count' => count($errors),
                'errors' => $errors
            ], $message);
        } catch (\Exception $e) {
            return $this->error('批量操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 复制知识点
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function copy(Request $request): JsonResponse
    {
        try {
            $sourceId = $request->input('source_id');
            $targetParentId = $request->input('target_parent_id', 0);
            $nameSuffix = $request->input('name_suffix', '_副本');
            
            $sourceKnowledge = \App\Models\Evaluation\Knowledges::findOrFail($sourceId);
            $newKnowledge = $sourceKnowledge->duplicate($targetParentId, $nameSuffix);
            
            return $this->success($newKnowledge, '知识点复制成功');
        } catch (\Exception $e) {
            return $this->error('知识点复制失败: ' . $e->getMessage());
        }
    }

    /**
     * 导入知识点
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function import(Request $request): JsonResponse
    {
        try {
            // 这里需要实现知识点导入逻辑
            // 可以支持 Excel、CSV 等格式的导入
            
            return $this->success(null, '知识点导入成功');
        } catch (\Exception $e) {
            return $this->error('知识点导入失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出知识点
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $format = $request->input('format', 'excel'); // excel, csv
            $courseId = $request->input('course_id');
            $isHigh = $request->input('is_high');
            
            // 这里需要实现知识点导出逻辑
            // 可以使用 Laravel Excel 等包来实现
            
            return $this->success([
                'download_url' => '',
                'file_name' => "knowledges_" . date('YmdHis') . ".{$format}"
            ], '知识点导出成功');
        } catch (\Exception $e) {
            return $this->error('知识点导出失败: ' . $e->getMessage());
        }
    }
}
