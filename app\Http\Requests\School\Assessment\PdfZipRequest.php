<?php

namespace App\Http\Requests\School\Assessment;

use App\Http\Requests\BaseRequest;

class PdfZipRequest extends BaseRequest
{
    public function rules(): array
    {
        return [
            'assessment_schedule_id' => 'required|integer',
            'assessment_task_id' => 'required|integer',
            'assessment_id' => 'required|integer',
        ];
    }

    public function messages(): array
    {
        return [
            'assessment_schedule_id.required' => '计划ID不能为空',
            'assessment_schedule_id.integer' => '计划ID必须为整数',
            'assessment_task_id.required' => '任务ID不能为空',
            'assessment_task_id.integer' => '任务ID必须为整数',
            'assessment_id.required' => '测评ID不能为空',
            'assessment_id.integer' => '测评ID必须为整数',
        ];
    }
}
