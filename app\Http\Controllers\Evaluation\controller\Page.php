<?php
/**
 * Created by PhpStorm.
 * User: Dosion
 * Date: 2019/1/7
 * Time: 14:43
 */
namespace app\evaluation\controller;
use think\Config;
use app\common\controller\Authentication;

class Page extends Authentication {
    public function __construct() {

        parent::__construct();
    }
    public function index(){
        echo 'THE PAGE NOT EXISTS!!! 非法访问';
    }
    //档案列表
    public function question(){
        return $this->view->fetch('page/question', ['data' =>$this->data]);
    }

    //档案列表
    public function exam(){
        $this->view->engine->layout(false);
        return $this->view->fetch('page/exam', ['data' =>$this->data]);
    }
}