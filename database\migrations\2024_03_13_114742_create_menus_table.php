<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menus', function (Blueprint $table) {
            $table->id();
            $table->integer('parent_id')->default(0)->comment('父级菜单ID');
            $table->string('menu_name', 20)->comment('菜单名称');
            $table->string('url', 100)->comment('菜单链接');
            $table->string('code', 10)->comment('菜单编码');
            $table->integer('parent_code')->default(0)->comment('父级菜单编码');
            $table->string('icon', 100)->nullable()->comment('菜单图标');
            $table->integer('sort')->default(1)->comment('排序');
            $table->string('description', 255)->nullable()->comment('菜单描述');
            $table->tinyInteger('status')->default(1)->comment('状态1正常2禁用');
            $table->tinyInteger('type')->default(3)->comment('菜单类型：1后台管理端，2教育局，3学校');
            $table->timestamps();
            $table->string('creator',20)->nullable()->comment('创建人');
            $table->string('updater',20)->nullable()->comment('最后更新人');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menus');
    }
};
