<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class College extends BaseModel
{
    use HasFactory;

    protected $table = 'College';

    // 指定连接
    protected $connection = 'sqlsrv_gk';

    //主键
    protected $primaryKey = 'ID';

    // 隐藏字段
    protected $hidden = [];

    public function ranks(): HasMany
    {
        return $this->hasMany(CollegeRank::class, 'CollegeId', 'ID')->where('Year', 2025);
    }

    public function tags(): HasMany
    {
        return $this->hasMany(CollegeTag::class, 'CollegeId', 'ID')->orderBy('Sort');
    }

    public function tagsFiltered(): HasMany
    {
        return $this->hasMany(CollegeTag::class, 'CollegeId', 'ID')
            ->where('Influence', '<=', 1)->orderBy('Sort');
    }


    //MajorAssess
    public function majorAssess(): HasMany
    {
        return $this->hasMany(MajorAssess::class, 'CollegeId', 'ID')
            ->where('Num', 4);
    }

    //对应一个college_detail
    public function detail()
    {
        return $this->hasOne(CollegeDetail::class, 'CollegeID', 'ID');
    }

    public function teacher()
    {
        return $this->hasOne(CollegeDetailTeacher::class, 'CollegeID', 'ID');
    }

    public function province()
    {
        return $this->belongsTo(Province::class, 'ProvinceID', 'ID');
    }

    public function city()
    {
        return $this->belongsTo(City::class, 'CityID', 'ID');
    }


}
