<?php

namespace App\Http\Controllers\School\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\System\TeacherViewRequest;
use App\Services\School\System\TeacherViewService;
use Illuminate\Http\Request;

class TeacherViewController extends Controller
{
    protected $teacherViewService;

    public function __construct(TeacherViewService $teacherViewService)
    {
        $this->teacherViewService = $teacherViewService;
    }

    /**
     * 获取教师可查看班级数据
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getViewClasses(TeacherViewRequest $request)
    {
        return $this->teacherViewService->getViewClasses($request);
    }

    /**
     * 设置教师可查看班级数据
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function setViewClasses(TeacherViewRequest $request)
    {
        return $this->teacherViewService->setViewClasses($request);
    }


}