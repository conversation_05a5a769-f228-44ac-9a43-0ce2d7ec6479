<?php

namespace App\Http\Controllers\Partner;

use App\Http\Controllers\Controller;
use App\Models\Partner\Partner;
use App\Services\Partner\PartnerService;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;

class PartnerController extends Controller
{
    use CrudOperations;

    protected string $model = Partner::class;

    // 构造函数
    public function __construct(protected PartnerService $partnerService)
    {
    }

    public function show(Request $request)
    {
        // 获取当前登录用户所属机构id
        $partner_id = $this->getPartnerIdFromRequest($request);

        $record = $this->model::whereId($partner_id)->with(['config','organization'])->first();
        return $this->success($record);
    }

    public function update(Request $request)
    {
        // 获取当前登录用户所属机构id
        $partner_id = $this->getPartnerIdFromRequest($request);
        $this->partnerService->updatePartner($request, $partner_id);
        return $this->message("修改成功");
    }

    public function setConfig(Request $request)
    {
        // TODO
        return $this->success("");
    }

    public function showDetail($id)
    {
        $record = $this->model::with(['organization'])->find($id);
        if (!$record) {
            return $this->notFound('查询对象不存在');
        }
        return $this->success($record);
    }
    

     /**
     * 从请求中获取合作伙伴ID
     */
    protected function getPartnerIdFromRequest(Request $request): int
    {
        return $request->user()->organization->model_id;
    }

}
