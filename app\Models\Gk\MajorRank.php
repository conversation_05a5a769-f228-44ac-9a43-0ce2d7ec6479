<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MajorRank extends BaseModel
{
    use HasFactory;
    
    // 表名
    protected $table = 'MajorRank';
    
    // 指定连接
    protected $connection = 'sqlsrv_gk';
    
    // 主键
    protected $primaryKey = 'Id';
    
    // 表示字段是否自动维护时间戳（如果有 created_at 和 updated_at 字段，默认开启）
    public $timestamps = false;
    
    // 隐藏字段
    protected $hidden = [];
    
    // 定义允许批量赋值的字段
    protected $fillable = [
        'MajorCode',
        'MajorName',
        'Rank',
        'CollegeType',
        'CollegeId',
        'CollegeName',
        'StartRank',
        'CollegeLevel',
        'Year',
        'Level',
        'Score',
        'Source',
    ];
    
    /**
     * 获取关联的学校信息
     */
    public function college(): BelongsTo
    {
        return $this->belongsTo(College::class, 'CollegeId', 'ID');
    }
}