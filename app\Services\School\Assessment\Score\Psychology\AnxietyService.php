<?php

namespace App\Services\School\Assessment\Score\Psychology;

/**
 * 焦虑评估服务类
 * 
 * 该类用于计算学生的焦虑评估结果，包括各维度分数计算
 */
class AnxietyService extends AbstractScoreService
{
    /**
     * 计算焦虑评估结果
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 评估结果数组，包含维度分数
     */
    public function calculate($params): array
    {
        $dimension_scores = $this->calculateScores($params);
        
        // 计算各维度得分
        foreach ($dimension_scores as $key => $dimension) {
            // 维度得分 = (维度内题目得分总和 / 维度题目数量) × 25
            $dimension_scores[$key]['score'] = round($dimension['score'] / $dimension['question_count'] * 25, 1);
            unset($dimension_scores[$key]['question_count']);
        }
        return ['dimensions'=>$dimension_scores];
    }
}