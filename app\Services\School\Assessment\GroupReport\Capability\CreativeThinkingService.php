<?php

namespace App\Services\School\Assessment\GroupReport\Capability;

/**
 * 创造思维倾向评估团体报告服务类
 * 
 * 该类用于生成创造思维倾向评估的团体报告
 */
class CreativeThinkingService extends AbstractCapabilityService
{
    /**
     * 获取配置键名
     * 
     * @return string 配置键名
     */
    protected function getConfigKey(): string
    {
        return 'creative_thinking';
    }

    /**
     * 获取配置分类
     * 
     * @return string 配置分类
     */
    protected function getConfigCategory(): string
    {
        return 'creative_thinking';
    }
}