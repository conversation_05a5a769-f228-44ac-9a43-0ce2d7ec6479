<?php

namespace App\Services\School\Assessment\GroupReport\Career;

use App\Services\School\Assessment\GroupReport\AbstractGroupReportService;

/**
 * 学习生活适应性评估团体报告服务类
 * 
 * 该类用于生成学习生活适应性评估的团体报告，包括适应性表现和各维度分布
 */
class AdaptationService extends AbstractGroupReportService
{
    /**
     * 维度名称列表
     * 
     * @var array
     */
    protected $dimension_names = [
        '学习焦虑', '对人焦虑', '孤独倾向', '自责倾向', 
        '过敏倾向', '身体症状', '恐怖倾向', '冲动倾向'
    ];

    /**
     * 生成评估报告
     * 
     * @param array $params 请求参数
     * @param int $school_id 学校ID
     * @return array 评估报告数据
     */
    public function generateReport(array $params, int $school_id): array
    {
        // 获取评估数据
        $filtered_assessments = $this->getAssessmentData($params, $school_id, $this->getFilterConditions($params));
        $filtered_assessment_with_results = array_filter($filtered_assessments, fn($v) => !empty($v['standard_results']));
        
        // 统计数据
        $class_statistics = $this->calculateClassStatistics($filtered_assessments);
        
        // 由子类实现具体的报告生成逻辑
        return $this->generateReportData($filtered_assessment_with_results, $class_statistics, $params);
    }

    /**
     * 生成报告数据
     * 
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @param array $params 请求参数
     * @return array 报告数据
     */
    protected function generateReportData(
        array $filtered_assessment_with_results, 
        array $class_statistics,
        array $params
    ): array {
        return [
            'participation_count' => $this->getMemberCount($class_statistics),
            'adaptation_performance' => $this->calculateAdaptationPerformance(
                $class_statistics['stats'],
                $filtered_assessment_with_results,
                $class_statistics['category_data']
            ),
            'adaptation_distribution' => $this->calculateDimensionDistribution(
                $filtered_assessment_with_results
            )
        ];
    }

    /**
     * 计算适应性表现数据
     * 
     * @param array $class_stats 班级统计数据
     * @param array $assessments 评估数据
     * @param array $category_data 分类数据
     * @return array 适应性表现数据
     */
    protected function calculateAdaptationPerformance(array $class_stats, array $assessments, array $category_data): array
    {
        $adaptation_stats = $this->calculateAdaptationStats($assessments);
        $good_rate = round($adaptation_stats['good'] / count($assessments) * 100, 2);
        $bad_rate = round($adaptation_stats['bad'] / count($assessments) * 100, 2);

        $default_data = [
            'sort' => sprintf(
                '本次筛选学生中学习适应良好的人数比例为%s%%，不太适应的人数比例为%s%%。在发生适应不良的学生中，男生有%d名，女生有%d名。',
                $good_rate,
                $bad_rate,
                $adaptation_stats['maladapted'][1] ?? 0,
                $adaptation_stats['maladapted'][2] ?? 0
            ),
            'axis' => ['常模对比', '已选数据'],
            'legend' => ['表现良好', '不太适应'],
            'series' => [[73.89, 26.11], [$good_rate, $bad_rate]]
        ];

        $class_data = $this->generateClassPerformanceData($class_stats, $category_data);

        return [
            'header'=>[
                'number' => 2,
                'name' => '学习生活适应性表现',
            ],
            'default' => $default_data,
            'class' => $class_data
        ];
    }

    /**
     * 计算维度分布数据
     * 
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @return array 维度分布数据
     */
    protected function calculateDimensionDistribution(array $filtered_assessment_with_results): array
    {
        $selected_high_ratios = $this->calculateHighRatios($filtered_assessment_with_results);
        $selected_low_ratios = $this->calculateLowRatios($filtered_assessment_with_results);
        
        $sorted_high_ratios = $selected_high_ratios;
        arsort($sorted_high_ratios);
        $top_dimensions = array_keys(array_slice($sorted_high_ratios, 0, 3));

        return [
            'header'=>[
                'number' => 3,
                'name' => '学习生活适应性具体分布',
            ],
            'good' => [
                'sort' => sprintf(
                    '以下是学习生活适应性评估各维度的适应性对比结果，左边代表着适应学生的比例，右边代表不适应学生的比例。根据结果展示请着重关注学生群体的%s、%s、%s情况。',
                    $top_dimensions[0],
                    $top_dimensions[1],
                    $top_dimensions[2]
                ),
                'axis' => $this->dimension_names,
                'legend' => ['常模对比', '已选数据'],
                'dimension_labels' => ['各维度的适应的人数', '各维度的不太适应的人数'],
                'series' => [
                    $this->combineRatios($this->getNormRatios()),
                    $this->combineRatios($selected_high_ratios, $selected_low_ratios)
                ]
            ]
        ];
    }

    /**
     * 生成班级表现数据
     * 
     * @param array $class_stats 班级统计数据
     * @param array $category_data 分类数据
     * @return array 班级表现数据
     */
    private function generateClassPerformanceData(array $class_stats, array $category_data): array
    {
        $class_data = [
            'axis' => [],
            'legend' => ['表现良好', '不太适应'],
            'series' => [],
            'rate' => [],
        ];

        foreach ($class_stats as $class_name => $stats) {
            $class_data['axis'][] = $class_name;

            $class_adaptation_stats = $this->calculateAdaptationStats($category_data[$class_name] ?? []);
            $good = $class_adaptation_stats['good'] ?? 0;
            $bad = $class_adaptation_stats['bad'] ?? 0;
            $class_data['series'][] = [$good, $bad];
            $class_data['rate'][] = [round($good / $stats['total'], 2) * 100, round($bad / $stats['total'], 2) * 100];
        }

        return $class_data;
    }

    /**
     * 计算高比例
     * 
     * @param array $data 评估数据
     * @return array 高比例数据
     */
    private function calculateHighRatios(array $data): array
    {
        return $this->calculateDimensionRatios($data, fn($score) => $score > 8);
    }

    /**
     * 计算低比例
     * 
     * @param array $data 评估数据
     * @return array 低比例数据
     */
    private function calculateLowRatios(array $data): array
    {
        return $this->calculateDimensionRatios($data, fn($score) => $score <= 8);
    }

    /**
     * 计算维度比例
     * 
     * @param array $data 评估数据
     * @param callable $condition 条件函数
     * @return array 维度比例数据
     */
    private function calculateDimensionRatios(array $data, callable $condition): array
    {
        $result = array_fill_keys($this->dimension_names, 0);

        $count = count($data);
        
        if ($count === 0) {
            return $result;
        }

        foreach ($data as $item) {
            if (empty($item['standard_results'])) {
                continue;
            }
            
            $scores = json_decode($item['standard_results'], true);
            foreach ($scores['dimensions'] as $dimension) {
                if ($condition($dimension['score'])) {
                    $result[$dimension['name']]++;
                }
            }
        }

        return array_map(
            fn($value) => round(($value / $count * 100), 2),
            $result
        );
    }

    /**
     * 计算适应性统计数据
     * 
     * @param array $assessments 评估数据
     * @return array 适应性统计数据
     */
    private function calculateAdaptationStats(array $assessments): array
    {
        $stats = [
            'bad' => 0,
            'good' => 0,
            'maladapted' => [],
            'adapted' => []
        ];
        foreach ($assessments as $item) {
            $results = json_decode($item['standard_results'], true);
            $gender = $item['gender'];

            if ($results['total_score'] > 65) {
                $stats['bad']++;
                $stats['maladapted'][$gender] = ($stats['maladapted'][$gender] ?? 0) + 1;
            } else {
                $stats['good']++;
                $stats['adapted'][$gender] = ($stats['adapted'][$gender] ?? 0) + 1;
            }
        }

        return $stats;
    }

    /**
     * 获取常模比例
     * 
     * @return array 常模比例数据
     */
    private function getNormRatios(): array
    {
        return [
            'high' => [80.08, 82.43, 90.62, 83.43, 95.45, 83.03, 81.35, 83.42],
            'low' => [19.92, 17.57, 9.38, 16.57, 4.55, 16.97, 18.65, 16.58]
        ];
    }

    /**
     * 合并比例数据
     * 
     * @param array $high_ratios 高比例数据
     * @param array|null $low_ratios 低比例数据
     * @return array 合并后的比例数据
     */
    private function combineRatios(array $high_ratios, ?array $low_ratios = null): array
    {
        if ($low_ratios === null) {
            return array_map(fn($high, $low) => [$high, $low], 
                $high_ratios['high'], 
                $high_ratios['low']
            );
        }

        return array_map(fn($high, $low) => [$high, $low], 
            array_values($high_ratios), 
            array_values($low_ratios)
        );
    }
}