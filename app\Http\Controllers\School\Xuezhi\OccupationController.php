<?php

namespace App\Http\Controllers\School\Xuezhi;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\Xuezhi\OccupationRequest;
use App\Http\Resources\School\Xuezhi\OccupationCollection;
use App\Http\Resources\School\Xuezhi\OccupationResource;
use App\Services\School\Xuezhi\OccupationService;

use Illuminate\Http\JsonResponse;

class OccupationController extends Controller
{

    protected OccupationService $service;

    public function __construct(OccupationService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取职业列表
     *
     * @param OccupationRequest $request
     * @return JsonResponse
     */
    public function index(OccupationRequest $request): JsonResponse
    {
        $occupationName = $request->input('occupation_name');

        $list = $this->service->getOccupationList($occupationName);
        
        // 使用修改后的 OccupationCollection 处理数组数据
        if ($occupationName) {
            // 如果是按名称搜索，只返回LevelType=3的数据，处理平铺列表
            $data = collect($list)
                ->filter(function ($item) {
                    // 确保只返回LevelType=3的数据
                    $levelType = is_array($item) ? ($item['LevelType'] ?? null) : ($item->LevelType ?? null);
                    return $levelType == 3;
                })
                ->map(function ($item) {
                    return new OccupationCollection($item);
                });
        } else {
            // 如果是获取层级结构，递归处理
            $data = collect($list)->map(function ($item) {
                return new OccupationCollection($item);
            });
        }
        
        return $this->success($data);
    }

    /**
     * 获取职业详情
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $detail = $this->service->getOccupationDetail($id);

        if (!$detail) {
            return $this->notFound('职业信息不存在');
        }

        return $this->success(new OccupationResource($detail));
    }
}
