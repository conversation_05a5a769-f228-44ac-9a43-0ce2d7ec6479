<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Response;
use App\Traits\ApiResponse;

class ApiAiUserCheck
{
    use ApiResponse;

    /**
     * 处理传入的请求，验证用户是否有权限访问
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        $Authorization = $request->header('Authorization');
        if (!$Authorization)
            return $this->error('缺少Authorization', 401);

        try {
            $url_type = $request->header('Urltype');
            $url = 'https://ai.yishengya.cn/api/user';
            if ($url_type == 'demo') {
                $url = 'https://ai-demo.yishengya.cn/api/user';
            }

            $response = Http::withToken($Authorization)->get($url);
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();
            
            // 如果响应头里有Authorization，则当前token失效，获取刷新后的token
            $token = $response->header('Authorization');
            // 根据响应进行处理
            if ($statusCode === 200) {
                $user_provinceId = json_decode($body)->data->school_province_id;
                $provinceId = $request['province_id'];
                if ($provinceId != $user_provinceId) {
                    return $this->error('当前用户省份与数据查询省份不匹配错误');
                }
            } else {
                return $this->error(json_decode($body)->message);
            }
        } catch (Exception $e) {
            return $this->error('外部服务请求异常');
        }
        
        if($token){
            $response = $next($request);
            // 返回的响应头中添加新的Authorization，确保前端可以获取到
            $response->header('Access-Control-Expose-Headers', "Authorization");
            return $response->header('Authorization', $token);
        }
        return $next($request);
    }
    
}