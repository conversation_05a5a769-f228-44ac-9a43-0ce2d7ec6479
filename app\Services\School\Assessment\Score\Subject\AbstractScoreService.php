<?php

namespace App\Services\School\Assessment\Score\Subject;

use App\Repositories\AnswerRepository;
use App\Services\BaseService;
use App\Services\School\Assessment\Score\ScoreServiceInterface;

/**
 * 学科测评抽象评分服务
 * 
 * 提供学科测评相关的基础评分功能
 */
abstract class AbstractScoreService extends BaseService implements ScoreServiceInterface
{
    public function __construct(
        protected AnswerRepository $answerRepository
    ) {
        
    }

    /**
     * 计算学科测评分数
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 维度分数数组
     */
    public function calculateScores(array $params): array
    {
        $data = $this->answerRepository->getSubjectDimensionScores($params);

        return $data;
    }
}
