<?php

namespace App\Models\Admin;

use App\Models\BaseModel;

class AssessmentCareerMiTemplate extends BaseModel
{
    /**
     * 关联的数据表名称
     *
     * @var string
     */
    protected $table = 'assessment_career_mi_templates';

    /**
     * 可以批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'intelligence_type',
        'type',
        'grade_type',
        'content',
        'update_now'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */

}