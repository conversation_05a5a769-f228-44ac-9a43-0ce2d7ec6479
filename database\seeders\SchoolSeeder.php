<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SchoolSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';
    protected string $defaultDate = '2025-01-01';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school_id = $this->school_id;
        $info = DB::connection($this->connect)->table('school')->where('id', $school_id)->first();
        // 移除省份字符串里最后一个字 省 这个字符
        $info->province = str_replace('省', '', str_replace('市', '', $info->province));
        // 同步学校基本数据
        $school_data = [
            'id' => $info->id,
            'name' => $info->name,
            'address' => $info->address,
            'province' =>  $info->province,
            'city' => $info->city,
            'district' => $info->district,
            'date_start' => empty($info->add_time)?$this->defaultDate:$info->add_time,
            'date_due' => ($info->date_due == '0000-00-00')?$this->defaultDate:$info->date_due,
            'is_link_gk' => $info->first_zhiyuan,
            'created_at' => date('Y-m-d H:i:s', time()),
        ];
        // 新增学校数据
        DB::table('schools')->insert($school_data);
        // 新增机构数据
        $orgId = DB::table('organizations')->insertGetId([
            'model_type' => 'school',// 学校
            'model_id' => $school_id,
            'org_name' => $info->name,
            'created_at' => date('Y-m-d H:i:s', time())]);
        // 同步校区
        $campuses = DB::connection($this->connect)->table('school_district')
            ->where('school_id', $school_id)->where('step', 0)->get();
        $campuses->each(function ($item) use ($school_id) {
            $district_data = [
                'id' => $item->id,// 保留原校区ID
                'school_id' => $school_id,
                'campus_name' => $item->campus_name,
                'type' => $item->school_type,
                'is_main' => $item->main_campus ?? 0,
                'created_at' => date('Y-m-d H:i:s', time()),
                'updated_at' => date('Y-m-d H:i:s', time()),
            ];
            DB::table('school_campuses')->insert($district_data);
        });
    }

}
