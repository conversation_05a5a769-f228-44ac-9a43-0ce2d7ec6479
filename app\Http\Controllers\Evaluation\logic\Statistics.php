<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */

namespace app\evaluation\logic;

use app\backend\logic\Backend as BackendLogic;
use app\evaluation\logic\Loadpdf as LoadpdfLogic;
use app\evaluation\model\SurveyUserSession as SurveyUserSessionModel;
use app\evaluation\model\EvaluationCareer as EvaluationCareerModel;
use think\Db;
use ZipArchive;

class Statistics
{
    protected $user;
    protected $loadpdfLogic;
    protected $backendLogic;

    public function __construct()
    {
        $this->user = get_user();
    }

    //和统计分析的列表接口一样，防止后面可能会改，所以先做成两个接口
    public function get_student_report()
    {
        $school_district = input('school_district') ? input('school_district') : '';
        $grade_id        = input('grade_id') ? input('grade_id') : '';
        $class_ids       = input('class_ids') ? input('class_ids') : '';
        $gender          = input('gender') ? input('gender') : '';
        $content         = input('content') ? input('content') : '';
        $pageSize        = input('pagesize') ? input('pagesize') : 10;
        $pageNumber      = input('page') ? input('page') : 1;
        $distribution_id = input('distribution_id') ? input('distribution_id') : '';
        $title           = input('title') ? input('title') : '';
        $participated    = input('participated') ? input('participated') : '';

        $where['a.school_id']         = $this->user['school_id'];
        $where['a.step']              = 0;
        $where['distribution.status'] = 0;
        if (!empty($school_district)) $where['a.school_district'] = $school_district;
        if (!empty($grade_id)) $where['a.grade_id'] = $grade_id;
        if (!empty($gender)) $where['a.gender'] = $gender;
        if (!empty($class_ids)) $where['a.class_id'] = ['in', $class_ids];
        if (!empty($content)) $where['a.student_no|a.name'] = ['like', '%' . $content . '%'];
        if ($distribution_id) $where['distribution.id'] = $distribution_id;
        if ($title) $where['distribution.title'] = $title;
        //请注意！！！content可以搜索学号，姓名，试卷名称！！！且优先搜索学号姓名，基于此才有如下逻辑
        //1.先查询学生列表，如果学生列表不为空，content就不往下匹配了
        //2.如果学生列表为空，则去匹配试卷名称
        //3.如果试卷也为空，此接口就直接返回空
        //4.如果试卷不为空，则需要避开content重新查询学生列表
        //1.学生列表
        $student_list = Db::name('student')->alias('a')
            ->join('school sch', 'a.school_id = sch.id')
            ->join('school_district g', 'a.school_district = g.id')
            ->join('grade b', 'a.grade_id = b.id')
            ->join('class c', 'a.class_id = c.id')
            ->join('evaluation_distribution distribution', 'find_in_set(a.member_id,distribution.member_ids)')
            ->field('a.member_id,a.student_no,(case when a.gender=1 then "男" else "女" end) as gender,a.name as student_name,b.name as grade_year,b.grade_name,c.name as class_name,sch.name as school_name,g.campus_name as school_district_name,distribution.id as distribution_id,distribution.title')
            ->where($where)
            ->group('a.member_id,distribution.id')
            ->order('a.id desc')
            ->select();

        //2
        if($distribution_id) $paper_where['distribution.id'] = $distribution_id;
        $paper_where['detail.status']           = 0;
        $paper_where['distribution.school_ids'] = $this->user['school_id'];
        $shengya_paper                         = true;//默认生涯测评存在，也就是true
        if (!empty($content) && empty($student_list)) {
            $paper_where['paper.paper_name'] = ['like', '%' . $content . '%'];
            //判断keyword是否命中生涯测评，比如传了个'生涯'
            if (strpos('生涯测评', $content) !== false) {
                $shengya_paper = true;
            } else {
                $shengya_paper = false;
            }
        }
        //查询测评与试卷对应关系
        $paper_info = Db::name('evaluation_distribution_detail detail')
            ->join('evaluation_distribution distribution', 'detail.distribution_id = distribution.id')
            ->join('evaluation_papers paper', 'detail.paper_id = paper.id')
            ->field('distribution.id as distribution_id,paper.id as paper_id,paper.paper_name,paper.grade_id')
            ->where($paper_where)
            ->select();

        $distribution_id_with_grade_id = Db::name('evaluation_distribution_detail detail')
            ->join('evaluation_papers papers', 'detail.paper_id = papers.id')
            ->whereIn('distribution_id', function($query) {
                $query->name('evaluation_distribution')
                    ->where('school_ids', $this->user['school_id'])
                    ->field('id');
            })
            ->column('distribution_id,grade_id');

        //生涯测评，也算作一个试卷
        $whe['session.time_error'] = 0;
        $whe['session.is_delete'] = 0;
        $whe['session.is_abnormal'] = 0;
        $whe['sur.survey_type'] = ['in', [25, 29]];
        if($distribution_id) $whe['relation.distribution_id'] = $distribution_id;
        $shengya_info = Db::name('survey sur')
            ->join('survey_user_session session', 'sur.id = session.survey_id')
            ->join('evaluation_distribution_shengya_relation relation', 'relation.session_id = session.session_id')
            ->where($whe)
            ->column('session.member_id,session.used_time,session.create_time as create_at,session.session_id,session.pdf_url');

        //3
        if (!empty($content) && empty($student_list) && empty($paper_info) && $shengya_paper == false) return [];

        //4
        if (!empty($content) && empty($student_list) && (!empty($paper_info) || $shengya_paper == true)) {
            unset($where['a.student_no|a.name']);
            $student_list = Db::name('student')->alias('a')
                ->join('school sch', 'a.school_id = sch.id')
                ->join('school_district g', 'a.school_district = g.id')
                ->join('grade b', 'a.grade_id = b.id')
                ->join('class c', 'a.class_id = c.id')
                ->join('evaluation_distribution distribution', 'find_in_set(a.member_id,distribution.member_ids)')
                ->field('a.member_id,a.student_no,(case when a.gender=1 then "男" else "女" end) as gender,a.name as student_name,b.name as grade_year,b.grade_name,c.name as class_name,sch.name as school_name,g.campus_name as school_district_name,distribution.id as distribution_id,distribution.title')
                ->where($where)
                ->group('a.member_id,distribution.id')
                ->order('a.id desc')
                ->select();
        }

        //将paper_name塞入学生列表
        foreach ($student_list as $key => $value) {
            $student_list[$key]['fit_grade_id'] = $distribution_id_with_grade_id[$value['distribution_id']];
            if (empty($content) || $shengya_paper == true) $student_list[$key]['papers'][]['paper_name'] = '生涯测评';
            foreach ($paper_info as $pv) {
                if($value['distribution_id'] == $pv['distribution_id']){
                    $student_list[$key]['papers'][] = $pv;
                }
            }
        }

        //答卷记录列表
        if (!empty($school_district)) $param['a.school_district_id'] = $school_district;
        $param['a.school_id'] = $this->user['school_id'];
        if (!empty($grade_id)) $param['a.grade_id'] = $grade_id;
        if (!empty($class_ids)) $param['a.class_id'] = ['in', $class_ids];
        if (!empty($distribution_id)) $param['a.distribution_id'] = $distribution_id;
        $answer_log = Db::name('evaluation_student_log')->alias('a')
            ->field('a.*,p.paper_name,p.has_subjective,f.name as teacher_name,a.score')
            ->join('evaluation_papers p', 'a.paper_id = p.id')
            ->join('member f', 'a.check_member_id = f.id', 'left')
            ->where($param)
            ->order('a.id desc')
            ->select();
        $answer_log_arr = [];
        foreach ($answer_log as $ank => $anv) {
            $answer_log_arr[$anv['distribution_id'] . '-' . $anv['paper_id'] . '-' . $anv['member_id']] = $anv;
        }
        //将测评或答题记录塞入学生列表
        foreach ($student_list as $key => $value) {
            $ifunset = 1;
            if($value['fit_grade_id'] == 10){
                foreach ($value['papers'] as $k => $v) {
                    if ($v['paper_name'] == '生涯测评') {
                        if (isset($shengya_info[$value['member_id']]) && !empty($shengya_info[$value['member_id']])) {
                            $student_list[$key]['papers'][$k]['create_at']  = $shengya_info[$value['member_id']]['create_at'];
                            $student_list[$key]['papers'][$k]['used_time']  = $shengya_info[$value['member_id']]['used_time'];
                            $student_list[$key]['papers'][$k]['session_id'] = $shengya_info[$value['member_id']]['session_id'];
                            $student_list[$key]['papers'][$k]['pdf_url']    = $shengya_info[$value['member_id']]['pdf_url'];
                        } else {
                            $student_list[$key]['papers'][$k]['create_at'] = '未完成';
                            $student_list[$key]['papers'][$k]['used_time'] = null;
                            if ($ifunset == 1) $ifunset = 2;
                        }
                    }
                }
            }else{
                foreach ($value['papers'] as $k => $v) {
                    if ($v['paper_name'] == '生涯测评') {
                        if (isset($shengya_info[$value['member_id']]) && !empty($shengya_info[$value['member_id']])) {
                            $student_list[$key]['papers'][$k]['create_at']  = $shengya_info[$value['member_id']]['create_at'];
                            $student_list[$key]['papers'][$k]['used_time']  = $shengya_info[$value['member_id']]['used_time'];
                            $student_list[$key]['papers'][$k]['session_id'] = $shengya_info[$value['member_id']]['session_id'];
                            $student_list[$key]['papers'][$k]['pdf_url']    = $shengya_info[$value['member_id']]['pdf_url'];
                        } else {
                            $student_list[$key]['papers'][$k]['create_at'] = '未完成';
                            $student_list[$key]['papers'][$k]['used_time'] = null;
                            if ($ifunset == 1) $ifunset = 2;
                        }
                    } else {
                        if (isset($answer_log_arr[$v['distribution_id'] . '-' . $v['paper_id'] . '-' . $value['member_id']]) && !empty($answer_log_arr[$v['distribution_id'] . '-' . $v['paper_id'] . '-' . $value['member_id']])) {
                            $student_list[$key]['papers'][$k]['create_at'] = $answer_log_arr[$v['distribution_id'] . '-' . $v['paper_id'] . '-' . $value['member_id']]['create_at'];
                            $student_list[$key]['papers'][$k]['used_time'] = $answer_log_arr[$v['distribution_id'] . '-' . $v['paper_id'] . '-' . $value['member_id']]['used_time'];
                            $student_list[$key]['papers'][$k]['score'] = $answer_log_arr[$v['distribution_id'] . '-' . $v['paper_id'] . '-' . $value['member_id']]['score'];
                        } else {
                            $student_list[$key]['papers'][$k]['create_at'] = '未完成';
                            $student_list[$key]['papers'][$k]['used_time'] = null;
                            if ($ifunset == 1) $ifunset = 2;
                        }
                    }
                }
            }


            if ($participated == 1) {
                if ($ifunset == 2) unset($student_list[$key]);
            } elseif ($participated == 2) {
                if ($ifunset == 1) unset($student_list[$key]);
            }
        }
        return pageing($student_list, $pageSize, $pageNumber);
    }

    public function integrated_statistics_student()
    {
        $school_district = input('school_district') ? input('school_district') : '';
        $grade_id        = input('grade_id') ? input('grade_id') : '';
        $class_ids       = input('class_ids') ? input('class_ids') : '';
        $gender          = input('gender') ? input('gender') : '';
        $keyword         = input('keyword') ? input('keyword') : '';
        $pageSize        = input('pagesize') ? input('pagesize') : 10;
        $pageNumber      = input('page') ? input('page') : 1;
        $distribution_id = input('distribution_id') ? input('distribution_id') : '';
        $participated    = input('participated') ? input('participated') : '';

        $where['a.school_id']         = $this->user['school_id'];
        $where['a.step']              = 0;
        $where['distribution.status'] = 0;
        if (!empty($school_district)) $where['a.school_district'] = $school_district;
        if (!empty($grade_id)) $where['a.grade_id'] = $grade_id;
        if (!empty($gender)) $where['a.gender'] = $gender;
        if (!empty($class_ids)) $where['a.class_id'] = ['in', $class_ids];
        if (!empty($keyword)) $where['a.student_no|a.name'] = ['like', '%' . $keyword . '%'];
        if ($distribution_id) $where['distribution.id'] = $distribution_id;
        //请注意！！！keyword可以搜索学号，姓名，试卷名称！！！且优先搜索学号姓名，基于此才有如下逻辑
        //1.先查询学生列表，如果学生列表不为空，keyword就不往下匹配了
        //2.如果学生列表为空，则去匹配试卷名称
        //3.如果试卷也为空，此接口就直接返回空
        //4.如果试卷不为空，则需要避开keyword重新查询学生列表
        //1.学生列表
        $student_list = Db::name('student')->alias('a')
            ->join('school sch', 'a.school_id = sch.id')
            ->join('school_district g', 'a.school_district = g.id')
            ->join('grade b', 'a.grade_id = b.id')
            ->join('class c', 'a.class_id = c.id')
            ->join('evaluation_distribution distribution', 'find_in_set(a.member_id,distribution.member_ids)')
            ->field('a.member_id,a.student_no,(case when a.gender=1 then "男" else "女" end) as gender,a.name as student_name,b.name as grade_year,b.grade_name,c.name as class_name,sch.name as school_name,g.campus_name as school_district_name,distribution.id as distribution_id,distribution.title')
            ->where($where)
            ->group('a.member_id,distribution.id')
            ->order('a.id desc')
            ->select();

        //2
        if($distribution_id) $paper_where['distribution.id'] = $distribution_id;
        $paper_where['detail.status']           = 0;
        $paper_where['distribution.school_ids'] = $this->user['school_id'];
        $shengya_paper                         = true;//默认生涯测评存在，也就是true
        if (!empty($keyword) && empty($student_list)) {
            $paper_where['paper.paper_name'] = ['like', '%' . $keyword . '%'];
            //判断keyword是否命中生涯测评，比如传了个'生涯'
            if (strpos('生涯测评', $keyword) !== false) {
                $shengya_paper = true;
            } else {
                $shengya_paper = false;
            }
        }
        //查询测评与试卷对应关系
        $paper_info = Db::name('evaluation_distribution_detail detail')
            ->join('evaluation_distribution distribution', 'detail.distribution_id = distribution.id')
            ->join('evaluation_papers paper', 'detail.paper_id = paper.id')
            ->field('distribution.id as distribution_id,paper.id as paper_id,paper.paper_name')
            ->where($paper_where)
            ->select();

        //生涯测评，也算作一个试卷
        $whe['session.time_error']  = 0;
        $whe['session.is_delete']   = 0;
        $whe['session.is_abnormal'] = 0;
        $whe['sur.survey_type']     = 25;
        if($distribution_id) $whe['relation.distribution_id'] = $distribution_id;
        $shengya_info = Db::name('survey sur')
            ->join('survey_user_session session', 'sur.id = session.survey_id')
            ->join('evaluation_distribution_shengya_relation relation', 'relation.session_id = session.session_id')
            ->where($whe)
            ->column('relation.member_id,session.used_time,session.create_time as create_at');

        //3
        if (!empty($keyword) && empty($student_list) && empty($paper_info) && $shengya_paper == false) return [];

        //4
        if (!empty($keyword) && empty($student_list) && (!empty($paper_info) || $shengya_paper == true)) {
            unset($where['a.student_no|a.name']);
            $student_list = Db::name('student')->alias('a')
                ->join('school sch', 'a.school_id = sch.id')
                ->join('school_district g', 'a.school_district = g.id')
                ->join('grade b', 'a.grade_id = b.id')
                ->join('class c', 'a.class_id = c.id')
                ->join('evaluation_distribution distribution', 'find_in_set(a.member_id,distribution.member_ids)')
                ->field('a.member_id,a.student_no,(case when a.gender=1 then "男" else "女" end) as gender,a.name as student_name,b.name as grade_year,b.grade_name,c.name as class_name,sch.name as school_name,g.campus_name as school_district_name,distribution.id as distribution_id,distribution.title')
                ->where($where)
                ->group('a.member_id,distribution.id')
                ->order('a.id desc')
                ->select();
        }

        //将paper_name塞入学生列表
        foreach ($student_list as $key => $value) {
            if (empty($keyword) || $shengya_paper == true) $student_list[$key]['papers'][]['paper_name'] = '生涯测评';
            foreach ($paper_info as $pk => $pv) {
                if($value['distribution_id'] == $pv['distribution_id']){
                    $student_list[$key]['papers'][] = $pv;
                }
            }
        }

        //答卷记录列表
        if (!empty($school_district)) $param['a.school_district_id'] = $school_district;
        $param['a.school_id'] = $this->user['school_id'];
        if (!empty($grade_id)) $param['a.grade_id'] = $grade_id;
        if (!empty($class_ids)) $param['a.class_id'] = ['in', $class_ids];
        if (!empty($distribution_id)) $param['a.distribution_id'] = $distribution_id;
        $answer_log     = Db::name('evaluation_student_log')->alias('a')
            ->field('a.*,p.paper_name,p.has_subjective,f.name as teacher_name,a.score')
            ->join('evaluation_papers p', 'a.paper_id = p.id')
            ->join('member f', 'a.check_member_id = f.id', 'left')
            ->where($param)
            ->order('a.id desc')
            ->select();
        $answer_log_arr = [];
        foreach ($answer_log as $ank => $anv) {
            $answer_log_arr[$anv['paper_id'] . '-' . $anv['member_id']] = $anv;
        }
        //将测评或答题记录塞入学生列表
        foreach ($student_list as $key => $value) {
            $ifunset = 1;
            foreach ($value['papers'] as $k => $v) {
                if ($v['paper_name'] == '生涯测评') {
                    if (isset($shengya_info[$value['member_id']]) && !empty($shengya_info[$value['member_id']])) {
                        $student_list[$key]['papers'][$k]['create_at'] = $shengya_info[$value['member_id']]['create_at'];
                        $student_list[$key]['papers'][$k]['used_time'] = $shengya_info[$value['member_id']]['used_time'];
                    } else {
                        $student_list[$key]['papers'][$k]['create_at'] = '未完成';
                        $student_list[$key]['papers'][$k]['used_time'] = '未完成';
                        if ($ifunset == 1) $ifunset = 2;
                    }
                } else {
                    if (isset($answer_log_arr[$v['paper_id'] . '-' . $value['member_id']]) && !empty($answer_log_arr[$v['paper_id'] . '-' . $value['member_id']])) {
                        $student_list[$key]['papers'][$k]['create_at'] = $answer_log_arr[$v['paper_id'] . '-' . $value['member_id']]['create_at'];
                        $student_list[$key]['papers'][$k]['used_time'] = $answer_log_arr[$v['paper_id'] . '-' . $value['member_id']]['used_time'];
                        $student_list[$key]['papers'][$k]['score'] = $answer_log_arr[$v['paper_id'] . '-' . $value['member_id']]['score'];
                    } else {
                        $student_list[$key]['papers'][$k]['create_at'] = '未完成';
                        $student_list[$key]['papers'][$k]['used_time'] = '未完成';
                        if ($ifunset == 1) $ifunset = 2;
                    }
                }
            }

            if ($participated == 1) {
                if ($ifunset == 2) unset($student_list[$key]);
            } elseif ($participated == 2) {
                if ($ifunset == 1) unset($student_list[$key]);
            }
        }
        return pageing($student_list, $pageSize, $pageNumber);
    }

    public function integrated_statistics_head(){
        $school_district = input('school_district')?input('school_district'):'';
        $grade_id = input('grade_id')?input('grade_id'):'';
        $class_ids = input('class_ids')?input('class_ids'):'';
        $distribution_id = input('distribution_id')?input('distribution_id'):'';

        if(!empty($class_ids)){
            if (!empty($school_district)) $where['school_district'] = $school_district;
            if (!empty($grade_id)) $where['grade_id'] = $grade_id;
            $where['class_id'] = ['in', $class_ids];
            if (!empty($school_district)) $param['school_district'] = $school_district;
            if (!empty($grade_id)) $param['grade_id'] = $grade_id;
            $param['class_id'] = ['in', $class_ids];
        }
        //学生数量
        $where['school_id'] = $this->user['school_id'];
        $where['step'] = 0;
        $member_list = Db::name('student')->where($where)->group('member_id')->column('member_id');

        //查询测评与试卷对应关系
        $paper_where['detail.distribution_id'] = $distribution_id;
        $paper_where['detail.status'] = 0;
        $paper_info = Db::name('evaluation_distribution_detail detail')
            ->join('evaluation_papers paper','detail.paper_id = paper.id')
            ->field('paper.id as paper_id,paper.paper_name')
            ->where($paper_where)
            ->select();

        //生涯测评，也算作一个试卷
        $whe['session.time_error']       = 0;
        $whe['session.is_delete']        = 0;
        $whe['session.is_abnormal']      = 0;
        $whe['sur.survey_type']          = 25;
        $whe['relation.status']          = 0;
        $whe['relation.distribution_id'] = $distribution_id;
        $shengya_info = Db::name('survey sur')
            ->join('survey_user_session session','sur.id = session.survey_id')
            ->join('evaluation_distribution_shengya_relation relation','relation.session_id = session.session_id')
            ->where($whe)
            ->column('relation.member_id,session.used_time');
        if(empty($shengya_info)){
            $shengya_avg = 0;
        }else{
            $s = 0;
            foreach ($shengya_info as $sk => $sv){
                $s += $sv;
            }
            $shengya_avg = round($s/count($shengya_info));
        }

        //答题记录
        $param['distribution_id'] = $distribution_id;
        $param['status'] = 0;
        $log_data = Db::name('evaluation_student_log log')
            ->where($param)
            ->field('member_id,paper_id,used_time')
            ->select();
        foreach ($log_data as $lk => $lv){
            $log_data_map[$lv['member_id'].'-'.$lv['paper_id']] = $lv;
        }
        //初始值为0，生涯测评加所有试卷做完了，才算完成
        $i = 0;
        foreach ($member_list as $mk => $mv){
            //只要有一个没完成就跳出本次循环
            if(!isset($shengya_info[$mv])) continue;
            foreach ($paper_info as $pk => $pv){
                if(!isset($log_data_map[$mv.'-'.$pv['paper_id']])) continue 2;//使用continue 2语句会跳过内层循环的剩余迭代，并继续执行外层循环的下一个迭代
            }
            $i++;//当生涯和所有的paper_id都完成时才会执行此项
        }
        foreach ($paper_info as $pk => $pv){
            $j = 0;
            $paper_info[$pk]['used_time'] = 0;
            foreach ($log_data as $logk => $logv){
                if($pv['paper_id'] == $logv['paper_id']){
                    $paper_info[$pk]['used_time'] += $logv['used_time'];
                    $j++;
                }
            }
            $paper_info[$pk]['avg_time'] = $j == 0 ? 0:round($paper_info[$pk]['used_time']/$j);
        }

        $data['member_num']   = count($member_list);
        $data['complete_num'] = $i;
        $data['statistics'] = array_merge([0=>['paper_name' => '生涯测评','avg_time'=>$shengya_avg]],$paper_info);
        return $data;
    }

    public function statistics_career()
    {

        $school_id = input('school_id') ?? 979;
        $survey_id = input('survey_id') ?? 464;
        $model = new SurveyUserSessionModel();
        $where['is_delete']   = 0;
        $where['is_abnormal'] = 0;
        $where['time_error']  = 0;
        $where['survey_id']   = $survey_id;
        $where['school_id']   = $school_id;
        $data = $model->field('id, survey_id, session_id,member_id,result,school_id')->where($where)->select();
        $data = to_arr($data);

        // Process all data
        $processedData = $this->Processing_data($data);

        foreach ($processedData as $k => $v){
            $career_data[$k] = [
                "survey_id" => $v['survey_id'],
                "session_id" => $v['session_id'],
                "member_id" => $v['member_id'],
                "result" => $v['result'],
                "school_id" => $v['school_id'],
                "career_focus_avg" => $v['average_0'],
                "career_focus_rank" => $v['percentile_rank_0'],
                "career_control_avg" => $v['average_1'],
                "career_control_rank" => $v['percentile_rank_1'],
                "career_curiosity_avg" => $v['average_2'],
                "career_curiosity_rank" => $v['percentile_rank_2'],
                "career_confidence_avg" => $v['average_3'],
                "career_confidence_rank" => $v['percentile_rank_3'],
            ];
        }

        // Group data by school_id
        $groupedData = array_reduce($data, function ($result, $item) {
            $result[$item['school_id']][] = $item;
            return $result;
        }, []);

        foreach ($groupedData as $school_id => $group) {
            // Process each group
            $processedGroup = $this->Processing_data($group);
            foreach ($processedGroup as $k => $v){
                // Find the corresponding key in $career_data
                $key = array_search($v['session_id'], array_column($career_data, 'session_id'));
                if ($key !== false) {
                    $career_data[$key]["school_career_focus_avg"] = $v['average_0'];
                    $career_data[$key]["school_career_focus_rank"] = $v['percentile_rank_0'];
                    $career_data[$key]["school_career_control_avg"] = $v['average_1'];
                    $career_data[$key]["school_career_control_rank"] = $v['percentile_rank_1'];
                    $career_data[$key]["school_career_curiosity_avg"] = $v['average_2'];
                    $career_data[$key]["school_career_curiosity_rank"] = $v['percentile_rank_2'];
                    $career_data[$key]["school_career_confidence_avg"] = $v['average_3'];
                    $career_data[$key]["school_career_confidence_rank"] = $v['percentile_rank_3'];
                }
            }
        }

        $career_model = new EvaluationCareerModel();
        $career_model->saveAll($career_data);
        apiReturn($career_model);
    }

    public function Processing_data($data)
    {
        $scores = [[], [], [], []];

        foreach ($data as $item) {
            $results = json_decode($item['result']);
            $itemScores = $results[0];
            foreach ($itemScores as $index => $score) {
                $scores[$index][] = $score;
            }
        }

        for ($i = 0; $i < 4; $i++) {
            $average = array_sum($scores[$i]) / count($scores[$i]);
            rsort($scores[$i]); // Sort scores in descending order
            foreach ($data as &$item) {
                $results = json_decode($item['result']);
                $itemScores = $results[0];
                $rank = array_search($itemScores[$i], $scores[$i]) + 1;
                $percentileRank = ($rank / count($scores[$i])) * 100;
                $item["average_$i"] = $average;
                $item["percentile_rank_$i"] = $percentileRank;

            }

        }
        return $data;
    }

    public function batch_create_evaluation_pdf(){
        $school_id = input('school_id') ?? 979;
        $survey_id = input('survey_id') ?? 464;

        $eval_exam = Db::name('evaluation_exam')
            ->field('member_id,distribution_id')
            ->where(['school_id'=>$school_id])
            ->select();

        $eval_career = Db::name('evaluation_career')
            ->where(['school_id'=>$school_id,'survey_id'=>$survey_id])
            ->column('member_id,session_id');

        $this->loadpdfLogic = new LoadpdfLogic();
        $this->backendLogic = new BackendLogic();

        $logo = $this->backendLogic->logo_school($school_id);
        $logo = $logo['logo_base64'];

        $survey_type = Db::name('survey')->where(['id'=>$survey_id])->value('survey_type');

        // 创建RedisConcurrencyControl实例，设置最大并发为2，TTL为40秒
        $concurrencyControl = new \app\common\controller\RedisConcurrencyControl(2, 40);

        // 要处理的测评列表
        foreach($eval_exam as $v){
            //有三门考试，且有测评的才能有报告
            if(isset($eval_career[$v['member_id']])){
                try {
                    $result = $concurrencyControl->callApi(function() use ($eval_career,$v,$logo,$survey_type) {
                        $where['session_id'] = $eval_career[$v['member_id']];
                        $where['member_id'] = $v['member_id'];
                        if($survey_type == 25){
                            $url= 'https://'.$_SERVER['HTTP_HOST'].'/evaluation/loadpdf/sixth_grade_personal_pdf?member_id='.$v['member_id'].'&distribution_id='.$v['distribution_id'].'#/exam_statistic_individual/list/detail';
                        }
                        if($survey_type == 29){
                            $url= 'https://'.$_SERVER['HTTP_HOST'].'/evaluation/loadpdf/tenth_grade_personal_pdf?member_id='.$v['member_id'].'&distribution_id='.$v['distribution_id'].'#/exam_statistic_individual/list/detail2';
                        }
                        $res = $this->loadpdfLogic->evaluation_create($url,$v['member_id'],$v['distribution_id'],$logo);
                        Db::name('survey_user_session')->where($where)->update(['pdf_url'=>$res[0]]);
                    });
                    echo $result . "\n";
                } catch (\Exception $e) {
                    echo "处理member_id {$v['member_id']} 时发生错误: " . $e->getMessage() . "\n";
                }
            }
        }

        apiReturn(true);
    }

    public function batch_download_evaluation_pdf(){
        $session_id = input("session_id");
        $pdf_url_arr = Db::name('survey_user_session session')
            ->join('student stu','session.student_id = stu.id')
            ->field('stu.name,session.pdf_url')
            ->where('session_id','in',$session_id)
            ->where('session.pdf_url','<>','')
            ->whereNotNull('session.pdf_url')
            ->select();

        if(empty($pdf_url_arr)){
            apiReturn([],'暂无数据');
        }
        $url = $this->pack_zip($pdf_url_arr);
        apiReturn($url);
    }

    public function pack_zip($pdf_url_arr){
        $sftp = new \app\common\controller\Sftp();
        $filename = date('YmdHis')."_evaluation.zip"; // 最终生成的文件名（含路径）
        $zip = new ZipArchive (); // 使用本类，linux需开启zlib，windows需取消php_zip.dll前的注释
        $localDir = "../runtime/sftp/";
        if(!file_exists($localDir)){
            mkdir($localDir,0755,true);
        }
        if ($zip->open($localDir.$filename, ZIPARCHIVE::CREATE) !== TRUE) {
            exit ('无法打开文件，或者文件创建失败');
        }
        foreach ($pdf_url_arr as $val) {
            $pdf_info  = pathinfo($val['pdf_url']);
            $pdf_parse = parse_url($val['pdf_url']);
            $sftp->downftp($pdf_parse['path'],$localDir.$pdf_info['basename']);
            $zip->addFile($localDir.$pdf_info['basename'], mb_convert_encoding($val['name'].'.pdf', "GBK", "UTF-8")); // 第二个参数是放在压缩包中的文件名称，如果文件可能会有重复，就需要注意一下
        }

        $zip->close(); // 关闭
        $saveDir = "/saas_upload/evaluation/pdf/";

        $sftp->upftp($localDir.$filename,$saveDir.$filename);
        //需要将zip包关闭后，才能删除下载文件,不然会报错
        foreach ($pdf_url_arr as $val) {
            $pdf_info = pathinfo($val['pdf_url']);
            unlink($localDir.$pdf_info['basename']);
        }
        //将压缩包地址存入数据库
        $file_url = 'https://s.yishengya.cn'.$saveDir.$filename;
        unlink($localDir.$filename);
        return $file_url;
    }

}