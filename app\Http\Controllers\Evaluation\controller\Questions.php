<?php

namespace app\evaluation\controller;

use app\login\controller\ApiAuth;
use app\evaluation\model\Question as QuestionModel;
use think\Db;

class Questions extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->questions = new \app\evaluation\service\Questions();
    }

    /**
     * 模块：素养测评-素养题库管理
     * @SWG\Post(path="/evaluation/questions",
     *   tags={"素养测评-素养题库:questions"},
     *   summary="试题添加",
    * @SWG\Parameter(
     * in="body",
     * name="data",
     * description="试题添加",
     * required=true,
     * @SWG\Schema(
     * type="object",
     * @SWG\Property(property="is_common", type="string", description="是否题冒题"),
     * @SWG\Property(property="category_ids", type="integer", description="素养类别ids"),
     * @SWG\Property(property="type_id", type="integer", description="归属题目类型id(例如选择题、填空题、简答题)"),
     * @SWG\Property(property="grade", type="integer", description="学段、几年级"),
     * @SWG\Property(property="course_id", type="integer", description="科目id"),
     * @SWG\Property(property="situation", type="integer", description="情景类型（题冒题）"),
     * @SWG\Property(property="options", type="object", description="question_id,score,sort,(更新需要id)"),
     * @SWG\Property(property="categorey_percentage", type="object", description="question_id,categorey_id,percentage,(更新需要id)"),
     * @SWG\Property(property="score", type="integer", description="满分值"),
     * @SWG\Property(property="knowlege_ids", type="string", description="相关知识点"),
     * @SWG\Property(property="content", type="string", description="题干内容"),
     * @SWG\Property(property="answer", type="string", description="答案"),
     * @SWG\Property(property="analysis", type="string", description="答案解析"),
     * @SWG\Property(property="parent_id", type="integer", description="题目从属于")
     * )
     * ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-素养题库管理
     * @SWG\Put(path="/evaluation/questions/{id}",
     *   tags={"素养测评-素养题库:questions"},
     *   summary="试题修改",
     *  @SWG\Parameter(
     *     in="path",
     *     name="id",
     *     type="integer",
     *     description="id",
     *     required=true,
     *  ),
     * @SWG\Parameter(
     * in="body",
     * name="data",
     * description="题目修改",
     * required=true,
     * @SWG\Schema(
     * type="object",
     * @SWG\Property(property="is_common", type="string", description="是否题冒题"),
     * @SWG\Property(property="category_ids", type="integer", description="素养类别ids"),
     * @SWG\Property(property="type_id", type="integer", description="归属题目类型id(例如选择题、填空题、简答题)"),
     * @SWG\Property(property="grade", type="integer", description="学段、几年级"),
     * @SWG\Property(property="course_id", type="integer", description="科目id"),
     * @SWG\Property(property="situation", type="integer", description="情景类型（题冒题）"),
     * @SWG\Property(property="options", type="object", description="question_id,score,sort,(更新需要id)"),
     * @SWG\Property(property="categorey_percentage", type="object", description="question_id,categorey_id,percentage,(更新需要id)"),
     * @SWG\Property(property="score", type="integer", description="满分值"),
     * @SWG\Property(property="knowlege_ids", type="string", description="相关知识点"),
     * @SWG\Property(property="content", type="string", description="题干内容"),
     * @SWG\Property(property="answer", type="string", description="答案"),
     * @SWG\Property(property="analysis", type="string", description="答案解析"),
     * @SWG\Property(property="parent_id", type="integer", description="题目从属于")
     * )
     * ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-素养题库管理
     * @SWG\Delete(path="/evaluation/questions/{id}",
     *   tags={"素养测评-素养题库:questions"},
     *   summary="试题删除",
     *   @SWG\Parameter(
     *     in="path",
     *     name="id",
     *     type="integer",
     *     description="题目类型ID",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-素养题库管理
     * @SWG\Get(path="/evaluation/questions",
     *   tags={"素养测评-素养题库:questions"},
     *   summary="试题查询",
     *   description="数据说明：",
     *   operationId="summary",
     *   produces={"application/json"},
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="json",
     *     description="题目ID,多个用逗号拼接",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="paper_id",
     *     type="json",
     *     description="试卷ID,通过此参数可以调用redis缓存",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="is_common",
     *     type="integer",
     *     description="是否题冒题",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="course_id",
     *     type="integer",
     *     description="学科ID",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="category_ids",
     *     type="integer",
     *     description="学科素养ID",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="grade",
     *     type="integer",
     *     description="适用年级",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="type_id",
     *     type="integer",
     *     description="题目类型",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="start_score",
     *     type="integer",
     *     description="题目分值开始值",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="end_score",
     *     type="integer",
     *     description="题目小于等于分值",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="content",
     *     type="string",
     *     description="关键字查询 ",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="pagesize",
     *     type="integer",
     *     description="每页显示记录数",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="page",
     *     type="integer",
     *     description="页码",
     *     required=false,
     *   ),
     *   @SWG\Response(
     *     response="200",
     *     description="操作成功"
     *  ),
     * )
     */

    public function questions(){
        $data = $this->questions->hand_out();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-素养题库管理
     * @SWG\Post(path="/evaluation/uploads",
     *   tags={"素养测评-素养题库:questions"},
     *   summary="上传图片",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="file",
     *     type="file",
     *     description="图片",
     *     required=true,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function uploads(){
        apiReturn(sftpUpload('/evaluation/'));
    }

    /**
     * 模块：素养测评-素养题库管理
     * @SWG\Put(path="/evaluation/questions/sort",
     *   tags={"素养测评-素养题库修改子题排序:questions"},
     *   summary="修改子题排序",
     * @SWG\Parameter(
     * in="body",
     * name="data",
     * description="子题排序修改",
     * required=true,
     * @SWG\Schema(
     * type="object",
     * @SWG\Property(property="questions", type="object", description="question_id,sort"),
     * )
     * ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function sort(){

        $data = $this->questions->sort();
    }

}