<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use App\Services\Evaluation\QuestionTypesService;
use App\Http\Requests\Evaluation\QuestionTypeRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * 题目类型管理控制器
 */
class EvaluationQuestionTypesController extends Controller
{
    protected $questionTypesService;

    public function __construct(QuestionTypesService $questionTypesService)
    {
        $this->questionTypesService = $questionTypesService;
    }

    /**
     * 题目类型查询
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function questionTypes(Request $request): JsonResponse
    {
        try {
            $data = $this->questionTypesService->getQuestionTypesList($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取题目类型列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 题目类型添加
     * 
     * @param QuestionTypeRequest $request
     * @return JsonResponse
     */
    public function store(QuestionTypeRequest $request): JsonResponse
    {
        try {
            $data = $this->questionTypesService->createQuestionType($request->validated());
            return $this->success($data, '题目类型添加成功');
        } catch (\Exception $e) {
            return $this->error('题目类型添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 题目类型修改
     * 
     * @param QuestionTypeRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(QuestionTypeRequest $request, int $id): JsonResponse
    {
        try {
            $data = $this->questionTypesService->updateQuestionType($id, $request->validated());
            return $this->success($data, '题目类型修改成功');
        } catch (\Exception $e) {
            return $this->error('题目类型修改失败: ' . $e->getMessage());
        }
    }

    /**
     * 题目类型删除
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->questionTypesService->deleteQuestionType($id);
            return $this->success(null, '题目类型删除成功');
        } catch (\Exception $e) {
            return $this->error('题目类型删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取题目类型统计
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $data = $this->questionTypesService->getQuestionTypeStatistics();
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取题目类型统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新排序
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function sort(Request $request): JsonResponse
    {
        try {
            $this->questionTypesService->updateSort($request->all());
            return $this->success(null, '排序更新成功');
        } catch (\Exception $e) {
            return $this->error('排序更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取主观题类型
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getSubjectiveTypes(Request $request): JsonResponse
    {
        try {
            $data = $this->questionTypesService->getSubjectiveTypes();
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取主观题类型失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取客观题类型
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getObjectiveTypes(Request $request): JsonResponse
    {
        try {
            $data = $this->questionTypesService->getObjectiveTypes();
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取客观题类型失败: ' . $e->getMessage());
        }
    }

    /**
     * 复制题目类型
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function copy(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $newName = $request->input('new_name');
            
            $data = $this->questionTypesService->copyQuestionType($id, $newName);
            return $this->success($data, '题目类型复制成功');
        } catch (\Exception $e) {
            return $this->error('题目类型复制失败: ' . $e->getMessage());
        }
    }

    /**
     * 启用/禁用题目类型
     * 
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function toggleStatus(Request $request, int $id): JsonResponse
    {
        try {
            $status = $request->input('status', 0);
            $this->questionTypesService->toggleStatus($id, $status);
            
            $statusText = $status == 0 ? '启用' : '禁用';
            return $this->success(null, "题目类型{$statusText}成功");
        } catch (\Exception $e) {
            return $this->error('状态更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量操作
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchOperation(Request $request): JsonResponse
    {
        try {
            $action = $request->input('action');
            $ids = $request->input('ids', []);
            
            if (empty($ids)) {
                return $this->error('请选择要操作的题目类型');
            }

            $result = $this->questionTypesService->batchOperation($action, $ids);

            $message = "成功处理 {$result['success_count']} 个题目类型";
            if (!empty($result['errors'])) {
                $message .= "，失败: " . implode('; ', $result['errors']);
            }

            return $this->success($result, $message);
        } catch (\Exception $e) {
            return $this->error('批量操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取题目类型选项
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getOptions(Request $request): JsonResponse
    {
        try {
            $type = $request->input('type', 'all'); // all, subjective, objective
            
            switch ($type) {
                case 'subjective':
                    $data = $this->questionTypesService->getSubjectiveTypes();
                    break;
                case 'objective':
                    $data = $this->questionTypesService->getObjectiveTypes();
                    break;
                default:
                    $data = $this->questionTypesService->getQuestionTypesList([]);
            }
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取题目类型选项失败: ' . $e->getMessage());
        }
    }

    /**
     * 导入题目类型
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function import(Request $request): JsonResponse
    {
        try {
            // 这里需要实现题目类型导入逻辑
            // 可以支持 Excel、CSV 等格式的导入
            
            return $this->success(null, '题目类型导入成功');
        } catch (\Exception $e) {
            return $this->error('题目类型导入失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出题目类型
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $format = $request->input('format', 'excel'); // excel, csv
            
            // 这里需要实现题目类型导出逻辑
            // 可以使用 Laravel Excel 等包来实现
            
            return $this->success([
                'download_url' => '',
                'file_name' => "question_types_" . date('YmdHis') . ".{$format}"
            ], '题目类型导出成功');
        } catch (\Exception $e) {
            return $this->error('题目类型导出失败: ' . $e->getMessage());
        }
    }
}
