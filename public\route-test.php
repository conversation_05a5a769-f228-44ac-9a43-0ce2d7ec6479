<?php
/**
 * 路由测试页面
 */

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-link { display: block; margin: 10px 0; padding: 10px; background: #f0f0f0; text-decoration: none; color: #333; border-radius: 5px; }
        .test-link:hover { background: #e0e0e0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Laravel 路由测试页面</h1>
    
    <div class="section">
        <h2>基础测试</h2>
        <a href="/debug.php" class="test-link" target="_blank">调试信息 - /debug.php</a>
        <a href="/test" class="test-link" target="_blank">基础测试 - /test</a>
    </div>
    
    <div class="section">
        <h2>Evaluation 路由测试（无认证）</h2>
        <a href="/evaluation/test" class="test-link" target="_blank">基础测试 - /evaluation/test</a>
        <a href="/evaluation/test-direct" class="test-link" target="_blank">直接测试 - /evaluation/test-direct</a>
        <a href="/evaluation/config-test" class="test-link" target="_blank">配置测试 - /evaluation/config-test</a>
        <a href="/evaluation/config" class="test-link" target="_blank">配置接口 - /evaluation/config</a>
        <a href="/evaluation/Config" class="test-link" target="_blank">配置接口(大写) - /evaluation/Config</a>
        <a href="/evaluation/question_types" class="test-link" target="_blank">题目类型(下划线) - /evaluation/question_types</a>
    </div>
    
    <div class="section">
        <h2>Evaluation 路由测试（需要认证）</h2>
        <p style="color: orange;">以下路由需要认证，可能返回401错误</p>
        <a href="/evaluation/questions" class="test-link" target="_blank">题目管理 - /evaluation/questions</a>
        <a href="/evaluation/papers" class="test-link" target="_blank">试卷管理 - /evaluation/papers</a>
        <a href="/evaluation/categories" class="test-link" target="_blank">类别管理 - /evaluation/categories</a>
        <a href="/evaluation/question-types" class="test-link" target="_blank">题目类型(连字符) - /evaluation/question-types</a>
    </div>
    
    <div class="section">
        <h2>服务器信息</h2>
        <p><strong>PHP版本:</strong> <?php echo PHP_VERSION; ?></p>
        <p><strong>服务器:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
        <p><strong>文档根目录:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></p>
        <p><strong>当前域名:</strong> <?php echo $_SERVER['HTTP_HOST'] ?? 'Unknown'; ?></p>
        <p><strong>请求URI:</strong> <?php echo $_SERVER['REQUEST_URI'] ?? 'Unknown'; ?></p>
    </div>
    
    <div class="section">
        <h2>文件检查</h2>
        <?php
        $files = [
            'Laravel入口文件' => __DIR__ . '/index.php',
            'Web路由文件' => __DIR__ . '/../routes/web.php',
            'API路由文件' => __DIR__ . '/../routes/api.php',
            'Evaluation路由文件' => __DIR__ . '/../routes/evaluation.php',
            '.htaccess文件' => __DIR__ . '/.htaccess',
            'vendor目录' => __DIR__ . '/../vendor',
            'bootstrap目录' => __DIR__ . '/../bootstrap',
        ];
        
        foreach ($files as $name => $path) {
            $exists = file_exists($path);
            $class = $exists ? 'success' : 'error';
            $status = $exists ? '✓ 存在' : '✗ 不存在';
            echo "<p class='{$class}'><strong>{$name}:</strong> {$status}</p>";
        }
        ?>
    </div>
    
    <script>
        // 自动测试一些路由
        function testRoute(url, elementId) {
            fetch(url)
                .then(response => {
                    const element = document.getElementById(elementId);
                    if (response.ok) {
                        element.innerHTML = '<span style="color: green;">✓ 可访问</span>';
                    } else {
                        element.innerHTML = '<span style="color: red;">✗ ' + response.status + '</span>';
                    }
                })
                .catch(error => {
                    const element = document.getElementById(elementId);
                    element.innerHTML = '<span style="color: red;">✗ 错误</span>';
                });
        }
        
        // 页面加载后自动测试
        window.onload = function() {
            // 可以在这里添加自动测试逻辑
        };
    </script>
</body>
</html>
