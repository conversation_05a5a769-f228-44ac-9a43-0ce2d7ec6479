<?php
namespace App\Services\School\Assessment\IndividualReport\Psychology;

// Removed unused import: use App\Services\School\Assessment\IndividualReport\AbstractIndividualReportService;

/**
 * 自信评估报告服务。
 * 负责生成自信评估的个人报告，包括分数分析、图表和建议。
 */
class ConfidenceService extends AbstractPsychologyReportService
{
    /**
     * 获取自信评估的等级常量。
     *
     * @return array 返回包含总分和维度得分等级定义的数组。
     */
    protected function getLevelConstants(): array
    {
        return [
            'total' => [
                ['max' => 45, 'level' => '信心不足'],
                ['max' => 70, 'level' => '一般自信'],
                ['max' => 85, 'level' => '比较自信'],
                ['max' => PHP_FLOAT_MAX, 'level' => '非常自信']
            ],
            'dimension' => [
                ['max' => 45, 'code' => 'low', 'text' => '不太自信，有很大的提升空间'],
                ['max' => 70, 'code' => 'moderate', 'text' => '表现一般'],
                ['max' => PHP_FLOAT_MAX, 'code' => 'high', 'text' => '有较强的信心']
            ]
        ];
    }

    /**
     * 获取此服务对应的配置键名。
     *
     * @return string 返回配置键名 'confidence'。
     */
    protected function getConfigKey(): string
    {
        return 'confidence';
    }

    /**
     * 处理评估信息，生成报告所需的图表、描述、能力和建议。
     *
     * @param array $standard_results 标准化后的评估结果。
     * @param array $config 相关的配置信息。
     * @return array 包含图表、描述、能力和建议的数组。
     */
    protected function processAssessmentInfo(array $standard_results, array $config): array
    {
        $dimension_results = $this->analyzeDimensions($standard_results['dimensions'], $config);
        return [
            'charts'      => $this->generateCharts($standard_results),
            'discription' => $this->generateReportText($standard_results, $dimension_results, $config),
            'competence'  => $dimension_results['competence'],
            'advise'      => $dimension_results['advise']
        ];
    }

    /**
     * 分析维度得分，将其分类并生成能力描述和建议。
     *
     * @param array $standard_results_dimensions 标准化结果中的维度数据。
     * @param array $config 相关的配置信息。
     * @return array 包含分类后的维度名称、能力描述和建议的数组。
     */
    protected function analyzeDimensions(array $standard_results_dimensions, array $config): array
    {
        $categories = [
            'low' => [], 
            'moderate' => [], 
            'high' => []
        ];
        $competence = [];
        $advise = [];

        foreach ($standard_results_dimensions as $index => $dimension_data) { // Renamed $dimension to $dimension_data
            // 假设 config['dimension_codes'] 的索引与 $standard_results_dimensions 的索引对应
            $dimension_name = $config['dimension_codes'][$index] ?? $dimension_data['name'];
            $level = $this->getScoreLevel($dimension_data['score']); // 使用基类方法
            $categories[$level][] = $dimension_name;
            $competence[$index] = $config[$index][$level]; // 索引访问配置
            $advise[$index] = $config[$index]['advise'];
        }

        return [
            'categories' => $categories,
            'competence' => $competence,
            'advise' => $advise
        ];
    }

    /**
     * 生成报告的文本描述部分。
     *
     * @param array $standard_results 标准化后的评估结果。
     * @param array $dimension_results 分析后的维度结果。
     * @param array $config 相关的配置信息。
     * @return array 包含总结和详细描述的文本数组。
     */
    protected function generateReportText(array $standard_results, array $dimension_results, array $config): array
    {
        $total_level = $this->getScoreLevel($standard_results['total_score'], 'total');
        $summary = sprintf(
            '测验结果表明，你的自信总体得分为%s分，结果表示你%s。',
            $standard_results['total_score'],
            $total_level
        );

        $detail = '具体来说，你';
        $parts = [];
        // 使用 getLevelConstants 获取维度文本
        $dimension_level_texts = $this->getLevelConstants()['dimension'];
        $level_code_to_text_map = array_column($dimension_level_texts, 'text', 'code');

        foreach ($dimension_results['categories'] as $level_code => $dimensions) {
            if (!empty($dimensions)) {
                $level_text = $level_code_to_text_map[$level_code] ?? '';
                $parts[] = sprintf('在%s方面%s', implode('、', $dimensions), $level_text);
            }
        }
        
        return [$summary, $detail . implode('，', $parts) . '。'];
    }
}
