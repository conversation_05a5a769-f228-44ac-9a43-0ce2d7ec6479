<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     * php artisan migrate --path=/database/migrations/2024_03_21_095242_create_user_access_logs_table.php
     */
    public function up(): void
    {
        Schema::create('user_access_logs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('method')->comment('方法:get,post...');
            $table->string('url')->comment('接口url');
            $table->integer('permission_id')->nullable()->comment('权限编号');
            $table->string('permission_name', 20)->nullable()->comment('权限名称');
            $table->json('parameters')->comment('参数');
            $table->string('creator', 20)->comment('添加人');
            $table->string('api_token', 80)->nullable()->comment('token');
            $table->string('ip', 20)->nullable()->comment('ip');
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `user_access_logs` comment '用户访问日志表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_access_logs');
    }
};
