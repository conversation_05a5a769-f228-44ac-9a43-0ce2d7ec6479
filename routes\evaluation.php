<?php

use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Evaluation\EvaluationQuestionsController;
use App\Http\Controllers\Evaluation\EvaluationAnswerController;
use App\Http\Controllers\Evaluation\EvaluationPapersController;
use App\Http\Controllers\Evaluation\EvaluationCategoriesController;
use App\Http\Controllers\Evaluation\EvaluationDistributionController;
use App\Http\Controllers\Evaluation\EvaluationReportController;
use App\Http\Controllers\Evaluation\EvaluationStatisticsController;
use App\Http\Controllers\Evaluation\EvaluationConfigController;
use App\Http\Controllers\Evaluation\EvaluationKnowledgesController;
use App\Http\Controllers\Evaluation\EvaluationQuestionTypesController;

/*
|--------------------------------------------------------------------------
| Evaluation API Routes
|--------------------------------------------------------------------------
|
| 素养测评模块的API路由
| 保持原有接口名称和参数不变，确保前端兼容性
|
*/

// 测试路由（无中间件）
Route::get('evaluation/test', function () {
    return response()->json([
        'message' => 'Evaluation routes are working!',
        'file' => 'routes/evaluation.php',
        'timestamp' => now(),
        'route_loaded' => true
    ]);
});

// 配置路由测试（无认证）
Route::get('evaluation/config-test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'Config route is working!',
        'data' => [
            'test' => true,
            'timestamp' => now()
        ]
    ]);
});

// 配置路由（无认证，临时测试用）- 调用 ConfigService
Route::get('evaluation/config', function () {
    try {
        $configService = new \App\Services\Evaluation\ConfigService();
        $data = $configService->getConfig();

        return response()->json([
            'status' => 'success',
            'code' => 200,
            'message' => '操作成功',
            'data' => $data
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'code' => 500,
            'message' => '获取配置失败: ' . $e->getMessage()
        ], 500);
    }
});

// 阅卷管理列表路由（无认证，临时测试用）
Route::get('evaluation/answer', function () {
    try {
        // 模拟阅卷管理列表数据
        $page = request()->input('page', 1);
        $pageSize = request()->input('pagesize', 15);

        // 生成一些测试数据
        $data = [];
        $total = 50;

        for ($i = 1; $i <= min($pageSize, $total - ($page - 1) * $pageSize); $i++) {
            $index = ($page - 1) * $pageSize + $i;
            $data[] = [
                'id' => $index,
                'distribution_id' => rand(1, 10),
                'paper_id' => rand(1, 20),
                'member_id' => rand(100, 200),
                'score' => rand(60, 100),
                'check_status' => rand(0, 2),
                'created_at' => now()->subDays(rand(1, 30))->toDateTimeString(),
                'paper' => [
                    'id' => rand(1, 20),
                    'paper_name' => '测试试卷 ' . rand(1, 20)
                ],
                'student' => [
                    'id' => rand(100, 200),
                    'name' => '学生 ' . rand(100, 200)
                ],
                'distribution' => [
                    'id' => rand(1, 10),
                    'title' => '测试发布 ' . rand(1, 10)
                ]
            ];
        }

        return response()->json([
            'status' => 'success',
            'code' => 200,
            'message' => '获取阅卷管理列表成功',
            'data' => [
                'data' => $data,
                'total' => $total,
                'page' => (int)$page,
                'pagesize' => (int)$pageSize,
                'total_pages' => ceil($total / $pageSize)
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'code' => 500,
            'message' => '获取阅卷管理列表失败: ' . $e->getMessage()
        ], 500);
    }
});

// 兼容大写的Config路由
Route::get('evaluation/Config', function () {
    try {
        $configService = new \App\Services\Evaluation\ConfigService();
        $data = $configService->getConfig();

        return response()->json([
            'status' => 'success',
            'code' => 200,
            'message' => '操作成功',
            'data' => $data
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'code' => 500,
            'message' => '获取配置失败: ' . $e->getMessage()
        ], 500);
    }
});

// 无认证的答题接口测试路由
Route::get('evaluation/answer/test', function () {
    return response()->json([
        'status' => 'success',
        'code' => 200,
        'message' => '答题接口测试成功',
        'data' => [
            'timestamp' => now()->toDateTimeString(),
            'test_mode' => true,
            'available_endpoints' => [
                'GET /evaluation/answer/test' => '测试接口（无认证）',
                'GET /evaluation/answer' => '阅卷管理列表（需认证）',
                'POST /evaluation/answer' => '提交答案（需认证）',
                'GET /evaluation/answer/record' => '获取答题记录（需认证）'
            ]
        ]
    ]);
});

// 兼容下划线的question_types路由
Route::get('evaluation/question_types', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'Question types endpoint working (underscore)!',
        'data' => [
            [
                'id' => 1,
                'type_name' => '单选题',
                'is_subjective' => 0,
                'description' => '单项选择题'
            ],
            [
                'id' => 2,
                'type_name' => '多选题',
                'is_subjective' => 0,
                'description' => '多项选择题'
            ],
            [
                'id' => 3,
                'type_name' => '主观题',
                'is_subjective' => 1,
                'description' => '主观题目'
            ]
        ]
    ]);
});

Route::group(['prefix' => 'evaluation', 'middleware' => ['auth.refresh']], function () {
    
    // ==================== 题库管理 ====================
    
    // 题目相关路由
    Route::get('questions', [EvaluationQuestionsController::class, 'questions'])->name('evaluation.questions.index');
    Route::post('questions', [EvaluationQuestionsController::class, 'store'])->name('evaluation.questions.store');
    Route::put('questions/{id}', [EvaluationQuestionsController::class, 'update'])->name('evaluation.questions.update');
    Route::delete('questions/{id}', [EvaluationQuestionsController::class, 'destroy'])->name('evaluation.questions.destroy');
    
    // 题目排序
    Route::put('questions/sort', [EvaluationQuestionsController::class, 'sort'])->name('evaluation.questions.sort');
    
    // 图片上传
    Route::post('uploads', [EvaluationQuestionsController::class, 'uploads'])->name('evaluation.uploads');
    
    // ==================== 试卷管理 ====================
    
    // 试卷相关路由
    Route::get('papers', [EvaluationPapersController::class, 'papers'])->name('evaluation.papers.index');
    Route::post('papers', [EvaluationPapersController::class, 'store'])->name('evaluation.papers.store');
    Route::put('papers/{id}', [EvaluationPapersController::class, 'update'])->name('evaluation.papers.update');
    Route::delete('papers/{id}', [EvaluationPapersController::class, 'destroy'])->name('evaluation.papers.destroy');
    Route::get('papers/{id}', [EvaluationPapersController::class, 'show'])->name('evaluation.papers.show');
    
    // 试卷复制和预览
    Route::post('papers/copy', [EvaluationPapersController::class, 'copy'])->name('evaluation.papers.copy');
    Route::get('papers/{id}/preview', [EvaluationPapersController::class, 'preview'])->name('evaluation.papers.preview');
    
    // ==================== 答题管理 ====================
    
    // 答题相关路由
    Route::get('answer', [EvaluationAnswerController::class, 'index'])->name('evaluation.answer.index');
    Route::post('answer', [EvaluationAnswerController::class, 'answer'])->name('evaluation.answer.submit');
    Route::get('answer/record', [EvaluationAnswerController::class, 'getAnswerRecord'])->name('evaluation.answer.record');
    Route::get('answer/batch', [EvaluationAnswerController::class, 'getBatchAnswerRecord'])->name('evaluation.answer.batch');
    Route::get('answer/question-score', [EvaluationAnswerController::class, 'getQuestionScore'])->name('evaluation.answer.question-score');
    Route::get('answer/question-score-competence', [EvaluationAnswerController::class, 'getQuestionScoreWithCompetence'])->name('evaluation.answer.question-score-competence');
    Route::get('answer/student-exam', [EvaluationAnswerController::class, 'getStudentExam'])->name('evaluation.answer.student-exam');
    Route::get('answer/student-excel', [EvaluationAnswerController::class, 'getStudentExcel'])->name('evaluation.answer.student-excel');
    Route::get('answer/student-excel-tenth', [EvaluationAnswerController::class, 'getStudentExcelTenth'])->name('evaluation.answer.student-excel-tenth');
    Route::get('answer/student-excel-level-tenth', [EvaluationAnswerController::class, 'getStudentExcelLevelTenth'])->name('evaluation.answer.student-excel-level-tenth');
    Route::post('answer/single', [EvaluationAnswerController::class, 'insertAnswerSingle'])->name('evaluation.answer.single');
    Route::post('answer/mult', [EvaluationAnswerController::class, 'insertAnswerMult'])->name('evaluation.answer.mult');
    Route::post('answer/sub', [EvaluationAnswerController::class, 'insertAnswerSub'])->name('evaluation.answer.sub');
    Route::post('answer/log', [EvaluationAnswerController::class, 'insertLog'])->name('evaluation.answer.log');
    Route::put('answer/log-competence', [EvaluationAnswerController::class, 'updateLogCompetence'])->name('evaluation.answer.log-competence');
    Route::put('answer/answer-competence', [EvaluationAnswerController::class, 'updateAnswerCompetence'])->name('evaluation.answer.answer-competence');
    Route::get('answer/detail', [EvaluationAnswerController::class, 'getStudentAnswerDetail'])->name('evaluation.answer.detail');
    Route::get('answer/batch', [EvaluationAnswerController::class, 'getBatchAnswerRecord'])->name('evaluation.answer.batch');
    Route::delete('answer/record', [EvaluationAnswerController::class, 'deleteAnswerRecord'])->name('evaluation.answer.delete');
    Route::get('answer/statistics', [EvaluationAnswerController::class, 'getAnswerStatistics'])->name('evaluation.answer.statistics');

    // 答题管理扩展接口
    Route::post('answer/change_review_teacher', [EvaluationAnswerController::class, 'changeReviewTeacher'])->name('evaluation.answer.change-review-teacher');
    Route::post('answer/correction_score', [EvaluationAnswerController::class, 'correctionScore'])->name('evaluation.answer.correction-score');
    Route::get('answer/get_question_score', [EvaluationAnswerController::class, 'getQuestionScore'])->name('evaluation.answer.question-score');
    Route::post('answer/submit_subjective', [EvaluationAnswerController::class, 'submitSubjective'])->name('evaluation.answer.submit-subjective');
    Route::get('answer/log_papers', [EvaluationAnswerController::class, 'logPapers'])->name('evaluation.answer.log-papers');
    Route::post('answer/insert_answer_single', [EvaluationAnswerController::class, 'insertAnswerSingle'])->name('evaluation.answer.insert-single');
    Route::post('answer/insert_answer_mult', [EvaluationAnswerController::class, 'insertAnswerMult'])->name('evaluation.answer.insert-mult');
    Route::post('answer/insert_answer_sub', [EvaluationAnswerController::class, 'insertAnswerSub'])->name('evaluation.answer.insert-sub');
    Route::post('answer/insert_log', [EvaluationAnswerController::class, 'insertLog'])->name('evaluation.answer.insert-log');
    Route::post('answer/update_log_competence', [EvaluationAnswerController::class, 'updateLogCompetence'])->name('evaluation.answer.update-log-competence');
    Route::post('answer/update_answer_competence', [EvaluationAnswerController::class, 'updateAnswerCompetence'])->name('evaluation.answer.update-answer-competence');
    Route::get('answer/get_question_score_with_competence', [EvaluationAnswerController::class, 'getQuestionScoreWithCompetence'])->name('evaluation.answer.question-score-competence');
    Route::get('answer/get_student_exam', [EvaluationAnswerController::class, 'getStudentExam'])->name('evaluation.answer.student-exam');
    Route::get('answer/get_student_excel', [EvaluationAnswerController::class, 'getStudentExcel'])->name('evaluation.answer.student-excel');
    Route::get('answer/get_student_excel_tenth', [EvaluationAnswerController::class, 'getStudentExcelTenth'])->name('evaluation.answer.student-excel-tenth');
    Route::get('answer/get_student_excel_level_tenth', [EvaluationAnswerController::class, 'getStudentExcelLevelTenth'])->name('evaluation.answer.student-excel-level-tenth');
    
    // ==================== 素养类别管理 ====================
    
    // 素养类别相关路由
    Route::get('categories', [EvaluationCategoriesController::class, 'categories'])->name('evaluation.categories.index');
    Route::post('categories', [EvaluationCategoriesController::class, 'store'])->name('evaluation.categories.store');
    Route::put('categories/{id}', [EvaluationCategoriesController::class, 'update'])->name('evaluation.categories.update');
    Route::delete('categories/{id}', [EvaluationCategoriesController::class, 'destroy'])->name('evaluation.categories.destroy');
    Route::get('categories/tree', [EvaluationCategoriesController::class, 'tree'])->name('evaluation.categories.tree');
    Route::get('categories/children', [EvaluationCategoriesController::class, 'children'])->name('evaluation.categories.children');
    Route::put('categories/sort', [EvaluationCategoriesController::class, 'sort'])->name('evaluation.categories.sort');
    Route::put('categories/move', [EvaluationCategoriesController::class, 'move'])->name('evaluation.categories.move');
    
    // ==================== 分发管理 ====================
    
    // 分发相关路由
    Route::get('distribution', [EvaluationDistributionController::class, 'distribution'])->name('evaluation.distribution.index');
    Route::post('distribution', [EvaluationDistributionController::class, 'store'])->name('evaluation.distribution.store');
    Route::put('distribution/{id}', [EvaluationDistributionController::class, 'update'])->name('evaluation.distribution.update');
    Route::delete('distribution/{id}', [EvaluationDistributionController::class, 'destroy'])->name('evaluation.distribution.destroy');
    
    // 分发详情和统计
    Route::get('distribution/{id}/detail', [EvaluationDistributionController::class, 'getDistributionDetail'])->name('evaluation.distribution.detail');
    Route::get('distribution/{id}/students', [EvaluationDistributionController::class, 'getDistributionStudents'])->name('evaluation.distribution.students');
    Route::get('distribution/{id}/statistics', [EvaluationDistributionController::class, 'getDistributionStatistics'])->name('evaluation.distribution.statistics');
    Route::put('distribution/{id}/status', [EvaluationDistributionController::class, 'updateStatus'])->name('evaluation.distribution.status');
    Route::post('distribution/{id}/assign-teachers', [EvaluationDistributionController::class, 'assignTeachers'])->name('evaluation.distribution.assign-teachers');
    Route::post('distribution/copy', [EvaluationDistributionController::class, 'copy'])->name('evaluation.distribution.copy');
    Route::post('distribution/batch', [EvaluationDistributionController::class, 'batchOperation'])->name('evaluation.distribution.batch');
    
    // ==================== 报告管理 ====================
    
    // 报告相关路由
    Route::get('report', [EvaluationReportController::class, 'report'])->name('evaluation.report.index');
    Route::get('report/student', [EvaluationReportController::class, 'getStudentReport'])->name('evaluation.report.student');
    Route::get('report/class', [EvaluationReportController::class, 'getClassReport'])->name('evaluation.report.class');
    Route::get('report/school', [EvaluationReportController::class, 'getSchoolReport'])->name('evaluation.report.school');
    
    // 报告生成和下载
    Route::post('report/generate', [EvaluationReportController::class, 'generateReport'])->name('evaluation.report.generate');
    Route::get('report/{id}/download', [EvaluationReportController::class, 'downloadReport'])->name('evaluation.report.download');
    Route::get('report/personal', [EvaluationReportController::class, 'personalReport'])->name('evaluation.report.personal');
    Route::get('report/settlement', [EvaluationReportController::class, 'settlement'])->name('evaluation.report.settlement');
    Route::get('report/all-settlement', [EvaluationReportController::class, 'allSettlement'])->name('evaluation.report.all-settlement');
    Route::get('report/overview', [EvaluationReportController::class, 'overview'])->name('evaluation.report.overview');
    Route::get('report/trends', [EvaluationReportController::class, 'trends'])->name('evaluation.report.trends');
    Route::get('report/comparison', [EvaluationReportController::class, 'comparison'])->name('evaluation.report.comparison');
    Route::post('report/export', [EvaluationReportController::class, 'export'])->name('evaluation.report.export');
    

    
    // ==================== 配置管理 ====================

    // 配置相关路由
    Route::get('config', [EvaluationConfigController::class, 'config'])->name('evaluation.config.index');
    Route::post('config', [EvaluationConfigController::class, 'store'])->name('evaluation.config.store');
    Route::put('config/batch', [EvaluationConfigController::class, 'batchUpdate'])->name('evaluation.config.batch');
    Route::post('config/reset-cache', [EvaluationConfigController::class, 'resetCache'])->name('evaluation.config.reset-cache');
    Route::get('config/full', [EvaluationConfigController::class, 'getFullConfig'])->name('evaluation.config.full');
    Route::get('config/courses', [EvaluationConfigController::class, 'getCourses'])->name('evaluation.config.courses');
    Route::get('config/grades', [EvaluationConfigController::class, 'getGrades'])->name('evaluation.config.grades');
    Route::get('config/schools', [EvaluationConfigController::class, 'getSchools'])->name('evaluation.config.schools');
    Route::get('config/classes', [EvaluationConfigController::class, 'getClasses'])->name('evaluation.config.classes');
    Route::get('config/teachers', [EvaluationConfigController::class, 'getTeachers'])->name('evaluation.config.teachers');
    Route::get('config/students', [EvaluationConfigController::class, 'getStudents'])->name('evaluation.config.students');
    Route::get('config/question-types', [EvaluationConfigController::class, 'getQuestionTypes'])->name('evaluation.config.question-types');
    Route::get('config/scenarios', [EvaluationConfigController::class, 'getScenarios'])->name('evaluation.config.scenarios');
    Route::get('config/literacy-levels', [EvaluationConfigController::class, 'getLiteracyLevels'])->name('evaluation.config.literacy-levels');
    Route::get('config/system-settings', [EvaluationConfigController::class, 'getSystemSettings'])->name('evaluation.config.system-settings');

    // ==================== 统计分析 ====================

    // 统计相关路由
    Route::get('statistics', [EvaluationStatisticsController::class, 'statistics'])->name('evaluation.statistics.index');
    Route::get('statistics/overview', [EvaluationStatisticsController::class, 'getOverview'])->name('evaluation.statistics.overview');
    Route::get('statistics/student-report', [EvaluationStatisticsController::class, 'getStudentReport'])->name('evaluation.statistics.student-report');
    Route::get('statistics/integrated-student', [EvaluationStatisticsController::class, 'integratedStatisticsStudent'])->name('evaluation.statistics.integrated-student');
    Route::get('statistics/integrated-head', [EvaluationStatisticsController::class, 'integratedStatisticsHead'])->name('evaluation.statistics.integrated-head');
    Route::get('statistics/career', [EvaluationStatisticsController::class, 'statisticsCareer'])->name('evaluation.statistics.career');
    Route::get('statistics/trend', [EvaluationStatisticsController::class, 'getTrend'])->name('evaluation.statistics.trend');
    Route::get('statistics/comparison', [EvaluationStatisticsController::class, 'getComparison'])->name('evaluation.statistics.comparison');
    Route::post('statistics/batch-create-pdf', [EvaluationStatisticsController::class, 'batchCreateEvaluationPdf'])->name('evaluation.statistics.batch-create-pdf');
    Route::post('statistics/batch-download-pdf', [EvaluationStatisticsController::class, 'batchDownloadEvaluationPdf'])->name('evaluation.statistics.batch-download-pdf');
    Route::post('statistics/export', [EvaluationStatisticsController::class, 'exportStatistics'])->name('evaluation.statistics.export');
    Route::get('statistics/realtime', [EvaluationStatisticsController::class, 'getRealTimeStats'])->name('evaluation.statistics.realtime');
    Route::get('statistics/ranking', [EvaluationStatisticsController::class, 'getRanking'])->name('evaluation.statistics.ranking');

    // ==================== 知识点管理 ====================

    // 知识点相关路由
    Route::get('knowledges', [EvaluationKnowledgesController::class, 'knowledges'])->name('evaluation.knowledges.index');
    Route::post('knowledges', [EvaluationKnowledgesController::class, 'store'])->name('evaluation.knowledges.store');
    Route::put('knowledges/{id}', [EvaluationKnowledgesController::class, 'update'])->name('evaluation.knowledges.update');
    Route::delete('knowledges/{id}', [EvaluationKnowledgesController::class, 'destroy'])->name('evaluation.knowledges.destroy');
    Route::get('knowledges/tree', [EvaluationKnowledgesController::class, 'tree'])->name('evaluation.knowledges.tree');
    Route::get('knowledges/children', [EvaluationKnowledgesController::class, 'children'])->name('evaluation.knowledges.children');
    Route::put('knowledges/sort', [EvaluationKnowledgesController::class, 'sort'])->name('evaluation.knowledges.sort');
    Route::put('knowledges/move', [EvaluationKnowledgesController::class, 'move'])->name('evaluation.knowledges.move');
    Route::get('knowledges/search', [EvaluationKnowledgesController::class, 'search'])->name('evaluation.knowledges.search');
    Route::get('knowledges/statistics', [EvaluationKnowledgesController::class, 'statistics'])->name('evaluation.knowledges.statistics');
    Route::post('knowledges/batch', [EvaluationKnowledgesController::class, 'batchOperation'])->name('evaluation.knowledges.batch');
    Route::post('knowledges/copy', [EvaluationKnowledgesController::class, 'copy'])->name('evaluation.knowledges.copy');
    Route::post('knowledges/import', [EvaluationKnowledgesController::class, 'import'])->name('evaluation.knowledges.import');
    Route::post('knowledges/export', [EvaluationKnowledgesController::class, 'export'])->name('evaluation.knowledges.export');

    // ==================== 题目类型管理 ====================

    // 题目类型相关路由
    Route::get('question-types', [EvaluationQuestionTypesController::class, 'questionTypes'])->name('evaluation.question-types.index');
    Route::post('question-types', [EvaluationQuestionTypesController::class, 'store'])->name('evaluation.question-types.store');
    Route::put('question-types/{id}', [EvaluationQuestionTypesController::class, 'update'])->name('evaluation.question-types.update');
    Route::delete('question-types/{id}', [EvaluationQuestionTypesController::class, 'destroy'])->name('evaluation.question-types.destroy');

    // 兼容下划线格式的题目类型路由
    Route::get('question_types', [EvaluationQuestionTypesController::class, 'questionTypes'])->name('evaluation.question_types.index');
    Route::post('question_types', [EvaluationQuestionTypesController::class, 'store'])->name('evaluation.question_types.store');
    Route::put('question_types/{id}', [EvaluationQuestionTypesController::class, 'update'])->name('evaluation.question_types.update');
    Route::delete('question_types/{id}', [EvaluationQuestionTypesController::class, 'destroy'])->name('evaluation.question_types.destroy');
    Route::get('question-types/statistics', [EvaluationQuestionTypesController::class, 'statistics'])->name('evaluation.question-types.statistics');
    Route::put('question-types/sort', [EvaluationQuestionTypesController::class, 'sort'])->name('evaluation.question-types.sort');
    Route::get('question-types/subjective', [EvaluationQuestionTypesController::class, 'getSubjectiveTypes'])->name('evaluation.question-types.subjective');
    Route::get('question-types/objective', [EvaluationQuestionTypesController::class, 'getObjectiveTypes'])->name('evaluation.question-types.objective');
    Route::post('question-types/copy', [EvaluationQuestionTypesController::class, 'copy'])->name('evaluation.question-types.copy');
    Route::put('question-types/{id}/status', [EvaluationQuestionTypesController::class, 'toggleStatus'])->name('evaluation.question-types.status');
    Route::post('question-types/batch', [EvaluationQuestionTypesController::class, 'batchOperation'])->name('evaluation.question-types.batch');
    Route::get('question-types/options', [EvaluationQuestionTypesController::class, 'getOptions'])->name('evaluation.question-types.options');
    Route::post('question-types/import', [EvaluationQuestionTypesController::class, 'import'])->name('evaluation.question-types.import');
    Route::post('question-types/export', [EvaluationQuestionTypesController::class, 'export'])->name('evaluation.question-types.export');
    

    
    // ==================== 页面相关 ====================

    // 页面相关路由（前端页面渲染）
    Route::get('page/question', function() {
        return view('evaluation.page.question');
    })->name('evaluation.page.question');

    Route::get('page/exam', function() {
        return view('evaluation.page.exam');
    })->name('evaluation.page.exam');

    // ==================== PDF加载 ====================

    // PDF相关路由
    Route::get('loadpdf/sixth_grade_personal_pdf', function() {
        $memberId = request('member_id');
        $distributionId = request('distribution_id');
        // 这里需要调用报告服务生成PDF视图
        return view('evaluation.page.question_pdf', compact('memberId', 'distributionId'));
    })->name('evaluation.loadpdf.sixth-grade');

    Route::get('loadpdf/tenth_grade_personal_pdf', function() {
        $memberId = request('member_id');
        $distributionId = request('distribution_id');
        // 这里需要调用十年级报告服务生成PDF视图
        return view('evaluation.page.question_pdf', compact('memberId', 'distributionId'));
    })->name('evaluation.loadpdf.tenth-grade');

    Route::get('loadpdf/individual', [EvaluationReportController::class, 'generatePdf'])->name('evaluation.loadpdf.individual');

    // ==================== 十年级报告 ====================

    // 十年级报告相关路由
    Route::get('tenth_grade_report', [EvaluationReportController::class, 'tenthGradePersonalReport'])->name('evaluation.tenth-grade-report.personal');
    
});

// ==================== 公开路由（无需认证） ====================

Route::group(['prefix' => 'evaluation/public'], function () {
    
    // 公开的题目预览（如果需要）
    Route::get('questions/{id}/preview', [EvaluationQuestionsController::class, 'preview'])->name('evaluation.questions.preview.public');
    
    // 公开的试卷预览（如果需要）
    Route::get('papers/{id}/preview', [EvaluationPapersController::class, 'publicPreview'])->name('evaluation.papers.preview.public');
});