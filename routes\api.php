<?php

use App\Http\Controllers\GKOauthController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\Tool\AreaController;
use App\Http\Controllers\Tool\ImgCodeController;
use App\Http\Controllers\Tool\MessageController;
use App\Http\Controllers\Tool\UploadController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

//Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
//    return $request->user();
//});


// 登录
Route::post('login', [LoginController::class, 'login']);
//重设密码
Route::post('reset_password', [UserController::class, 'resetPasswordNoLogin']);

// 通用接口
Route::group(['middleware' => ['access_log', 'throttle:100']], function () {
    Route::post('validate_data', [GKOauthController::class, 'validateData']);
    Route::post('validate_ticket', [GKOauthController::class, 'validateTicket']);
    // 无登录状态获取机构及配置信息
    Route::get('orgInfo', [UserController::class, 'getOrganizationInfo']);

    // 省市区
    Route::get('areas', [AreaController::class, 'index']);
});

// 业务类接口
Route::group(['middleware' => ['access_log', 'throttle:500']], function () {
    // 通用权限接口
    require base_path('routes/auth.php');
    // 后台管理端接口
    require base_path('routes/admin.php');
    // 教育局、代理端接口
    require base_path('routes/partner.php');
    // 学校端接口
    require base_path('routes/school.php');
    // 数据同步模块接口
    require base_path('routes/datasync.php');

});

// 工具类接口
Route::group(['middleware' => ['access_log', 'throttle:200']], function () {
    Route::post('upload/image', [UploadController::class, 'uploadImage'])->name('upload_image');
    Route::post('upload/file', [UploadController::class, 'uploadFile'])->name('upload_file');
    Route::post('upload/tinymce_picture', [UploadController::class, 'uploadTinyMceImage'])->name('upload_tinymce_picture');

    Route::get('captcha', [ImgCodeController::class, 'getCaptcha'])->name('captcha');
    Route::post('checkCaptcha', [ImgCodeController::class, 'checkCaptcha'])->name('checkCaptcha');
});

// 消息相关路由
Route::group(['prefix' => 'messages', 'middleware' => ['auth.refresh', 'access_log', 'throttle:200']],function () {
    Route::get('/', [MessageController::class, 'index'])->name('message.index');
    Route::get('/unreadMessageNum', [MessageController::class, 'getUnreadMessageNum'])->name('message.unreadMessageNum');
    Route::get('/{id}', [MessageController::class, 'show'])->name('message.show');
    Route::post('/markAsRead', [MessageController::class, 'markAsRead'])->name('message.markAsRead');
    Route::post('/markAllAsRead', [MessageController::class, 'markAllAsRead'])->name('message.markAllAsRead');
    Route::delete('/', [MessageController::class, 'delete'])->name('message.delete');
});

Route::get('/phpinfo', function () {
    echo phpinfo();
});

