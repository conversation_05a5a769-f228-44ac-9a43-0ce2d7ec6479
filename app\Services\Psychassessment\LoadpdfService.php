<?php

namespace App\Services\Psychassessment;

use App\Models\Psychassessment\PsychassessmentSurvey;
use App\Models\Psychassessment\PsychassessmentResult;
use App\Models\Psychassessment\PsychassessmentPdfTemplate;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;

/**
 * 心理评估-PDF生成服务层
 */
class LoadpdfService
{
    protected $surveyModel;
    protected $resultModel;
    protected $templateModel;
    protected $reportService;

    public function __construct(ReportService $reportService)
    {
        $this->surveyModel = new PsychassessmentSurvey();
        $this->resultModel = new PsychassessmentResult();
        $this->templateModel = new PsychassessmentPdfTemplate();
        $this->reportService = $reportService;
    }

    /**
     * 生成个人报告PDF
     * 
     * @param int $planId
     * @param int $surveyType
     * @param int $memberId
     * @return array
     */
    public function generateIndividualPdf($planId, $surveyType, $memberId): array
    {
        try {
            // 获取报告数据
            $reportData = $this->reportService->getReport($planId, $surveyType, $memberId);
            
            if (!$reportData) {
                return [
                    'success' => false,
                    'message' => '未找到报告数据'
                ];
            }

            // 获取PDF模板
            $template = $this->getPdfTemplate($surveyType, 'individual');
            
            // 生成PDF
            $pdf = $this->generatePdf($reportData, $template);
            
            // 生成文件名
            $fileName = $this->generateFileName($reportData, 'individual');
            
            // 保存PDF文件
            $filePath = $this->savePdfFile($pdf, $fileName);
            
            return [
                'success' => true,
                'type' => 'download',
                'data' => response()->download($filePath, $fileName)->deleteFileAfterSend(true)
            ];
        } catch (\Exception $e) {
            Log::error('生成个人报告PDF失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '生成PDF失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 批量生成个人报告PDF
     * 
     * @param int $planId
     * @param int $surveyType
     * @param array $memberIds
     * @return array
     */
    public function batchGenerateIndividualPdf($planId, $surveyType, array $memberIds): array
    {
        try {
            $results = [];
            $successCount = 0;
            $failCount = 0;

            foreach ($memberIds as $memberId) {
                try {
                    $result = $this->generateIndividualPdf($planId, $surveyType, $memberId);
                    
                    if ($result['success']) {
                        $successCount++;
                        $results[] = [
                            'member_id' => $memberId,
                            'status' => 'success',
                            'file_path' => $result['data'] ?? null
                        ];
                    } else {
                        $failCount++;
                        $results[] = [
                            'member_id' => $memberId,
                            'status' => 'failed',
                            'message' => $result['message']
                        ];
                    }
                } catch (\Exception $e) {
                    $failCount++;
                    $results[] = [
                        'member_id' => $memberId,
                        'status' => 'failed',
                        'message' => $e->getMessage()
                    ];
                }
            }

            return [
                'success' => true,
                'data' => [
                    'total' => count($memberIds),
                    'success_count' => $successCount,
                    'fail_count' => $failCount,
                    'results' => $results
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '批量生成失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 生成班级报告PDF
     * 
     * @param int $planId
     * @param int $surveyType
     * @param int $classId
     * @return array
     */
    public function generateClassReportPdf($planId, $surveyType, $classId): array
    {
        try {
            // 获取班级报告数据
            $reportData = $this->getClassReportData($planId, $surveyType, $classId);
            
            if (!$reportData) {
                return [
                    'success' => false,
                    'message' => '未找到班级报告数据'
                ];
            }

            // 获取PDF模板
            $template = $this->getPdfTemplate($surveyType, 'class');
            
            // 生成PDF
            $pdf = $this->generatePdf($reportData, $template);
            
            // 生成文件名
            $fileName = $this->generateFileName($reportData, 'class');
            
            // 保存PDF文件
            $filePath = $this->savePdfFile($pdf, $fileName);
            
            return [
                'success' => true,
                'type' => 'download',
                'data' => response()->download($filePath, $fileName)->deleteFileAfterSend(true)
            ];
        } catch (\Exception $e) {
            Log::error('生成班级报告PDF失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '生成PDF失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 生成年级报告PDF
     * 
     * @param int $planId
     * @param int $surveyType
     * @param int $gradeId
     * @return array
     */
    public function generateGradeReportPdf($planId, $surveyType, $gradeId): array
    {
        try {
            // 获取年级报告数据
            $reportData = $this->getGradeReportData($planId, $surveyType, $gradeId);
            
            if (!$reportData) {
                return [
                    'success' => false,
                    'message' => '未找到年级报告数据'
                ];
            }

            // 获取PDF模板
            $template = $this->getPdfTemplate($surveyType, 'grade');
            
            // 生成PDF
            $pdf = $this->generatePdf($reportData, $template);
            
            // 生成文件名
            $fileName = $this->generateFileName($reportData, 'grade');
            
            // 保存PDF文件
            $filePath = $this->savePdfFile($pdf, $fileName);
            
            return [
                'success' => true,
                'type' => 'download',
                'data' => response()->download($filePath, $fileName)->deleteFileAfterSend(true)
            ];
        } catch (\Exception $e) {
            Log::error('生成年级报告PDF失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '生成PDF失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 生成学校报告PDF
     * 
     * @param int $planId
     * @param int $surveyType
     * @param int|null $schoolId
     * @return array
     */
    public function generateSchoolReportPdf($planId, $surveyType, $schoolId = null): array
    {
        try {
            // 获取学校报告数据
            $reportData = $this->getSchoolReportData($planId, $surveyType, $schoolId);
            
            if (!$reportData) {
                return [
                    'success' => false,
                    'message' => '未找到学校报告数据'
                ];
            }

            // 获取PDF模板
            $template = $this->getPdfTemplate($surveyType, 'school');
            
            // 生成PDF
            $pdf = $this->generatePdf($reportData, $template);
            
            // 生成文件名
            $fileName = $this->generateFileName($reportData, 'school');
            
            // 保存PDF文件
            $filePath = $this->savePdfFile($pdf, $fileName);
            
            return [
                'success' => true,
                'type' => 'download',
                'data' => response()->download($filePath, $fileName)->deleteFileAfterSend(true)
            ];
        } catch (\Exception $e) {
            Log::error('生成学校报告PDF失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '生成PDF失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取PDF生成状态
     * 
     * @param string $taskId
     * @return array
     */
    public function getPdfGenerationStatus($taskId): array
    {
        // 这里可以实现异步任务状态查询
        // 暂时返回模拟数据
        return [
            'task_id' => $taskId,
            'status' => 'completed', // pending, processing, completed, failed
            'progress' => 100,
            'message' => '生成完成',
            'file_url' => null
        ];
    }

    /**
     * 取消PDF生成任务
     * 
     * @param string $taskId
     * @return bool
     */
    public function cancelPdfGeneration($taskId): bool
    {
        // 这里可以实现取消异步任务的逻辑
        return true;
    }

    /**
     * 获取PDF模板列表
     * 
     * @param int|null $surveyType
     * @return array
     */
    public function getPdfTemplates($surveyType = null): array
    {
        $query = $this->templateModel->where('status', 1);
        
        if ($surveyType) {
            $query->where('survey_type', $surveyType);
        }
        
        return $query->orderBy('sort', 'desc')->get()->toArray();
    }

    /**
     * 更新PDF模板
     * 
     * @param int $templateId
     * @param array $templateData
     * @param object $user
     * @return bool
     */
    public function updatePdfTemplate($templateId, array $templateData, $user): bool
    {
        return $this->templateModel->where('id', $templateId)->update([
            'template_content' => $templateData['template_content'] ?? null,
            'template_config' => json_encode($templateData['template_config'] ?? []),
            'updated_by' => $user->id,
            'updated_at' => now()
        ]);
    }

    /**
     * 预览PDF
     * 
     * @param int $planId
     * @param int $surveyType
     * @param int $memberId
     * @param int|null $templateId
     * @return array
     */
    public function previewPdf($planId, $surveyType, $memberId, $templateId = null): array
    {
        // 获取报告数据
        $reportData = $this->reportService->getReport($planId, $surveyType, $memberId);
        
        // 获取模板
        $template = $templateId ? 
            $this->templateModel->find($templateId) : 
            $this->getPdfTemplate($surveyType, 'individual');
        
        return [
            'report_data' => $reportData,
            'template' => $template,
            'preview_html' => $this->renderTemplate($reportData, $template)
        ];
    }

    /**
     * 生成PDF
     * 
     * @param array $data
     * @param object $template
     * @return \Barryvdh\DomPDF\PDF
     */
    private function generatePdf(array $data, $template)
    {
        $html = $this->renderTemplate($data, $template);
        
        return Pdf::loadHTML($html)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true
            ]);
    }

    /**
     * 渲染模板
     * 
     * @param array $data
     * @param object $template
     * @return string
     */
    private function renderTemplate(array $data, $template): string
    {
        // 这里可以使用模板引擎渲染HTML
        // 暂时返回简单的HTML结构
        return view('psychassessment.pdf.template', [
            'data' => $data,
            'template' => $template
        ])->render();
    }

    /**
     * 获取PDF模板
     * 
     * @param int $surveyType
     * @param string $type
     * @return object|null
     */
    private function getPdfTemplate($surveyType, $type = 'individual')
    {
        return $this->templateModel
            ->where('survey_type', $surveyType)
            ->where('template_type', $type)
            ->where('status', 1)
            ->first();
    }

    /**
     * 生成文件名
     * 
     * @param array $data
     * @param string $type
     * @return string
     */
    private function generateFileName(array $data, $type): string
    {
        $timestamp = date('YmdHis');
        $prefix = match($type) {
            'individual' => '个人报告',
            'class' => '班级报告',
            'grade' => '年级报告',
            'school' => '学校报告',
            default => '报告'
        };
        
        return "{$prefix}_{$timestamp}.pdf";
    }

    /**
     * 保存PDF文件
     * 
     * @param \Barryvdh\DomPDF\PDF $pdf
     * @param string $fileName
     * @return string
     */
    private function savePdfFile($pdf, $fileName): string
    {
        $tempPath = storage_path('app/temp/pdf/');
        
        if (!file_exists($tempPath)) {
            mkdir($tempPath, 0755, true);
        }
        
        $filePath = $tempPath . $fileName;
        $pdf->save($filePath);
        
        return $filePath;
    }

    /**
     * 获取班级报告数据
     * 
     * @param int $planId
     * @param int $surveyType
     * @param int $classId
     * @return array|null
     */
    private function getClassReportData($planId, $surveyType, $classId): ?array
    {
        // 实现班级报告数据获取逻辑
        return [];
    }

    /**
     * 获取年级报告数据
     * 
     * @param int $planId
     * @param int $surveyType
     * @param int $gradeId
     * @return array|null
     */
    private function getGradeReportData($planId, $surveyType, $gradeId): ?array
    {
        // 实现年级报告数据获取逻辑
        return [];
    }

    /**
     * 获取学校报告数据
     * 
     * @param int $planId
     * @param int $surveyType
     * @param int|null $schoolId
     * @return array|null
     */
    private function getSchoolReportData($planId, $surveyType, $schoolId = null): ?array
    {
        // 实现学校报告数据获取逻辑
        return [];
    }
}
