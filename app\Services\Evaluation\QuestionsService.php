<?php

namespace App\Services\Evaluation;

use App\Models\Evaluation\Question;
use App\Models\Evaluation\QuestionOption;
use App\Models\Evaluation\CategoryPortion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

/**
 * 题目管理服务类
 */
class QuestionsService
{
    protected $questionModel;
    protected $questionOptionModel;
    protected $categoryPortionModel;

    public function __construct(
        Question $questionModel,
        QuestionOption $questionOptionModel,
        CategoryPortion $categoryPortionModel
    ) {
        $this->questionModel = $questionModel;
        $this->questionOptionModel = $questionOptionModel;
        $this->categoryPortionModel = $categoryPortionModel;
    }

    /**
     * 获取题目列表
     * 
     * @param array $params
     * @return array
     */
    public function getQuestionsList(array $params): array
    {
        $query = $this->questionModel->query();

        // 应用筛选条件
        $this->applyFilters($query, $params);

        // 分页参数
        $page = $params['page'] ?? 1;
        $pagesize = $params['pagesize'] ?? 15;
        
        // 获取总数
        $total = $query->count();

        // 获取数据
        $questions = $query->with(['options', 'categoryPortions.category'])
            ->orderBy('id', 'desc')
            ->offset(($page - 1) * $pagesize)
            ->limit($pagesize)
            ->get();

        return [
            'list' => $questions,
            'cnt' => $total,
        ];
    }

    /**
     * 应用筛选条件
     * 
     * @param $query
     * @param array $params
     */
    private function applyFilters($query, array $params): void
    {
        // ID筛选
        if (!empty($params['id'])) {
            $ids = is_array($params['id']) ? $params['id'] : explode(',', $params['id']);
            $query->whereIn('id', $ids);
        }

        // 试卷ID筛选
        if (!empty($params['paper_id'])) {
            // 这里可以添加Redis缓存逻辑
            $query->whereHas('papers', function ($q) use ($params) {
                $q->where('paper_id', $params['paper_id']);
            });
        }

        // 是否题冒题
        if (isset($params['is_common'])) {
            $query->where('is_common', $params['is_common']);
        }

        // 默认只查询status=0的记录
        $query->where('status', 0);

        // 学科ID
        if (!empty($params['course_id'])) {
            $query->where('course_id', $params['course_id']);
        }

        // 学科素养ID
        if (!empty($params['category_ids'])) {
            $categoryIds = is_array($params['category_ids']) ? $params['category_ids'] : explode(',', $params['category_ids']);
            $query->whereHas('categoryPortions', function ($q) use ($categoryIds) {
                $q->whereIn('category_id', $categoryIds);
            });
        }

        // 适用年级
        if (!empty($params['grade'])) {
            $query->where('grade', $params['grade']);
        }

        // 题目类型
        if (!empty($params['type_id'])) {
            $query->where('type_id', $params['type_id']);
        }

        // 分值范围
        if (!empty($params['start_score'])) {
            $query->where('score', '>=', $params['start_score']);
        }
        if (!empty($params['end_score'])) {
            $query->where('score', '<=', $params['end_score']);
        }

        // 关键字查询
        if (!empty($params['content'])) {
            $query->where('content', 'like', '%' . $params['content'] . '%');
        }
    }

    /**
     * 创建题目
     * 
     * @param array $data
     * @return Question
     */
    public function createQuestion(array $data): Question
    {
        return DB::transaction(function () use ($data) {
            // 创建题目
            $question = $this->questionModel->create([
                'is_common' => $data['is_common'] ?? 0,
                'type_id' => $data['type_id'],
                'grade' => $data['grade'],
                'course_id' => $data['course_id'],
                'situation' => $data['situation'] ?? 0,
                'score' => $data['score'],
                'knowlege_ids' => $data['knowlege_ids'] ?? '',
                'content' => $data['content'],
                'answer' => $data['answer'] ?? '',
                'analysis' => $data['analysis'] ?? '',
                'parent_id' => $data['parent_id'] ?? 0,
            ]);

            // 创建选项
            if (!empty($data['options'])) {
                $this->createQuestionOptions($question->id, $data['options']);
            }

            // 创建素养占比
            if (!empty($data['categorey_percentage'])) {
                $this->createCategoryPortions($question->id, $data['categorey_percentage']);
            }

            return $question->load(['options', 'categoryPortions']);
        });
    }

    /**
     * 更新题目
     * 
     * @param int $id
     * @param array $data
     * @return Question
     */
    public function updateQuestion(int $id, array $data): Question
    {
        return DB::transaction(function () use ($id, $data) {
            $question = $this->questionModel->findOrFail($id);

            // 更新题目基本信息
            $question->update([
                'is_common' => $data['is_common'] ?? $question->is_common,
                'type_id' => $data['type_id'] ?? $question->type_id,
                'grade' => $data['grade'] ?? $question->grade,
                'course_id' => $data['course_id'] ?? $question->course_id,
                'situation' => $data['situation'] ?? $question->situation,
                'score' => $data['score'] ?? $question->score,
                'knowlege_ids' => $data['knowlege_ids'] ?? $question->knowlege_ids,
                'content' => $data['content'] ?? $question->content,
                'answer' => $data['answer'] ?? $question->answer,
                'analysis' => $data['analysis'] ?? $question->analysis,
                'parent_id' => $data['parent_id'] ?? $question->parent_id,
            ]);

            // 更新选项
            if (isset($data['options'])) {
                $this->updateQuestionOptions($question->id, $data['options']);
            }

            // 更新素养占比
            if (isset($data['categorey_percentage'])) {
                $this->updateCategoryPortions($question->id, $data['categorey_percentage']);
            }

            return $question->load(['options', 'categoryPortions']);
        });
    }

    /**
     * 删除题目
     * 
     * @param int $id
     * @return bool
     */
    public function deleteQuestion(int $id): bool
    {
        return DB::transaction(function () use ($id) {
            $question = $this->questionModel->findOrFail($id);
            
            // 删除相关数据
            $this->questionOptionModel->where('question_id', $id)->delete();
            $this->categoryPortionModel->where('question_id', $id)->delete();
            
            return $question->delete();
        });
    }

    /**
     * 上传图片
     * 
     * @param Request $request
     * @return array
     */
    public function uploadImage(Request $request): array
    {
        $request->validate([
            'file' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $file = $request->file('file');
        $path = $file->store('evaluation', 'public');
        
        return [
            'url' => Storage::url($path),
            'path' => $path
        ];
    }

    /**
     * 更新排序
     * 
     * @param array $data
     * @return bool
     */
    public function updateSort(array $data): bool
    {
        if (empty($data['questions'])) {
            throw new \InvalidArgumentException('排序数据不能为空');
        }

        return DB::transaction(function () use ($data) {
            foreach ($data['questions'] as $questionData) {
                if (isset($questionData['question_id']) && isset($questionData['sort'])) {
                    $this->questionModel->where('id', $questionData['question_id'])
                        ->update(['sort' => $questionData['sort']]);
                }
            }
            return true;
        });
    }

    /**
     * 创建题目选项
     * 
     * @param int $questionId
     * @param array $options
     */
    private function createQuestionOptions(int $questionId, array $options): void
    {
        foreach ($options as $option) {
            $this->questionOptionModel->create([
                'question_id' => $questionId,
                'content' => $option['content'] ?? '',
                'score' => $option['score'] ?? 0,
                'title' => $option['title'] ?? 0,
                'answer' => $option['answer'] ?? '',
            ]);
        }
    }

    /**
     * 更新题目选项
     * 
     * @param int $questionId
     * @param array $options
     */
    private function updateQuestionOptions(int $questionId, array $options): void
    {
        // 删除原有选项
        $this->questionOptionModel->where('question_id', $questionId)->delete();
        
        // 创建新选项
        $this->createQuestionOptions($questionId, $options);
    }

    /**
     * 创建素养占比
     * 
     * @param int $questionId
     * @param array $portions
     */
    private function createCategoryPortions(int $questionId, array $portions): void
    {
        foreach ($portions as $portion) {
            $this->categoryPortionModel->create([
                'question_id' => $questionId,
                'parent_id' => $portion['parent_id'],
                'category_id' => $portion['category_id'],
                'percentage' => $portion['percentage'] ?? 0,
            ]);
        }
    }

    /**
     * 更新素养占比
     * 
     * @param int $questionId
     * @param array $portions
     */
    private function updateCategoryPortions(int $questionId, array $portions): void
    {
        // 删除原有占比
        $this->categoryPortionModel->where('question_id', $questionId)->delete();
        
        // 创建新占比
        $this->createCategoryPortions($questionId, $portions);
    }
}
