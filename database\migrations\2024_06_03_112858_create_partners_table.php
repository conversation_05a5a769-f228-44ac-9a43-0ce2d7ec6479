<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 合作伙伴表
 * 包括但不仅限于：代理商、教育局
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('partners', function (Blueprint $table) {
            $table->id();
            $table->string('name',100)->comment('伙伴名称');
            $table->string('province',20)->comment('省');
            $table->string('city',20)->nullable()->comment('市');
            $table->string('district',20)->nullable()->comment('区县');
            $table->text('address')->nullable()->comment('详细地址');
            $table->tinyInteger('type')->comment('伙伴类型1普代2区代3省代4区局5市局6省局');
            $table->date('date_start')->comment('启用日期');
            $table->date('date_due')->comment('到期日期');
            $table->integer('site_config_id')->default(0)->comment('站点配置ID');
            $table->tinyInteger('status')->default(1)->comment('状态1启用2禁用');
            $table->integer('agent_id')->comment('原表ID,用完可删');
            $table->timestamps();
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('partners');
    }
};
