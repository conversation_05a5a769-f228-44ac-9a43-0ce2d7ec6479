<?php
namespace App\Services\School\Assessment\IndividualReport\Psychology;

/**
 * 焦虑评估报告服务。
 * 负责生成焦虑评估的个人报告，包括焦虑程度分析、图表和建议。
 */
class AnxietyService extends AbstractPsychologyReportService
{
    /**
     * 获取焦虑评估的等级常量。
     *
     * @return array 返回包含总分和维度得分等级定义的数组。
     */
    protected function getLevelConstants(): array
    {
        return [
            'total' => [
                ['max' => 50, 'code' => 'free'],
                ['max' => 60, 'code' => 'mild'],
                ['max' => 70, 'code' => 'moderate'],
                ['max' => PHP_FLOAT_MAX, 'code' => 'severe']
            ],
            'dimension' => [
                ['max' => 50, 'code' => 'normal', /* 'text' => '正常水平' */], // 可选添加text用于报告
                ['max' => 70, 'code' => 'some',   /* 'text' => '一些焦虑' */],
                ['max' => PHP_FLOAT_MAX, 'code' => 'much', /* 'text' => '非常焦虑' */]
            ]
        ];
    }

    /**
     * 获取此服务对应的配置键名。
     *
     * @return string 返回配置键名 'anxiety'。
     */
    protected function getConfigKey(): string
    {
        return 'anxiety';
    }

    /**
     * 处理评估信息，生成报告所需的图表、描述、能力和建议。
     * AnxietyService 的此方法需要特殊处理 standard_results，因为第一个维度代表总体焦虑分数。
     *
     * @param array $standard_results 标准化后的评估结果。
     * @param array $config 相关的配置信息。
     * @return array 包含图表、描述、能力和建议的数组。
     */
    protected function processAssessmentInfo(array $standard_results, array $config): array
    {
        $total_anxiety_score = 0;
        $specific_dimension_scores = [];
        if (isset($standard_results['dimensions'][0])) {
            $total_anxiety_score = $standard_results['dimensions'][0]['score'];
            $specific_dimension_scores = array_slice($standard_results['dimensions'], 1);
        }

        $dimension_results = $this->analyzeDimensions($specific_dimension_scores, $config);
        $anxiety_code = $this->getScoreLevel($total_anxiety_score, 'total');
        
        return [
            'charts'      => $this->generateCharts($standard_results), // 基类方法应该能处理
            'description' => $this->generateReportTextInternal($total_anxiety_score, $anxiety_code, $dimension_results, $config),
            'competence'  => $dimension_results['competence'],
            'advise'      => [
                'degree' => $config['degree']['advise'][$anxiety_code]['desc'],
                'specific' => $dimension_results['advise']
            ]
        ];
    }

    /**
     * 分析具体的焦虑维度得分。
     *
     * @param array $specific_dimension_scores 具体的焦虑维度分数数组。
     * @param array $config 相关的配置信息。
     * @return array 包含分类后的维度名称、能力描述和建议的数组。
     */
    protected function analyzeDimensions(array $specific_dimension_scores, array $config): array
    {
        $categories = ['normal' => [], 'some' => [], 'much' => []];
        $competence = [];
        $advise = [];

        foreach ($specific_dimension_scores as $key => $dimension_data) {
            // 假设 config['dimension_codes'] 的索引从0开始，对应 specific_dimension_scores
            $dimension_name = $config['dimension_codes'][$key] ?? $dimension_data['name']; 
            $level = $this->getScoreLevel($dimension_data['score'], 'dimension');
            $categories[$level][] = $dimension_name;
            if (isset($config[$key])) { // 确保配置键存在
                $competence[$key] = $config[$key][$level];
                $advise[$key] = $config[$key]['advise'];
            }
        }

        return [
            'categories' => $categories,
            'competence' => $competence,
            'advise' => $advise
        ];
    }

    /**
     * 生成焦虑报告的内部文本描述部分。
     *
     * @param float $total_anxiety_score 总体焦虑分数。
     * @param string $anxiety_code 焦虑等级代码。
     * @param array $dimension_results 分析后的维度结果。
     * @param array $config 相关的配置信息。
     * @return array 包含总结和详细描述的文本数组。
     */
    protected function generateReportTextInternal(float $total_anxiety_score, string $anxiety_code, array $dimension_results, array $config): array
    {
        $anxiety_situation = $config['degree'][$anxiety_code]['title'];
        $description = sprintf(
            '测验结果表明，你的焦虑程度总体得分为%.2f分，处于%s。%s',
            $total_anxiety_score,
            $anxiety_situation,
            $config['degree'][$anxiety_code]['desc']
        );

        $text = $this->generateDetailedText($dimension_results['categories']);

        return [$description, $text];
    }
    
    /**
     * 生成报告的文本描述部分 (满足基类抽象方法签名)。
     * 实际调用 generateReportTextInternal 进行处理。
     *
     * @param array $standard_results 标准化后的评估结果。
     * @param array $dimension_results 分析后的维度结果。
     * @param array $config 相关的配置信息。
     * @return array 包含总结和详细描述的文本数组。
     */
    protected function generateReportText(array $standard_results, array $dimension_results, array $config): array
    { 
        // 此方法是为了满足基类抽象方法的签名，实际调用的是 generateReportTextInternal
        $total_anxiety_score = $standard_results['dimensions'][0]['score'] ?? 0;
        $anxiety_code = $this->getScoreLevel($total_anxiety_score, 'total');
        return $this->generateReportTextInternal($total_anxiety_score, $anxiety_code, $dimension_results, $config);
    }

    /**
     * 生成详细的焦虑来源文本。
     *
     * @param array $categories 分类后的维度名称。
     * @return string 详细的焦虑来源文本。
     */
    private function generateDetailedText(array $categories): string
    {
        $text = '具体来说，你';
        $levels = [
            'some' => ['prefix' => '在', 'suffix' => '上处于一些焦虑水平'],
            'normal' => ['prefix' => '，在', 'suffix' => '上处于正常水平'],
            'much' => ['prefix' => '，但在', 'suffix' => '上处于非常焦虑水平']
        ];

        $has_content = false;
        foreach ($levels as $level => $format) {
            if (!empty($categories[$level])) {
                $dimensions = implode('、', $categories[$level]);
                $text .= ($has_content ? $format['prefix'] : ($level === 'normal' ? '在' : $format['prefix'])) . $dimensions . $format['suffix'];
                $has_content = true;
            }
        }
        if (!$has_content) {
            $text = '具体来说，各项焦虑指标均在正常范围内。';
        } else {
            $text .= '。说明你焦虑的来源可能';
            $source_parts = [];
            if (!empty($categories['much'])) {
                $source_parts[] = '大部分是' . implode('、', $categories['much']);
            }
            if (!empty($categories['some'])) {
                $source_parts[] = '一小部分来源于' . implode('、', $categories['some']);
            }
            if (empty($source_parts)) {
                $text .= '主要在可控范围内。';
            } else {
                $text .= implode('，', $source_parts) . '。';
            }
        }
        return $text;
    }
}
