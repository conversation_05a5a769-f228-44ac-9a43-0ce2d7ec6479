<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAssessmentCareerMbtiTemplatesTable extends Migration
{
    /**
     * 运行迁移
     * 创建MBTI职业性格测评模板表
     *
     * @return void
     */
    public function up()
    {
        Schema::create('assessment_career_mbti_templates', function (Blueprint $table) {
            $table->id();
            $table->string('code', 50)->comment('模板代码');
            $table->string('appellation')->comment('称谓');
            $table->text('preface')->comment('前言');
            $table->text('interpret')->comment('解释');
            $table->text('keywords')->comment('关键词');
            $table->text('superpowers')->comment('超能力');
            $table->text('campus_roles')->comment('校园角色');
            $table->text('subject_adaptation')->comment('学科适应');
            $table->text('learning_strategy')->comment('学习策略');
            $table->text('major_analyse')->comment('专业分析');
            $table->text('occupation_analyse')->comment('职业分析');
            $table->text('social_comfort_level')->comment('社交舒适度');
            $table->text('making_friends_analyse')->comment('交友分析');
            $table->text('communication_skill')->comment('沟通技巧');
            $table->text('stress_analyse')->comment('压力分析');
            $table->text('growth_analyse')->comment('成长分析');
            $table->text('example_case')->comment('案例');
            $table->text('summary')->comment('总结');
            $table->unsignedInteger('grade_type')->default(2)->comment('适用年级类别1初中 2高中');
            $table->string('creator')->nullable()->comment('创建者');
            $table->string('updater')->nullable()->comment('更新者');
            $table->timestamps();
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index('code');
            $table->index('grade_type');
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `assessment_career_mbti_templates` comment 'MBTI职业性格测评模板表'");
    }

    /**
     * 回滚迁移
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('assessment_career_mbti_templates');
    }
}