<?php

namespace app\evaluation\model;
use think\Db;
class Distribution extends BaseModel
{
    protected $table = 'ysy_evaluation_distribution';

    // 设置子级关联
    public function papers()
    {
        return $this->hasMany('DistributionDetail', 'distribution_id', 'id')->where('status',0);
    }

    public function getClassIdsAttr($value)
    {
        $arr = explode(',', $value);
        if (!empty($arr)) {
            return Db::name('class c')->join('grade g','c.grade_id = g.id')->field('c.id,concat(g.grade_name,c.name) as name')->whereIn('c.id', $arr)->select();
        } else {
            return [];
        }
    }

    public function getMemberIdsAttr($value)
    {
        if(empty($value)) return [];
        $arr = explode(',', $value);
        if (!empty($arr)) {
            $sql = "select a.name,a.member_id,b.id as class_id,concat(c.grade_name,b.name) as class_name from 
                (
                    select name, member_id, class_id
                    from  ysy_student  t1
                    where id = (
                        select max(id)
                        from ysy_student t2
                        where t1.member_id = t2.member_id and t2.member_id in (".$value.") and step >= 0
                    )
                    and t1.member_id in (".$value.") and step >= 0 
                ) a
               left join ysy_class b on a.class_id = b.id
               left join ysy_grade c on b.grade_id = c.id
               ";
            return Db::query($sql);
        } else {
            return [];
        }
    }

}