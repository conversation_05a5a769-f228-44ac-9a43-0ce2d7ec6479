<?php

namespace App\Http\Resources\School\Assessment;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * 问题统计资源类
 *
 * 用于将问题统计数据转换为API响应格式，移除不必要的字段
 */
class QuestionStatisticsResource extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'assessment_id' => $this->assessment_id,
            'is_normal' => $this->is_normal,
            'content' => $this->content,
            'number' => $this->number,
            'correct' => $this->correct,
            'title' => $this->title,
            'statistics' => $this->statistics,
        ];
    }
}
