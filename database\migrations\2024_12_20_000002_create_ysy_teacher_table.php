<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 使用sync_mysql连接创建ysy_teacher表
        Schema::connection('sync_mysql')->create('ysy_teacher', function (Blueprint $table) {
            $table->id();
            $table->integer('member_id')->comment('关联ysy_member表的ID');
            $table->string('name', 50)->comment('教师姓名');
            $table->integer('school_id')->comment('学校ID');
            $table->integer('school_district')->comment('校区ID');
            $table->tinyInteger('is_psych')->default(0)->comment('是否心理教师1是0否');
            $table->string('class_ids', 255)->nullable()->comment('班级ID列表，逗号分隔');
            $table->tinyInteger('step')->default(0)->comment('步骤状态');
            $table->timestamps();
            
            // 索引
            $table->index('member_id');
            $table->index('school_id');
            $table->index('school_district');
            $table->index('step');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('sync_mysql')->dropIfExists('ysy_teacher');
    }
};
