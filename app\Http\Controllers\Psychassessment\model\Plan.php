<?php
namespace app\psychassessment\model;

use think\Model;
use think\Db;

class Plan extends Model
{
    protected $table = 'ysy_psychassessment_plan';

    // 设置子级关联
    public function surveys()
    {
        return $this->hasMany('PlanDetail', 'plan_id', 'id')->where('status',0);
    }

    public function assess_list(){
        return Db::name('survey')->field('id,title,survey_type')->where(['survey_type'=>['in',[26,27,28]]])->select();
    }

    public function get_psychassessment_class_member(){
        return Db::name('survey')->field('id,title')->where(['survey_type'=>['in',[26,27,28]]])->select();
    }
}