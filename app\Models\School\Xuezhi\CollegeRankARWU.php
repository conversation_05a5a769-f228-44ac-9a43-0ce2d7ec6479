<?php

namespace App\Models\School\Xuezhi;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CollegeRankARWU extends BaseModel
{
    use HasFactory;
    protected $table = 'college_rank_arwu';

    public static function getSortedData()
    {
        return self::query()
            ->orderByRaw("CAST(SUBSTRING_INDEX(`rank`, '-', 1) AS UNSIGNED) ASC")
            ->orderBy('college_id', 'ASC')
            ->get();
    }

}
