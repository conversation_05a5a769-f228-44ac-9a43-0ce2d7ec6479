# 测评系统开发文档

**文档日期：** 2024-07-10

## 目录

1. [系统概述](#系统概述)
2. [测评流程](#测评流程)
3. [系统架构](#系统架构)
4. [测评类型](#测评类型)
5. [测评算分逻辑](#测评算分逻辑)
6. [报告生成](#报告生成)
7. [数据模型](#数据模型)
8. [开发注意事项](#开发注意事项)

## 系统概述

测评系统是一个综合性的学生能力评估平台，通过多种类型的测评帮助学校、教师和学生了解学生在不同领域的能力水平和发展潜力。系统支持多种测评类型，包括生涯测评、五力测评、核心素养测评、心理健康测评和学科兴趣测评等。

系统采用工厂模式和模板方法模式设计，具有高度的可扩展性和灵活性，能够根据不同的测评类型和模块动态加载相应的服务实现。

## 测评流程

测评系统的完整流程如下：

1. **测评安排**：教师或管理员创建测评任务，设置测评时间、参与学生等
2. **任务分发**：系统将测评任务分发给指定的学生
3. **答题过程**：学生在规定时间内完成测评问卷
4. **答案提交**：学生提交答案后，系统进行以下处理：
   - 验证测评任务和时间
   - 检查是否重复提交
   - 保存答案数据
   - 触发异步任务链进行后续处理
5. **分数计算**：系统根据测评类型计算各维度得分和总分
6. **报告生成**：系统生成个人报告和团体报告
7. **PDF生成**：系统生成可下载的PDF报告

整个流程采用异步任务队列处理，确保系统的高性能和可靠性。

## 系统架构

测评系统采用分层架构设计，主要包括以下组件：

### 控制器层

- `AssessmentProcessController`：处理测评流程，包括答案提交和报告生成
- `PdfHtmlController`：处理PDF报告生成

### 服务层

服务层采用工厂模式和模板方法模式，根据测评类型和模块动态加载相应的服务实现：

- `AssessmentServiceFactory`：根据测评类型和模块创建相应的服务实例
- `ScoreServiceFactory`：根据测评ID创建相应的分数计算服务

服务层主要包括以下几个模块：

1. **Answer模块**：处理答案提交和保存
2. **Score模块**：处理分数计算
3. **IndividualReport模块**：生成个人报告
4. **GroupReport模块**：生成团体报告

### 任务队列

系统使用Laravel的队列系统处理异步任务：

- `SaveAnswerJob`：保存答案
- `CalculateScoreJob`：计算分数
- `GeneratePdfJob`：生成PDF报告

## 测评类型

系统支持多种测评类型，主要分为以下几类：

### 1. 生涯测评 (Career)

生涯测评主要评估学生的职业发展潜力和倾向，包括：

- 学习生活适应性 (Adaptation)
- 智能评估 (Intelligence)
- 性格评估 (Personality)
- 兴趣评估 (Interest)
- 生涯发展水平评估 (Development)

### 2. 五力测评 (Capability)

五力测评主要评估学生的核心能力，包括：

- 学习力 (LearningAbility)
- 批判性思维 (CriticalThinking)
- 问题解决能力 (ProblemSolving)
- 创造性思维 (CreativeThinking)
- 沟通与合作 (CommunicationCollaboration)

### 3. 核心素养测评 (Competency)

核心素养测评主要评估学生的综合素质，包括：

- 批判性思维 (CriticalThinking)
- 创造性思维 (CreativeThinking)
- 沟通与合作 (CommunicationCollaboration)
- 问题解决能力 (ProblemSolving)

### 4. 心理健康测评 (Psychology)

心理健康测评主要评估学生的心理状态，包括：

- 自信心 (Confidence)
- 自我意识 (SelfAwareness)
- 焦虑 (Anxiety)

### 5. 学科兴趣测评 (Subject)

学科兴趣测评主要评估学生对不同学科的兴趣程度。

## 测评算分逻辑

系统针对不同类型的测评采用不同的算分逻辑，以下是主要的算分逻辑：

### 1. 生涯测评算分逻辑

#### 学习生活适应性 (AdaptationService)

```php
public function calculate($params): array
{
    $dimensionScores = $this->calculateScores($params);
    return $this->convertToStandardScores(
        $params['assessment_task_assignment_id'],
        $dimensionScores
    );
}
```

生涯测评通常采用标准分转换，将原始分数转换为标准化的分数，便于比较和解释。

### 2. 五力测评算分逻辑

#### 批判性思维 (CriticalThinkingService)

```php
public function calculate(array $params): array
{
    $dimensionScores = $this->calculateScores($params);

    $totalQuestions = 0;
    $totalScore = 0;
    foreach ($dimensionScores as $key => $dimension){
        $totalScore += $dimension['score'];
        $dimensionScores[$key]['score'] = round($dimension['score'] * 10 / $dimension['question_count'], 1);
        $totalQuestions += $dimension['question_count'];
        unset($dimensionScores[$key]['question_count']);
    }
    $averageScore = round($totalScore * 10 / $totalQuestions, 1);

    return ['dimensions'=>$dimensionScores,'totalScore'=>$averageScore];
}
```

五力测评通常采用加权平均的方式计算分数，每个维度的得分 = 原始得分 * 权重 / 题目数量。

### 3. 核心素养测评算分逻辑

#### 问题解决能力 (ProblemSolvingService)

```php
public function calculate(array $params): array
{
    $answers = $this->getAnswers($params);
    $questions = $this->getQuestions($params['assessment_id']);
    
    $answerScores = $this->calculateAnswerScores($answers, $questions);
    $dimensionScores = $this->calculateDimensionScores(
        $params['assessment_id'],
        $questions,
        $answerScores
    );
    
    return $this->calculateProblemSolvingDimensionScores($dimensionScores);
}
```

问题解决能力测评区分主观题和客观题，采用不同的计分方式：

```php
$subjectiveShow = round($subjectiveTotal * self::SCORE_MULTIPLIER['subjective'] / $subjectiveCount, 1);
$dimensionObjectiveScores['score'] = $dimensionObjectiveScores['score'] * self::SCORE_MULTIPLIER['objective'];
```

### 4. 心理健康测评算分逻辑

#### 自信心 (ConfidenceService)

```php
public function calculate(array $params): array
{
    $dimensionScores = $this->calculateScores($params);
    
    // 计算各维度得分
    $totalScores = [];
    foreach ($dimensionScores as $key => $dimension) {
        // 维度得分 = (维度内题目得分总和 / 维度题目数量) × 20
        $score = round($dimension['score'] / $dimension['question_count'] * 20, 1);
        $dimensionScores[$key]['score'] = $score;
        $totalScores[] = $score;
        unset($dimensionScores[$key]['question_count']);
    }

    // 计算总分
    $averageScore = ceil(array_sum($totalScores) / count($totalScores));

    return ['dimensions'=>$dimensionScores,'total_score'=>$averageScore];
}
```

心理健康测评通常采用平均分计算方式，并乘以一个固定系数（如20）进行标准化。

### 5. 学科兴趣测评算分逻辑

学科兴趣测评通常采用简单的累加方式计算分数，然后进行标准化处理。

### 通用算分流程

尽管不同测评类型的具体算法不同，但它们都遵循以下通用流程：

1. 获取学生答案数据
2. 获取测评问题数据
3. 计算每个问题的得分
4. 按维度汇总得分
5. 应用特定的计分规则和权重
6. 返回最终的分数结果

## 报告生成

系统支持两种类型的报告：个人报告和团体报告。

### 个人报告生成

个人报告生成由`IndividualReport`模块处理，主要流程如下：

```php
public function generateReport(array $params): array
{
    $assignmentId = $params['assessment_task_assignment_id'];
    // 获取分发信息
    $assignmentInfo = $this->getAssignmentInfo($assignmentId);
    
    // 生成报告
    $results = json_decode($assignmentInfo['results'], true);
    $assessmentInfo = $this->generateSpecificReport($results, $params);

    // 合并结果
    return array_merge($assignmentInfo, $assessmentInfo);
}
```

不同类型的测评有不同的报告生成逻辑，但都遵循上述模板方法。

### 团体报告生成

团体报告生成由`GroupReport`模块处理，主要流程如下：

```php
public function generateReport(array $params, int $schoolId): array
{
    // 获取评估数据
    $allAssessments = $this->getAssessmentData($params, $schoolId);
    $filteredAssessments = $this->getAssessmentData($params, $schoolId, $this->getFilterConditions($params));
    
    $allAssessmentWithResults = array_filter($allAssessments, fn($v) => !empty($v['results']));
    $filteredAssessmentWithResults = array_filter($filteredAssessments, fn($v) => !empty($v['results']));
    
    // 统计数据
    $classStatistics = $this->calculateClassStatistics($filteredAssessments);
    
    // 由子类实现具体的报告生成逻辑
    return $this->generateReportData($allAssessmentWithResults, $filteredAssessmentWithResults, $classStatistics, $params);
}
```

团体报告通常包括参与统计、分布统计、平均分统计等内容。

## 数据模型

测评系统的主要数据模型包括：

1. **Assessment**：测评类型表，存储测评的基本信息
2. **AssessmentSchedule**：测评安排表，存储测评的时间安排
3. **AssessmentTask**：测评任务表，存储具体的测评任务
4. **AssessmentTaskAssignment**：测评任务分配表，存储学生的测评任务
5. **AssessmentXXXQuestion**：测评问题表，存储不同类型的测评问题
6. **AssessmentXXXAnswer**：测评答案表，存储学生的答案

其中，XXX代表不同的测评类型，如Career、Capability、Competency、Psychology、Subject等。

## 开发注意事项

1. **扩展新测评类型**：
   - 在`AssessmentTypeEnum`中添加新的测评类型
   - 创建相应的问题表和答案表
   - 实现相应的Answer、Score、IndividualReport和GroupReport服务

2. **修改算分逻辑**：
   - 修改相应的Score服务中的calculate方法
   - 注意保持与现有报告格式的兼容性

3. **修改报告生成逻辑**：
   - 修改相应的IndividualReport或GroupReport服务
   - 注意保持与前端展示的兼容性

4. **性能优化**：
   - 答案提交和分数计算使用异步队列处理
   - 考虑使用缓存减少数据库查询
   - 大量数据处理时考虑分批处理

5. **安全性考虑**：
   - 验证学生只能提交自己的测评答案
   - 验证测评时间是否在有效范围内
   - 防止重复提交答案

通过遵循以上开发注意事项，可以确保测评系统的稳定性、可扩展性和安全性。
