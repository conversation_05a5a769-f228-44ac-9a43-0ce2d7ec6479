<?php
/**
 * 配置服务测试脚本
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

try {
    // 检查 Laravel 是否可以启动
    require_once __DIR__ . '/../vendor/autoload.php';
    
    $app = require_once __DIR__ . '/../bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    
    // 创建 ConfigService 实例
    $configService = new \App\Services\Evaluation\ConfigService();
    
    // 测试各个配置方法
    $testResults = [
        'getConfig' => $configService->getConfig(),
        'isHigh' => $configService->isHigh(),
        'course' => $configService->course(),
        'grade' => $configService->grade(),
        'scenario' => $configService->scenario(),
        'cenarioType' => $configService->cenarioType(),
        'competenceShine' => $configService->competenceShine(),
        'characterCode' => $configService->characterCode(),
        'getRedisKey' => $configService->getRedisKey(),
        'getFullConfig' => $configService->getFullConfig(),
        'getSystemSettings' => $configService->getSystemSettings(),
        'getMiIntelligenceTypes' => $configService->getMiIntelligenceTypes(),
        'getHollandTypes' => $configService->getHollandTypes(),
    ];
    
    $response = [
        'status' => 'success',
        'message' => 'ConfigService 测试成功！',
        'debug_info' => [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'config_service_class' => get_class($configService),
            'timestamp' => date('Y-m-d H:i:s')
        ],
        'test_results' => $testResults
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $response = [
        'status' => 'error',
        'message' => 'ConfigService 测试失败',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString(),
        'debug_info' => [
            'php_version' => PHP_VERSION,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
