<?php

namespace App\Services\School\Assessment;

use App\Enums\AssessmentTypeEnum;
use App\Models\School\Assessment\Question\AssessmentCareerQuestion;
use App\Models\School\Assessment\Question\AssessmentSubjectQuestion;
use App\Models\School\Assessment\Question\AssessmentCapabilityQuestion;
use App\Models\School\Assessment\Question\AssessmentCompetencyQuestion;
use App\Models\School\Assessment\Question\AssessmentPsychologyQuestion;
use InvalidArgumentException;
use Illuminate\Support\Facades\Redis;
use App\Exceptions\BusinessException;

class QuestionService
{
    /**
     * 问题类型与模型的映射关系
     *
     * @var array
     */
    protected $questionModels = [
        AssessmentTypeEnum::CAREER => AssessmentCareerQuestion::class,
        AssessmentTypeEnum::SUBJECT => AssessmentSubjectQuestion::class,
        AssessmentTypeEnum::CAPABILITY => AssessmentCapabilityQuestion::class,
        AssessmentTypeEnum::COMPETENCY => AssessmentCompetencyQuestion::class,
        AssessmentTypeEnum::PSYCHOLOGY => AssessmentPsychologyQuestion::class,
    ];

    /**
     * 获取测评问题列表
     *
     * @param int $assessmentId
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getQuestions(int $assessmentId, $type)
    {
        try {
            // 定义缓存键
            $cacheKey = $this->getQuestionCacheKey($assessmentId, $type);
            
            // 尝试从缓存获取数据
            $cachedData = Redis::get($cacheKey);
            if ($cachedData) {
                return unserialize($cachedData);
            }
    
            // 获取问题列表
            $questions = $this->fetchQuestionsByType($assessmentId, $type);
    
            // 将数据存入缓存（永久缓存）
            Redis::set($cacheKey, serialize($questions));
            
            return $questions;
        } catch (\Exception $e) {
            throw new BusinessException("获取问题列表失败逻辑错误", 500, $e->getMessage());
        }
    }

    /**
     * 更新问题缓存
     *
     * @param int $assessmentId
     * @param string $type
     * @return void
     */
    public function updateQuestionCache(int $assessmentId, string $type)
    {
        $this->validateQuestionType($type);
        
        // 定义缓存键
        $cacheKey = $this->getQuestionCacheKey($assessmentId, $type);
        
        // 获取最新问题列表
        $questions = $this->fetchQuestionsByType($assessmentId, $type);
        
        // 更新缓存
        Redis::set($cacheKey, serialize($questions));
    }

    /**
     * 清除问题缓存
     *
     * @param int $assessmentId
     * @param string|null $type
     * @return void
     */
    public function clearQuestionCache(int $assessmentId, ?string $type = null)
    {
        if ($type) {
            $this->validateQuestionType($type);
            
            // 清除特定类型的缓存
            $cacheKey = $this->getQuestionCacheKey($assessmentId, $type);
            Redis::del($cacheKey);
        } else {
            // 清除所有类型的缓存
            foreach (AssessmentTypeEnum::getAll() as $t) {
                $cacheKey = $this->getQuestionCacheKey($assessmentId, $t);
                Redis::del($cacheKey);
            }
        }
    }

    /**
     * 根据类型获取问题列表
     *
     * @param int $assessmentId
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function fetchQuestionsByType(int $assessmentId, string $type)
    {
        $this->validateQuestionType($type);
        
        $modelClass = $this->questionModels[$type];
        $query = $modelClass::where('assessment_id', $assessmentId);
        
        // 对测评特殊处理，按number字段排序
        if ($type === AssessmentTypeEnum::CAPABILITY) {
            $query->orderBy('number');
        }
        
        return $query->get();
    }

    /**
     * 验证问题类型是否有效
     *
     * @param string $type
     * @return void
     * @throws InvalidArgumentException
     */
    protected function validateQuestionType(string $type)
    {
        if (!isset($this->questionModels[$type])) {
            throw new InvalidArgumentException("不支持的问题类型: {$type}");
        }
    }

    /**
     * 获取问题缓存键
     *
     * @param int $assessmentId
     * @param string $type
     * @return string
     */
    protected function getQuestionCacheKey(int $assessmentId, string $type)
    {
        return "assessment:{$assessmentId}:questions:{$type}";
    }
}