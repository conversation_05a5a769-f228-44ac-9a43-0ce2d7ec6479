<?php

namespace App\Services\Evaluation;

use App\Models\Evaluation\EvaluationLog;
use App\Models\Evaluation\EvaluationAnswer;
use App\Models\Evaluation\Distribution;
use App\Models\Evaluation\Papers;
use App\Models\School\System\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

/**
 * 统计分析服务类
 */
class StatisticsService
{
    protected $evaluationLogModel;
    protected $evaluationAnswerModel;
    protected $distributionModel;
    protected $paperModel;
    protected $studentModel;
    protected $user;

    public function __construct(
        EvaluationLog $evaluationLogModel,
        EvaluationAnswer $evaluationAnswerModel,
        Distribution $distributionModel,
        Papers $paperModel,
        Student $studentModel
    ) {
        $this->evaluationLogModel = $evaluationLogModel;
        $this->evaluationAnswerModel = $evaluationAnswerModel;
        $this->distributionModel = $distributionModel;
        $this->paperModel = $paperModel;
        $this->studentModel = $studentModel;
        $this->user = Auth::user();
    }

    /**
     * 获取统计概览
     * 
     * @param array $params
     * @return array
     */
    public function getOverview(array $params): array
    {
        $schoolId = $params['school_id'] ?? $this->user->school_id;
        $distributionId = $params['distribution_id'] ?? null;

        // 基础统计
        $totalStudents = $this->getTotalStudents($schoolId, $distributionId);
        $totalPapers = $this->getTotalPapers($schoolId, $distributionId);
        $totalDistributions = $this->getTotalDistributions($schoolId);
        $completedTests = $this->getCompletedTests($schoolId, $distributionId);

        // 完成率统计
        $completionRate = $totalStudents > 0 ? round(($completedTests / $totalStudents) * 100, 2) : 0;

        // 平均分统计
        $avgScore = $this->getAverageScore($schoolId, $distributionId);

        // 素养水平分布
        $literacyDistribution = $this->getLiteracyDistribution($schoolId, $distributionId);

        // 年级分布
        $gradeDistribution = $this->getGradeDistribution($schoolId, $distributionId);

        return [
            'basic_stats' => [
                'total_students' => $totalStudents,
                'total_papers' => $totalPapers,
                'total_distributions' => $totalDistributions,
                'completed_tests' => $completedTests,
                'completion_rate' => $completionRate,
                'avg_score' => $avgScore
            ],
            'literacy_distribution' => $literacyDistribution,
            'grade_distribution' => $gradeDistribution,
            'recent_activity' => $this->getRecentActivity($schoolId)
        ];
    }

    /**
     * 获取学生报告列表
     * 
     * @param array $params
     * @return array
     */
    public function getStudentReport(array $params): array
    {
        $schoolId = $this->user->school_id;
        $page = $params['page'] ?? 1;
        $pagesize = $params['pagesize'] ?? 10;

        $query = DB::table('student as a')
            ->join('school as sch', 'a.school_id', '=', 'sch.id')
            ->join('school_district as g', 'a.school_district', '=', 'g.id')
            ->join('grade as b', 'a.grade_id', '=', 'b.id')
            ->join('class as c', 'a.class_id', '=', 'c.id')
            ->join('evaluation_distributions as distribution', function ($join) {
                $join->whereRaw('FIND_IN_SET(a.member_id, distribution.member_ids)');
            })
            ->select([
                'a.member_id',
                'a.student_no',
                DB::raw('CASE WHEN a.gender = 1 THEN "男" ELSE "女" END as gender'),
                'a.name as student_name',
                'b.name as grade_year',
                'b.grade_name',
                'c.name as class_name',
                'sch.name as school_name',
                'g.campus_name as school_district_name',
                'distribution.id as distribution_id',
                'distribution.title'
            ])
            ->where('a.school_id', $schoolId)
            ->where('a.status', 0)
            ->where('distribution.status', 0);

        // 应用筛选条件
        $this->applyStudentReportFilters($query, $params);

        $total = $query->count();
        $students = $query->groupBy('a.member_id', 'distribution.id')
            ->orderBy('a.id', 'desc')
            ->offset(($page - 1) * $pagesize)
            ->limit($pagesize)
            ->get();

        // 获取每个学生的测评完成情况
        foreach ($students as &$student) {
            $student->test_status = $this->getStudentTestStatus($student->member_id, $student->distribution_id);
            $student->score_info = $this->getStudentScoreInfo($student->member_id, $student->distribution_id);
        }

        return [
            'list' => $students,
            'total' => $total,
            'page' => $page,
            'pagesize' => $pagesize
        ];
    }

    /**
     * 综合统计 - 学生维度
     * 
     * @param array $params
     * @return array
     */
    public function integratedStatisticsStudent(array $params): array
    {
        $schoolId = $this->user->school_id;
        $distributionId = $params['distribution_id'] ?? null;

        // 按班级统计
        $classStat = $this->getClassStatistics($schoolId, $distributionId);
        
        // 按年级统计
        $gradeStat = $this->getGradeStatistics($schoolId, $distributionId);
        
        // 按性别统计
        $genderStat = $this->getGenderStatistics($schoolId, $distributionId);

        return [
            'class_statistics' => $classStat,
            'grade_statistics' => $gradeStat,
            'gender_statistics' => $genderStat,
            'summary' => $this->getStatisticsSummary($schoolId, $distributionId)
        ];
    }

    /**
     * 综合统计 - 头部信息
     * 
     * @param array $params
     * @return array
     */
    public function integratedStatisticsHead(array $params): array
    {
        $schoolId = $this->user->school_id;
        $distributionId = $params['distribution_id'] ?? null;

        return [
            'school_info' => $this->getSchoolInfo($schoolId),
            'distribution_info' => $distributionId ? $this->getDistributionInfo($distributionId) : null,
            'time_range' => $this->getTimeRange($distributionId),
            'participation_summary' => $this->getParticipationSummary($schoolId, $distributionId)
        ];
    }

    /**
     * 生涯统计
     * 
     * @param array $params
     * @return array
     */
    public function statisticsCareer(array $params): array
    {
        $schoolId = $this->user->school_id;
        $distributionId = $params['distribution_id'] ?? null;

        // 生涯适应力统计
        $careerAdaptability = $this->getCareerAdaptabilityStats($schoolId, $distributionId);
        
        // 兴趣分布统计
        $interestDistribution = $this->getInterestDistributionStats($schoolId, $distributionId);
        
        // 能力分布统计
        $abilityDistribution = $this->getAbilityDistributionStats($schoolId, $distributionId);

        return [
            'career_adaptability' => $careerAdaptability,
            'interest_distribution' => $interestDistribution,
            'ability_distribution' => $abilityDistribution,
            'career_summary' => $this->getCareerSummary($schoolId, $distributionId)
        ];
    }

    /**
     * 批量创建评估PDF
     * 
     * @param array $params
     * @return array
     */
    public function batchCreateEvaluationPdf(array $params): array
    {
        $distributionId = $params['distribution_id'];
        $memberIds = $params['member_ids'] ?? [];

        if (empty($memberIds)) {
            // 获取分发下的所有学生
            $distribution = $this->distributionModel->findOrFail($distributionId);
            $memberIds = $distribution->getMemberIdsArray();
        }

        $results = [];
        $successCount = 0;
        $failCount = 0;

        foreach ($memberIds as $memberId) {
            try {
                $pdfPath = $this->generateStudentPdf($memberId, $distributionId);
                $results[] = [
                    'member_id' => $memberId,
                    'status' => 'success',
                    'pdf_path' => $pdfPath
                ];
                $successCount++;
            } catch (\Exception $e) {
                $results[] = [
                    'member_id' => $memberId,
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
                $failCount++;
            }
        }

        return [
            'total' => count($memberIds),
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'results' => $results
        ];
    }

    /**
     * 批量下载评估PDF
     * 
     * @param array $params
     * @return array
     */
    public function batchDownloadEvaluationPdf(array $params): array
    {
        $distributionId = $params['distribution_id'];
        $memberIds = $params['member_ids'] ?? [];

        // 创建ZIP文件
        $zipFileName = "evaluation_reports_{$distributionId}_" . date('YmdHis') . '.zip';
        $zipPath = storage_path('app/temp/' . $zipFileName);

        // 确保目录存在
        if (!file_exists(dirname($zipPath))) {
            mkdir(dirname($zipPath), 0755, true);
        }

        $zip = new \ZipArchive();
        if ($zip->open($zipPath, \ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('无法创建ZIP文件');
        }

        $addedCount = 0;
        foreach ($memberIds as $memberId) {
            $pdfPath = $this->getStudentPdfPath($memberId, $distributionId);
            if (file_exists($pdfPath)) {
                $studentName = $this->getStudentName($memberId);
                $zip->addFile($pdfPath, "{$studentName}_评估报告.pdf");
                $addedCount++;
            }
        }

        $zip->close();

        return [
            'zip_path' => $zipPath,
            'zip_url' => url('storage/temp/' . $zipFileName),
            'file_count' => $addedCount,
            'file_name' => $zipFileName
        ];
    }

    /**
     * 获取趋势分析
     * 
     * @param array $params
     * @return array
     */
    public function getTrend(array $params): array
    {
        $schoolId = $this->user->school_id;
        $timeRange = $params['time_range'] ?? 30; // 默认30天

        $startDate = now()->subDays($timeRange);
        $endDate = now();

        // 每日完成数量趋势
        $dailyCompletion = $this->getDailyCompletionTrend($schoolId, $startDate, $endDate);
        
        // 分数趋势
        $scoreTrend = $this->getScoreTrend($schoolId, $startDate, $endDate);
        
        // 参与率趋势
        $participationTrend = $this->getParticipationTrend($schoolId, $startDate, $endDate);

        return [
            'daily_completion' => $dailyCompletion,
            'score_trend' => $scoreTrend,
            'participation_trend' => $participationTrend,
            'time_range' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'days' => $timeRange
            ]
        ];
    }

    /**
     * 获取对比分析
     * 
     * @param array $params
     * @return array
     */
    public function getComparison(array $params): array
    {
        $compareType = $params['compare_type']; // grade, class, distribution
        $compareIds = $params['compare_ids'];

        $comparisonData = [];

        switch ($compareType) {
            case 'grade':
                foreach ($compareIds as $gradeId) {
                    $comparisonData[$gradeId] = $this->getGradeComparisonData($gradeId);
                }
                break;
            case 'class':
                foreach ($compareIds as $classId) {
                    $comparisonData[$classId] = $this->getClassComparisonData($classId);
                }
                break;
            case 'distribution':
                foreach ($compareIds as $distributionId) {
                    $comparisonData[$distributionId] = $this->getDistributionComparisonData($distributionId);
                }
                break;
        }

        return [
            'compare_type' => $compareType,
            'comparison_data' => $comparisonData,
            'summary' => $this->getComparisonSummary($comparisonData)
        ];
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取学生总数
     */
    private function getTotalStudents(?int $schoolId, ?int $distributionId): int
    {
        $query = $this->studentModel->where('status', 0);
        
        if ($schoolId) {
            $query->where('school_id', $schoolId);
        }

        if ($distributionId) {
            $distribution = $this->distributionModel->find($distributionId);
            if ($distribution && $distribution->member_ids) {
                $memberIds = explode(',', $distribution->member_ids);
                $query->whereIn('member_id', $memberIds);
            }
        }

        return $query->count();
    }

    /**
     * 获取试卷总数
     */
    private function getTotalPapers(?int $schoolId, ?int $distributionId): int
    {
        $query = $this->paperModel->where('status', 0);
        
        if ($schoolId) {
            $query->where('school_id', $schoolId);
        }

        if ($distributionId) {
            $distribution = $this->distributionModel->find($distributionId);
            if ($distribution && $distribution->paper_ids) {
                $paperIds = explode(',', $distribution->paper_ids);
                $query->whereIn('id', $paperIds);
            }
        }

        return $query->count();
    }

    /**
     * 获取分发总数
     */
    private function getTotalDistributions(?int $schoolId): int
    {
        $query = $this->distributionModel->where('status', 0);
        
        if ($schoolId) {
            $query->where('school_ids', 'like', '%' . $schoolId . '%');
        }

        return $query->count();
    }

    /**
     * 获取已完成测试数
     */
    private function getCompletedTests(?int $schoolId, ?int $distributionId): int
    {
        $query = $this->evaluationLogModel->where('status', 0)->where('check_status', 1);
        
        if ($schoolId) {
            $query->where('school_id', $schoolId);
        }

        if ($distributionId) {
            $query->where('distribution_id', $distributionId);
        }

        return $query->count();
    }

    /**
     * 获取平均分
     */
    private function getAverageScore(?int $schoolId, ?int $distributionId): float
    {
        $query = $this->evaluationLogModel->where('status', 0)->where('check_status', 1);
        
        if ($schoolId) {
            $query->where('school_id', $schoolId);
        }

        if ($distributionId) {
            $query->where('distribution_id', $distributionId);
        }

        return round($query->avg('score') ?? 0, 2);
    }

    /**
     * 获取素养水平分布
     */
    private function getLiteracyDistribution(?int $schoolId, ?int $distributionId): array
    {
        $query = $this->evaluationLogModel->where('status', 0)->where('check_status', 1);
        
        if ($schoolId) {
            $query->where('school_id', $schoolId);
        }

        if ($distributionId) {
            $query->where('distribution_id', $distributionId);
        }

        return $query->selectRaw('literacy_level, COUNT(*) as count')
            ->groupBy('literacy_level')
            ->pluck('count', 'literacy_level')
            ->toArray();
    }

    /**
     * 获取年级分布
     */
    private function getGradeDistribution(?int $schoolId, ?int $distributionId): array
    {
        $query = $this->evaluationLogModel->where('status', 0)->where('check_status', 1);
        
        if ($schoolId) {
            $query->where('school_id', $schoolId);
        }

        if ($distributionId) {
            $query->where('distribution_id', $distributionId);
        }

        return $query->selectRaw('grade_id, COUNT(*) as count')
            ->groupBy('grade_id')
            ->pluck('count', 'grade_id')
            ->toArray();
    }

    /**
     * 获取最近活动
     */
    private function getRecentActivity(?int $schoolId): array
    {
        $query = $this->evaluationLogModel->where('status', 0);
        
        if ($schoolId) {
            $query->where('school_id', $schoolId);
        }

        return $query->with(['paper', 'student'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * 应用学生报告筛选条件
     */
    private function applyStudentReportFilters($query, array $params): void
    {
        if (!empty($params['school_district'])) {
            $query->where('a.school_district', $params['school_district']);
        }

        if (!empty($params['grade_id'])) {
            $query->where('a.grade_id', $params['grade_id']);
        }

        if (!empty($params['class_ids'])) {
            $classIds = is_array($params['class_ids']) ? $params['class_ids'] : explode(',', $params['class_ids']);
            $query->whereIn('a.class_id', $classIds);
        }

        if (!empty($params['gender'])) {
            $query->where('a.gender', $params['gender']);
        }

        if (!empty($params['content'])) {
            $query->where(function ($q) use ($params) {
                $q->where('a.student_no', 'like', '%' . $params['content'] . '%')
                  ->orWhere('a.name', 'like', '%' . $params['content'] . '%');
            });
        }

        if (!empty($params['distribution_id'])) {
            $query->where('distribution.id', $params['distribution_id']);
        }

        if (!empty($params['title'])) {
            $query->where('distribution.title', 'like', '%' . $params['title'] . '%');
        }
    }

    /**
     * 获取学生测试状态
     */
    private function getStudentTestStatus(int $memberId, int $distributionId): array
    {
        $log = $this->evaluationLogModel
            ->where('member_id', $memberId)
            ->where('distribution_id', $distributionId)
            ->where('status', 0)
            ->first();

        if (!$log) {
            return ['status' => 'not_started', 'text' => '未开始'];
        }

        if ($log->check_status == 0) {
            return ['status' => 'pending', 'text' => '待批阅'];
        }

        return ['status' => 'completed', 'text' => '已完成'];
    }

    /**
     * 获取学生分数信息
     */
    private function getStudentScoreInfo(int $memberId, int $distributionId): array
    {
        $log = $this->evaluationLogModel
            ->where('member_id', $memberId)
            ->where('distribution_id', $distributionId)
            ->where('status', 0)
            ->where('check_status', 1)
            ->first();

        if (!$log) {
            return ['score' => null, 'literacy_level' => null];
        }

        return [
            'score' => $log->score,
            'literacy_level' => $log->literacy_level
        ];
    }

    // 其他私有方法的实现...
    // 由于篇幅限制，这里只展示主要方法的实现
    // 实际使用时需要补充完整的私有方法实现

    private function getClassStatistics(?int $schoolId, ?int $distributionId): array { return []; }
    private function getGradeStatistics(?int $schoolId, ?int $distributionId): array { return []; }
    private function getGenderStatistics(?int $schoolId, ?int $distributionId): array { return []; }
    private function getStatisticsSummary(?int $schoolId, ?int $distributionId): array { return []; }
    private function getSchoolInfo(?int $schoolId): array { return []; }
    private function getDistributionInfo(?int $distributionId): array { return []; }
    private function getTimeRange(?int $distributionId): array { return []; }
    private function getParticipationSummary(?int $schoolId, ?int $distributionId): array { return []; }
    private function getCareerAdaptabilityStats(?int $schoolId, ?int $distributionId): array { return []; }
    private function getInterestDistributionStats(?int $schoolId, ?int $distributionId): array { return []; }
    private function getAbilityDistributionStats(?int $schoolId, ?int $distributionId): array { return []; }
    private function getCareerSummary(?int $schoolId, ?int $distributionId): array { return []; }
    private function generateStudentPdf(int $memberId, int $distributionId): string { return ''; }
    private function getStudentPdfPath(int $memberId, int $distributionId): string { return ''; }
    private function getStudentName(int $memberId): string { return ''; }
    private function getDailyCompletionTrend(?int $schoolId, $startDate, $endDate): array { return []; }
    private function getScoreTrend(?int $schoolId, $startDate, $endDate): array { return []; }
    private function getParticipationTrend(?int $schoolId, $startDate, $endDate): array { return []; }
    private function getGradeComparisonData(int $gradeId): array { return []; }
    private function getClassComparisonData(int $classId): array { return []; }
    private function getDistributionComparisonData(int $distributionId): array { return []; }
    private function getComparisonSummary(array $comparisonData): array { return []; }
}
