<?php

namespace App\Services\School\Assessment\GroupReport\Career;

use App\Services\School\Assessment\GroupReport\AbstractGroupReportService;

/**
 * 性格评估团体报告服务类
 * 
 * 该类用于生成MBTI性格评估的团体报告，包括性格维度分布、性格类型分布和学习风格分布
 */
class PersonalityService extends AbstractGroupReportService
{
    /**
     * 维度分数初始值
     * 
     * @var array
     */
    protected $dimension_counts = array('e' => 0, 'i' => 0, 's' => 0, 'n' => 0, 't' => 0, 'f' => 0, 'j' => 0, 'p' => 0);

    /**
     * 维度分数初始值
     * 
     * @var array
     */
    protected $dimension_name_mapping = array('e' => '外倾(E)', 'i' => '内倾(I)', 's' => '感觉(S)', 'n' => '直觉(N)', 't' => '思维(T)', 'f' => '情感(F)', 'j' => '判断(J)', 'p' => '知觉(P)');
    
    /**
     * 16种性格类型初始值
     * 
     * @var array
     */
    protected $personality_types = array('ISFP'=>0,'ISTJ'=>0,'ISFJ'=>0,'ESTJ'=>0,'ESFJ'=>0,'INFJ'=>0,'INFP'=>0,'ENFJ'=>0,'ENFP'=>0,'ISTP'=>0,'ESTP'=>0,'ESFP'=>0,'INTJ'=>0,'INTP'=>0,'ENTJ'=>0,'ENTP'=>0);
    
    /**
     * 学习风格初始值
     * 
     * @var array
     */
    protected $learning_styles = array('NF' => 0, 'NT' => 0, 'SF' => 0, 'ST' => 0);
    
    /**
     * 维度常模数据
     * 
     * @var array
     */
    protected $dimension_norms = ['e-i'=>[38.87,61.13],'s-n'=>[42.82,57.18],'t-f'=>[21.16,78.84],'j-p'=>[28.6,71.4]];
    
    /**
     * 性格类型常模数据
     * 
     * @var array
     */
    protected $personality_norms = ['ISFP'=>15.49,'ISTJ'=>4.34,'ISFJ'=>7.91,'ESTJ'=>1.17,'ESFJ'=>3.26,'INFJ'=>4.05,'INFP'=>18.16,'ENFJ'=>3.44,'ENFP'=>19.36,'ISTP'=>2.75,'ESTP'=>0.72,'ESFP'=>7.07,'INTJ'=>2.97,'INTP'=>5.39,'ENTJ'=>1.47,'ENTP'=>2.45];
    
    /**
     * 学习风格常模数据
     * 
     * @var array
     */
    protected $learning_style_norms = ['NF' => 44.96, 'NT' => 12.22, 'SF' => 33.89, 'ST' => 8.94];
    
    /**
     * 维度标签
     * 
     * @var array
     */
    protected $dimension_labels = ['e-i'=>'外倾(E)-内倾(I)','s-n'=>'感觉(S)-直觉(N)','t-f'=>'思维(T)-情感(F)','j-p'=>'判断(J)-知觉(P)'];

    /**
     * 生成评估报告
     * 
     * @param array $params 请求参数
     * @param int $school_id 学校ID
     * @return array 评估报告数据
     */
    public function generateReport(array $params, int $school_id): array
    {
        // 获取评估数据
        $filtered_assessments = $this->getAssessmentData($params, $school_id, $this->getFilterConditions($params));
        $filtered_assessment_with_results = array_filter($filtered_assessments, fn($v) => !empty($v['standard_results']));
        
        // 统计数据
        $class_statistics = $this->calculateClassStatistics($filtered_assessments);
        
        // 由子类实现具体的报告生成逻辑
        return $this->generateReportData($filtered_assessment_with_results, $class_statistics, $params);
    }
    
    /**
     * 生成报告数据
     * 
     * @param array $all_assessment_with_results 所有有结果的评估数据
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @param array $params 请求参数
     * @return array 报告数据
     */
    protected function generateReportData(
        array $filtered_assessment_with_results, 
        array $class_statistics,
        array $params
    ): array {
        return [
            'participation_count' => $this->getMemberCount($class_statistics),
            'dimension_distribution' => $this->calculateDimensionDistribution(
                $filtered_assessment_with_results,
                $class_statistics
            ),
            'personality_distribution' => $this->calculatePersonalityDistribution(
                $filtered_assessment_with_results,
                $class_statistics
            ),
            'learning_style_distribution' => $this->calculateLearningStyleDistribution(
                $filtered_assessment_with_results,
                $class_statistics
            ),
        ];
    }

    /**
     * 计算4个性格维度分布
     * 
     * @param array $all_assessment_with_results 所有有结果的评估数据
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @return array 维度分布数据和文案
     */
    protected function calculateDimensionDistribution(array $filtered_assessment_with_results, array $class_statistics): array
    {
        // 选中数据
        $selected_scores = $this->calculateDimensionScores($filtered_assessment_with_results, $this->dimension_counts, $this->dimension_labels);

        // 生成维度分布描述
        $dimension_descriptions = [];
        foreach ($selected_scores as $dimension => $scores) {
            $labels = explode('-', $this->dimension_labels[$dimension]);
            $dominant_index = array_keys($scores, max($scores))[0];
            $recessive_index = array_keys($scores, min($scores))[0];

            $high_dimension = $scores[0] > $this->dimension_norms[$dimension][0] ? $labels[0] : $labels[1];
            $dimension_descriptions[] = $labels[0] . '与' . $labels[1] . '维度中，' . $labels[$dominant_index] . '多于' . $labels[$recessive_index] . '，较常模而言，本次参与测评学生的' . $high_dimension . '倾向更明显';
        }

        $distribution_data = [
            'axis' => array_values($this->dimension_labels),
            'summary' => $dimension_descriptions,
            'legend' => ['常模对比', '本次测评'],
            'series' => [
                array_values($this->dimension_norms),
                array_values($selected_scores)
            ]
        ];

        // 班级统计
        $class_distribution = $this->calculateClassDimensionDistribution($class_statistics);
        list($dimension_nums,$str) = $this->getGenderDistribution($filtered_assessment_with_results);

        return [
            'header'=>[
                'number' => 2,
                'name' => '性格维度分析',
            ],
            'default' => $distribution_data,
            'class_distribution' => $class_distribution,
            'gender_data' => $dimension_nums,
            'gender_summary' => $str,
        ];
    }

    /**
     * 各性格维度性别差异分析
     * 
     * @param array $data 过滤后有结果的评估数据
     * @return array 各性格维度性别分布数据
     */
    private function getGenderDistribution(array $data): array
    {
        $boy_dimension_nums = $this->dimension_counts;
        $girl_dimension_nums = $this->dimension_counts;
        $boy_count = 0;
        $girl_count = 0;
        $dimension_nums = [];
        foreach ($data as $assessment) {
            $result = is_array($assessment['standard_results']) 
                ? $assessment['standard_results'] 
                : json_decode($assessment['standard_results'], true);
            
            if (empty($result) || empty($result['dimensions'])) {
                continue;
            }
            
            $code_arr = str_split(strtolower($result['code']));

            if($assessment['gender'] == 1){//男生
                foreach ($code_arr as $code) {
                    if (isset($boy_dimension_nums[$code])) {
                        $boy_dimension_nums[$code] += 1;
                    }
                }
                $boy_count++;
            }else{//女生
                foreach ($code_arr as $code) {
                    if (isset($girl_dimension_nums[$code])) {
                        $girl_dimension_nums[$code] += 1;
                    }
                }
                $girl_count++;
            }
        }
        foreach ($this->dimension_counts as $key => $value) {
            $boy_num = round($boy_dimension_nums[$key] / $boy_count * 100, 2);
            $girl_num = round($girl_dimension_nums[$key] / $girl_count * 100, 2);
            $dimension_nums[] = [
                'label'=>$this->dimension_name_mapping[$key],
                'value'=>[
                    'boy'=>$boy_num . '%',
                    'girl'=>$girl_num . '%',
                ],
            ];
            if($boy_num >= $girl_num){
                $boy_high[] = $this->dimension_name_mapping[$key];
            }else{
                $girl_high[] = $this->dimension_name_mapping[$key];
            }
        }

        $boy_str = implode('、',$boy_high);
        $girl_str = implode('、',$girl_high);
        $str = sprintf('从性别差异分析来看，男生的%s倾向更明显，女生的%s倾向更明显。但需注意，个体差异远大于性别差异，此分析仅作群体趋势参考。',$boy_str,$girl_str);

        return [$dimension_nums,$str];
    }

    /**
     * 计算学习风格分布
     * 
     * @param array $all_assessment_with_results 所有有结果的评估数据
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @return array 维度分布数据
     */
    protected function calculateLearningStyleDistribution(array $filtered_assessment_with_results, array $class_statistics): array
    {
        // 选中数据
        $selected_styles = $this->calculateLearningStyleScores(
            $filtered_assessment_with_results,
            $this->dimension_labels,
            $this->personality_types,
            $this->learning_styles
        );
        

        $distribution_data = [
            'sort' => $this->getSortSummary($selected_styles),
            'axis' => array_keys($this->learning_styles),
            'legend' => ['常模对比', '本次测评'],
            'series' => [
                array_values($this->learning_style_norms),
                array_values($selected_styles)
            ]
        ];

        // 班级统计
        $class_distribution = $this->calculateClassLearningStyleDistribution($class_statistics);

        return [
            'header'=>[
                'number' => 4,
                'name' => '学习风格分布',
            ],
            'default' => $distribution_data,
            'class_distribution' => $class_distribution
        ];
    }

    /**
     * 学习风格排序后的文案
     *
     * @param array $selected_styles 学习风格数据
     * @return array 学习风格排序后的文案
     */
    private function getSortSummary(array $selected_styles): array
    {
        $personality_learning_style = config('assessment.career.personality_learning_style');
        arsort($selected_styles);
        $i = 0;
        foreach ($selected_styles as $key => $value) {
            $style_sort[$i] = $personality_learning_style[$key];
            $style_sort[$i]['code'] = $key;
            $style_sort[$i]['percentage_sort'] = '在学生中占比第'.($i+1).'，达到'.$value.'%。';
            $i++;
        }

        return $style_sort;
    }

    /**
     * 计算16种性格类型分布
     * 
     * @param array $all_assessment_with_results 所有有结果的评估数据
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @return array 维度分布数据
     */
    protected function calculatePersonalityDistribution(array $filtered_assessment_with_results, array $class_statistics): array
    {
        // 选中数据
        $selected_types = $this->calculatePersonalityScores(
            $filtered_assessment_with_results,
            $this->dimension_labels,
            $this->personality_types
        );

        $sorted_types = $selected_types;
        arsort($sorted_types);

        $descriptions = config('assessment.career.personality_descriptions');
        $topThreeTypes = array_slice($sorted_types, 0, 3);
        foreach ($topThreeTypes as $key => $value) {
            $sorts[] = [
                'label' => $key,
                'value' => round($value, 2) . '%',
                'summary' => $descriptions[$key][0]['description'],
            ];
        }

        $distribution_data = [
            'sort' => $sorts,
            'axis' => array_keys($this->personality_types),
            'legend' => ['常模对比', '本次测评'],
            'series' => [
                array_values($this->personality_norms),
                array_values($selected_types)
            ]
        ];

        // 班级统计
        $class_distribution = $this->calculateClassPersonalityDistribution($class_statistics);

        return [
            'header'=>[
                'number' => 3,
                'name' => '性格类型分析',
            ],
            'default' => $distribution_data,
            'class_distribution' => $class_distribution
        ];
    }

    /**
     * 评估数据维度重组
     * 
     * @param array $data 需要处理的评估数据
     * @param array $initial_scores 维度初始值
     * @param array $dimension_labels 维度标签
     * @return array 重组后的数据
     */
    private function calculateDimensionScores(array $data, array $initial_scores, array $dimension_labels): array
    {
        $scores = $initial_scores;
        $count = count($data);
        
        if ($count === 0) {
            return array_map(function($dimension) {
                return [0, 0];
            }, $dimension_labels);
        }
        
        foreach ($data as $assessment) {
            $result = is_array($assessment['standard_results']) 
                ? $assessment['standard_results'] 
                : json_decode($assessment['standard_results'], true);
            
            if (empty($result) || empty($result['dimensions'])) {
                continue;
            }
            
            // 从维度数组中提取分数
            foreach ($result['dimensions'] as $dimension) {
                $code = strtolower($dimension['code']);
                if (isset($scores[$code])) {
                    $scores[$code] += (intval($dimension['score']) / 22) * 100;
                }
            }
        }

        $average_scores = array_map(
            fn($score) => round($score / $count, 2),
            $scores
        );

        $dimension_scores = [];
        foreach ($dimension_labels as $dimension => $label) {
            $parts = explode('-', $dimension);
            $dimension_scores[$dimension] = [
                $average_scores[$parts[0]],
                $average_scores[$parts[1]]
            ];
        }

        return $dimension_scores;
    }

    /**
     * 计算性格类型分数
     * 
     * @param array $data 评估数据
     * @param array $dimension_labels 维度标签
     * @param array $personality_types 性格类型初始值
     * @return array 计算后的性格类型百分比
     */
    private function calculatePersonalityScores(array $data, array $dimension_labels, array $personality_types): array
    {
        $type_frequencies = $personality_types;
        $personality_codes = [];
        $count = count($data);
        
        if ($count === 0) {
            return $personality_types;
        }

        foreach ($data as $assessment) {
            $result = is_array($assessment['standard_results']) 
                ? $assessment['standard_results'] 
                : json_decode($assessment['standard_results'], true);
            
            if (empty($result) || empty($result['dimensions'])) {
                continue;
            }
            
            // 创建维度代码到分数的映射
            $dimension_map = [];
            foreach ($result['dimensions'] as $dimension) {
                $dimension_map[strtolower($dimension['code'])] = intval($dimension['score']);
            }
            
            $code = '';
            foreach ($dimension_labels as $dimension => $label) {
                $parts = explode('-', $dimension);
                $scores = [
                    $dimension_map[strtolower($parts[0])] ?? 0,
                    $dimension_map[strtolower($parts[1])] ?? 0
                ];
                $dominant_index = array_keys($scores, max($scores))[0] ?? 0;
                $code .= strtoupper($parts[$dominant_index]);
            }
            
            $personality_codes[] = $code;
        }

        foreach ($personality_codes as $code) {
            if (isset($type_frequencies[$code])) {
                $type_frequencies[$code]++;
            }
        }

        return array_map(
            fn($frequency) => round($frequency / $count * 100, 2),
            $type_frequencies
        );
    }

    /**
     * 计算学习风格分数
     * 
     * @param array $data 评估数据
     * @param array $dimension_labels 维度标签
     * @param array $personality_types 性格类型初始值
     * @param array $learning_styles 学习风格初始值
     * @return array 计算后的学习风格百分比
     */
    private function calculateLearningStyleScores(array $data, array $dimension_labels, array $personality_types, array $learning_styles): array
    {
        $type_frequencies = $personality_types;
        $personality_codes = [];
        $count = count($data);
        
        if ($count === 0) {
            return $learning_styles;
        }

        foreach ($data as $assessment) {
            $result = is_array($assessment['standard_results']) 
                ? $assessment['standard_results'] 
                : json_decode($assessment['standard_results'], true);
            
            if (empty($result) || empty($result['dimensions'])) {
                continue;
            }
            
            // 创建维度代码到分数的映射
            $dimension_map = [];
            foreach ($result['dimensions'] as $dimension) {
                $dimension_map[strtolower($dimension['code'])] = intval($dimension['score']);
            }
            
            $code = '';
            foreach ($dimension_labels as $dimension => $label) {
                $parts = explode('-', $dimension);
                $scores = [
                    $dimension_map[strtolower($parts[0])] ?? 0,
                    $dimension_map[strtolower($parts[1])] ?? 0
                ];
                $dominant_index = array_keys($scores, max($scores))[0] ?? 0;
                $code .= strtoupper($parts[$dominant_index]);
            }
            
            $personality_codes[] = $code;
        }

        foreach ($personality_codes as $code) {
            if (isset($type_frequencies[$code])) {
                $type_frequencies[$code]++;
            }
        }

        $percentages = array_map(
            fn($frequency) => round($frequency / $count * 100, 2),
            $type_frequencies
        );

        $style_scores = $learning_styles;
        foreach ($percentages as $type => $percentage) {
            foreach ($style_scores as $style => $score) {
                if (strpos($type, $style) !== false) {
                    $style_scores[$style] += $percentage;
                }
            }
        }

        return $style_scores;
    }
    
    /**
     * 计算班级维度分布
     * 
     * @param array $class_statistics 班级统计数据
     * @return array 班级维度分布数据
     */
    private function calculateClassDimensionDistribution(array $class_statistics): array
    {
        $class_distribution = [
            'axis' => [],
            'legend' => array_values($this->dimension_labels),
            'series' => []
        ];

        foreach ($class_statistics['stats'] as $class_name => $stats) {
            $class_distribution['axis'][] = $stats['class_name'];
            $class_data = $class_statistics['category_data'][$class_name] ?? [];
            
            if (empty($class_data)) {
                $class_distribution['series'][] = array_fill(0, 4, [0, 0]);
                continue;
            }

            $class_scores = $this->calculateDimensionScores(
                $class_data,
                $this->dimension_counts,
                $this->dimension_labels
            );
            $class_distribution['series'][] = array_values($class_scores);
        }

        return $class_distribution;
    }

    /**
     * 计算班级学习风格分布
     * 
     * @param array $class_statistics 班级统计数据
     * @return array 班级学习风格分布数据
     */
    private function calculateClassLearningStyleDistribution(array $class_statistics): array
    {
        $class_distribution = [
            'axis' => [],
            'legend' => array_keys($this->learning_styles),
            'series' => []
        ];

        foreach ($class_statistics['stats'] as $class_name => $stats) {
            $class_distribution['axis'][] = $stats['class_name'];
            $class_data = $class_statistics['category_data'][$class_name] ?? [];
            
            if (empty($class_data)) {
                $class_distribution['series'][] = array_fill(0, count($this->learning_styles), 0);
                continue;
            }

            $class_styles = $this->calculateLearningStyleScores(
                $class_data,
                $this->dimension_labels,
                $this->personality_types,
                $this->learning_styles
            );
            $class_distribution['series'][] = array_values($class_styles);
        }

        return $class_distribution;
    }

    /**
     * 计算班级性格类型分布
     * 
     * @param array $class_statistics 班级统计数据
     * @return array 班级性格类型分布数据
     */
    private function calculateClassPersonalityDistribution(array $class_statistics): array
    {
        $class_distribution = [
            'axis' => [],
            'legend' => array_keys($this->personality_types),
            'series' => []
        ];

        foreach ($class_statistics['stats'] as $class_name => $stats) {
            $class_distribution['axis'][] = $stats['class_name'];
            $class_data = $class_statistics['category_data'][$class_name] ?? [];
            
            if (empty($class_data)) {
                $class_distribution['series'][] = array_fill(0, count($this->personality_types), 0);
                continue;
            }

            $class_types = $this->calculatePersonalityScores(
                $class_data,
                $this->dimension_labels,
                $this->personality_types
            );
            $class_distribution['series'][] = array_values($class_types);
        }

        return $class_distribution;
    }
}