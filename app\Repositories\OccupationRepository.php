<?php

namespace App\Repositories;

use App\Models\Gk\OccupationAi;
use App\Models\Gk\OccupationAiCase;
use Illuminate\Database\Eloquent\Collection;

class OccupationRepository
{
    /**
     * 获取职业列表
     *
     * @param string|null $occupationName 职业名称（模糊查询）
     * @return Collection
     */
    public function getOccupations(?string $occupationName = null): Collection
    {
        $query = OccupationAi::select([
            'Id', 
            'Code', 
            'ParentCode', 
            'OccupationName', 
            'LevelType', 
            'State'
        ])
        ->where('State', 1); // 只查询已发布的职业

        // 如果提供了职业名称，进行模糊查询，并且只返回LevelType=3的数据
        if ($occupationName) {
            $query->where('OccupationName', 'like', '%' . $occupationName . '%')
                  ->where('LevelType', 3);
        }

        return $query->orderBy('LevelType')->orderBy('Id')->get();
    }

    /**
     * 获取职业详情
     *
     * @param int $id 职业ID
     * @return OccupationAi|null
     */
    public function getOccupationById(int $id): ?OccupationAi
    {
        return OccupationAi::select([
            'Id',
            'Code',
            'ParentCode',
            'OccupationName',
            'LevelType',
            'Description',
            'Strategy',
            'Click'
        ])
        ->with('cases')
        ->where('Id', $id)
        ->where('State', 1) // 只查询已发布的职业
        ->first();
    }

    /**
     * 更新职业点击量
     *
     * @param OccupationAi $occupation
     * @return void
     */
    public function incrementClick(OccupationAi $occupation): void
    {
        $occupation->increment('Click');
    }

}
