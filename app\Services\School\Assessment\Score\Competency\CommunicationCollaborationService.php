<?php

namespace App\Services\School\Assessment\Score\Competency;

use App\Services\School\Assessment\Score\Competency\AbstractScoreService;

/**
 * 沟通与合作能力评估服务类
 * 
 * 该类用于计算学生的沟通与合作能力评估结果，包括维度分数计算和总分计算
 */
class CommunicationCollaborationService extends AbstractScoreService
{
    /**
     * 计算沟通与合作能力评估结果
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 评估结果数组，包含维度分数和总分
     */
    public function calculate(array $params): array
    {
        $dimension_scores = $this->calculateScores($params);

        return $this->calculateCreativeThinkingDimensionScores($dimension_scores);
    }

    /**
     * 计算创造性思维维度分数
     * 
     * @param array $dimension_scores 原始维度分数数组
     * @return array 计算后的维度分数和总分
     */
    public function calculateCreativeThinkingDimensionScores(array $dimension_scores): array
    {
        //前三个维度
        $basic_dimension_scores = array_slice($dimension_scores, 0, 3);
        
        //重组数组。第四个维度是第二三个维度的复合，分数要加到二三维度上去
        $composite_dimension_scores = array_slice($dimension_scores, 3, 1);
        $composite_dimension_names = explode('-', $composite_dimension_scores[0]['name']);
        foreach ($basic_dimension_scores as $key => $dimension){
            if(in_array($dimension['name'], $composite_dimension_names)){
                $basic_dimension_scores[$key]['score'] += $composite_dimension_scores[0]['score'];
                $basic_dimension_scores[$key]['question_count'] += $composite_dimension_scores[0]['question_count'];
            }
        }

        $total_questions = 0;
        $total_score = 0;

        foreach ($basic_dimension_scores as $key => $dimension){
            $basic_dimension_scores[$key]['score'] = round($dimension['score'] * 10 / $dimension['question_count'], 1);
            $total_score += $dimension['score'];
            $total_questions += $dimension['question_count'];
            unset($basic_dimension_scores[$key]['question_count']);
        }
        
        $average_score = round($total_score * 10 / $total_questions, 1);

        return ['dimensions'=>$basic_dimension_scores,'total_score'=>$average_score];
    }
}