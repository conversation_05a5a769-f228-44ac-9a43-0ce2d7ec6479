<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MajorAcademicGroupResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->Id,
            'academic_group_name' => $this->AcademicGroupName,
            'introduction' => $this->Introduction,
            'ability' => $this->Ability,
            'high_school_subject' => $this->HighSchoolSubject,
            'major' => $this->Major,
            'theory' => $this->Theory,
            'ying_yong' => $this->YingYong,
            'shi_cao' => $this->Shi<PERSON>ao,
            'picture' => $this->Picture,
            'explain' => $this->Explain,
            'major_subjects' => $this->transformMajorSubjects($this->major_subjects ?? []),
        ];
    }

    /**
     * Transform major_subjects array
     *
     * @param array $majorSubjects
     * @return array
     */
    private function transformMajorSubjects(array $majorSubjects): array
    {
        return collect($majorSubjects)->map(function ($subject) {
            return [
                'subject_code' => $subject['SubjectCode'] ?? null,
                'subject_name' => $subject['SubjectName'] ?? null,
                'major_subject_id' => $subject['MajorSubjectID'] ?? null,
                'category' => $this->transformCategories($subject['Category'] ?? []),
            ];
        })->toArray();
    }

    /**
     * Transform categories array
     *
     * @param mixed $categories
     * @return array
     */
    private function transformCategories($categories): array
    {
        if (!$categories) {
            return [];
        }

        // 如果是 Collection 对象，转换为数组
        if (is_object($categories) && method_exists($categories, 'toArray')) {
            $categories = $categories->toArray();
        }

        return collect($categories)->map(function ($category) {
            return [
                'id' => $category['ID'] ?? ($category->ID ?? null),
                'major_subject_id' => $category['MajorSubjectID'] ?? ($category->MajorSubjectID ?? null),
                'major_category_code' => $category['MajorCategoryCode'] ?? ($category->MajorCategoryCode ?? null),
                'major_category_name' => $category['MajorCategoryName'] ?? ($category->MajorCategoryName ?? null),
                'majors' => $this->transformMajors($category['majors'] ?? ($category->majors ?? [])),
            ];
        })->toArray();
    }

    /**
     * Transform majors array
     *
     * @param mixed $majors
     * @return array
     */
    private function transformMajors($majors): array
    {
        if (!$majors) {
            return [];
        }

        // 如果是 Collection 对象，转换为数组
        if (is_object($majors) && method_exists($majors, 'toArray')) {
            $majors = $majors->toArray();
        }

        return collect($majors)->map(function ($major) {
            $majorData = [
                'id' => $major['ID'] ?? ($major->ID ?? null),
                'major_code' => $major['MajorCode'] ?? ($major->MajorCode ?? null),
                'major_name' => $major['MajorName'] ?? ($major->MajorName ?? null),
                'major_subject_id' => $major['MajorSubjectID'] ?? ($major->MajorSubjectID ?? null),
                'subject_code' => $major['SubjectCode'] ?? ($major->SubjectCode ?? null),
                'subject_name' => $major['SubjectName'] ?? ($major->SubjectName ?? null),
                'major_category_id' => $major['MajorCategoryID'] ?? ($major->MajorCategoryID ?? null),
                'category_code' => $major['CategoryCode'] ?? ($major->CategoryCode ?? null),
                'category_name' => $major['CategoryName'] ?? ($major->CategoryName ?? null),
                'remark' => $major['Remark'] ?? ($major->Remark ?? null),
            ];

            // 检查 AcademicGroupId 是否存在
            $academicGroupId = $major['AcademicGroupId'] ?? ($major->AcademicGroupId ?? null);
            if (isset($academicGroupId)) {
                $majorData['academic_group_id'] = $academicGroupId;
            }

            return $majorData;
        })->toArray();
    }
}
