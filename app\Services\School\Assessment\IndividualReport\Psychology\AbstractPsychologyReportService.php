<?php

namespace App\Services\School\Assessment\IndividualReport\Psychology;

use App\Services\School\Assessment\IndividualReport\AbstractIndividualReportService;

/**
 * 心理学评估报告服务的抽象基类。
 * 继承自 AbstractIndividualReportService，为特定类型的心理学报告提供通用结构和方法。
 */
abstract class AbstractPsychologyReportService extends AbstractIndividualReportService
{
    /**
     * 获取评估的等级常量定义。
     *
     * @return array 子类必须实现此方法以返回其特定的等级常量数组。
     */
    abstract protected function getLevelConstants(): array;

    /**
     * 获取此服务对应的配置键名。
     *
     * @return string 子类必须实现此方法以返回其特定的配置键名。
     */
    abstract protected function getConfigKey(): string;

    /**
     * 处理核心的评估信息。
     *
     * @param array $standard_results 标准化后的评估结果。
     * @param array $config 相关的配置信息。
     * @return array 子类必须实现此方法以处理评估信息并返回结果。
     */
    abstract protected function processAssessmentInfo(array $standard_results, array $config): array;

    /**
     * 分析评估维度。
     *
     * @param array $standard_results_dimensions 标准化结果中的维度数据。
     * @param array $config 相关的配置信息。
     * @return array 子类必须实现此方法以分析维度并返回结果。
     */
    abstract protected function analyzeDimensions(array $standard_results_dimensions, array $config): array;

    /**
     * 生成报告的文本描述部分。
     *
     * @param array $standard_results 标准化后的评估结果。
     * @param array $dimension_results 分析后的维度结果。
     * @param array $config 相关的配置信息。
     * @return array 子类必须实现此方法以生成报告文本并返回结果。
     */
    abstract protected function generateReportText(array $standard_results, array $dimension_results, array $config): array;

    /**
     * 生成个人评估报告。
     *
     * @param array $params 包含评估任务分配ID等参数的数组。
     * @return array 返回包含作业信息和评估信息的报告数组。
     */
    public function generateReport(array $params): array
    {
        $assignment_id = $params['assessment_task_assignment_id'];
        $assignment_info = $this->getAssignmentInfo($assignment_id);
        
        $assessment_info = $this->buildAssessmentInfo($assignment_info['standard_results']);

        return array_merge($assignment_info, $assessment_info);
    }

    /**
     * 构建评估信息。
     *
     * @param array $standard_results 标准化后的评估结果。
     * @return array 返回处理后的评估信息。
     */
    protected function buildAssessmentInfo(array $standard_results): array
    {
        $config_key = $this->getConfigKey();
        $config = config("assessment.psychology.{$config_key}");
        return $this->processAssessmentInfo($standard_results, $config);
    }

    /**
     * 生成图表数据。
     *
     * @param array $standard_results 标准化后的评估结果。
     * @return array 返回图表所需的名称和值对数组。
     */
    protected function generateCharts(array $standard_results): array
    {
        $charts = [];
        // 某些评估可能将总分放在 total_score 字段，或者作为 dimensions 的第一个元素
        if (isset($standard_results['total_score'])) {
            $charts[] = ['name' => '总分', 'value' => $standard_results['total_score']];
        }

        $dimensions_for_chart = $standard_results['dimensions'];
        // 特殊处理 AnxietyService，其第一个维度是总体焦虑，图表也应包含
        // 对于其他服务，如果总分已通过 total_score 添加，则 dimensions 可能不需要再添加总分项
        // 这里假设 standard_results['dimensions'] 总是包含所有需要在图表中显示的维度项

        foreach ($dimensions_for_chart as $dimension) {
            // 避免重复添加总分（如果它也存在于dimensions中且与total_score同名）
            if (isset($standard_results['total_score']) && $dimension['name'] === '总分' && $dimension['score'] == $standard_results['total_score']) {
                continue;
            }
            $charts[] = ['name' => $dimension['name'], 'value' => $dimension['score']];
        }
        return $charts;
    }

    /**
     * 根据分数获取对应的等级。
     *
     * @param float $score 得分。
     * @param string $type 类型，'dimension' 或 'total'，默认为 'dimension'。
     * @return string 返回分数的对应等级代码或等级名称。
     */
    protected function getScoreLevel(float $score, string $type = 'dimension'): string
    {
        $levels = $this->getLevelConstants();
        foreach ($levels[$type] as $level_info) { // Renamed $level to $level_info to avoid conflict with $level variable inside loop
            if ($score < $level_info['max']) { // 假设等级定义中用 'max' 作为阈值
                return $level_info['code'] ?? $level_info['level']; // 返回 code 或 level
            }
        }
        $last_level = end($levels[$type]);
        return $last_level['code'] ?? $last_level['level'];
    }
}