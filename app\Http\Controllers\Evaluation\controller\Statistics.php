<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */
namespace app\evaluation\controller;
use app\login\controller\ApiAuth;


/**
 * Class Questions
 * @package app\evaluation\controller
 */
class Statistics extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->Statistics = new \app\evaluation\service\Statistics();
    }
    /**
     * 模块：素养测评-数据统计个体报告
     * @SWG\Get(path="/evaluation/statistics/student_report",
     *   tags={"素养测评-个体报告列表:statistics"},
     *   summary="个体报告列表",
     *  @SWG\Parameter(
     *     in="query",
     *     name="grade_id",
     *     type="integer",
     *     description="年级id",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="query",
     *     name="class_ids",
     *     type="string",
     *     description="多个班级用逗号隔开",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="query",
     *     name="gender",
     *     type="integer",
     *     description="1男2女",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="query",
     *     name="distribution_id",
     *     type="integer",
     *     description="分发id",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="query",
     *     name="title",
     *     type="string",
     *     description="分发title",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="query",
     *     name="gender",
     *     type="integer",
     *     description="男为1女为2，0为全部",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="query",
     *     name="content",
     *     type="string",
     *     description="关键字查询",
     *     required=false,
     *   ),
     * @SWG\Parameter(
     *     in="query",
     *     name="pagesize",
     *     type="integer",
     *     description="每页显示记录数",
     *     required=false,
     *   ),
    * @SWG\Parameter(
     *     in="query",
     *     name="page",
     *     type="integer",
     *     description="页码",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    public function student_report(){
        $data = $this->Statistics->get_student_report();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-数据统计个体报告
     * @SWG\Get(path="/evaluation/integrated_statistics_student",
     *   tags={"素养测评-个体报告列表:statistics"},
     *   summary="统计分析学生列表",
     *  description="数据说明：",
     *  produces={"application/json"},
     *   @SWG\Parameter(
     *     in="query",
     *     name="school_district",
     *     type="integer",
     *     description="校区id",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="grade_id",
     *     type="integer",
     *     description="年级id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="class_ids",
     *     type="string",
     *     description="班级id（多个逗号分隔）",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="distribution_id",
     *     type="integer",
     *     description="测评id(也就是分发id)",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="participated",
     *     type="integer",
     *     description="1是已完成，2是未完成",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="gender",
     *     type="string",
     *     description="性别（男：1；女：2）",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="pagesize",
     *     type="integer",
     *     description="每页显示",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="page",
     *     type="integer",
     *     description="页数",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="keyword",
     *     type="string",
     *     description="可以搜姓名学号试卷名称",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="member_id",
     *     type="integer",
     *     description="学生member_id",
     *     required=false,
     *   ),
     *  @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function integrated_statistics_student(){
        $data = $this->Statistics->integrated_statistics_student();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-数据统计个体报告
     * @SWG\Get(path="/evaluation/integrated_statistics_head",
     *   tags={"素养测评-个体报告列表:statistics"},
     *   summary="统计分析学生列表",
     *  description="数据说明：",
     *  produces={"application/json"},
     *   @SWG\Parameter(
     *     in="query",
     *     name="school_district",
     *     type="integer",
     *     description="校区id",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="grade_id",
     *     type="integer",
     *     description="年级id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="class_ids",
     *     type="string",
     *     description="班级id（多个逗号分隔）",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="distribution_id",
     *     type="integer",
     *     description="测评id(也就是分发id)",
     *     required=true,
     *   ),
     *  @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function integrated_statistics_head(){
        $data = $this->Statistics->integrated_statistics_head();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-批量生成个体报告
     * @SWG\Get(path="/evaluation/batch_create_evaluation_pdf",
     *   tags={"素养测评-个体报告列表:statistics"},
     *   summary="批量生成个体报告",
     *  description="此接口暂由手动触发",
     *  produces={"application/json"},
     *   @SWG\Parameter(
     *     in="query",
     *     name="school_id",
     *     type="integer",
     *     description="学校id",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="survey_id",
     *     type="integer",
     *     description="素养测评id",
     *     required=true,
     *   ),
     *  @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function batch_create_evaluation_pdf(){
        $data = $this->Statistics->batch_create_evaluation_pdf();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-批量下载个体报告
     * @SWG\Get(path="/evaluation/batch_download_evaluation_pdf",
     *   tags={"素养测评-个体报告列表:statistics"},
     *   summary="批量生成个体报告",
     *  description="数据说明：",
     *  produces={"application/json"},
     *   @SWG\Parameter(
     *     in="query",
     *     name="session_id",
     *     type="string",
     *     description="session_id,多个用逗号拼接",
     *     required=true,
     *   ),
     *  @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function batch_download_evaluation_pdf(){
        $data = $this->Statistics->batch_download_evaluation_pdf();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-数据统计个体报告
     * @SWG\Get(path="/evaluation/statistics_career",
     *   tags={"素养测评-个体报告列表:statistics"},
     *   summary="统计分析学生列表",
     *  description="数据说明：",
     *  produces={"application/json"},
     *  @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function statistics_career(){
        $data = $this->Statistics->statistics_career();
        apiReturn($data);
    }
}