<?php

namespace App\Http\Controllers\School\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\System\ClassRequest;
use App\Models\School\System\Claass;
use App\Services\School\System\ClassService;
use App\Services\DataSync\DataSyncService;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ClassController extends Controller
{
    use CrudOperations;

    protected string $model = Claass::class;

    protected ClassService $classService;

    public function __construct(ClassService $classService)
    {
        $this->classService = $classService;
    }

    public function index(Request $request)
    {
        $query = $this->classService->listBuilder($request);
        $cnt = $query->count();
        $list = $query->orderBy('id', 'desc')->pagination()->with('grade', 'schoolCampus')->get();
        // return $this->success(compact('list', 'cnt'));
        return $this->paginateSuccess($list, $cnt);
    }

    // 根据校区区、年级获取班级下拉列表
    public function getClassList(ClassRequest $request)
    {
        $list = $this->classService->getClassList($request);
        return $this->success($list);
    }

    public function store(ClassRequest $request)
    {
        $class = $this->classService->store($request);

        // 数据同步调用
        if (config('datasync.enabled', false)) {
            $dataSyncService = app(\App\Services\DataSync\DataSyncService::class);
            $dataSyncService->syncClass($class, $request);
        }

        return $this->message('新增成功');
    }

    // 批量新增
    public function batchStore(ClassRequest $request)
    {
        $classes = $this->classService->batchStore($request);

        // 数据同步调用
        if (config('datasync.enabled', false)) {
            $dataSyncService = app(\App\Services\DataSync\DataSyncService::class);
            $dataSyncService->syncBatchClasses($classes, $request);
        }

        return $this->message('批量新增成功');
    }


    public function update(ClassRequest $request, $id)
    {
        $class = $this->classService->update($request, $id);

        // 数据同步调用
        if (config('datasync.enabled', false)) {
            $dataSyncService = app(\App\Services\DataSync\DataSyncService::class);
            $dataSyncService->syncClass($class, $request);
        }

        return $this->message('修改成功');
    }



}
