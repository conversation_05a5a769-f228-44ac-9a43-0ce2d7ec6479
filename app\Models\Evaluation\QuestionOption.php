<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 题目选项模型
 */
class QuestionOption extends Model
{
    protected $table = 'evaluation_question_options';

    protected $fillable = [
        'question_id',
        'content',
        'score',
        'title',
        'answer',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'question_id' => 'integer',
        'score' => 'integer',
        'sort' => 'integer',
        'is_correct' => 'boolean',
    ];

    /**
     * 所属题目关联
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class, 'question_id');
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort');
    }

    /**
     * 作用域：正确答案
     */
    public function scopeCorrect($query)
    {
        return $query->where('is_correct', true);
    }
}
