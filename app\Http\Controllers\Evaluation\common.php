<?php
/**
 * Created by PhpStorm.
 * User: kane
 * Date: 2023/8/30
 * Time: 13:24
 */
use  think\File;
use think\Response;
use think\Session;

function is_high(){
    $is_high = [
        '初中' => '0',
        '高中' => '1',
    ];
    return $is_high;
}

function course(){
    $course = [
        '阅读' => '1',
        '数学' => '2',
        '科学' => '3',
    ];
    return $course;
}

function grade(){
    $grade = [
        '高三' => '12',
        '高二' => '11',
        '十年级' => '10',
        '九年级' => '9',
        '八年级' => '8',
        '七年级' => '7',
        '六年级' => '6',
        '五年级' => '5',
        '四年级' => '4',
        '三年级' => '3',
        '二年级' => '2',
        '一年级' => '1',
        '通用' => '0',
    ];
    return $grade;
}
function scenario(){
    $scenario = [
        '素养测评' => '1',
        '阶段测试' => '2',
        '课时练习' => '3',
    ];
    return $scenario;

}
function cenario_Type(){
    $cenario_Type = [
        '生活' => '1',
        '科学' => '2',
        '社会' => '3',
        '职业' => '4',
        '无' => '0',
    ];
    return $cenario_Type;

}

function competence_shine(){
    $competence_shine = [
        '语言建构与运用' => 'language',
        '思维发展与提升' => 'thinking',
        '审美鉴赏与创造' => 'appreciation',
        '文化传承与理解' => 'culture',
        '数学推理' => 'reasoning',
        '数学建模' => 'modeling',
        '数学运算' => 'operation',
        '直观想象' => 'imagine',
        '数据分析' => 'analysis',
        '数学抽象' => 'abstract',
        '科学观念' => 'concept',
        '科学思维' => 'thought',
        '探究实践' => 'explore',
        '态度责任' => 'attitude',
    ];
    return $competence_shine;
}

function character_code(){
    $character_code = [
        'E' => '外倾',
        'I' => '内倾',
        'S' => '感觉',
        'N' => '直觉',
        'T' => '思维',
        'F' => '情感',
        'J' => '判断',
        'P' => '知觉'
    ];
    return $character_code;
}

function get_redis_key()
{
    $host = $_SERVER['HTTP_HOST'];
    $config_redis_key = [
        'www.yishengya.cn' => 'www',
        'demo.yishengya.cn' => 'demo',
        'test.yishengya.cn' => 'test'
    ];
    if(in_array($host,array_keys($config_redis_key))){
        $prefix = $config_redis_key[$host];
    }else{
        //本地测试域名
        $prefix = 'localtp';
    }
    return $prefix;
}


