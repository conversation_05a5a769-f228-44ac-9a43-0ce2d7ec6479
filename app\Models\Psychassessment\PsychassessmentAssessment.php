<?php

namespace App\Models\Psychassessment;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 心理评估类型模型
 */
class PsychassessmentAssessment extends Model
{
    protected $table = 'psychassessment_assessment';

    protected $fillable = [
        'name',
        'description',
        'type',
        'code',
        'dimensions',
        'questions_count',
        'time_limit',
        'status',
        'sort',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'questions_count' => 'integer',
        'time_limit' => 'integer',
        'status' => 'integer',
        'sort' => 'integer'
    ];

    /**
     * 关联测评计划
     */
    public function surveys(): HasMany
    {
        return $this->hasMany(PsychassessmentSurvey::class, 'survey_type');
    }

    /**
     * 获取维度数组
     */
    public function getDimensionsArrayAttribute()
    {
        return $this->dimensions ? json_decode($this->dimensions, true) : [];
    }

    /**
     * 设置维度数组
     */
    public function setDimensionsAttribute($value)
    {
        $this->attributes['dimensions'] = is_array($value) ? json_encode($value) : $value;
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            0 => '禁用',
            1 => '启用',
            default => '未知'
        };
    }

    /**
     * 作用域：已启用
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：按类型
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort', 'desc')->orderBy('id', 'desc');
    }
}
