<?php

namespace App\Helpers;

use App\Enums\ProvinceEnum;

/**
 * 省份相关辅助函数
 */
class ProvinceHelper
{
    /**
     * 获取省份的分数表单类型
     *
     * @param int $provinceId 省份ID
     * @return string 分数表单类型
     */
    public static function getScoreFormType2(int $provinceId): string
    {
        // 定义省份ID到返回值的映射
        $provinceFormMap = [
            ProvinceEnum::ZHEJIANG->value => "x3",
            ProvinceEnum::SHANDONG->value => "x3",
            ProvinceEnum::SHANGHAI->value => "x3",
            ProvinceEnum::BEIJING->value => "x3",
            ProvinceEnum::TIANJIN->value => "x3",

            ProvinceEnum::JIANGSU->value => "312-2021",
            ProvinceEnum::HUNAN->value => "312-2021",
            ProvinceEnum::HUBEI->value => "312-2021",
            ProvinceEnum::GUANGDONG->value => "312-2021",
            ProvinceEnum::FUJIAN->value => "312-2021",
            ProvinceEnum::CHONGQING->value => "312-2021",
            ProvinceEnum::LIAONING->value => "312-2021",
            ProvinceEnum::HEBEI->value => "312-2021",

            ProvinceEnum::HEILONGJIANG->value => "312-2024",
            ProvinceEnum::JILIN->value => "312-2024",
            ProvinceEnum::ANHUI->value => "312-2024",
            ProvinceEnum::JIANGXI->value => "312-2024",
            ProvinceEnum::GUANGXI->value => "312-2024",
            ProvinceEnum::GUIZHOU->value => "312-2024",
            ProvinceEnum::GANSU->value => "312-2024",

            ProvinceEnum::SICHUAN->value => "312-2025",
            ProvinceEnum::HENAN->value => "312-2025",
            ProvinceEnum::SHANXI->value => "312-2025",
            ProvinceEnum::SHAANXI->value => "312-2025",
            ProvinceEnum::YUNNAN->value => "312-2025",
            ProvinceEnum::NEIMENGGU->value => "312-2025",
            ProvinceEnum::NINGXIA->value => "312-2025",
            ProvinceEnum::QINGHAI->value => "312-2025",
        ];

        return $provinceFormMap[$provinceId] ?? "wl";
    }
    
    /**
     * 院校数据查询，包含选科个数
     *
     * @param int $provinceId 省份ID
     * @return string|null 选科个数
     */
    public static function collegeQueryHasCourseProvinceDefault(int $provinceId): ?string
    {
        // 定义省份ID到返回值的映射
        $provinceMap = [
            ProvinceEnum::SHANGHAI->value => 'one',

            // 2021年开始新高考的省份
            ProvinceEnum::JIANGSU->value => 'two',
            ProvinceEnum::HUNAN->value => 'two',
            ProvinceEnum::HUBEI->value => 'two',
            ProvinceEnum::GUANGDONG->value => 'two',
            ProvinceEnum::FUJIAN->value => 'two',

            // 2024年开始新高考的省份
            ProvinceEnum::HEILONGJIANG->value => 'two',
            ProvinceEnum::JILIN->value => 'two',
            ProvinceEnum::GUANGXI->value => 'two',
            ProvinceEnum::GANSU->value => 'two',
            ProvinceEnum::JIANGXI->value => 'two',
            ProvinceEnum::ANHUI->value => 'two',
        ];

        return $provinceMap[$provinceId] ?? null;
    }
    
    /**
     * 专业数据查询，包含选科个数
     *
     * @param int $provinceId 省份ID
     * @param int $year 年份
     * @return string|null 选科个数
     */
    public static function majorQueryHasCourseProvince(int $provinceId, int $year): ?string
    {
        // 定义省份ID和年份到返回值的映射：年份为该省份已经开始新高考的第一年，返回值是选科个数
        $provinceYearMap = [
            [ProvinceEnum::SHANGHAI->value, 2017, 'one'],
            [ProvinceEnum::ZHEJIANG->value, 2017, 'one'],
            [ProvinceEnum::SHANDONG->value, 2020, 'one'],

            [ProvinceEnum::JIANGSU->value, 2021, 'two'],
            [ProvinceEnum::HUNAN->value, 2021, 'two'],
            [ProvinceEnum::HUBEI->value, 2021, 'two'],
            [ProvinceEnum::GUANGDONG->value, 2021, 'two'],
            [ProvinceEnum::FUJIAN->value, 2021, 'two'],
            [ProvinceEnum::CHONGQING->value, 2021, 'two'],
            [ProvinceEnum::HEBEI->value, 2021, 'two'],
            [ProvinceEnum::LIAONING->value, 2021, 'two'],

            [ProvinceEnum::GUIZHOU->value, 2024, 'two'],
            [ProvinceEnum::JIANGXI->value, 2024, 'two'],
            [ProvinceEnum::HEILONGJIANG->value, 2024, 'two'],
            [ProvinceEnum::GANSU->value, 2024, 'two'],
            [ProvinceEnum::ANHUI->value, 2024, 'two'],
            [ProvinceEnum::JILIN->value, 2024, 'two'],
            [ProvinceEnum::GUANGXI->value, 2024, 'two'],
            
            [ProvinceEnum::SICHUAN->value, 2025, 'two'],
            [ProvinceEnum::HENAN->value, 2025, 'two'],
            [ProvinceEnum::SHANXI->value, 2025, 'two'],
            [ProvinceEnum::SHAANXI->value, 2025, 'two'],
            [ProvinceEnum::YUNNAN->value, 2025, 'two'],
            [ProvinceEnum::NEIMENGGU->value, 2025, 'two'],
        ];

        foreach ($provinceYearMap as $entry) {
            list($id, $yr, $value) = $entry;
            if ($provinceId == $id && $year >= $yr) {
                return $value;
            }
        }

        return null;
    }
}
