<?php

namespace App\Helpers;

/**
 * 文本加密辅助类
 * 用于将特定汉字转换为HTML实体编码
 */
class TextEncryptionHelper
{
    /**
     * 字符映射表
     * 将特定汉字映射为HTML实体编码
     *
     * @var array
     */
    protected static $char_mapping = [
        '学' => "&#xC666;",
        '科' => "&#xC667;",
        '批' => "&#xC668;",
        '本' => "&#xC669;",
        '院' => "&#xC66A;",
        '大' => "&#xC66B;",
        '工' => "&#xC66C;",
        '职' => "&#xC66D;",
        '业' => "&#xC66E;",
        '理' => "&#xC66F;",
        '专' => "&#xC670;",
        '技' => "&#xC671;",
        '术' => "&#xC672;",
        '高' => "&#xC673;",
        '程' => "&#xC674;",
        '定' => "&#xC675;",
        '二' => "&#xC676;",
        '向' => "&#xC677;",
        '非' => "&#xC678;",
        '生' => "&#xC679;",
        '与' => "&#xC67A;",
        '一' => "&#xC67B;",
        '物' => "&#xC67C;",
        '不' => "&#xC67D;",
        '校' => "&#xC67E;",
        '年' => "&#xC67F;",
        '管' => "&#xC680;",
        '限' => "&#xC681;",
        '计' => "&#xC682;",
        '南' => "&#xC683;",
        '化' => "&#xC684;",
        '语' => "&#xC685;",
        '考' => "&#xC686;",
        '电' => "&#xC687;",
        '中' => "&#xC688;",
        '类' => "&#xC689;",
        '师' => "&#xC68A;",
        '国' => "&#xC68B;",
        '西' => "&#xC68C;",
        '范' => "&#xC68D;",
        '经' => "&#xC68E;",
        '北' => "&#xC68F;",
        '招' => "&#xC690;",
        '医' => "&#xC691;",
        '的' => "&#xC692;",
        '区' => "&#xC693;",
        '信' => "&#xC694;",
        '江' => "&#xC695;",
        '机' => "&#xC696;",
        '四' => "&#xC697;",
        '体' => "&#xC698;",
        '商' => "&#xC699;",
        '州' => "&#xC69A;",
        '门' => "&#xC69B;",
        '第' => "&#xC69C;",
        '数' => "&#xC69D;",
        '通' => "&#xC69E;",
        '宋' => "&#xC69F;",
        '三' => "&#xC6A0;",
        '海' => "&#xC6A1;",
        '办' => "&#xC6A2;",
        '务' => "&#xC6A3;",
        '教' => "&#xC6A4;",
        '东' => "&#xC6A5;",
        '息' => "&#xC6A6;",
        '外' => "&#xC6A7;",
        '应' => "&#xC6A8;",
        '地' => "&#xC6A9;",
        '用' => "&#xC6AA;",
        '目' => "&#xC6AB;",
        '制' => "&#xC6AC;",
        '育' => "&#xC6AD;",
        '分' => "&#xC6AE;",
        '子' => "&#xC6AF;",
        '市' => "&#xC6B0;",
        '作' => "&#xC6B1;",
        '安' => "&#xC6B2;",
        '动' => "&#xC6B3;",
        '文' => "&#xC6B4;",
        '合' => "&#xC6B5;",
        '元' => "&#xC6B6;",
        '历' => "&#xC6B7;",
        '建' => "&#xC6B8;",
        '济' => "&#xC6B9;",
        '史' => "&#xC6BA;",
        '会' => "&#xC6BB;",
        '山' => "&#xC6BC;",
        '选' => "&#xC6BD;",
        '设' => "&#xC6BE;",
        '财' => "&#xC6BF;",
        '英' => "&#xC6C0;",
        '京' => "&#xC6C1;",
        '人' => "&#xC6C2;",
        '湖' => "&#xC6C3;",
        '成' => "&#xC6C4;",
        '金' => "&#xC6C5;",
        '省' => "&#xC6C6;",
        '自' => "&#xC6C7;",
        '取' => "&#xC6C8;",
        '行' => "&#xC6C9;",
    ];

    /**
     * 加密文本
     * 将输入文本中的特定汉字转换为HTML实体编码
     *
     * @param string $input 需要加密的文本
     * @return string 加密后的文本
     */
    public static function encryptText(string $input): string
    {
        $encrypted = '';
        $chars = preg_split('//u', $input, -1, PREG_SPLIT_NO_EMPTY);
        
        foreach ($chars as $char) {
            // 存在映射则替换，否则保留原字符
            $encrypted .= self::$char_mapping[$char] ?? $char;
        }
        
        return $encrypted;
    }
}