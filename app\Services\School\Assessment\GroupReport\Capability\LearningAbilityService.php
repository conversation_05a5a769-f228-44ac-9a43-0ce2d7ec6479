<?php

namespace App\Services\School\Assessment\GroupReport\Capability;

/**
 * 学习力评估团体报告服务类
 * 
 * 该类用于生成学习力评估的团体报告
 */
class LearningAbilityService extends AbstractCapabilityService
{
    /**
     * 获取配置键名
     * 
     * @return string 配置键名
     */
    protected function getConfigKey(): string
    {
        return 'learning_ability';
    }

    /**
     * 是否移除总分
     * 
     * @return bool 是否移除
     */
    protected function shouldRemoveTotal(): bool
    {
        return false; // 学习力不需要移除总分
    }
}