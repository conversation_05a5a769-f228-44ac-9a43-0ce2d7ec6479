<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class OccupationAi extends BaseModel
{
    use HasFactory;

    protected $table = 'OccupationAi';

    // 指定连接
    protected $connection = 'sqlsrv_gk';

    //主键
    protected $primaryKey = 'Id';

    // 隐藏字段
    protected $hidden = [];
    
    // 是否使用时间戳
    public $timestamps = false;

    // 可批量赋值的字段
    protected $fillable = [
        
    ];

    /**
     * 获取与职业相关的案例
     */
    public function cases(): HasMany
    {
        return $this->hasMany(OccupationAiCase::class, 'Code', 'Code');
    }
}
