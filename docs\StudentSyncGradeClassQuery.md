# 学生同步年级班级查询逻辑说明

## 概述

学生同步功能中实现了复杂的年级和班级ID查询逻辑，通过多步查询确保获取到同步数据库中正确的年级ID和班级ID。

## 查询流程

### 1. 参数获取
从请求中获取以下参数：
- `school_campus_id`: 校区ID
- `grade_year`: 年级名称
- `init_grade_id`: 年级排序
- `class_id`: 主数据库中的班级ID

### 2. 年级ID查询
通过校区ID、年级名称和年级排序查询同步数据库中的年级ID：

```php
// 通过校区ID、grade_year和init_grade_id查询ysy_grade表获取grade_id
$ysy_grade = $this->syncConnection->table('ysy_grade')
    ->where('school_id', $syncSchoolId)
    ->where('name', $grade_year)
    ->where('grade_sort', $init_grade_id)
    ->first();

$sync_grade_id = $ysy_grade->id;
```

**查询条件：**
- `school_id`: 同步数据库中的学校ID
- `name`: 年级名称（如：一年级、二年级）
- `grade_sort`: 年级排序（如：1、2、3）

### 3. 班级名称查询
通过主数据库中的班级ID查询班级名称：

```php
// 通过学生的class_id查询班级名称
$class = \App\Models\School\System\Claass::find($class_id);
$class_name = $class->class_name;
```

### 4. 班级ID查询
通过同步数据库中的年级ID和班级名称查询班级ID：

```php
// 通过grade_id和班级名称查询同步数据库中的班级ID
$ysy_class = $this->syncConnection->table('ysy_class')
    ->where('school_id', $syncSchoolId)
    ->where('grade_id', $sync_grade_id)
    ->where('name', $class_name)
    ->first();

$sync_class_id = $ysy_class->id;
```

**查询条件：**
- `school_id`: 同步数据库中的学校ID
- `grade_id`: 同步数据库中的年级ID
- `name`: 班级名称（如：1班、2班）

## 完整查询流程图

```
请求参数
├── school_campus_id
├── grade_year (年级名称)
├── init_grade_id (年级排序)
└── class_id (主数据库班级ID)
    ↓
获取校区信息
    ↓ (getCampusInfo)
同步数据库学校ID (syncSchoolId)
    ↓
查询ysy_grade表
    ↓ (school_id + name + grade_sort)
同步数据库年级ID (sync_grade_id)
    ↓
查询主数据库班级信息
    ↓ (class_id)
班级名称 (class_name)
    ↓
查询ysy_class表
    ↓ (school_id + grade_id + name)
同步数据库班级ID (sync_class_id)
    ↓
准备学生同步数据
```

## 数据库表结构

### ysy_grade表（同步数据库年级表）
```sql
CREATE TABLE ysy_grade (
    id INT PRIMARY KEY AUTO_INCREMENT,
    school_id INT COMMENT '学校ID',
    name VARCHAR(50) COMMENT '年级名称',
    grade_sort INT COMMENT '年级排序',
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### ysy_class表（同步数据库班级表）
```sql
CREATE TABLE ysy_class (
    id INT PRIMARY KEY AUTO_INCREMENT,
    school_id INT COMMENT '学校ID',
    grade_id INT COMMENT '年级ID',
    name VARCHAR(50) COMMENT '班级名称',
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 主数据库班级表（Claass）
```sql
CREATE TABLE claasses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    school_id INT COMMENT '学校ID',
    school_campus_id INT COMMENT '校区ID',
    grade_id INT COMMENT '年级ID',
    class_name VARCHAR(50) COMMENT '班级名称',
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## 错误处理

### 1. 年级查询失败
```php
if (!$ysy_grade) {
    return [
        'success' => false,
        'message' => '未找到对应的年级信息'
    ];
}
```

### 2. 班级信息查询失败
```php
if (!$class) {
    return [
        'success' => false,
        'message' => '未找到班级信息'
    ];
}
```

### 3. 同步班级查询失败
```php
if (!$ysy_class) {
    return [
        'success' => false,
        'message' => '未找到对应的班级信息'
    ];
}
```

## 使用示例

### 请求参数示例
```php
$request_data = [
    'student_name' => '张三',
    'username' => 'zhangsan001',
    'gender' => 1,
    'school_campus_id' => 1291,
    'grade_year' => '一年级',
    'init_grade_id' => 1,
    'class_id' => 101
];
```

### 查询结果示例
```php
// 1. 查询ysy_grade表
// 条件: school_id=1071, name='一年级', grade_sort=1
// 结果: sync_grade_id = 2001

// 2. 查询主数据库班级表
// 条件: id=101
// 结果: class_name = '1班'

// 3. 查询ysy_class表
// 条件: school_id=1071, grade_id=2001, name='1班'
// 结果: sync_class_id = 3001

// 4. 最终同步数据
$student_data = [
    'name' => '张三',
    'username' => 'zhangsan001',
    'class_id' => 3001, // 同步数据库中的班级ID
    'grade_id' => 2001, // 同步数据库中的年级ID
    // ... 其他字段
];
```

## 关键字段映射

| 来源 | 字段 | 用途 | 目标 |
|------|------|------|------|
| 请求参数 | grade_year | 年级名称 | ysy_grade.name |
| 请求参数 | init_grade_id | 年级排序 | ysy_grade.grade_sort |
| 请求参数 | class_id | 主数据库班级ID | 查询班级名称 |
| 主数据库 | class_name | 班级名称 | ysy_class.name |
| 同步数据库 | sync_grade_id | 年级ID | ysy_student.grade_id |
| 同步数据库 | sync_class_id | 班级ID | ysy_student.class_id |

## 注意事项

1. **数据一致性**：确保主数据库和同步数据库中的年级、班级名称保持一致
2. **查询顺序**：必须先查询年级ID，再查询班级ID，因为班级查询依赖年级ID
3. **错误处理**：每个查询步骤都有相应的错误处理，确保数据完整性
4. **性能考虑**：多次数据库查询可能影响性能，可考虑缓存或优化查询
5. **数据校验**：建议在同步前验证年级和班级数据的有效性

## 优势

1. **精确匹配**：通过多重条件确保获取正确的年级和班级ID
2. **数据隔离**：不同学校的年级班级数据完全隔离
3. **灵活性**：支持不同的年级命名和排序方式
4. **可扩展性**：易于扩展支持更多的查询条件
