<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PartnerSchoolSeeder extends Seeder
{
    protected string $connect = 'mysql_demo';
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        #1.查询教育局对应的学校
        $eduList = DB::connection($this->connect)->table('school_district')
            ->select('id','school_id','edu_id')
            ->where('edu_id','>',0)
            ->where('step',0)
            ->get()->each(function ($item){
                // 获取现在的合作伙伴ID
                $partnerId = DB::table('partners')
                    ->where('agent_id', $item->edu_id)
                    ->value('id');
                if($partnerId){
                    $info = [
                        'partner_id' => $partnerId,
                        'school_id' => $item->school_id,
                        'school_campus_id' => $item->id,
                        'created_at' => date('Y-m-d H:i:s',time()),
                        'updated_at' => date('Y-m-d H:i:s',time()),
                    ];
                    DB::table('partner_schools')->insert($info);
                    unset($info);
                }
            });
        #2.查询代理商对应的学校
        $agentList = DB::connection($this->connect)->table('school')
            ->select('id','supplier_id')
            ->where('supplier_id','>',0)
            ->where('step',0)
            ->get()
            ->each(function ($item){
                $partnerId = DB::table('partners')->where('agent_id', $item->supplier_id)
                    ->value('id');
                if($partnerId){
                    $info = [
                        'partner_id' => $partnerId,
                        'school_id' => $item->id,
                        'created_at' => date('Y-m-d H:i:s',time()),
                        'updated_at' => date('Y-m-d H:i:s',time()),
                    ];
                    DB::table('partner_schools')->insert($info);
                    unset($info);
                }
            });
        dd('end');
    }
}
