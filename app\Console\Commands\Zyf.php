<?php

namespace App\Console\Commands;

use App\Models\Admin\Menu;
use App\Models\School\System\Course;
use App\Models\School\System\School;
use App\Models\School\System\Student;
use App\Models\School\System\Teacher;
use App\Models\User;
use App\Notifications\DownloadPdf;
use App\Notifications\Message;
use App\Services\Tool\MessageService;
use App\Traits\DingDingMessage;
use Illuminate\Console\Command;

class Zyf extends Command
{
    use DingDingMessage;
    /**
     * The name and signature of the console command.
     * 执行命令 php artisan zyf
     * @var string
     */
    protected $signature = 'zyf';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        // 一对一关系：获取学生及所属学校信息
//        $res = Student::where('id', 4917)->with('school:id,name')->first()->toArray();

        // 一对多关系：获取学校及所属校区信息
//        $res = School::where('id', 301)->with('schoolCampuses')->get()->toArray();

        // 多对多关系：获取老师及所带课程信息
//        $res = Teacher::whereId(331)->with('courses')->get()->toArray();
        // 多对多关系：获取课程及所有带这个课程的老师信息
//        $res = Course::whereId(70)->with('teachers')->get()->toArray();

        // 递归获取菜单
//        $res = Menu::whereParentId(0)->whereType(1)->with('children')->get()->toArray();

        // 多态获取机构及机构的用户数量
//        $res = School::whereId(301)->with(['organization'=>function ($query) {
//            $query->withCount(['users as user_number']);
//        }])->get()->toArray();

        // 获取学生最后一条班级及年级信息
//        $res = Student::whereId(4917)->with(['lastStudentClass.claass.grade'])->get()->toArray();
//
//        $res = Student::query()->join('student_classes', 'student_classes.student_id', 'students.id')
//            ->leftJoin('classes', 'student_classes.class_id', 'classes.id')
//            ->leftJoin('grades', 'classes.grade_id', 'grades.id')
//            ->select('students.*', 'classes.class_name', 'grades.grade_name')
//            ->where('students.id', 4917)
//            ->get()->toArray();


//        dump($res);

        // $query = Student::where('school_id', 301)
        //     ->when(true, fn($query) => $query->hasByNonDependentSubquery('lastStudentClass', fn($query) =>
        //     $query->where('grade_id', 10))
        //     )
        //     ->with('school:id,name', 'schoolCampus:id,campus_name',  'user:id,real_name');
        // $query->get();

        // 多态获取用户所属的机构及机构的配置信息
//        $res = User::whereIn('id',[2])->with(['organization.model.config'])->get()->toArray();
        // 上下等同
//        $res = User::whereIn('id',[2])->with(['organization'=>function ($query) {
//            $query->with(['model'=>function ($query) {
//                $query->with('config');
//            }]);
//        }])->get()->toArray();

//        dd($res);

        // 测试钉钉消息
//        $this->send_dingding_message('测试钉钉消息11111222222221');

        // $server_ip = gethostbyname(gethostname());
        // echo $server_ip;

        // dd(1111);

        // ===================================== 消息通知：数据库通知 begin ==========================================
        // 获取用户通知对象
        $user = User::find(1);
        // 发送消息通知
        // $user->notify(new DownloadPdf(['notify_massage' => 'pdf批量下载成功', 'url' => 'C:\Intel\Logs\a.pdf']));
        // 调用 MessageService 的 sendMessage
        $messageService = new MessageService();
        $messageService->sendMessage($user->id, ['title' => 'pdf批量下载成功', 'content' => '测评任务：心理测评20250709 pdf批量下载成功', 'type' => 'info', 'url' => 'C:\Intel\Logs\a.pdf']);

//        // 获取所有用户通知信息
//        foreach ($user->notifications->where('type', 'App\Notifications\DownloadPdf') as $notification) {
//         //    dump($notification->data);
//            echo $notification->type . PHP_EOL;
//        }
//        // 获取 App\Notifications\DownloadPdf 类型用户通知信息
//        foreach ($user->notifications->where('type', 'App\Notifications\DownloadPdf') as $notification) {
//           dump($notification->data);
//        //    echo $notification->type . PHP_EOL;
//        }

        // 获取所有未读消息
        // foreach ($user->unreadNotifications as $notification) {
        //     echo $notification->type. PHP_EOL;
        //     // 标记为已读
        // //     $notification->markAsRead();
        // }
        // 获取 App\Notifications\DownloadPdf 类型所有未读消息
        // foreach ($user->unreadNotifications->where('type', 'App\Notifications\DownloadPdf') as $notification) {
        //     echo $notification->type. PHP_EOL;
        //     // 标记为已读
        // //     $notification->markAsRead();
        // }
        
       // 设置所有未读消息标记为已读
//        $user->unreadNotifications->markAsRead();
       // ===================================== 消息通知：数据库通知 end ==========================================
    }
}
