<?php

namespace App\Enums;

class AssessmentTypeEnum
{
    // 问题类型常量
    public const CAREER = 'career';
    public const SUBJECT = 'subject';
    public const CAPABILITY = 'capability';
    public const COMPETENCY = 'competency';
    public const PSYCHOLOGY = 'psychology';

    /**
     * 获取所有问题类型
     *
     * @return array
     */
    public static function getAll(): array
    {
        return [
            self::CAREER,
            self::SUBJECT,
            self::CAPABILITY,
            self::COMPETENCY,
            self::PSYCHOLOGY,
        ];
    }

    /**
     * 检查类型是否有效
     *
     * @param string $type
     * @return bool
     */
    public static function isValid(string $type): bool
    {
        return in_array($type, self::getAll());
    }
}