<?php

namespace App\Enums;

/**
 * 省份枚举
 * 
 * 定义系统中使用的省份ID
 */
enum ProvinceEnum: int
{
    // 北京
    case BEIJING = 1;
    
    // 天津
    case TIANJIN = 2;
    
    // 河北
    case HEBEI = 3;
    
    // 山西
    case SHANXI = 4;
    
    // 内蒙古
    case NEIMENGGU = 5;
    
    // 辽宁
    case LIAONING = 6;
    
    // 吉林
    case JILIN = 7;
    
    // 黑龙江
    case HEILONGJIANG = 8;
    
    // 上海
    case SHANGHAI = 9;
    
    // 江苏
    case JIANGSU = 10;
    
    // 浙江
    case ZHEJIANG = 11;
    
    // 安徽
    case ANHUI = 12;
    
    // 福建
    case FUJIAN = 13;
    
    // 江西
    case JIANGXI = 14;
    
    // 山东
    case SHANDONG = 15;
    
    // 河南
    case HENAN = 16;
    
    // 湖北
    case HUBEI = 17;
    
    // 湖南
    case HUNAN = 18;
    
    // 广东
    case GUANGDONG = 19;
    
    // 广西
    case GUANGXI = 20;
    
    // 海南
    case HAINAN = 21;
    
    // 重庆
    case CHONGQING = 22;
    
    // 四川
    case SICHUAN = 23;
    
    // 贵州
    case GUIZHOU = 24;
    
    // 云南
    case YUNNAN = 25;
    
    // 西藏
    case XIZANG = 26;
    
    // 陕西
    case SHAANXI = 27;
    
    // 甘肃
    case GANSU = 28;
    
    // 青海
    case QINGHAI = 29;
    
    // 宁夏
    case NINGXIA = 30;
    
    // 新疆
    case XINJIANG = 31;
    
    // 台湾
    case TAIWAN = 32;
    
    // 香港
    case HONGKONG = 33;
    
    // 澳门
    case MACAO = 34;
    
    /**
     * 获取省份的中文名称
     *
     * @return string 省份的中文名称
     */
    public function getDisplayName(): string
    {
        return match($this) {
            self::BEIJING => '北京',
            self::TIANJIN => '天津',
            self::HEBEI => '河北',
            self::SHANXI => '山西',
            self::NEIMENGGU => '内蒙古',
            self::LIAONING => '辽宁',
            self::JILIN => '吉林',
            self::HEILONGJIANG => '黑龙江',
            self::SHANGHAI => '上海',
            self::JIANGSU => '江苏',
            self::ZHEJIANG => '浙江',
            self::ANHUI => '安徽',
            self::FUJIAN => '福建',
            self::JIANGXI => '江西',
            self::SHANDONG => '山东',
            self::HENAN => '河南',
            self::HUBEI => '湖北',
            self::HUNAN => '湖南',
            self::GUANGDONG => '广东',
            self::GUANGXI => '广西',
            self::HAINAN => '海南',
            self::CHONGQING => '重庆',
            self::SICHUAN => '四川',
            self::GUIZHOU => '贵州',
            self::YUNNAN => '云南',
            self::XIZANG => '西藏',
            self::SHAANXI => '陕西',
            self::GANSU => '甘肃',
            self::QINGHAI => '青海',
            self::NINGXIA => '宁夏',
            self::XINJIANG => '新疆',
            self::TAIWAN => '台湾',
            self::HONGKONG => '香港',
            self::MACAO => '澳门',
        };
    }
    
    /**
     * 获取省份的拼音
     *
     * @return string 省份的拼音
     */
    public function getPinyin(): string
    {
        return match($this) {
            self::BEIJING => 'beijing',
            self::TIANJIN => 'tianjin',
            self::HEBEI => 'hebei',
            self::SHANXI => 'shanxi',
            self::NEIMENGGU => 'neimenggu',
            self::LIAONING => 'liaoning',
            self::JILIN => 'jilin',
            self::HEILONGJIANG => 'heilongjiang',
            self::SHANGHAI => 'shanghai',
            self::JIANGSU => 'jiangsu',
            self::ZHEJIANG => 'zhejiang',
            self::ANHUI => 'anhui',
            self::FUJIAN => 'fujian',
            self::JIANGXI => 'jiangxi',
            self::SHANDONG => 'shandong',
            self::HENAN => 'henan',
            self::HUBEI => 'hubei',
            self::HUNAN => 'hunan',
            self::GUANGDONG => 'guangdong',
            self::GUANGXI => 'guangxi',
            self::HAINAN => 'hainan',
            self::CHONGQING => 'chongqing',
            self::SICHUAN => 'sichuan',
            self::GUIZHOU => 'guizhou',
            self::YUNNAN => 'yunnan',
            self::XIZANG => 'xizang',
            self::SHAANXI => 'shaanxi',
            self::GANSU => 'gansu',
            self::QINGHAI => 'qinghai',
            self::NINGXIA => 'ningxia',
            self::XINJIANG => 'xinjiang',
            self::TAIWAN => 'taiwan',
            self::HONGKONG => 'xianggang',
            self::MACAO => 'aomen',
        };
    }
    
    /**
     * 从整数值获取枚举实例
     *
     * @param int $value 整数值
     * @return self|null 对应的枚举实例，如果不存在则返回 null
     */
    public static function fromInt(int $value): ?self
    {
        return self::tryFrom($value);
    }
}
