<?php

namespace App\Http\Controllers\Psychassessment;

use App\Http\Controllers\Controller;
use App\Services\Psychassessment\LoadpdfService;
use App\Services\Psychassessment\ReportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * 心理评估-PDF生成控制器 - 基于原 ThinkPHP Loadpdf 控制器重新实现
 */
class PsychassessmentLoadpdfController extends Controller
{
    protected $loadpdfService;
    protected $reportService;

    public function __construct(LoadpdfService $loadpdfService, ReportService $reportService)
    {
        $this->loadpdfService = $loadpdfService;
        $this->reportService = $reportService;
    }

    /**
     * 个人报告PDF页面
     * 
     * @param Request $request
     * @return \Illuminate\View\View|JsonResponse
     */
    public function personalPdf(Request $request)
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $memberId = $request->input('member_id');
            
            if (!$planId || !$surveyType || !$memberId) {
                return $this->error('缺少必要参数');
            }
            
            $data = $this->reportService->getReport($planId, $surveyType, $memberId);
            
            // 返回PDF页面视图
            return view('psychassessment.pdf.index', ['data' => $data]);
        } catch (\Exception $e) {
            return $this->error('生成PDF页面失败: ' . $e->getMessage());
        }
    }

    /**
     * 个人报告下载
     * 
     * @param Request $request
     * @return JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function individual(Request $request)
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $memberId = $request->input('member_id');
            
            if (!$planId || !$surveyType || !$memberId) {
                return $this->error('缺少必要参数', -105);
            }
            
            $result = $this->loadpdfService->generateIndividualPdf($planId, $surveyType, $memberId);
            
            if ($result['success']) {
                if ($result['type'] === 'download') {
                    // 返回文件下载
                    return $result['data'];
                } else {
                    // 返回PDF URL或其他数据
                    return $this->success($result['data'], '生成成功！');
                }
            } else {
                return $this->error($result['message'] ?? '生成失败！');
            }
        } catch (\Exception $e) {
            return $this->error('生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量生成个人报告PDF
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchIndividual(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $memberIds = $request->input('member_ids');
            
            if (!$planId || !$surveyType || !$memberIds) {
                return $this->error('缺少必要参数');
            }
            
            $result = $this->loadpdfService->batchGenerateIndividualPdf($planId, $surveyType, $memberIds);
            
            if ($result['success']) {
                return $this->success($result['data'], '批量生成成功！');
            } else {
                return $this->error($result['message'] ?? '批量生成失败！');
            }
        } catch (\Exception $e) {
            return $this->error('批量生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成班级报告PDF
     * 
     * @param Request $request
     * @return JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function classReport(Request $request)
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $classId = $request->input('class_id');
            
            if (!$planId || !$surveyType || !$classId) {
                return $this->error('缺少必要参数');
            }
            
            $result = $this->loadpdfService->generateClassReportPdf($planId, $surveyType, $classId);
            
            if ($result['success']) {
                if ($result['type'] === 'download') {
                    return $result['data'];
                } else {
                    return $this->success($result['data'], '生成成功！');
                }
            } else {
                return $this->error($result['message'] ?? '生成失败！');
            }
        } catch (\Exception $e) {
            return $this->error('生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成年级报告PDF
     * 
     * @param Request $request
     * @return JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function gradeReport(Request $request)
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $gradeId = $request->input('grade_id');
            
            if (!$planId || !$surveyType || !$gradeId) {
                return $this->error('缺少必要参数');
            }
            
            $result = $this->loadpdfService->generateGradeReportPdf($planId, $surveyType, $gradeId);
            
            if ($result['success']) {
                if ($result['type'] === 'download') {
                    return $result['data'];
                } else {
                    return $this->success($result['data'], '生成成功！');
                }
            } else {
                return $this->error($result['message'] ?? '生成失败！');
            }
        } catch (\Exception $e) {
            return $this->error('生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成学校报告PDF
     * 
     * @param Request $request
     * @return JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function schoolReport(Request $request)
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $schoolId = $request->input('school_id');
            
            if (!$planId || !$surveyType) {
                return $this->error('缺少必要参数');
            }
            
            $result = $this->loadpdfService->generateSchoolReportPdf($planId, $surveyType, $schoolId);
            
            if ($result['success']) {
                if ($result['type'] === 'download') {
                    return $result['data'];
                } else {
                    return $this->success($result['data'], '生成成功！');
                }
            } else {
                return $this->error($result['message'] ?? '生成失败！');
            }
        } catch (\Exception $e) {
            return $this->error('生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取PDF生成状态
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPdfStatus(Request $request): JsonResponse
    {
        try {
            $taskId = $request->input('task_id');
            
            if (!$taskId) {
                return $this->error('缺少任务ID');
            }
            
            $status = $this->loadpdfService->getPdfGenerationStatus($taskId);
            return $this->success($status, '获取状态成功');
        } catch (\Exception $e) {
            return $this->error('获取状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 取消PDF生成任务
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function cancelPdfGeneration(Request $request): JsonResponse
    {
        try {
            $taskId = $request->input('task_id');
            
            if (!$taskId) {
                return $this->error('缺少任务ID');
            }
            
            $result = $this->loadpdfService->cancelPdfGeneration($taskId);
            
            if ($result) {
                return $this->success([], '取消成功');
            } else {
                return $this->error('取消失败');
            }
        } catch (\Exception $e) {
            return $this->error('取消失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取PDF模板列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPdfTemplates(Request $request): JsonResponse
    {
        try {
            $surveyType = $request->input('survey_type');
            $templates = $this->loadpdfService->getPdfTemplates($surveyType);
            return $this->success($templates, '获取模板成功');
        } catch (\Exception $e) {
            return $this->error('获取模板失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新PDF模板
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updatePdfTemplate(Request $request): JsonResponse
    {
        try {
            $templateId = $request->input('template_id');
            $templateData = $request->input('template_data');
            
            $result = $this->loadpdfService->updatePdfTemplate($templateId, $templateData, auth()->user());
            
            if ($result) {
                return $this->success([], '更新模板成功');
            } else {
                return $this->error('更新模板失败');
            }
        } catch (\Exception $e) {
            return $this->error('更新模板失败: ' . $e->getMessage());
        }
    }

    /**
     * 预览PDF
     * 
     * @param Request $request
     * @return \Illuminate\View\View|JsonResponse
     */
    public function previewPdf(Request $request)
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $memberId = $request->input('member_id');
            $templateId = $request->input('template_id');
            
            if (!$planId || !$surveyType || !$memberId) {
                return $this->error('缺少必要参数');
            }
            
            $previewData = $this->loadpdfService->previewPdf($planId, $surveyType, $memberId, $templateId);
            
            // 返回预览页面
            return view('psychassessment.pdf.preview', ['data' => $previewData]);
        } catch (\Exception $e) {
            return $this->error('预览失败: ' . $e->getMessage());
        }
    }
}
