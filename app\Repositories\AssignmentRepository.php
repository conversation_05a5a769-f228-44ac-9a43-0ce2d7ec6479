<?php

namespace App\Repositories;

use App\Models\School\Assessment\AssessmentTaskAssignment;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;

class AssignmentRepository
{
    /**
     * 获取分发信息详情
     * 
     * @param int $task_id 任务ID
     * @param int $school_id 学校ID
     * @return \Illuminate\Database\Eloquent\Builder 分发信息详情数据
     */
    public function getAssignmentsByTaskId(int $task_id, int $school_id): Builder
    {
        return AssessmentTaskAssignment::select('id as assignment_id','student_id', 'student_class_id', 'updated_at as completion_time', 'duration', 'standard_results')
            ->where('assessment_task_id', $task_id)
            ->where('school_id', $school_id)
            ->with([
                'student:id,student_name,gender',
                'studentClass:id,student_id,class_id,school_year',
                'studentClass.claass:id,class_name,grade_id',
                'studentClass.claass.grade:id,grade_name'
            ]);
    }

    /**
     * 获取测评信息详情
     * 
     * @param int $user_id 用户ID
     * @param int $school_id 学校ID
     * @param array $assessment_ids 测评id组成的数组
     * @return array 测评信息详情数据
     */
    public function getLatestAssignments(int $user_id, int $school_id, array $assessment_ids)
    {
        return AssessmentTaskAssignment::query()
        ->select('id','standard_results','assessment_id')
        ->whereIn('id', function($query) use ($user_id, $school_id, $assessment_ids) {
            $query->select(DB::raw('MAX(id)'))
                ->from('assessment_task_assignments')
                ->groupBy('assessment_id')
                ->where('user_id', $user_id)
                ->where('school_id', $school_id)
                ->whereNull('deleted_at')
                ->whereIn('assessment_id', $assessment_ids);
        })
        ->where('user_id', $user_id)
        ->where('school_id', $school_id)
        ->whereIn('assessment_id', $assessment_ids)
        ->orderBy('id', 'desc')
        ->get()
        ->toArray();
    }
}