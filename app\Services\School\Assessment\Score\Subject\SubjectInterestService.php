<?php

namespace App\Services\School\Assessment\Score\Subject;

/**
 * 学科兴趣评估服务类
 * 
 * 该类用于计算学生的学科兴趣评估结果，包括各维度分数计算和总分计算
 */
class SubjectInterestService extends AbstractScoreService
{
    /**
     * 计算学科兴趣评估结果
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 评估结果数组，包含维度分数和总分
     */
    public function calculate(array $params): array
    {
        $dimension_scores = $this->calculateScores($params);

        // 计算各维度得分
        $total_score = 0;
        $total_question_count = 0;
        foreach ($dimension_scores as $key => $dimension) {
            // 维度得分 = (维度内题目得分总和 / 维度题目数量) × 2
            $dimension_scores[$key]['score'] = round($dimension['score'] / $dimension['question_count'] * 2, 1);
            $total_score += $dimension['score'];
            $total_question_count += $dimension['question_count'];
            unset($dimension_scores[$key]['question_count']);
        }
        
        $average_score = round($total_score * 2 / $total_question_count, 1);//展示总分=所有题目总分*2/所有题数

        return ['dimensions'=>$dimension_scores,'total_score'=>$average_score];
    }
}