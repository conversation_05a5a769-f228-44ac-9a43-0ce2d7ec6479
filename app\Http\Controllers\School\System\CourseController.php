<?php

namespace App\Http\Controllers\School\System;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\System\CourseRequest;
use App\Models\School\System\Course;
use App\Services\School\System\CourseService;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;

class CourseController extends Controller
{
    use CrudOperations;

    protected string $model = Course::class;

    protected CourseService $service;

    public function __construct(CourseService $service)
    {
        $this->service = $service;
    }

    public function index(Request $request)
    {
        $query = $this->service->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->with('schoolCampus', 'grade')->get();
        return $this->paginateSuccess($list, $cnt);
    }


    // 根据校区、年级获取课程下拉列表
    public function getCourseList(CourseRequest $request)
    {
        $school_id = $request->user()->organization->model_id;
        $school_campus_id = $request['school_campus_id'];
        $grade_id = $request['grade_id'];

        $query = Course::where('school_id', $school_id)
            ->when($school_campus_id, fn($query) => $query->where('school_campus_id', $school_campus_id))
            ->when($grade_id, fn($query) => $query->where('grade_id', $grade_id));
        $list = $query->get(['id', 'course_name']);
        return $this->success($list);
    }

    public function store(CourseRequest $request)
    {
        $this->service->store($request);
        return $this->message('新增成功');
    }

    // 批量新增
    public function batchStore(CourseRequest $request)
    {
        $this->service->batchStore($request);
        return $this->message('新增成功');
    }


    public function update(Request $request, $id)
    {
        $this->service->update($request, $id);
        return $this->message('修改成功');
    }

}
