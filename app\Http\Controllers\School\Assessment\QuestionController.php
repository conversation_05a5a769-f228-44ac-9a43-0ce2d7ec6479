<?php

namespace App\Http\Controllers\School\Assessment;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\Assessment\AssessmentRequest;
use App\Http\Resources\School\Assessment\QuestionResource;
use App\Services\School\Assessment\QuestionService;

class QuestionController extends Controller
{

    // 构造函数注入服务
    public function __construct(
        protected QuestionService $questionService
    ) {
    }

    /**
     * 根据测评ID和类型获取问题列表
     *
     * @param AssessmentRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function questionList(AssessmentRequest $request)
    {
        $questions = $this->questionService->getQuestions($request->assessment_id, $request->type);
        return $this->success(QuestionResource::collection($questions));
    }

}
