<?php

namespace App\Services\School\Assessment\Score\Career;

use App\Services\School\Assessment\Score\Career\AbstractScoreService;

/**
 * 生涯发展水平评估服务
 * 
 * 用于计算学生的生涯发展水平评估结果
 */
class DevelopmentService extends AbstractScoreService
{
    /**
     * 计算生涯发展水平评估结果
     * 
     * @param array $params 包含评估所需参数的数组
     * @return array 生涯发展水平评估结果数组
     */
    public function calculate($params): array
    {
        $dimension_scores = $this->calculateScores($params);

        $attitude_dimensions = ['生涯自主', '生涯自信'];
        $cognition_dimensions = $params['assessment_id'] == 5 ? 
            ['自我认知', '教育认知', '职业认知'] : 
            ['自我认知', '教育认知'];
        $action_dimensions = $params['assessment_id'] == 5 ? 
            ['生涯探索', '生涯计划', '生涯决策'] : 
            ['生涯探索', '生涯计划'];

        // 计算各维度分数
        $grouped_dimensions = $this->groupDimensions(
            $dimension_scores,
            $attitude_dimensions,
            $cognition_dimensions,
            $action_dimensions
        );

        // 计算总分
        $total_score = round(
            array_sum(array_column($dimension_scores, 'score')) / count($dimension_scores),
            1
        );

        return [
            'dimensions' => $grouped_dimensions,
            'total_score' => $total_score,
        ];
    }

    /**
     * 对维度进行分组
     * 
     * @param array $dimensions 维度数组
     * @param array $attitude 态度维度名称数组
     * @param array $cognition 认知维度名称数组
     * @param array $action 行动维度名称数组
     * @return array 分组后的维度数组
     */
    private function groupDimensions(array $dimensions, array $attitude, array $cognition, array $action): array
    {
        $attitude_children = [];
        $cognition_children = [];
        $action_children = [];
        
        foreach ($dimensions as $dimension) {
            if (in_array($dimension['name'], $attitude)) {
                $attitude_children[] = [
                    'code' => $dimension['code'],
                    'name' => $dimension['name'],
                    'score' => $dimension['score']
                ];
            } elseif (in_array($dimension['name'], $cognition)) {
                $cognition_children[] = [
                    'code' => $dimension['code'],
                    'name' => $dimension['name'],
                    'score' => $dimension['score']
                ];
            } elseif (in_array($dimension['name'], $action)) {
                $action_children[] = [
                    'code' => $dimension['code'],
                    'name' => $dimension['name'],
                    'score' => $dimension['score']
                ];
            }
        }

        // 计算各组的平均分
        $attitude_score = round(array_sum(array_column($attitude_children, 'score')) / count($attitude_children), 1);
        $cognition_score = round(array_sum(array_column($cognition_children, 'score')) / count($cognition_children), 1);
        $action_score = round(array_sum(array_column($action_children, 'score')) / count($action_children), 1);
        
        return [
            [
                'name' => '生涯态度',
                'score' => $attitude_score,
                'children' => $attitude_children
            ],
            [
                'name' => '生涯认知',
                'score' => $cognition_score,
                'children' => $cognition_children
            ],
            [
                'name' => '生涯行动',
                'score' => $action_score,
                'children' => $action_children
            ]
        ];
    }
}