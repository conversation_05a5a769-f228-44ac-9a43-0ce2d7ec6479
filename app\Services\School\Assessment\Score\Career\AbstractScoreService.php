<?php

namespace App\Services\School\Assessment\Score\Career;

use App\Repositories\AnswerRepository;
use App\Services\BaseService;
use App\Services\School\Assessment\Score\ScoreServiceInterface;

/**
 * 生涯测评抽象评分服务
 * 
 * 提供生涯测评相关的基础评分功能，包括分数计算和人格分数计算
 */
abstract class AbstractScoreService extends BaseService implements ScoreServiceInterface
{
    public function __construct(
        protected AnswerRepository $answerRepository
    ) {
        
    }

    /**
     * 查询生涯测评分数，维度分由sql完成计算
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 维度分数数组
     */
    public function calculateScores(array $params): array
    {
        $data = $this->answerRepository->getCareerDimensionScores($params);

        return $data;
    }
    
    /**
     * 计算性格测评分数
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 性格维度分数数组
     */
    public function calculatePersonalityScores(array $params): array
    {
        // 尝试用trae将其转换成查询构造器写法失败了，所以使用原生SQL查询
        $data = $this->answerRepository->getCareerPersonalityDimensionScores($params);

        return $data;
    }

    /**
     * 计算评估结果
     * 
     * @param array $params 包含评估所需参数的数组
     * @return array 评估结果数组
     */
    abstract public function calculate(array $params): array;
}
