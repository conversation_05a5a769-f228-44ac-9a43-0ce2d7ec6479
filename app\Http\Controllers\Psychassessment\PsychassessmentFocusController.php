<?php

namespace App\Http\Controllers\Psychassessment;

use App\Http\Controllers\Controller;
use App\Services\Psychassessment\FocusService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * 心理评估-重点关注控制器 - 基于原 ThinkPHP Focus 控制器重新实现
 */
class PsychassessmentFocusController extends Controller
{
    protected $focusService;

    public function __construct(FocusService $focusService)
    {
        $this->focusService = $focusService;
    }

    /**
     * 重点关注操作（添加/查询/删除）
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function focus(Request $request): JsonResponse
    {
        try {
            $method = $request->method();
            
            switch ($method) {
                case 'POST':
                    // 添加重点关注
                    $studentIds = $request->input('student_ids');
                    $reason = $request->input('reason');
                    
                    if (!$studentIds || !$reason) {
                        return $this->error('缺少必要参数');
                    }
                    
                    $data = $this->focusService->addFocus($studentIds, $reason, auth()->user());
                    return $this->success($data, '添加成功');
                    
                case 'GET':
                    // 查询重点关注名单
                    $params = $request->all();
                    $data = $this->focusService->getFocusList($params);
                    return $this->success($data, '获取成功');
                    
                case 'DELETE':
                    // 删除重点关注记录
                    $id = $request->route('id') ?: $request->input('id');
                    $removeReason = $request->input('remove_reason');
                    
                    if (!$id || !$removeReason) {
                        return $this->error('缺少必要参数');
                    }
                    
                    $data = $this->focusService->removeFocus($id, $removeReason, auth()->user());
                    return $this->success($data, '删除成功');
                    
                default:
                    return $this->error('不支持的请求方法');
            }
        } catch (\Exception $e) {
            return $this->error('操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取重点关注详情
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getFocusDetail(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $data = $this->focusService->getFocusDetail($id);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量添加重点关注
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchAdd(Request $request): JsonResponse
    {
        try {
            $students = $request->input('students'); // [{'student_id': 1, 'reason': '原因'}, ...]
            $data = $this->focusService->batchAddFocus($students, auth()->user());
            return $this->success($data, '批量添加成功');
        } catch (\Exception $e) {
            return $this->error('批量添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量删除重点关注
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchDelete(Request $request): JsonResponse
    {
        try {
            $ids = $request->input('ids');
            $removeReason = $request->input('remove_reason');
            
            $data = $this->focusService->batchRemoveFocus($ids, $removeReason, auth()->user());
            return $this->success($data, '批量删除成功');
        } catch (\Exception $e) {
            return $this->error('批量删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新重点关注信息
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateFocus(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $data = $request->only(['reason', 'status', 'remark']);
            
            $result = $this->focusService->updateFocus($id, $data, auth()->user());
            return $this->success($result, '更新成功');
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取重点关注统计数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getFocusStatistics(Request $request): JsonResponse
    {
        try {
            $params = $request->all();
            $data = $this->focusService->getFocusStatistics($params);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出重点关注名单
     * 
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|JsonResponse
     */
    public function exportFocus(Request $request)
    {
        try {
            $params = $request->all();
            return $this->focusService->exportFocus($params);
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取关注原因分类
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getReasonCategories(Request $request): JsonResponse
    {
        try {
            $data = $this->focusService->getReasonCategories();
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取学生关注历史
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudentFocusHistory(Request $request): JsonResponse
    {
        try {
            $studentId = $request->input('student_id');
            $data = $this->focusService->getStudentFocusHistory($studentId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 添加关注跟进记录
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function addFollowUp(Request $request): JsonResponse
    {
        try {
            $focusId = $request->input('focus_id');
            $content = $request->input('content');
            $followUpType = $request->input('follow_up_type');
            
            $data = $this->focusService->addFollowUp($focusId, $content, $followUpType, auth()->user());
            return $this->success($data, '添加成功');
        } catch (\Exception $e) {
            return $this->error('添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取关注跟进记录
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getFollowUpList(Request $request): JsonResponse
    {
        try {
            $focusId = $request->input('focus_id');
            $data = $this->focusService->getFollowUpList($focusId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新关注状态
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateStatus(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $status = $request->input('status');
            
            $data = $this->focusService->updateStatus($id, $status, auth()->user());
            return $this->success($data, '状态更新成功');
        } catch (\Exception $e) {
            return $this->error('状态更新失败: ' . $e->getMessage());
        }
    }
}
