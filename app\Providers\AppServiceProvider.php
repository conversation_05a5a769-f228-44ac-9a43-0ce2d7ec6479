<?php

namespace App\Providers;

use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }

        // 注册 TeacherService 依赖
        $this->app->when(\App\Services\School\System\TeacherService::class)
            ->needs(\App\Services\School\System\ClassService::class)
            ->give(\App\Services\School\System\ClassService::class);

        $this->app->when(\App\Services\School\System\TeacherService::class)
            ->needs(\App\Services\UserService::class)
            ->give(\App\Services\UserService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // 设置模型别名
        Relation::morphMap([
            'school' => 'App\Models\School\System\School',
            'partner' => 'App\Models\Partner\Partner',
        ]);
    }
}
