# API 响应格式示例

## 统一响应格式

所有API接口都使用统一的JSON响应格式，便于前端处理和转换为数组格式。

### 基本响应结构

```json
{
  "status": "success|error",
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

## 1. 生成Token接口

### 请求
```http
POST /api/user/generate-token
Content-Type: application/json

{
  "username": "test_user",
  "password": "password123"
}
```

### 成功响应 (200)
```json
{
  "status": "success",
  "code": 200,
  "message": "Token 生成成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vbG9jYWxob3N0OjgwMDAvYXBpL3VzZXIvZ2VuZXJhdGUtdG9rZW4iLCJpYXQiOjE3MDk4ODg4ODgsImV4cCI6MTcwOTg5MjQ4OCwibmJmIjoxNzA5ODg4ODg4LCJqdGkiOiJhYmMxMjMiLCJzdWIiOiIxIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.signature",
    "user": {
      "id": 1,
      "username": "test_user",
      "real_name": "测试用户"
    }
  }
}
```

### 错误响应 (400)
```json
{
  "status": "error",
  "code": 400,
  "message": "用户名未提供"
}
```

### 错误响应 (404)
```json
{
  "status": "error",
  "code": 404,
  "message": "用户不存在"
}
```

### 错误响应 (403)
```json
{
  "status": "error",
  "code": 403,
  "message": "用户账号已被禁用"
}
```

## 2. 获取用户详细信息接口

### 请求
```http
GET /api/user/detail
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

### 成功响应 (200) - 学生用户
```json
{
  "status": "success",
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "id": 1,
    "username": "student001",
    "real_name": "张三",
    "gender": 1,
    "email": "<EMAIL>",
    "organization_id": 1,
    "status": 1,
    "role_info": {
      "role_types": [1],
      "role_names": ["学生"],
      "roles": [
        {
          "id": 1,
          "name": "学生",
          "type": 1,
          "type_name": "学生"
        }
      ]
    },
    "detail_info": {
      "student_info": {
        "id": 1,
        "student_name": "张三",
        "gender": 1,
        "school_no": "2024001",
        "student_no": "2024001",
        "init_grade_id": 1,
        "grade_year": 2024,
        "date_start": "2024-09-01",
        "date_due": "2027-06-30",
        "school": {
          "id": 1,
          "name": "示例中学"
        },
        "school_campus": {
          "id": 1,
          "campus_name": "主校区"
        },
        "current_class": {
          "id": 1,
          "class_name": "高一(1)班",
          "grade_id": 1,
          "school_year": "2024-2025"
        },
        "current_grade": {
          "id": 1,
          "grade_name": "高一年级"
        }
      }
    },
    "organization_info": {
      "id": 1,
      "org_name": "示例中学",
      "model_type": "school",
      "model_name": "示例中学",
      "province": "北京市"
    }
  }
}
```

### 成功响应 (200) - 教师用户
```json
{
  "status": "success",
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "id": 2,
    "username": "teacher001",
    "real_name": "李老师",
    "gender": 2,
    "email": "<EMAIL>",
    "organization_id": 1,
    "status": 1,
    "role_info": {
      "role_types": [3],
      "role_names": ["教师"],
      "roles": [
        {
          "id": 3,
          "name": "教师",
          "type": 3,
          "type_name": "教师"
        }
      ]
    },
    "detail_info": {
      "teacher_info": {
        "id": 1,
        "teacher_name": "李老师",
        "school": {
          "id": 1,
          "name": "示例中学"
        },
        "school_campus": {
          "id": 1,
          "campus_name": "主校区"
        },
        "teaching_classes": [
          {
            "id": 1,
            "class_name": "高一(1)班",
            "grade_id": 1,
            "grade_name": "高一年级"
          },
          {
            "id": 2,
            "class_name": "高一(2)班",
            "grade_id": 1,
            "grade_name": "高一年级"
          }
        ],
        "viewable_classes": [
          {
            "id": 1,
            "class_name": "高一(1)班",
            "grade_id": 1,
            "grade_name": "高一年级"
          }
        ],
        "teaching_courses": [
          {
            "id": 1,
            "course_name": "数学"
          }
        ],
        "viewable_courses": [
          {
            "id": 1,
            "course_name": "数学"
          }
        ]
      }
    },
    "organization_info": {
      "id": 1,
      "org_name": "示例中学",
      "model_type": "school",
      "model_name": "示例中学",
      "province": "北京市"
    }
  }
}
```

### 错误响应 (401)
```json
{
  "status": "error",
  "code": 401,
  "message": "Token 未提供"
}
```

```json
{
  "status": "error",
  "code": 401,
  "message": "Token 已过期"
}
```

```json
{
  "status": "error",
  "code": 401,
  "message": "Token 无效"
}
```

### 错误响应 (404)
```json
{
  "status": "error",
  "code": 404,
  "message": "用户不存在"
}
```

### 错误响应 (403)
```json
{
  "status": "error",
  "code": 403,
  "message": "用户账号已被禁用"
}
```

## 前端处理示例

### JavaScript 处理
```javascript
// 处理API响应
function handleApiResponse(response) {
  if (response.status === 'success') {
    console.log('操作成功:', response.message);
    return response.data;
  } else {
    console.error('操作失败:', response.message);
    throw new Error(response.message);
  }
}

// 使用示例
async function getUserInfo(token) {
  try {
    const response = await fetch('/api/user/detail', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json'
      }
    });
    
    const result = await response.json();
    const userData = handleApiResponse(result);
    
    // 转换为数组格式（如果需要）
    const userArray = Object.entries(userData);
    console.log('用户数据数组:', userArray);
    
    return userData;
  } catch (error) {
    console.error('获取用户信息失败:', error.message);
  }
}
```

### PHP 处理
```php
// 处理API响应
function handleApiResponse($response) {
    $data = json_decode($response, true);
    
    if ($data['status'] === 'success') {
        return $data['data'];
    } else {
        throw new Exception($data['message']);
    }
}

// 使用示例
try {
    $response = file_get_contents('http://api.example.com/user/detail', false, $context);
    $userData = handleApiResponse($response);
    
    // 转换为数组格式
    $userArray = (array) $userData;
    
    echo "用户信息获取成功\n";
    print_r($userArray);
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
```

## 响应格式优势

1. **统一性**: 所有接口使用相同的响应格式
2. **易解析**: 前端可以统一处理响应
3. **状态明确**: 通过 status 字段明确操作结果
4. **错误处理**: 统一的错误信息格式
5. **扩展性**: 可以轻松添加新的字段
6. **数组转换**: 便于转换为数组格式处理
