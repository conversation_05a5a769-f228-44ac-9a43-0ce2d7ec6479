<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */
namespace app\evaluation\controller;
use app\login\controller\ApiAuth;
use think\Loader;

/**
 * Class Questions
 * @package app\evaluation\controller
 */
class Answer extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->Answer = new \app\evaluation\service\Answer();
    }
    /**
     * 模块：素养测评-答题管理
     * @SWG\Post(path="/evaluation/answer",
     *   tags={"素养测评-答题管理:answer"},
     *   summary="提交答案",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="paper_id",
     *     type="string",
     *     description="试卷id",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="distribution_id",
     *     type="integer",
     *     description="发卷id,学生答题需要展示分发人",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="used_time",
     *     type="integer",
     *     description="做题时间，单位是秒",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="answer_arr",
     *     type="array",
     *     description="",
     *     required=true,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-答题管理
     * @SWG\Delete(path="/evaluation/answer/{id}",
     *   tags={"素养测评-答题管理:answer"},
     *   summary="删除记录",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-答题管理
     * @SWG\Get(path="/evaluation/answer",
     *   tags={"素养测评-答题管理:answer"},
     *   summary="记录查询",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="grade_id",
     *     type="integer",
     *     description="一年级到高三对应1-12",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="class_id",
     *     type="integer",
     *     description="班级class_id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="start_time",
     *     type="datetime",
     *     description="做卷子时间左区间，格式为 'YYYY-MM-DD HH:MM:SS'",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="end_time",
     *     type="datetime",
     *     description="做卷子时间右区间",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="paper_name",
     *     type="string",
     *     description="试卷名称",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="name",
     *     type="string",
     *     description="学生或老师姓名",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="check_status",
     *     type="integer",
     *     description="阅卷状态",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="literacy_level",
     *     type="string",
     *     description="素养水平，直接传参优，良，有待提高",
     *     required=false,
     *   ),
     *   description="数据说明：check_status，0是未批阅，1是已批阅",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    public function record(){

        $data = $this->Answer->hand_out();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-答题管理
     * @SWG\Put(path="/evaluation/change_review_teacher",
     *   tags={"素养测评-答题管理:answer"},
     *   summary="修改批阅老师",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="答题记录id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="check_member_id",
     *     type="integer",
     *     description="老师的member_id，注意此处只能是单个老师的member_id",
     *     required=false,
     *   ),
     *   description="",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function change_review_teacher(){
        $data = $this->Answer->change_review_teacher();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-答题管理
     * @SWG\Put(path="/evaluation/correction_score",
     *   tags={"素养测评-答题管理:answer"},
     *   summary="核正分数",
     *   @SWG\Parameter(
     *     in="query",
     *     name="record_id",
     *     type="integer",
     *     description="做题记录id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="score",
     *     type="string",
     *     description="分数",
     *     required=false,
     *   ),
     *   description="",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function correction_score(){
        $data = $this->Answer->correction_score();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-答题管理
     * @SWG\Get(path="/evaluation/get_question_score",
     *   tags={"素养测评-答题管理:answer"},
     *   summary="获取试卷中每一题的分数",
     *   @SWG\Parameter(
     *     in="query",
     *     name="distribution_id",
     *     type="integer",
     *     description="分发id",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="paper_id",
     *     type="integer",
     *     description="试卷id",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="member_id",
     *     type="integer",
     *     description="学生member_id",
     *     required=false,
     *   ),
     *   description="",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function get_question_score(){
        $data = $this->Answer->get_question_score();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-答题管理
     * @SWG\PUT(path="/evaluation/submit_subjective",
     *   tags={"素养测评-答题管理:answer"},
     *   summary="提交主观题分数",
     *   @SWG\Parameter(
     *     in="body",
     *     name="data",
     *     description="试卷分发相关信息",
     *     required=true,
     *     @SWG\Schema(
     *       type="object",
     *       @SWG\Property(property="distribution_id", type="integer", description="发卷id,学生答题需要展示分发人"),
     *       @SWG\Property(property="paper_id", type="integer", description="试卷id,需要区分套卷中的某一张"),
     *       @SWG\Property(property="member_id", type="integer", description="学生的member_id"),
     *       @SWG\Property(property="answer_arr", type="object", description="question_id和分数的JSON数组，例如：[[1811,5],[186,7]]")
     *     )
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function submit_subjective(){
        $data = $this->Answer->submit_subjective();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-答题管理
     * @SWG\Get(path="/evaluation/log_papers",
     *   tags={"素养测评-答题管理:answer"},
     *   summary="获取做过的试卷",
     *   description="用作团体报告的搜索栏",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function log_papers(){
        $data = $this->Answer->log_papers();
        apiReturn($data);
    }

    //将temp_yuwen导入answer和log表,客观题单选题
    public function insert_answer_single(){
        $data = $this->Answer->insert_answer_single();
        apiReturn($data);
    }

    //将temp_yuwen导入answer和log表,客观题多选题
    public function insert_answer_mult(){
        $data = $this->Answer->insert_answer_mult();
        apiReturn($data);
    }

    //将temp_yuwen导入answer和log表,主观题直接录入分数
    public function insert_answer_sub(){
        $data = $this->Answer->insert_answer_sub();
        apiReturn($data);
    }

    public function insert_log(){
        $data = $this->Answer->insert_log();
        apiReturn($data);
    }

    //这一段是更新log表各维度分数
    public function update_log_competence()
    {
        $data = $this->Answer->update_log_competence();
        apiReturn($data);
    }

    //这一段是更新answer表各维度分数
    public function update_answer_competence()
    {
        $data = $this->Answer->update_answer_competence();
        apiReturn($data);
    }

    //导出语文/数学/科学每题得分和子维度得分
    public function get_question_score_with_competence()
    {
        $data = $this->Answer->get_question_score_with_competence();
        apiReturn($data);
    }

    public function get_student_exam()
    {
        $data = $this->Answer->get_student_exam();
        apiReturn($data);
    }

    public function get_student_excel()
    {
        $data = $this->Answer->get_student_excel();
        apiReturn($data);
    }

    public function get_student_excel_tenth()
    {
        $data = $this->Answer->get_student_excel_tenth();
        apiReturn($data);
    }
    
    public function get_student_excel_level_tenth()
    {
        $data = $this->Answer->get_student_excel_level_tenth();
        apiReturn($data);
    }

}