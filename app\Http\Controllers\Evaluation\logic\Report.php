<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */

namespace app\evaluation\logic;
use app\evaluation\model\Student as StudentModel;
use app\backend\logic\Backend as BackendLogic;
use think\Config;

class Report
{
    protected $studentModel;
    protected $backendLogic;
    
    public function __construct()
    {
        $this->studentModel    = new StudentModel();
        $this->backendLogic = new BackendLogic();
        Config::load(APP_PATH.'survey/config.php');
    }

    public function personal_report($member_id,$distribution_id)
    {
        $member_info = $this->studentModel->alias('stu')
            ->join('school','stu.school_id = school.id')
            ->field('stu.name,case when stu.gender = 1 then "男" when stu.gender = 2 then "女" else "weizhi" end as gender,stu.student_no,school.name as school_name')
            ->where('member_id',$member_id)
            ->find();
        $member_info = to_arr($member_info);
        //查询做生涯测评时间
        $whe['a.time_error']  = 0;
        $whe['a.is_delete']   = 0;
        $whe['a.is_abnormal'] = 0;
        $whe['a.member_id']   = $member_id;
        $whe['b.survey_type'] = 25;
        $shengya_survey = db('survey_user_session a')
            ->join('survey b','a.survey_id = b.id')
            ->field('a.id,a.create_time')
            ->where($whe)
            ->order('a.id desc')
            ->find();

        $data['info'] = $member_info;
        $data['info']['survey_time'] = $shengya_survey['create_time'];
        $shengya = db('evaluation_career')
            ->field('id,result,session_id,career_focus_avg,career_focus_rank,school_career_focus_avg,school_career_focus_rank,
            career_curiosity_avg,career_curiosity_rank,school_career_curiosity_avg,school_career_curiosity_rank,
            career_control_avg,career_control_rank,school_career_control_avg,school_career_control_rank,
            career_confidence_avg,career_confidence_rank,school_career_confidence_avg,school_career_confidence_rank')
            ->where(['member_id'=>$member_id])
            ->find();
        if(empty($shengya)) apiReturn([], '没有找到生涯测评数据！',-1);
        $shengya_arr = json_decode($shengya['result'],true);

        //根据session_id查询pdf_url
        $data['info']['pdf_url'] = db('survey_user_session')->where(['member_id'=>$member_id,'session_id'=>$shengya['session_id']])->value('pdf_url');

        //我的画像
        //画像生涯
        $data['portrait']['care'] = round($shengya_arr[0][0] * 100 / 30) ;
        $data['portrait']['curious'] = round($shengya_arr[0][1] * 100 / 30) ;
        $data['portrait']['control'] = round($shengya_arr[0][2] * 100 / 30) ;
        $data['portrait']['confident'] = round($shengya_arr[0][3] * 100 / 30) ;
        //画像兴趣
        $data['portrait']['read_interest'] = round($shengya_arr[2][4] * 100 / 60) ;
        $data['portrait']['math_interest'] = round($shengya_arr[3][4] * 100 / 60) ;
        $data['portrait']['science_interest'] = round($shengya_arr[4][4] * 100 / 60) ;
        //阅读，数学，科学三门考试
        $where_exam['member_id'] = $member_id;
        if($distribution_id) $where_exam['distribution_id'] = $distribution_id;
        $exam = db('evaluation_exam')
            ->field('read,math,science,read_avg,math_avg,science_avg,
            language,thinking,appreciation,culture,reasoning,modeling,operation,imagine,analysis,abstract,concept,thought,explore,attitude,
            language_avg,thinking_avg,appreciation_avg,culture_avg,
            reasoning_avg,modeling_avg,operation_avg,imagine_avg,analysis_avg,abstract_avg,
            concept_avg,thought_avg,explore_avg,attitude_avg')
            ->where($where_exam)
            ->find();
        //画像科目
        $data['portrait']['math_literacy'] = round($exam['math'] * 100 / 50) ;
        $data['portrait']['read_literacy'] = round($exam['read'] * 100 / 120) ;
        $data['portrait']['science_literacy'] = round($exam['science'] * 100 / 40) ;

        //生涯适应力（悬浮查看）水平描述和解读
        //雷达图显示,第三个参数暂时填百分位数字
        $data['career']['care'] = [round($shengya_arr[0][0] * 100 / 30),80,60];
        $data['career']['curious'] = [round($shengya_arr[0][1] * 100 / 30),80,60];
        $data['career']['control'] = [round($shengya_arr[0][2] * 100 / 30),80,60];
        $data['career']['confident'] = [round($shengya_arr[0][3] * 100 / 30),80,60];

        //个性化应用建议
        $input = db('evaluation_distribution_shengya_relation a')
            ->join('survey_user_answer b','a.session_id = b.session')
            ->field('b.input')
            ->where('a.distribution_id',$distribution_id)
            ->find();
        $original_answer = json_decode($input['input'],true);
        ksort($original_answer);
        $career_care = array_slice($original_answer, 0, 6, true); 
        $career_curious = array_slice($original_answer, 6, 6, true);
        $career_control = array_slice($original_answer, 12, 6, true);
        $career_confident = array_slice($original_answer, 18, 6, true);

        $data['career']['person_app_suggest']['care'] = $this->search_question($career_care);
        $data['career']['person_app_suggest']['curious'] = $this->search_question($career_curious);
        $data['career']['person_app_suggest']['control'] = $this->search_question($career_control);
        $data['career']['person_app_suggest']['confident'] = $this->search_question($career_confident);

        //(二)学科兴趣

        //你作答的学科学习兴趣评估的结果分析。
        $data['subject_interests']['result'] = [
            'base'=>[0,20,80,100],
            'read_interest'=>round($shengya_arr[2][4] * 100 / 60),
            'math_interest'=>round($shengya_arr[3][4] * 100 / 60),
            'science_interest'=>round($shengya_arr[4][4] * 100 / 60)
        ];

        //你的学科兴趣各维度水平如下：
        $data['subject_interests']['competence'] = [
            'base'=>[0,40,80,100],
            'read_interest'=>$this->transform_to_percentage(array_slice($shengya_arr[2],0,4),20/3),
            'math_interest'=>$this->transform_to_percentage(array_slice($shengya_arr[3],0,4),20/3),
            'science_interest'=>$this->transform_to_percentage(array_slice($shengya_arr[4],0,4),20/3)
        ];
        $read_emotion = $this->characteristic_or_advice('read',$shengya_arr[2][0],'emotion');
        $math_emotion = $this->characteristic_or_advice('math',$shengya_arr[3][0],'emotion');
        $science_emotion = $this->characteristic_or_advice('science',$shengya_arr[4][0],'emotion');

        $read_value = $this->characteristic_or_advice('read',$shengya_arr[2][1],'value');
        $math_value = $this->characteristic_or_advice('math',$shengya_arr[3][1],'value');
        $science_value = $this->characteristic_or_advice('science',$shengya_arr[4][1],'value');

        $read_knowledge = $this->characteristic_or_advice('read',$shengya_arr[2][2],'knowledge');
        $math_knowledge = $this->characteristic_or_advice('math',$shengya_arr[3][2],'knowledge');
        $science_knowledge = $this->characteristic_or_advice('science',$shengya_arr[4][2],'knowledge');

        $read_active = $this->characteristic_or_advice('read',$shengya_arr[2][3],'active');
        $math_active = $this->characteristic_or_advice('math',$shengya_arr[3][3],'active');
        $science_active = $this->characteristic_or_advice('science',$shengya_arr[4][3],'active');

        $read_advice = $this->characteristic_or_advice('read',$shengya_arr[2][4],'advice',[0,12,48,60]);
        $math_advice = $this->characteristic_or_advice('math',$shengya_arr[3][4],'advice',[0,12,48,60]);
        $science_advice = $this->characteristic_or_advice('science',$shengya_arr[4][4],'advice',[0,12,48,60]);
        //兴趣特征表格数据
        $data['subject_interests']['table'] = [
            'read'=>['阅读兴趣',[$read_emotion,$read_value,$read_knowledge,$read_active],$read_advice],
            'math'=>['数学兴趣',[$math_emotion,$math_value,$math_knowledge,$math_active],$math_advice],
            'science'=>['科学兴趣',[$science_emotion,$science_value,$science_knowledge,$science_active],$science_advice],
        ];

        //学科素养发展指标整体描述表格

        //常模
        $math_mode = round(30.88 * 100 / 50,2);
        $science_mode = round(23.65 * 100 / 40,2);
        $read_mode = round(64.14 * 100 / 120,2);

        //初始规则：数学[0,19,20,27,28,36,37,44,45,50]，阅读[0,50,51,61,62,70,71,82,83,120]，科学[0,18,19,23,24,26,27,32,33,40]
        //初始规则不包括小数点，所以作了修改，只保留右边的数字
        $math_level = $this->total_score_to_level($exam['math'],[19,27,36,44,50]);
        $read_level = $this->total_score_to_level($exam['read'],[50,61,70,82,120]);
        $science_level = $this->total_score_to_level($exam['science'],[18,23,26,32,40]);
        $data['subject_literacy']['table'] =
        [
            'math' => [
                round($exam['math'] * 100 / 50),
                $math_mode,
                $math_level
            ],
            'read' => [
                round($exam['read'] * 100 / 120),
                $read_mode,
                $read_level
            ],
            'science' => [
                round($exam['science'] * 100 / 40),
                $science_mode,
                $science_level
            ],
        ];

        $competence_shine = array_flip(competence_shine());//各科素养汉字与英文下标对应关系

        $reasoning_level = $this->score_to_level($exam['reasoning'],[0,3.3,3.4,4.9,5,7,7.1,8.9,9]);
        $modeling_level = $this->score_to_level($exam['modeling'],[0,4.3,4.4,6.1,6.2,8.3,8.4,10.1,10.1]);
        $operation_level = $this->score_to_level($exam['operation'],[0,2,2.1,3.3,3.4,4.7,4.8,5.4,5.5]);
        $imagine_level = $this->score_to_level($exam['imagine'],[0,3.5,3.6,5.3,5.4,7.7,7.8,9.7,9.8]);
        $analysis_level = $this->score_to_level($exam['analysis'],[0,0.8,0.9,2,2.1,2.6,2.7,2.9,3.0]);
        $abstract_level = $this->score_to_level($exam['abstract'],[0,3.3,3.4,5,5.1,6.8,6.9,8.4,8.5]);
        $data['competence']['math'] =[
            ['数学推理',$exam['reasoning'],$exam['reasoning_avg'],$reasoning_level],
            ['数学建模',$exam['modeling'],$exam['modeling_avg'],$modeling_level],
            ['数学运算',$exam['operation'],$exam['operation_avg'],$operation_level],
            ['直观想象',$exam['imagine'],$exam['imagine_avg'],$imagine_level],
            ['数据分析',$exam['analysis'],$exam['analysis_avg'],$analysis_level],
            ['数学抽象',$exam['abstract'],$exam['abstract_avg'],$abstract_level],
        ];
        $math_level_arr = [
            'reasoning'=>$reasoning_level,
            'modeling'=>$modeling_level,
            'operation'=>$operation_level,
            'imagine'=>$imagine_level,
            'analysis'=>$analysis_level,
            'abstract'=>$abstract_level,
        ];
        $math_summary = $this->explain_and_advise('math',$math_level_arr,5,$competence_shine);

        $data['competence']['math_summary'] = [
            'high'=>$math_summary[0],
            'low'=>$math_summary[1],
            'high_word'=>$math_summary[2],
            'low_word'=>$math_summary[3],
        ];

        $language_level = $this->score_to_level($exam['language'],[0,18.4,18.5,22.3,22.4,25.8,25.9,30.2,30.3]);
        $thinking_level = $this->score_to_level($exam['thinking'],[0,12.1,12.2,14.6,14.7,17.2,17.3,20.9,21]);
        $appreciation_level = $this->score_to_level($exam['appreciation'],[0,5.7,5.8,7.8,7.9,9.4,9.5,11.8,11.9]);
        $culture_level = $this->score_to_level($exam['culture'],[0,13.2,13.3,16.1,16.2,18.3,18.4,21.2,21.3]);
        $data['competence']['read'] =[
            ['语言建构与运用',$exam['language'],$exam['language_avg'],$language_level],
            ['思维发展与提升',$exam['thinking'],$exam['thinking_avg'],$thinking_level],
            ['审美鉴赏与创造',$exam['appreciation'],$exam['appreciation_avg'],$appreciation_level],
            ['文化传承与理解',$exam['culture'],$exam['culture_avg'],$culture_level],
        ];
        $read_level_arr = [
            'language'=>$language_level,
            'thinking'=>$thinking_level,
            'appreciation'=>$appreciation_level,
            'culture'=>$culture_level,
        ];
        $read_summary = $this->explain_and_advise('read',$read_level_arr,3,$competence_shine);

        $data['competence']['read_summary'] = [
            'high'=>$read_summary[0],
            'low'=>$read_summary[1],
            'high_word'=>$read_summary[2],
            'low_word'=>$read_summary[3],
        ];


        $concept_level = $this->score_to_level($exam['concept'],[0,3.5,3.6,4.5,4.6,5.4,5.5,6.5,6.6]);
        $thought_level = $this->score_to_level($exam['thought'],[0,9,9.1,11.2,11.3,12.8,12.9,15.8,15.9]);
        $explore_level = $this->score_to_level($exam['explore'],[0,3.7,3.8,5,5.1,6.1,6.2,7.5,7.6]);
        $attitude_level = $this->score_to_level($exam['attitude'],[0,1.2,1.3,2.1,2.2,2.7,2.8,3.6,3.7]);
        $data['competence']['science'] =[
            ['科学观念',$exam['concept'],$exam['concept_avg'],$concept_level],
            ['科学思维',$exam['thought'],$exam['thought_avg'],$thought_level],
            ['探究实践',$exam['explore'],$exam['explore_avg'],$explore_level],
            ['态度责任',$exam['attitude'],$exam['attitude_avg'],$attitude_level],
        ];
        $science_level_arr = [
            'concept'=>$concept_level,
            'thought'=>$thought_level,
            'explore'=>$explore_level,
            'attitude'=>$attitude_level,
        ];
        $science_summary = $this->explain_and_advise('science',$science_level_arr,3,$competence_shine);

        $data['competence']['science_summary'] = [
            'high'=>$science_summary[0],
            'low'=>$science_summary[1],
            'high_word'=>$science_summary[2],
            'low_word'=>$science_summary[3],
        ];

        //三、总结和建议
        //兴趣排名
        $interests_rank = [
            ['name'=>'数学','alias'=>'math','score'=>$shengya_arr[3][4]],
            ['name'=>'阅读','alias'=>'read','score'=>$shengya_arr[2][4]],
            ['name'=>'科学','alias'=>'science','score'=>$shengya_arr[4][4]],
        ];
        usort($interests_rank, function($a, $b) {
            return $b['score'] <=> $a['score'];
        });
        $data['summarize']['interests']['high'] = $interests_rank[0]['name'];
        $data['summarize']['interests']['low']  = $interests_rank[2]['name'];
        $literacy_rank = [
            ['name'=>'数学','alias'=>'math','level'=>$math_level],
            ['name'=>'阅读','alias'=>'read','level'=>$read_level],
            ['name'=>'科学','alias'=>'science','level'=>$science_level],
        ];
        usort($literacy_rank, function($a, $b) {
            return $b['level'] <=> $a['level'];
        });
        $interests_high_level = $this->find_competence_high_and_low($interests_rank[0]['name'],$math_summary,$read_summary,$science_summary);

        //星级最高（并列最高），大于等于3
        switch ($interests_rank[0]['name']){
            case ($interests_rank[0]['name'] == $literacy_rank[0]['name'] && $literacy_rank[0]['level'] >= 3) ||
                ($interests_rank[0]['name'] == $literacy_rank[1]['name'] && $literacy_rank[0]['level'] == $literacy_rank[1]['level']  && $literacy_rank[1]['level'] >= 3) ||
                ($interests_rank[0]['name'] == $literacy_rank[2]['name'] && $literacy_rank[0]['level'] == $literacy_rank[2]['level']  && $literacy_rank[2]['level'] >= 3)
                ://高兴趣高素养
                $high_interests = '您对'.$interests_rank[0]['name'].'比较感兴趣，';
                if($interests_high_level['competence']['high']) $high_interests .= implode('，',$interests_high_level['competence']['high']).'素养是您在学习本学科的优势，';
                if($interests_high_level['competence']['low']) $high_interests .= implode('，',$interests_high_level['competence']['low']).'素养是您的不足，';
                $high_interests .= '请您详细阅读学科素养测评的改进建议，让您在'.$interests_rank[0]['name'].'学习上更进一步。';
                break;

            default://高兴趣低素养
                $high_interests_low_literacy = Config::get('high_interests_low_literacy');
                $high_interests = $high_interests_low_literacy[$interests_rank[0]['alias']];
                if($interests_high_level['competence']['low']) $high_interests .= '逐步提高'.implode('，',$interests_high_level['competence']['low']).'等薄弱的素养指标。';
        }

        switch ($interests_rank[2]['name']){
            case ($interests_rank[2]['name'] == $literacy_rank[0]['name'] && $literacy_rank[0]['level'] >= 3) ||
                ($interests_rank[2]['name'] == $literacy_rank[1]['name'] && $literacy_rank[0]['level'] == $literacy_rank[1]['level']  && $literacy_rank[1]['level'] >= 3) ||
                ($interests_rank[2]['name'] == $literacy_rank[2]['name'] && $literacy_rank[0]['level'] == $literacy_rank[2]['level']  && $literacy_rank[2]['level'] >= 3)
            ://低兴趣高素养
                $low_interests_high_literacy = Config::get('low_interests_high_literacy');
                $low_interests = $low_interests_high_literacy[$interests_rank[2]['alias']];
                break;

            default://低兴趣低素养
                $low_interests_low_literacy = Config::get('low_interests_low_literacy');
                $low_interests = $low_interests_low_literacy[$interests_rank[2]['alias']];
                break;
        }

        $data['summarize']['literacy']['word'] = $high_interests.$low_interests;

        $school_id   = db('member')->where('id', $member_id)->value('school_id');
        $data['logo'] = $this->backendLogic->logo_school($school_id);
        return $data;
    }

    public function find_competence_high_and_low($name,$math_summary,$read_summary,$science_summary)
    {
        $subject_level = [];
        switch ($name){
            case '数学':
                $subject_level = [
                    'name'=>'数学',
                    'competence'=>[
                        'high'=>$math_summary[0],
                        'low'=>$math_summary[1],
                    ],
                ];
                break;
            case '阅读':
                $subject_level = [
                    'name'=>'阅读',
                    'competence'=>[
                        'high'=>$read_summary[0],
                        'low'=>$read_summary[1],
                    ],
                ];
                break;
            case '科学':
                $subject_level = [
                    'name'=>'科学',
                    'competence'=>[
                        'high'=>$science_summary[0],
                        'low'=>$science_summary[1],
                    ],
                ];
                break;
        }
        return $subject_level;
    }

    public function transform_to_percentage($arr,$rate)
    {
        $data = [];
        foreach ($arr as $value){
            $data[] = round($value * $rate);
        }
        return $data;
    }

    public function search_question($arr)
    {
        $question_id_arr = [];
        foreach ($arr as $key => $value){
            if($value['select_num'] < 5) $question_id_arr[] = $key;
        }

        if(empty($question_id_arr)) return [];
        $question_name = db('survey_question')->where('id','in',$question_id_arr)->column('name');
        return $question_name;
    }

    public function explain_and_advise($subject = 'math',$subject_level_arr,$num = 5,$competence_shine){
        $subject_setting = Config::get($subject);
        arsort($subject_level_arr);//根据value排序
        $math_rank = array_values($subject_level_arr);//第0个是排名最高的，第5个是排名最低的
        $youshi_text=$lieshi_text=$youshi_word=$lieshi_word=[];
        foreach ($subject_level_arr as $key => $value){
            if($value == $math_rank[0] && $value >= 3){
                $youshi[] = $key;//只要和第0个相等且大于等级3的都是优势素养
                $youshi_text[] = $competence_shine[$key];
                $subject_setting[$key]['high']['advice'] = array_slice($subject_setting[$key]['high']['advice'],0,4);//只显示前四个，数组长度为3并不会报错
                $youshi_word[] = $subject_setting[$key]['high'];
            }elseif($value < 3){
                $lieshi[] = $key;//只要和第5个相等且小于等级3的都是劣势素养
                $lieshi_text[] = $competence_shine[$key];
                $subject_setting[$key]['low']['advice'] = array_slice($subject_setting[$key]['low']['advice'],0,4);
                $lieshi_word[] = $subject_setting[$key]['low'];
            }
        }
        return [$youshi_text,$lieshi_text,$youshi_word,$lieshi_word];
    }

    public function total_score_to_level($score,$norm){
        if($score < $norm[0]){
            $level = 1;
        }elseif($score >= $norm[0] && $score < $norm[1]){
            $level = 2;
        }elseif($score >= $norm[1] && $score < $norm[2]){
            $level = 3;
        }elseif($score >= $norm[2] && $score < $norm[3]){
            $level = 4;
        }elseif($score >= $norm[3]){
            $level = 5;
        }
        return $level;
    }

    public function score_to_level($score,$norm){
        if($score <= $norm[1]){
            $level = 1;
        }elseif($score >= $norm[2] && $score <= $norm[3]){
            $level = 2;
        }elseif($score >= $norm[4] && $score <= $norm[5]){
            $level = 3;
        }elseif($score >= $norm[6] && $score <= $norm[7]){
            $level = 4;
        }elseif($score >= $norm[8]){
            $level = 5;
        }
        return $level;
    }

    public function settlement()
    {
        $where_school['log.status'] = 0;
        $school_id = input('school_id');
        $distribution_id = input('distribution_id');//根据学校来算平均分等值
        $paper_id = input('paper_id');
        if($school_id) $where_school['log.school_id'] = $school_id;
        if($paper_id) $where_school['log.paper_id'] = $paper_id;
        if($distribution_id) $where_school['log.distribution_id'] = ['in',$distribution_id];//根据学校来算平均分等值
        $school_log = db('evaluation_student_log')->alias('log')
            ->join('evaluation_papers papers','log.paper_id = papers.id')
            ->field('papers.course_id,papers.id,papers.paper_name,log.id as log_id,log.member_id,log.distribution_id,log.score
            ,log.language,log.thinking,log.appreciation,log.culture
            ,log.reasoning,log.modeling,log.operation,log.imagine,log.analysis,log.abstract
            ,log.concept,log.thought,log.explore,log.attitude
            ')
            ->where($where_school)
            ->group('log.member_id,papers.id')
            ->order('log.score desc')
            ->select();
        $school_log = to_arr($school_log);
        $distribution_id_by_member_id = array_column($school_log,'distribution_id','member_id');

        $i = $j = $k = 0;

        foreach ($school_log as $key => $value){
            switch ($value['course_id']){
                case 1:
                    $read[$i] = [
                        'course_id'=>$value['course_id'],
                        'id'=>$value['id'],
                        'paper_name'=>$value['paper_name'],
                        'log_id'=>$value['log_id'],
                        'member_id'=>$value['member_id'],
                        'read'=>$value['score'],
                        'language'=>$value['language'],
                        'thinking'=>$value['thinking'],
                        'appreciation'=>$value['appreciation'],
                        'culture'=>$value['culture'],
                    ];
                    $i++;
                    break;
                case 2:
                    $math[$j] = [
                        'course_id'=>$value['course_id'],
                        'id'=>$value['id'],
                        'paper_name'=>$value['paper_name'],
                        'log_id'=>$value['log_id'],
                        'member_id'=>$value['member_id'],
                        'math'=>$value['score'],
                        'reasoning'=>$value['reasoning'],
                        'modeling'=>$value['modeling'],
                        'operation'=>$value['operation'],
                        'imagine'=>$value['imagine'],
                        'analysis'=>$value['analysis'],
                        'abstract'=>$value['abstract'],
                    ];
                    $j++;
                    break;
                case 3:
                    $science[$k] = [
                        'course_id'=>$value['course_id'],
                        'id'=>$value['id'],
                        'paper_name'=>$value['paper_name'],
                        'log_id'=>$value['log_id'],
                        'member_id'=>$value['member_id'],
                        'science'=>$value['score'],
                        'concept'=>$value['concept'],
                        'thought'=>$value['thought'],
                        'explore'=>$value['explore'],
                        'attitude'=>$value['attitude'],
                    ];
                    $k++;
                    break;
            }
        }

        $read_num = count($read);
        $math_num = count($math);
        $science_num = count($science);
        $read = array_column($read,null,'member_id');
        $math = array_column($math,null,'member_id');
        $science = array_column($science,null,'member_id');
        $member_id_arr = array_column($read,'member_id');

        $read_avg = $language_avg = $thinking_avg = $appreciation_avg = $culture_avg = 0;
        $math_avg = $reasoning_avg = $modeling_avg = $operation_avg = $imagine_avg = $analysis_avg = $abstract_avg = 0;
        $science_avg = $concept_avg = $thought_avg = $explore_avg = $attitude_avg = 0;
        foreach ($read as $vr){
            $read_avg += $vr['read'];
            $language_avg += $vr['language'];
            $thinking_avg += $vr['thinking'];
            $appreciation_avg += $vr['appreciation'];
            $culture_avg += $vr['culture'];
        }
        $read_avg = round($read_avg/$read_num,2);
        $language_avg = round($language_avg/$read_num,2);
        $thinking_avg = round($thinking_avg/$read_num,2);
        $appreciation_avg = round($appreciation_avg/$read_num,2);
        $culture_avg = round($culture_avg/$read_num,2);

        foreach ($math as $vm){
            $math_avg += $vm['math'];
            $reasoning_avg += $vm['reasoning'];
            $modeling_avg += $vm['modeling'];
            $operation_avg += $vm['operation'];
            $imagine_avg += $vm['imagine'];
            $analysis_avg += $vm['analysis'];
            $abstract_avg += $vm['abstract'];
        }
        $math_avg = round($math_avg/$math_num,2);
        $reasoning_avg = round($reasoning_avg/$math_num,2);
        $modeling_avg = round($modeling_avg/$math_num,2);
        $operation_avg = round($operation_avg/$math_num,2);
        $imagine_avg = round($imagine_avg/$math_num,2);
        $analysis_avg = round($analysis_avg/$math_num,2);
        $abstract_avg = round($abstract_avg/$math_num,2);


        foreach ($science as $vs){
            $science_avg += $vs['science'];
            $concept_avg += $vs['concept'];
            $thought_avg += $vs['thought'];
            $explore_avg += $vs['explore'];
            $attitude_avg += $vs['attitude'];
        }
        $science_avg = round($science_avg/$science_num,2);
        $concept_avg = round($concept_avg/$science_num,2);
        $thought_avg = round($thought_avg/$science_num,2);
        $explore_avg = round($explore_avg/$science_num,2);
        $attitude_avg = round($attitude_avg/$science_num,2);

        //查所有的学生
        $student_list = db('student')->field('member_id')->where(['school_id'=>$school_id,'step'=>0,'member_id'=>['in',$member_id_arr]])->select();
        foreach ($student_list as $v){
            $ins[] = [
                'member_id'=>$v['member_id'],
                'school_id'=>$school_id,
                'distribution_id'=>$distribution_id_by_member_id[$v['member_id']] ?? null,//根据学校来算平均分等值

                //阅读字段
                'read'         => $read[$v['member_id']]['read'] ?? 0,
                'language'     => $read[$v['member_id']]['language'] ?? 0,
                'thinking'     => $read[$v['member_id']]['thinking'] ?? 0,
                'appreciation' => $read[$v['member_id']]['appreciation'] ?? 0,
                'culture'      => $read[$v['member_id']]['culture'] ?? 0,

                'read_avg'         => $read_avg ?? 0,
                'language_avg'     => $language_avg ?? 0,
                'thinking_avg'     => $thinking_avg ?? 0,
                'appreciation_avg' => $appreciation_avg ?? 0,
                'culture_avg'      => $culture_avg ?? 0,

                //数学字段
                'math'      => $math[$v['member_id']]['math'] ?? 0,
                'reasoning' => $math[$v['member_id']]['reasoning'] ?? 0,
                'modeling'  => $math[$v['member_id']]['modeling'] ?? 0,
                'operation' => $math[$v['member_id']]['operation'] ?? 0,
                'imagine'   => $math[$v['member_id']]['imagine'] ?? 0,
                'analysis'  => $math[$v['member_id']]['analysis'] ?? 0,
                'abstract'  => $math[$v['member_id']]['abstract'] ?? 0,
                
                'math_avg'      => $math_avg ?? 0,
                'reasoning_avg' => $reasoning_avg ?? 0,
                'modeling_avg'  => $modeling_avg ?? 0,
                'operation_avg' => $operation_avg ?? 0,
                'imagine_avg'   => $imagine_avg ?? 0,
                'analysis_avg'  => $analysis_avg ?? 0,
                'abstract_avg'  => $abstract_avg ?? 0,

                //科学字段
                'science'  => $science[$v['member_id']]['science'] ?? 0,
                'concept'  => $science[$v['member_id']]['concept'] ?? 0,
                'thought'  => $science[$v['member_id']]['thought'] ?? 0,
                'explore'  => $science[$v['member_id']]['explore'] ?? 0,
                'attitude' => $science[$v['member_id']]['attitude'] ?? 0,
                
                'science_avg'  => $science_avg ?? 0,
                'concept_avg'  => $concept_avg ?? 0,
                'thought_avg'  => $thought_avg ?? 0,
                'explore_avg'  => $explore_avg ?? 0,
                'attitude_avg' => $attitude_avg ?? 0,

            ];
        }

        $res = db('evaluation_exam')->insertAll($ins);
        apiReturn($res);

    }

    //历史所有数据跑出来的平均分
    public function all_settlement()
    {
        $where_all['status'] = 0;
        $school_id = input('school_id');
//        $paper_id = input('paper_id');
        if($school_id) $where_all['school_id'] = $school_id;
//        if($paper_id) $where_all['paper_id'] = $paper_id;
        $log = db('evaluation_exam')
            ->field('id,member_id,read,language,thinking,appreciation,culture
            ,math,reasoning,modeling,operation,imagine,analysis,abstract
            ,science,concept,thought,explore,attitude
            ')
            ->where($where_all)
            ->group('member_id')
            ->select();

        foreach ($log as $k => $v){
            $ins[] = [
                'id'=>$v['id'],
                'member_id'=>$v['member_id'],
                
                //平均分
                'read_avg'         => $v['avg']['read'],
                'language_avg'     => $v['avg']['language'],
                'thinking_avg'     => $v['avg']['thinking'],
                'appreciation_avg' => $v['avg']['appreciation'],
                'culture_avg'      => $v['avg']['culture'],

                'math_avg'      => $v['avg']['math'] ?? 0,
                'reasoning_avg' => $v['avg']['reasoning'] ?? 0,
                'modeling_avg'  => $v['avg']['modeling'] ?? 0,
                'operation_avg' => $v['avg']['operation'] ?? 0,
                'imagine_avg'   => $v['avg']['operation'] ?? 0,
                'analysis_avg'  => $v['avg']['operation'] ?? 0,
                'abstract_avg'  => $v['avg']['operation'] ?? 0,

                'science_avg'  => $v['avg']['science'] ?? 0,
                'concept_avg'  => $v['avg']['science'] ?? 0,
                'thought_avg'  => $v['avg']['science'] ?? 0,
                'explore_avg'  => $v['avg']['science'] ?? 0,
                'attitude_avg' => $v['avg']['science'] ?? 0,

            ];
        }

        $user = new \app\evaluation\model\EvaluationExam();
        $res = $user->saveAll($ins);
        apiReturn($res);

    }

    public function interest_level($rank){
        if($rank <= 12){
            $level = '低水平';
        }elseif($rank > 12 && $rank <= 48){
            $level = '中水平';
        }elseif($rank > 48){
            $level = '高水平';
        }
        return $level;
    }

    public function interest_competence_level($rank){
        if($rank <= 6){
            $level = 'low';
        }elseif($rank > 6 && $rank <= 12){
            $level = 'middle';
        }elseif($rank > 12 && $rank <= 15){
            $level = 'high';
        }
        return $level;
    }

    public function characteristic_or_advice($subject,$score,$term,$norm = [0,6,12,15])
    {
        $interest = Config::get('subject_interest');
        if($score < $norm[1]){
            $back = $interest[$subject][$term]['low'];
        }elseif($score >= $norm[1] && $score < $norm[2]){
            $back = $interest[$subject][$term]['middle'];
        }elseif($score >= $norm[2] && $score <= $norm[3]){
            $back = $interest[$subject][$term]['high'];
        }
        return $back;
    }
}