<?php

namespace App\Services\School\Xuezhi;

use App\Http\Requests\School\Xuezhi\CollegeRequest;
use App\Models\Gk\College;
use App\Models\Gk\CollegeTag;
use App\Models\Gk\CollegeZhangCheng;
use App\Models\Gk\MajorBK;
use App\Models\Gk\MajorSubjectBK;
use App\Models\Gk\MajorZK;
use App\Models\Gk\CollegeRank;
use App\Models\School\Xuezhi\CollegeRankARWU;
use App\Models\School\Xuezhi\CollegeRankQS;
use App\Services\BaseService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use App\Http\Resources\CollegeRankCollection;
use App\Repositories\CollegeRepository;

class CollegeService extends BaseService
{
    // 指定连接
    protected $connection = 'sqlsrv_gk';

    /**
     * 注入CollegeRepository
     */
        // 构造函数注入 SchoolService
    public function __construct(protected CollegeRepository $collegeRepository)
    {
    }

    public function listBuilder(CollegeRequest $request): Builder
    {
        $provinceId = $request['province_id'];
        $collegeName = $request['college_name'];
        $collegeTags = $request['college_tags'];
        $collegeType = $request['yxtype'];
        $yxjbz = $request['yxjbz'];
        $levels = $request['levels'];
        // 新增排序参数
        $sortBy = $request['sort_by'] ?? 'rank'; // 默认按综合排名排序

        $query = College::query()
            ->with(['tagsFiltered', 'province', 'city','ranks'])
            ->leftJoin('CollegeRank', function ($join) use ($collegeType) {
                $join->on('College.Id', '=', 'CollegeRank.CollegeId')
                    ->where('CollegeRank.Year', 2025);
                if (is_array($collegeType) && !empty($collegeType)) {
                    //如果collegeType包含工科，则转为理工
                    if (in_array('工科', $collegeType)) {
                        $modifiedTypes = array_diff($collegeType, ['工科']);
                        $modifiedTypes = array_merge($modifiedTypes, ['理工']);
                        $join->whereIn('CollegeRank.Type', $modifiedTypes);
                    }else if (in_array('医药', $collegeType)) {
                        $collegeType = array_merge($collegeType, ['中医']);
                        $join->whereIn('CollegeRank.Type', $collegeType);
                    }else{
                        $join->whereIn('CollegeRank.Type', $collegeType);
                    }
                } else {
                    $join->where('CollegeRank.Type', '全国');
                }
            })
            ->where('state', 1)
            ->when(!empty($provinceId), fn($query) => $query->whereIn('ProvinceID', $provinceId))
            ->when(!empty($collegeType), callback: function($query) use ($collegeType) {
                // 如果collegeType包含农林，则查询农业和林业
                if (is_array($collegeType) && in_array('农林', $collegeType)) {
                    $modifiedTypes = array_diff($collegeType, ['农林']);
                    $modifiedTypes = array_merge($modifiedTypes, ['农业', '林业']);
                    return $query->whereIn('Yxtype', $modifiedTypes);
                }
                return $query->whereIn('Yxtype', $collegeType);
            })
            ->when(!empty($yxjbz), fn($query) => $query->whereIn('Yxjbz', $yxjbz))
            ->when(!empty($levels), fn($query) => $query->whereIn('Levels', $levels))
            ->when($collegeName, fn($query) => $query->where('College.CollegeName', 'like', "%{$collegeName}%"));

        $query->when($collegeTags, function ($query) use ($collegeTags) {
            return $query->whereHas('tagsFiltered', function ($q) use ($collegeTags) {
                return is_array($collegeTags)
                    ? $q->whereIn('Type', $collegeTags)
                    : $q->where('Type', $collegeTags);
            });
        });

           // 根据排序参数设置排序方式
        if ($sortBy === 'rank') {
            // $query->orderByRaw($sortOrder === 'asc' 
            //     ? 'COALESCE(CollegeRank.Rank, 9999) ASC' 
            //     : 'COALESCE(CollegeRank.Rank, 9999) DESC');
            $query->orderByRaw( 'COALESCE(CollegeRank.Rank, 9999) ASC' );
        } else if ($sortBy === 'hot') {
            $query->orderBy('College.ClickRate', 'DESC');
        }

        return $query->select([
                'College.*',
                'CollegeRank.Rank'
            ]);
            
    }

    /**
     * show
     */
    public function show($id)
    {
        $college = $this->findCollege($id);

        if (!$college) {
            return null;
        }

        $this->processMajorAiData($college);
        $this->processRankData($college);

        return $college;
    }


    /**
     * getZhangCheng
     */
    public function zhangChengList($college_id)
    {
        $results = DB::connection('sqlsrv_gk')->select("
            SELECT * FROM (
                SELECT
                    ID, CollegeName, ZhangchengTitle, Year, Type, Remark,
                    ROW_NUMBER() OVER (PARTITION BY Type ORDER BY Year DESC) as rn
                FROM CollegeZhangCheng
                WHERE CollegeID = :collegeId
                AND Type IN (1, 3, 4, 5)
            ) tt
            WHERE rn = 1 AND Year >= YEAR(GETDATE()) - 3
            ORDER BY Year DESC, Type",
            [
            'collegeId' => $college_id,
            ]
        );

        return $results;
    }

    /**
     * zhangChengDetail
     */
    public function zhangChengDetail($id)
    {
        $collegeZhangCheng = CollegeZhangCheng::query()
            ->where('ID', $id)
            ->select('ID as id', 'ZhangChengTitle as zhangcheng_title', 'Zhangcheng as zhangcheng')
            ->first();

        return $collegeZhangCheng;
    }


    /**
     * employment
     */
    public function employment($id)
    {

        $distributions = $this->collegeRepository->distributions($id);
        $salaries = $this->collegeRepository->salaries($id);
        
        // 组织返回数据
        // 如果post字段存在且为字符串，则尝试将其解析为JSON数组
        if (isset($distributions['post'])) {
            // 尝试解析JSON字符串
            $decodedPost = json_decode($distributions['post'], true);
            $result['post'] = $decodedPost;
        } else {
            $result['post'] = $distributions['post'];
        }
        
        // 如果work字段存在且为字符串，则尝试将其解析为JSON数组
        if (isset($distributions['work'])) {
            // 尝试解析JSON字符串
            $decodedWork = json_decode($distributions['work'], true);
            $result['work'] = $decodedWork;
        } else {
            $result['work'] = $distributions['work'];
        }
        
        $result['salaries'] = $salaries;
        
        return $result;
        
    }


    /**
     * rank
     * 院校排名
     * @param $type 1:双一流名单 2:QS世界大学排名 3:全国学科评估 4:985大学名单 5:211大学名单 6世界大学学术榜
     */
    private const RANK_TYPE = [
        'tag' => 1,
        'QS' => 2,
        'SUBJECT' => 3,
        'ARWU' => 6,
        'RK' => 7,
    ];

    /**
     * 获取特定类型的学校标签数据
     */
    private function getCollegesByTag(string $tagType): \Illuminate\Database\Eloquent\Collection
    {
        return CollegeTag::with(['college:ID,ProvinceID,CityID', 'college.province', 'college.city'])
            ->where('Type', $tagType)
            ->select('CollegeId', 'CollegeName')
            ->orderBy('CollegeId', 'ASC')
            ->get();
    }

    /**
     * 获取院校排名
     */
    public function rank(int $type, ?string $rankType = null): mixed
    {
        return match ($type) {
            self::RANK_TYPE['tag'] => CollegeRankCollection::collection($this->getCollegesByTag($rankType)),
            self::RANK_TYPE['QS'] => CollegeRankQS::getSortedData(),
            self::RANK_TYPE['SUBJECT'] => $this->getSubjectRanking(),
            self::RANK_TYPE['ARWU'] => CollegeRankARWU::getSortedData(),
            self::RANK_TYPE['RK'] => $this->getSpecificRanking($rankType),
            default => throw new \InvalidArgumentException('Invalid rank type'),
        };
    }

     /**
     * 获取特定类型的院校排名
     * @param string|null $rankType 排名类型（如艺术、医药等）
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getSpecificRanking(?string $rankType): \Illuminate\Database\Eloquent\Collection
    {
        // 使用 CollegeRank 模型查询 2024 年特定类型的排名数据
        return CollegeRank::where('Year', 2025)
            ->where('type', $rankType)
            ->whereHas('college', function ($query) {
                $query->where('state', 1);
            })
            ->select(['CollegeId as college_id', 'Rank as rank', 'Year as year', 'Type as type',"CollegeName as college_name"])
            ->orderBy('rank')
            ->get();
    }

    /**
     * 获取学科排名数据
     */
    private function getSubjectRanking(): array
    {
        $subjects = MajorSubjectBK::where('SubjectName', '!=', '其他')->get();
        $result = [];

        foreach ($subjects as $subject) {
            $result[] = [
                'subject_code' => $subject->SubjectCode,
                'subject_name' => $subject->SubjectName,
                'category' => $this->processSubjectCategory($subject),
            ];
        }

        return $result;
    }

    /**
     * 处理学科分类数据
     */
    private function processSubjectCategory(MajorSubjectBK $subject): array
    {
        $category = [];
        $assesses = $subject->majorAssesses();

        foreach ($assesses as $assess) {
            $rank = $assess->Rank;
            $collegeInfo = [
                'college_id' => $assess->CollegeId,
                'college_name' => $assess->CollegeName,
            ];

            // 按 MajorCode 分组
            if (!isset($category[$assess->MajorCode])) {
                $category[$assess->MajorCode] = [
                    'major_code' => $assess->MajorCode,
                    'major' => $assess->Major,
                    'colleges' => [],
                ];
            }

            // 按 Rank 分组
            if (!isset($category[$assess->MajorCode]['colleges'][$rank])) {
                $category[$assess->MajorCode]['colleges'][$rank] = [];
            }

            $category[$assess->MajorCode]['colleges'][$rank][] = $collegeInfo;
        }
        // 调整 colleges 的数据结构
        foreach ($category as &$item) {
            $colleges = [];
            foreach ($item['colleges'] as $level => $list) {
                $colleges[] = [
                    'level' => $level,
                    'list' => $list,
                ];
            }
            $item['colleges'] = $colleges;
        }
        return array_values($category);
    }

    /**
     * 获取院校详情
     * @param mixed $id
     * @return Builder<College>|object|\Illuminate\Database\Eloquent\Model|null
     */
    private function findCollege($id)
    {
        return College::query()
            ->with([
                'tags', 
                'detail', 
                'teacher', 
                'majorAssess', 
                'province', 
                'city',
                'ranks'
            ])
            ->where('state', 1)
            ->where('Id', $id)
            ->first();
    }

    /**
     * 获取MajorAi数据
     * @param mixed $college
     * @return void
     */
    private function processMajorAiData($college)
    {
        if (!$college->teacher || !$college->teacher->MajorAi) {
            return;
        }

        $majorNames = array_map('trim', explode(',', $college->teacher->MajorAi));
        $processedMajors = collect($majorNames)->map(function ($majorName) {
            $cleanMajorName = trim(preg_replace('/\([^)]*\)/', '', $majorName));
            
            $majorCode = $this->findMajorCode($cleanMajorName);
            
            return [
                'major_name' => $majorName,
                'major_code' => $majorCode
            ];
        })->all();

        $college->teacher->MajorAi = $processedMajors;
    }

    /**
     * Summary of findMajorCode
     * @param mixed $majorName
     */
    private function findMajorCode($majorName)
    {
        $majorBK = MajorBK::where('MajorName', $majorName)->first();
        if ($majorBK) {
            return $majorBK->MajorCode;
        }

        $majorZK = MajorZK::where('MajorName', $majorName)->first();
        return $majorZK ? $majorZK->MajorCode : null;
    }

    /**
     * 获取QS排名数据
     * @param mixed $college
     * @return void
     */
    private function processRankData($college)
    {
        $ranks = $college->ranks->toArray();

        $qsRank = $this->getQSRank($college->ID);
        $arwuRank = $this->getARWURank($college->ID);

        if ($qsRank) {
            $ranks[] = $this->formatRankData('QS', $qsRank);
        }

        if ($arwuRank) {
            $ranks[] = $this->formatRankData('ARWU', $arwuRank);
        }

        $college->setRelation('ranks', collect($ranks));
    }

    private function getQSRank($collegeId)
    {
        return CollegeRankQS::where('college_id', $collegeId)->first();
    }

    private function getARWURank($collegeId)
    {
        return CollegeRankARWU::where('college_id', $collegeId)->first();
    }

    private function formatRankData($type, $rankData)
    {
        return [
            'Type' => $type,
            'Year' => $rankData->year,
            'Rank' => $rankData->rank,
        ];
    }


}
