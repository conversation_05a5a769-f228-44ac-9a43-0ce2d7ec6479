<?php

namespace App\Services\School\Assessment\CompositeReport;

use Illuminate\Container\Container;

class CompositeServiceFactory
{
    private const ASSESSMENT_MAPPING = [
        //生涯
        1 => 'Career\AdaptationService',
        2 => 'Career\IntelligenceService',
        3 => 'Career\PersonalityService',
        4 => 'Career\InterestService',
        5 => 'Career\DevelopmentService',
        6 => 'Career\IntelligenceService',
        7 => 'Career\DevelopmentService',
        8 => 'Career\InterestService',
        //创新
        14 => 'Competency\CreativeThinkingService',
        15 => 'Competency\CriticalThinkingService',
        16 => 'Competency\CommunicationCollaborationService',
        17 => 'Competency\ProblemSolvingService',
        18 => 'Competency\LearningAbilityService',
        19 => 'Competency\ProblemSolvingService',
        20 => 'Competency\LearningAbilityService',
    ];

    public static function create(int $assessmentId)
    {
        if (!isset(self::ASSESSMENT_MAPPING[$assessmentId])) {
            throw new \Exception('无效的测评类型');
        }

        $serviceClass = "App\\Services\\School\\Assessment\\IndividualReport\\" .
            self::ASSESSMENT_MAPPING[$assessmentId];

        if (!class_exists($serviceClass)) {
            throw new \Exception('测评服务不存在');
        }

        return Container::getInstance()->make($serviceClass);
    }
}