<?php

namespace App\Events\DataSync;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 教务人员创建事件
 */
class AdminCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $adminData;

    /**
     * Create a new event instance.
     */
    public function __construct(array $adminData)
    {
        $this->adminData = $adminData;
    }
}
