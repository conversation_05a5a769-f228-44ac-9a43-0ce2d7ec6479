<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessClassNames extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'class:process-names {--school_id= : 指定学校ID进行处理}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '处理classes_old表中的班级名称，标准化班级名称格式';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $schoolId = $this->option('school_id');
        
        $this->info('开始处理班级名称...');
        
        try {
            // 开启事务
            DB::beginTransaction();
            
            // 执行所有的SQL处理步骤
            $this->processChineseParentheses($schoolId);
            $this->processNumbersInParentheses($schoolId);
            $this->processPureNumbers($schoolId);
            $this->processGradeWithNumbers($schoolId);
            $this->processYearGradeFormat($schoolId);
            $this->processEndingWithNumberClass($schoolId);
            $this->processYearFormat($schoolId);
            $this->processChineseNumbers($schoolId);
            $this->setFinalClassName($schoolId);
            $this->insertIntoClasses($schoolId);
            $this->updateClassIds($schoolId);
            
            DB::commit();
            $this->info('班级名称处理完成！');
            
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('处理失败: ' . $e->getMessage());
            Log::error('班级名称处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
        
        return 0;
    }

    /**
     * 处理中文括号转换为英文括号
     */
    private function processChineseParentheses($schoolId = null)
    {
        $this->info('步骤1: 处理中文括号转换...');
        
        $query = "UPDATE classes_old 
                  SET class_name1 = REPLACE(REPLACE(class_name, '（', '('), '）', ')') 
                  WHERE class_name LIKE '%（%'";
        
        if ($schoolId) {
            $query .= " AND school_id = {$schoolId}";
        }
        
        DB::statement($query);
        $this->info('中文括号转换完成');
    }

    /**
     * 处理括号中的数字
     */
    private function processNumbersInParentheses($schoolId = null)
    {
        $this->info('步骤2: 处理括号中的数字...');
        
        // 提取括号中的数字并添加"班"
        $query1 = "UPDATE classes_old 
                   SET class_name2 = CONCAT(REGEXP_SUBSTR(class_name1, '\\\\([0-9]+\\\\)'), '班') 
                   WHERE class_name1 REGEXP '\\\\([0-9]+\\\\)'";
        
        if ($schoolId) {
            $query1 .= " AND school_id = {$schoolId}";
        }
        
        DB::statement($query1);
        
        // 移除括号
        $query2 = "UPDATE classes_old 
                   SET class_name2 = REPLACE(REPLACE(class_name2, ')', ''), '(', '') 
                   WHERE class_name1 REGEXP '\\\\([0-9]+\\\\)'";
        
        if ($schoolId) {
            $query2 .= " AND school_id = {$schoolId}";
        }
        
        DB::statement($query2);
        $this->info('括号中数字处理完成');
    }

    /**
     * 处理纯数字数据
     */
    private function processPureNumbers($schoolId = null)
    {
        $this->info('步骤3: 处理纯数字数据...');
        
        // 复制纯数字到class_name1
        $query1 = "UPDATE classes_old 
                   SET class_name1 = class_name 
                   WHERE class_name REGEXP '^[0-9]+$'";
        
        if ($schoolId) {
            $query1 .= " AND school_id = {$schoolId}";
        }
        
        DB::statement($query1);
        
        // 将01转换为1（去掉前导零）
        $query2 = "UPDATE classes_old 
                   SET class_name1 = CAST(class_name1 AS SIGNED) 
                   WHERE class_name1 REGEXP '^[0-9]+$'";
        
        if ($schoolId) {
            $query2 .= " AND school_id = {$schoolId}";
        }
        
        DB::statement($query2);
        
        // 添加"班"字
        $query3 = "UPDATE classes_old 
                   SET class_name2 = CONCAT(class_name1, '班') 
                   WHERE class_name1 REGEXP '^[0-9]+$'";
        
        if ($schoolId) {
            $query3 .= " AND school_id = {$schoolId}";
        }
        
        DB::statement($query3);
        $this->info('纯数字处理完成');
    }

    /**
     * 处理数字+班的格式
     */
    private function processGradeWithNumbers($schoolId = null)
    {
        $this->info('步骤4: 处理数字+班的格式...');
        
        // 处理纯数字班格式
        $query1 = "UPDATE classes_old 
                   SET class_name1 = class_name 
                   WHERE class_name2 IS NULL AND class_name REGEXP '^[0-9]+班$'";
        
        if ($schoolId) {
            $query1 .= " AND school_id = {$schoolId}";
        }
        
        DB::statement($query1);
        
        // 去掉前导0（但保留单独的0班）
        $query2 = "UPDATE classes_old 
                   SET class_name1 = REPLACE(class_name1, '0', '') 
                   WHERE class_name2 IS NULL 
                   AND class_name1 REGEXP '^0[0-9]*班$' 
                   AND class_name1 != '0班'";
        
        if ($schoolId) {
            $query2 .= " AND school_id = {$schoolId}";
        }
        
        DB::statement($query2);
        
        // 设置最终班级名称
        $query3 = "UPDATE classes_old 
                   SET class_name2 = class_name1 
                   WHERE class_name2 IS NULL AND class_name REGEXP '^[0-9]+班$'";
        
        if ($schoolId) {
            $query3 .= " AND school_id = {$schoolId}";
        }
        
        DB::statement($query3);
        $this->info('数字+班格式处理完成');
    }

    /**
     * 处理年级+数字+班的格式
     */
    private function processYearGradeFormat($schoolId = null)
    {
        $this->info('步骤5: 处理年级+数字+班的格式...');

        // 处理高一、高二、高三格式
        $queries = [
            // 移除年级前缀，保留数字+班
            "UPDATE classes_old
             SET class_name1 = REPLACE(REPLACE(REPLACE(class_name, '高一', ''), '高二', ''), '高三', '')
             WHERE class_name2 IS NULL AND class_name REGEXP '^(高一|高二|高三)([0-9]|[0-9][0-9])+班$'",

            "UPDATE classes_old
             SET class_name1 = REPLACE(REPLACE(REPLACE(class_name, '初一', ''), '初二', ''), '初三', '')
             WHERE class_name2 IS NULL AND class_name REGEXP '^(初一|初二|初三)([0-9]|[0-9][0-9])+班$'",

            // 设置class_name2
            "UPDATE classes_old
             SET class_name2 = class_name1
             WHERE class_name2 IS NULL AND class_name REGEXP '^(高一|高二|高三)([0-9]|[0-9][0-9])+班$'",

            "UPDATE classes_old
             SET class_name2 = class_name1
             WHERE class_name2 IS NULL AND class_name REGEXP '^(初一|初二|初三)([0-9]|[0-9][0-9])+班$'",

            // 处理没有"班"字的格式
            "UPDATE classes_old
             SET class_name1 = CONCAT(REPLACE(REPLACE(REPLACE(class_name, '高一', ''), '高二', ''), '高三', ''), '班')
             WHERE class_name2 IS NULL AND class_name REGEXP '^(高一|高二|高三)([0-9]|[0-9][0-9])$'",

            "UPDATE classes_old
             SET class_name1 = CONCAT(REPLACE(REPLACE(REPLACE(class_name, '初一', ''), '初二', ''), '初三', ''), '班')
             WHERE class_name2 IS NULL AND class_name REGEXP '^(初一|初二|初三)([0-9]|[0-9][0-9])$'",

            "UPDATE classes_old
             SET class_name2 = class_name1
             WHERE class_name2 IS NULL AND class_name REGEXP '^(高一|高二|高三)([1-9]|[1-9][0-9])$'",

            "UPDATE classes_old
             SET class_name2 = class_name1
             WHERE class_name2 IS NULL AND class_name REGEXP '^(初一|初二|初三)([0-9]|[0-9][0-9])$'"
        ];

        foreach ($queries as $query) {
            if ($schoolId) {
                $query .= " AND school_id = {$schoolId}";
            }
            DB::statement($query);
        }

        $this->info('年级+数字+班格式处理完成');
    }

    /**
     * 处理以数字+班结尾的格式
     */
    private function processEndingWithNumberClass($schoolId = null)
    {
        $this->info('步骤6: 处理以数字+班结尾的格式...');

        // 提取末尾的数字+班
        $query1 = "UPDATE classes_old
                   SET class_name1 = REPLACE(LTRIM(REPLACE(REGEXP_SUBSTR(class_name, '[0-9]{1,2}班$'), '0', ' ')), ' ', '0')
                   WHERE class_name2 IS NULL AND REGEXP_SUBSTR(class_name, '[0-9]{1,2}班$') IS NOT NULL";

        if ($schoolId) {
            $query1 .= " AND school_id = {$schoolId}";
        }

        DB::statement($query1);

        $query2 = "UPDATE classes_old
                   SET class_name2 = REPLACE(LTRIM(REPLACE(REGEXP_SUBSTR(class_name, '[0-9]{1,2}班$'), '0', ' ')), ' ', '0')
                   WHERE class_name2 IS NULL AND REGEXP_SUBSTR(class_name, '[0-9]{1,2}班$') IS NOT NULL";

        if ($schoolId) {
            $query2 .= " AND school_id = {$schoolId}";
        }

        DB::statement($query2);
        $this->info('以数字+班结尾格式处理完成');
    }

    /**
     * 处理年级格式（如2024级）
     */
    private function processYearFormat($schoolId = null)
    {
        $this->info('步骤7: 处理年级格式...');

        // 移除年份级前缀
        $query = "UPDATE classes_old
                  SET class_name1 = REPLACE(class_name, REGEXP_SUBSTR(class_name, '^[0-9]{4}级'), '')
                  WHERE class_name2 IS NULL
                  AND REGEXP_SUBSTR(class_name, '^[0-9]{4}级.+班') IS NOT NULL";

        if ($schoolId) {
            $query .= " AND school_id = {$schoolId}";
        }

        DB::statement($query);
        $this->info('年级格式处理完成');
    }

    /**
     * 处理中文数字转换
     */
    private function processChineseNumbers($schoolId = null)
    {
        $this->info('步骤8: 处理中文数字转换...');

        $chineseNumbers = [
            '十一' => '11', '十二' => '12', '十三' => '13', '十四' => '14', '十五' => '15',
            '十六' => '16', '十七' => '17', '十八' => '18', '十九' => '19',
            '一' => '1', '二' => '2', '三' => '3', '四' => '4', '五' => '5',
            '六' => '6', '七' => '7', '八' => '8', '九' => '9', '十' => '10'
        ];

        foreach ($chineseNumbers as $chinese => $number) {
            $query = "UPDATE classes_old
                      SET class_name1 = REPLACE(class_name1, '{$chinese}', '{$number}')
                      WHERE class_name2 IS NULL
                      AND REGEXP_SUBSTR(class_name, '^[0-9]{4}级.+班') IS NOT NULL";

            if ($schoolId) {
                $query .= " AND school_id = {$schoolId}";
            }

            DB::statement($query);
        }

        // 设置最终的class_name2
        $query = "UPDATE classes_old
                  SET class_name2 = class_name1
                  WHERE class_name2 IS NULL
                  AND REGEXP_SUBSTR(class_name, '^[0-9]{4}级.+班') IS NOT NULL";

        if ($schoolId) {
            $query .= " AND school_id = {$schoolId}";
        }

        DB::statement($query);
        $this->info('中文数字转换完成');
    }

    /**
     * 设置最终的班级名称
     */
    private function setFinalClassName($schoolId = null)
    {
        $this->info('步骤9: 设置最终班级名称...');

        $query = "UPDATE classes_old
                  SET class_name3 = COALESCE(class_name2, class_name)";

        if ($schoolId) {
            $query .= " WHERE school_id = {$schoolId}";
        }

        DB::statement($query);
        $this->info('最终班级名称设置完成');
    }

    /**
     * 插入到classes表
     */
    private function insertIntoClasses($schoolId = null)
    {
        $this->info('步骤10: 插入到classes表...');

        $query = "INSERT INTO classes(school_id, school_campus_id, grade_id, class_name)
                  SELECT school_id, school_campus_id, grade_id, class_name3
                  FROM classes_old
                  WHERE grade_id IS NOT NULL";

        if ($schoolId) {
            $query .= " AND school_id = {$schoolId}";
        }

        $query .= " GROUP BY school_id, school_campus_id, grade_id, class_name3";

        DB::statement($query);
        $this->info('插入到classes表完成');
    }

    /**
     * 更新class_id关联
     */
    private function updateClassIds($schoolId = null)
    {
        $this->info('步骤11: 更新class_id关联...');

        $query = "UPDATE classes_old a
                  JOIN classes b ON a.school_id = b.school_id
                      AND a.school_campus_id = b.school_campus_id
                      AND a.grade_id = b.grade_id
                      AND a.class_name3 = b.class_name
                  SET a.class_id = b.id";

        if ($schoolId) {
            $query .= " WHERE a.school_id = {$schoolId}";
        }

        DB::statement($query);
        $this->info('class_id关联更新完成');
    }
}
