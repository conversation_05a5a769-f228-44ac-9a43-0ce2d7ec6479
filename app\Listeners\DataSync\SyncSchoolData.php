<?php

namespace App\Listeners\DataSync;

use App\Events\DataSync\SchoolCreated;
use App\Services\DataSync\DataSyncService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * 同步学校数据监听器
 */
class SyncSchoolData implements ShouldQueue
{
    use InteractsWithQueue;

    protected $dataSyncService;

    /**
     * Create the event listener.
     */
    public function __construct(DataSyncService $dataSyncService)
    {
        $this->dataSyncService = $dataSyncService;
    }

    /**
     * Handle the event.
     */
    public function handle(SchoolCreated $event): void
    {
        try {
            $result = $this->dataSyncService->syncSchool($event->schoolData);
            
            if (!$result['success']) {
                Log::error('学校数据同步失败', [
                    'school_data' => $event->schoolData,
                    'error' => $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('学校数据同步监听器异常', [
                'school_data' => $event->schoolData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(SchoolCreated $event, \Throwable $exception): void
    {
        Log::error('学校数据同步队列任务失败', [
            'school_data' => $event->schoolData,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
