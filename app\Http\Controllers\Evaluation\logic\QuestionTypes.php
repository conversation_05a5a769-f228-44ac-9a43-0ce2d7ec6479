<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */
namespace app\evaluation\logic;
use app\evaluation\model\QuestionType as QuestionTypesModel;
use think\Loader;

class QuestionTypes{
    protected $user;
    public function __construct()
    {

        $this->user = get_user();
    }

    public function add()
    {
        $model = new QuestionTypesModel();
        $model->data([
            'type_name'  =>  input('type_name'),
            'is_subjective' =>  input('is_subjective')
        ]);
        $model->save();
        $id=$model->id;
        apiReturn($id);
    }

    public function edit()
    {

        $model = new QuestionTypesModel();
        $model->save([
            'type_name'  => input('type_name'),
            'is_subjective' => input('is_subjective')
        ],['id' => input('id')]);

        apiReturn(input('id'));
    }

    public function del()
    {
        $model = new QuestionTypesModel();
        $model->save([
            'status'  => '-1',
        ],['id' => input('id')]);
        //软删除
        apiReturn(input('id'));
    }

    public function get_list()
    {
        $id=input('id');

        $model = new QuestionTypesModel();
        if(empty($id)){
            $data = $model->where('status','=',0)->select();
        }else{
            $data= $model::where('id',input('id'))->find();

        }

        apiReturn($data);

    }

}