<?php

namespace App\Services\School\Assessment\Score\Competency;

use App\Repositories\AnswerRepository;
use App\Services\BaseService;
use App\Services\School\Assessment\Score\ScoreServiceInterface;

/**
 * 创新人才核心素养测评抽象评分服务
 * 
 * 提供创新人才核心素养测评相关的基础评分功能，包括分数计算和结果分析
 */
abstract class AbstractScoreService extends BaseService implements ScoreServiceInterface
{
    public function __construct(
        protected AnswerRepository $answerRepository
    ) {
        
    }
    /**
     * 查询生涯测评分数，维度分由sql完成计算，创造性思维，批判性思维，沟通与合作走这个方法
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 维度分数数组
     */
    public function calculateScores(array $params): array
    {
        $data = $this->answerRepository->getCompetencyDimensionScores($params);

        return $data;
    }
    
    /**
     * 计算维度分数
     * @param array $params
     * @return array<int, array{dimension_name: string, score: int}>
     */
    public function calculateDimensionScores(array $params): array
    {
        # 1.先查出所有的回答记录
        $results = $this->answerRepository->getCompetencyAnswers($params);

        # 2.计算每一题得分
        $results = $this->calculateQuestionScores($results);

        # 3.计算维度分数
        $dimensions = $this->calculateDimensionData($results);
        
        return $dimensions;
    }

    /**
     * 计算每一题得分
     * 
     * @param array $results 答案记录数组
     * @return array 计算得分后的答案记录数组
     */
    protected function calculateQuestionScores(array $results): array
    {
        foreach ($results as $key => $answer) {
            $score = 0;
            $options = json_decode($answer['options'], true);
            
            if($answer['correct']){//有正确答案
                $correct_strlen = strlen($answer['correct']);
                //单选题全等得1分
                if($correct_strlen == 1 && $answer['answer'] == $answer['correct']){
                    $score = 1;
                }elseif($correct_strlen > 1){//除密码锁题之外的多选题
                    $score = $this->calculateMultiChoiceScore($answer['answer'], $answer['correct']);
                }
            }else{
                //无正确答案就从options里匹配
                $score = $options[$answer['answer']]['score'];
            }
            $results[$key]['score'] = $score;
        }
        
        return $results;
    }
    
    /**
     * 计算维度数据
     * 
     * @param array $results 答案记录数组
     * @return array 维度数据数组
     */
    protected function calculateDimensionData(array $results): array
    {
        $dimensions = [];
        foreach ($results as $result) {
            $dimension_key = $result['dimension_code'];
            
            // 如果维度不存在，初始化维度数据
            if (!isset($dimensions[$dimension_key])) {
                $dimensions[$dimension_key] = [
                    'code' => $result['dimension_code'],
                    'name' => $result['dimension_name'],
                    'score' => 0,
                    'question_count' => 0
                ];
            }
            
            // 累加分数和题目数量
            $dimensions[$dimension_key]['score'] += $result['score'] ?? 0;
            $dimensions[$dimension_key]['question_count'] += 1;
        }

        // 按维度代码排序并重置数组索引
        ksort($dimensions);
        return array_values($dimensions);
    }

    /**
     * 计算多选题分数
     * @param string $answer 答案
     * @param string $correct_answer 正确答案
     * @return int 分数
     */
    private function calculateMultiChoiceScore(string $answer, string $correct_answer): int
    {
        $answer_arr = str_split($answer);
        $score = 0;

        foreach ($answer_arr as $selected_option) {
            if (strpos($correct_answer, $selected_option) === false) {
                return 0;
            }
            $score++;
        }

        return $score;
    }
    
    /**
     * 计算评估结果
     * @param array $params 参数
     * @return array 评估结果
     */
    abstract public function calculate(array $params): array;
}