<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('seeder_execution_logs', function (Blueprint $table) {
            $table->id();
            $table->string('seeder_class')->comment('Seeder类名');
            $table->string('assessment_type')->comment('测评类型');
            $table->integer('school_id')->comment('学校ID');
            $table->integer('last_processed_id')->default(0)->comment('最后处理的记录ID');
            $table->integer('total_processed')->default(0)->comment('总处理数量');
            $table->timestamp('last_executed_at')->nullable()->comment('最后执行时间');
            $table->timestamps();
            
            $table->unique(['seeder_class', 'assessment_type', 'school_id'], 'unique_seeder_execution');
            $table->index(['seeder_class', 'school_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('seeder_execution_logs');
    }
};
