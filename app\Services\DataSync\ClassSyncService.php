<?php

namespace App\Services\DataSync;

use App\Services\BaseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClassSyncService extends BaseService
{
    protected $syncConnection;
    protected $schoolSyncService;

    public function __construct(SchoolSyncService $schoolSyncService)
    {
        $this->syncConnection = DB::connection('sync_mysql');
        $this->schoolSyncService = $schoolSyncService;
    }

    /**
     * 同步班级数据（保持原有逻辑，包含年级推算）
     *
     * @param array $classData 班级数据
     * @return array
     */
    public function syncClass(array $classData): array
    {
        try {
            $this->syncConnection->beginTransaction();

            // 获取同步数据库中的学校ID
            $syncSchoolId = $this->schoolSyncService->getSyncDistrictId($classData['school_campus_id']);
            $districtId = $classData['school_campus_id'];
            if (!$syncSchoolId) {
                $this->throwBusinessException('未找到对应的同步学校');
               
            }

            // 创建或获取年级（根据年份推算）
            $gradeId = $this->createOrGetGrade($classData, $syncSchoolId, $districtId);

            // 应用字段映射转换
            $mappedData = $this->mapClassFields($classData);

            // 同步班级信息 - 生成新的ID
            $syncClassData = [
                'name' => $mappedData['name'],
                'school_id' => $syncSchoolId,
                'school_district' => $districtId,
                'grade_id' => $gradeId,
                'student_cnt'=> '0',
                'create_time'=> now(),
                'step' => 0
            ];

            // 使用insertGetId生成新的班级ID
            $syncClassId = $this->syncConnection->table('ysy_class')->insertGetId($syncClassData);

            $this->syncConnection->commit();

            Log::info('班级数据同步成功', [
                'sync_class_id' => $syncClassId,
                'grade_id' => $gradeId,
                'school_id' => $syncSchoolId,
                'district_id' => $districtId
            ]);

            return [
                'success' => true,
                'sync_class_id' => $syncClassId,
                'grade_id' => $gradeId,
                'message' => '班级数据同步成功'
            ];

        } catch (\Exception $e) {
            $this->syncConnection->rollBack();
            $this->throwBusinessException('班级数据同步失败');

        }
    }

    /**
     * 创建或获取年级（保持原有逻辑，根据年份推算）
     *
     * @param array $classData 班级数据
     * @param int $schoolId 学校ID
     * @param int $districtId 校区ID
     * @return int 年级ID
     */
    private function createOrGetGrade(array $classData, int $schoolId, int $districtId): int
    {
        $grade = $classData['grade_id'];
       
        if (!$grade) {
            throw new \Exception('缺少年级信息');
        }

        // 计算年级信息（根据年份推算）
        $gradeInfo = $this->calculateGradeInfo($grade);

        // 检查年级是否已存在
        $existingGrade = $this->syncConnection->table('ysy_grade')
            ->where('school_id', $schoolId)
            ->where('name', $gradeInfo['year'])           // 年份存储在name字段
            ->where('grade_name', $gradeInfo['grade_name']) // 年级名称存储在grade_name字段
            ->where('grade_sort', $grade)
            ->first();

        if ($existingGrade) {
            return $existingGrade->id;
        }

        // 创建新年级
        $gradeData = [
            'school_id' => $schoolId,
            'school_district' => $districtId,
            'name' => $gradeInfo['year'],
            'grade_name' => $gradeInfo['grade_name'],
            'grade_sort' => $grade,
            'create_time' => now(),
            'step' => 0
        ];

        return $this->syncConnection->table('ysy_grade')->insertGetId($gradeData);
    }

    /**
     * 计算年级信息（保持原有逻辑）
     * 根据当前时间和年级推算入学年份
     *
     * @param int $grade 年级
     * @return array
     */
    private function calculateGradeInfo(int $grade): array
    {
        $currentYear = date('Y');
        $currentMonth = date('n');

        // 判断是否过了9月
        $isAfterSeptember = $currentMonth >= 9;

        // 年级名称映射
        $gradeNames = [
            7 => '初一',
            8 => '初二',
            9 => '初三',
            10 => '高一',
            11 => '高二',
            12 => '高三'
        ];

        // 计算入学年份
        if ($isAfterSeptember) {
            // 9月后：2025高一，2024高二，2023高三
            switch ($grade) {
                case 10: // 高一
                    $year = $currentYear;
                    break;
                case 11: // 高二
                    $year = $currentYear - 1;
                    break;
                case 12: // 高三
                    $year = $currentYear - 2;
                    break;
                case 7: // 初一
                    $year = $currentYear;
                    break;
                case 8: // 初二
                    $year = $currentYear - 1;
                    break;
                case 9: // 初三
                    $year = $currentYear - 2;
                    break;
                default:
                    $year = $currentYear;
            }
        } else {
            // 9月前：2024高一，2023高二，2022高三
            switch ($grade) {
                case 10: // 高一
                    $year = $currentYear - 1;
                    break;
                case 11: // 高二
                    $year = $currentYear - 2;
                    break;
                case 12: // 高三
                    $year = $currentYear - 3;
                    break;
                case 7: // 初一
                    $year = $currentYear - 1;
                    break;
                case 8: // 初二
                    $year = $currentYear - 2;
                    break;
                case 9: // 初三
                    $year = $currentYear - 3;
                    break;
                default:
                    $year = $currentYear - 1;
            }
        }

        return [
            'year' => (string)$year,
            'grade_name' => $gradeNames[$grade] ?? '未知年级'
        ];
    }



    /**
     * 班级字段映射（保持原有逻辑）
     *
     * @param array $classData 原始班级数据
     * @return array 映射后的数据
     */
    private function mapClassFields(array $classData): array
    {
        return [
            'name' => $classData['name'] ?? $classData['class_name'] ?? '',
            'class_type' => $classData['class_type'] ?? '',
            'capacity' => $classData['capacity'] ?? 0,
            'status' => $classData['status'] ?? 1
        ];
    }

    /**
     * 批量同步班级数据（保持原有逻辑）
     *
     * @param array $classesData 班级数据数组
     * @return array
     */
    public function syncBatchClasses(array $classesData): array
    {
        $results = [];
        $successCount = 0;
        $failedCount = 0;

        foreach ($classesData as $classData) {
            $result = $this->syncClass($classData);
            $results[] = $result;

            if ($result['success']) {
                $successCount++;
            } else {
                $failedCount++;
            }
        }

        Log::info('批量班级同步完成', [
            'total_count' => count($classesData),
            'success_count' => $successCount,
            'failed_count' => $failedCount
        ]);

        return [
            'success' => true,
            'results' => $results,
            'total_count' => count($classesData),
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'message' => '批量班级同步完成'
        ];
    }

}
