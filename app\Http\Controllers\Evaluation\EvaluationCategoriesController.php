<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use App\Services\Evaluation\CategoriesService;
use App\Http\Requests\Evaluation\CategoryRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * 素养类别管理控制器
 */
class EvaluationCategoriesController extends Controller
{
    protected $categoriesService;

    public function __construct(CategoriesService $categoriesService)
    {
        $this->categoriesService = $categoriesService;
    }

    /**
     * 类别查询
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function categories(Request $request): JsonResponse
    {
        try {
            $data = $this->categoriesService->getCategoriesList($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取类别列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 类别添加
     * 
     * @param CategoryRequest $request
     * @return JsonResponse
     */
    public function store(CategoryRequest $request): JsonResponse
    {
        try {
            $data = $this->categoriesService->createCategory($request->validated());
            return $this->success($data, '类别添加成功');
        } catch (\Exception $e) {
            return $this->error('类别添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 类别修改
     * 
     * @param CategoryRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(CategoryRequest $request, int $id): JsonResponse
    {
        try {
            $data = $this->categoriesService->updateCategory($id, $request->validated());
            return $this->success($data, '类别修改成功');
        } catch (\Exception $e) {
            return $this->error('类别修改失败: ' . $e->getMessage());
        }
    }

    /**
     * 类别删除
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->categoriesService->deleteCategory($id);
            return $this->success(null, '类别删除成功');
        } catch (\Exception $e) {
            return $this->error('类别删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取类别树形结构
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function tree(Request $request): JsonResponse
    {
        try {
            $data = $this->categoriesService->getCategoryTree($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取类别树失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取子类别
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function children(Request $request): JsonResponse
    {
        try {
            $parentId = $request->input('parent_id', 0);
            $courseId = $request->input('course_id');
            $data = $this->categoriesService->getChildrenCategories($parentId, $courseId);
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取子类别失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新排序
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function sort(Request $request): JsonResponse
    {
        try {
            $this->categoriesService->updateSort($request->all());
            return $this->success(null, '排序更新成功');
        } catch (\Exception $e) {
            return $this->error('排序更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 移动类别
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function move(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $parentId = $request->input('parent_id', 0);
            $this->categoriesService->moveCategory($id, $parentId);
            return $this->success(null, '类别移动成功');
        } catch (\Exception $e) {
            return $this->error('类别移动失败: ' . $e->getMessage());
        }
    }
}
