<?php

namespace App\Services\School\Assessment;

use App\Models\School\Assessment\AssessmentTask;
use App\Services\BaseService;
use App\Services\School\Assessment\AssessmentBasicService;
use Illuminate\Support\Collection;
use App\Repositories\AssignmentRepository;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;

class AssessmentTeacherService extends BaseService
{
    public function __construct(protected AssessmentBasicService $assessmentBasicService, protected AssignmentRepository $assignmentRepository)
    {
    }

    /**
     * 获取学生得分情况的查询构建器
     *
     * @param Request $request 请求对象
     * @return Builder 查询构建器
     */
    public function getStudentsScoreBuilder(Request $request): Builder
    {
        $task_id = $request->input('task_id');
        $school_id = $request->user()->organization->model_id;
        $is_complete = $request->input('is_complete');
    
        // 获取任务分配给的所有学生的查询构建器
        $builder = $this->assignmentRepository->getAssignmentsByTaskId($task_id, $school_id);
        
        // 根据完成状态筛选
        if (!empty($is_complete)) {
            if ($is_complete == '已完成') {
                $builder->whereNotNull('standard_results');
            } elseif ($is_complete == '未完成') {
                $builder->whereNull('standard_results');
            }
        }
    
        return $builder;
    }

    /**
     * 处理学生得分数据
     *
     * @param Collection $assignments 学生分配信息集合
     * @param Request $request 请求对象
     * @return Collection 处理后的学生得分信息集合
     */
    public function processStudentsScore(Collection $assignments, Request $request): Collection
    {
        $task_id = $request->input('task_id');
        
        // 获取任务信息
        $task = AssessmentTask::with('assessment:id,name,category_code')->findOrFail($task_id);
        $assessment_id = $task->assessment_id;
        
        // 获取维度信息
        $dimension_names = $this->assessmentBasicService->getDimensionDetails($assessment_id);

        $code_mapping = [];
        if($assessment_id == 3){
            $code_mapping = config('assessment.career.personality_code_mapping');
        }elseif(in_array($assessment_id, [4, 8])){
            $code_mapping = config('assessment.career.interest_code_mapping');
        }

        // 处理每个学生的得分情况
        return $assignments->map(function ($assignment) use ($dimension_names, $assessment_id, $code_mapping) {
            return $this->processStudentScore($assignment, $dimension_names, $assessment_id, $code_mapping);
        })->values(); // 重新索引
    }

    /**
     * 处理单个学生的得分情况
     *
     * @param object $assignment 学生分配信息
     * @param array $dimension_names 维度名称列表
     * @return object 学生得分信息对象
     */
    protected function processStudentScore($assignment, array $dimension_names, int $assessment_id, array $code_mapping)
    {
        // 判断是否完成
        $is_completed = !empty($assignment->standard_results);
        $completed_text = $is_completed ? '已完成' : '未完成';
            
        // 创建基础学生信息
        $student_info = [
            'assignment_id' => $assignment->assignment_id,
            'student_name' => $assignment->student->student_name ?? '',
            'class_name' => $assignment->studentClass->claass->class_name ?? '',
            'grade_name' => $assignment->studentClass->claass->grade->grade_name ?? '',
            'completed' => $completed_text,
            'completion_time' => $assignment->completion_time,
            'duration' => $assignment->duration,
        ];
        
        // 处理维度得分
        if ($is_completed && isset($assignment->standard_results['dimensions'])) {
            $dimensions_data = [];
            
            foreach ($assignment->standard_results['dimensions'] as $dimension) {
                if (empty($dimension['children'])) {
                    $dimensions_data[$dimension['name']] = $dimension['score'] ?? 0;
                } else {
                    foreach ($dimension['children'] as $child) {
                        $dimensions_data[$child['name']] = $child['score'] ?? 0;
                    }
                }
            }
        }
        
        // 设置类型字段名称和默认值
        $type_field = null;
        $type_value = $is_completed ? ($assignment->standard_results['code'] ?? null) : null;
        
        if ($assessment_id == 3) {
            $type_field = '性格类型';
        } elseif (in_array($assessment_id, [4, 8])) {
            $type_field = '兴趣类型';
        }
        
        // 如果有类型字段，设置其值
        if ($type_field) {
            $student_info[$type_field] = $type_value;
        }
        
        // 返回所有维度的得分
        foreach ($dimension_names as $name) {
            $dimension_key = $name;
            $dimension_key .= isset($code_mapping[$name]) ? '（'.$code_mapping[$name].'）' : '';
            $student_info[$dimension_key] = $is_completed ? ($dimensions_data[$name] ?? 0) : null;
        }

        return (object)$student_info;
    }
}