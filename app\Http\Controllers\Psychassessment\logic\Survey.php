<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2024/01/22
 * Time: 15:16
 */
namespace app\psychassessment\logic;
use app\psychassessment\model\Plan as PlanModel;
use app\survey\model\Surveys as SurModel;
use app\psychassessment\model\PlanDetail as PlanDetailModel;
use app\psychassessment\model\PlanSurveyRelation as RelationModel;
use think\Db;
use think\Session;
use think\Config;

class Survey{
    protected $PlanModel;
    protected $PlanDetailModel;
    protected $SurModel;
    protected $RelationModel;
    protected $user;
    public function __construct()
    {
        $this->PlanModel = new PlanModel();
        $this->PlanDetailModel = new PlanDetailModel();
        $this->SurModel = new SurModel();
        $this->RelationModel = new RelationModel();
        $this->user = json_decode(Session::get('user'),true);
    }

    public function add()
    {
        //权限判断
        if($this->user['role_source_id'] != 2){
            $is_psych = db('teacher')->where(['member_id'=>$this->user['id'],'step'=>0])->value('is_psych');
            if(!is_numeric($is_psych) || $is_psych != 1) apiReturn([],'您不是心理老师，无权进行此操作',-5);
        }
        $data = input('post.');
        $data['school_id'] = $this->user['school_id'];
        $data['plan_by'] = $this->user['id'];
        $data['create_time'] = date('Y-m-d H:i:s');
        $this->PlanModel->allowField(true)->save($data);
        $id=$this->PlanModel->id;
        $survey_id_arr = explode(',',$data['survey_ids']);
        foreach ($survey_id_arr as $v){
            $ins[] = [
                'plan_id'=>$id,
                'survey_id'=>$v,
            ];
        }
        $this->PlanDetailModel->allowField(true)->saveAll($ins);
        apiReturn($id);
    }

    public function edit()
    {
        //权限判断
        $id = input('id');
        $data = input('put.');
        $this->PlanModel->save($data,['id' => $id]);
        if(!empty($data['survey_ids'])){
            $this->PlanDetailModel->where(['plan_id'=>$id,'status'=>0])->update(['status'=>-1]);
            $survey_id_arr = explode(',',$data['survey_ids']);
            foreach ($survey_id_arr as $v){
                $ins[] = [
                    'plan_id'=>$id,
                    'survey_id'=>$v,
                ];
            }
            $this->PlanDetailModel->allowField(true)->saveAll($ins);
        }
        apiReturn($id);
    }

    public function del()
    {
        $this->PlanModel->save([
            'status'  => '-1',
        ],['id' => input('id')]);
        //软删除
        apiReturn(input('id'));
    }

    //查询
    public function get_list()
    {
        $id = input('id');

        //survey_id与title对应关系
        $sur_title = $this->SurModel->where(['survey_type'=>['in',[26,27,28]]])->column('id,title,survey_type');
        $sur_title = to_arr($sur_title);
        $w['plan.school_id'] = $this->user['school_id'];
        if(!empty($id)){
            switch ($this->user['role_source_id']){
                case 2:
                    $result = $this->PlanModel->where(['status'=>0,'id'=>$id])->with(['surveys'])->select();
                    $w['sur.survey_type'] = ['in',[26,27,28]];
                    $w['relation.status'] = 0;
                    $w['plan.id'] = $id;
                    $relation_info = $this->PlanModel->alias('plan')
                        ->join('psychassessment_plan_survey_relation relation','relation.plan_id = plan.id and find_in_set(relation.member_id,plan.member_ids)')
                        ->join('survey_user_session session','relation.session_id = session.session_id')
                        ->join('survey sur','session.survey_id = sur.id')
                        ->field('plan.id as plan_id,session.survey_id,sur.title,sur.survey_type,session.survey_id,session.session_id,relation.member_id')
                        ->where($w)
                        ->group('plan.id,session.survey_id,relation.member_id')
                        ->select();

                    break;
                case 3:
                    $w['teacher.member_id'] = $this->user['id'];
                    $w['plan.id'] = $id;
                    $result = $this->PlanModel->alias('plan')
                        ->join('student student','find_in_set(student.class_id,plan.class_ids) and find_in_set(student.member_id,plan.member_ids)')
                        ->join('teacher teacher','find_in_set(student.class_id,teacher.class_ids)')
                        ->field('plan.id,plan.name,plan.start_time,plan.end_time,plan.survey_ids,plan.show_report,plan.plan_by,plan.create_time,group_concat(student.member_id) as member_ids')
                        ->where($w)
                        ->with(['surveys'])
                        ->group('plan.id')
                        ->select();
                    $w['sur.survey_type'] = ['in',[26,27,28]];
                    $w['relation.status'] = 0;
                    $relation_info = $this->PlanModel->alias('plan')
                        ->join('student student','find_in_set(student.class_id,plan.class_ids) and find_in_set(student.member_id,plan.member_ids)')
                        ->join('teacher teacher','find_in_set(student.class_id,teacher.class_ids)')
                        ->join('psychassessment_plan_survey_relation relation','relation.plan_id = plan.id and relation.member_id = student.member_id')
                        ->join('survey_user_session session','relation.session_id = session.session_id')
                        ->join('survey sur','session.survey_id = sur.id')
                        ->field('plan.id as plan_id,session.survey_id,sur.title,sur.survey_type,session.survey_id,session.session_id,relation.member_id')
                        ->where($w)
                        ->group('plan.id,session.survey_id,student.member_id')
                        ->order('plan.id')
                        ->select();
                    break;
            }
            $result = to_arr($result);
            $relation_info = to_arr($relation_info);

            $new_data = [];
            foreach ($relation_info as $key => $value){
                $new_data[$value['plan_id']][$value['survey_id']]['member_id'][] = $value['member_id'];
            }

            foreach ($new_data as $ke => $val){
                foreach ($val as $vk => $vv){
                    $new_data[$ke][$vk]['finish_num'] = count($vv['member_id']);
                }
            }

            foreach ($result as $k => $v){
                $member_id_arr = explode(',',$v['member_ids']);
                $plan_num = count($member_id_arr);
                $result[$k]['plan_num'] = $plan_num;
                foreach ($v['surveys'] as $ks => $vs){
                    $result[$k]['surveys'][$ks]['title'] = $sur_title[$vs['survey_id']]['title'];
                    $result[$k]['surveys'][$ks]['survey_type'] = $sur_title[$vs['survey_id']]['survey_type'];
                    $finish_num = $new_data[$v['id']][$vs['survey_id']]['finish_num'] ?? 0;
                    $result[$k]['surveys'][$ks]['finish_num'] = $finish_num;
                    $result[$k]['surveys'][$ks]['progress'] = round($finish_num * 100/ $plan_num) > 100 ? 100 : round($finish_num * 100/ $plan_num);
                }
                $is_finish = 0;//初始值设为未完成
                $i = 0;
                foreach ($member_id_arr as $ik => $iv){
                    foreach ($v['surveys'] as $ks => $vs){
                        if(isset($new_data[$v['id']][$vs['survey_id']]['member_id']) && in_array($iv,$new_data[$v['id']][$vs['survey_id']]['member_id'])){
                            $is_finish = 1;//当前测评完成就置为1
                        }else{
                            $is_finish = 0;//只要有一个测评没完成就置为0，并跳出当前循环外面一层循环
                            continue 2;
                        }
                    }
                    if($is_finish == 1) $i++;//1代表计划中的测评都已完成
                }
                $result[$k]['plan_num'] = $plan_num;
                $result[$k]['finish_num'] = $i;
                $result[$k]['progress'] = round($i * 100/ $plan_num);
                if(date('Y-m-d H:i:s') < $v['start_time']){
                    $result[$k]['plan_state'] = 0;
                }elseif(date('Y-m-d H:i:s') > $v['end_time']){
                    $result[$k]['plan_state'] = 2;
                }else{
                    $result[$k]['plan_state'] = 1;
                }
            }
            $result = $result[0];
            return $result;
        }else{
            $pageNumber = input('page', 1); // 获取页码，默认为1
            $pageSize = input('pagesize', 10); // 获取每页显示的记录数，默认为10

            switch ($this->user['role_source_id']){
                case 2:
                    $result = $this->PlanModel->where(['status'=>0,'school_id'=>$this->user['school_id']])->with(['surveys'])->order('id desc')->select();
                    $w['sur.survey_type'] = ['in',[26,27,28]];
                    $w['relation.status'] = 0;
                    $w['plan.status'] = 0;
                    $relation_info = $this->PlanModel->alias('plan')
                        ->join('psychassessment_plan_survey_relation relation','relation.plan_id = plan.id and find_in_set(relation.member_id,plan.member_ids)')
                        ->join('survey_user_session session','relation.session_id = session.session_id')
                        ->join('survey sur','session.survey_id = sur.id')
                        ->field('plan.id as plan_id,session.survey_id,sur.title,sur.survey_type,session.survey_id,session.session_id,relation.member_id')
                        ->where($w)
                        ->group('plan.id,session.survey_id,relation.member_id')
                        ->order('plan.id')
                        ->select();
                    break;
                case 3:
                    $w['teacher.member_id'] = $this->user['id'];
                    $w['plan.status'] = 0;
                    $result = $this->PlanModel->alias('plan')
                        ->join('student student','find_in_set(student.class_id,plan.class_ids) and find_in_set(student.member_id,plan.member_ids)')
                        ->join('teacher teacher','find_in_set(student.class_id,teacher.class_ids)')
                        ->field('plan.id,plan.name,plan.start_time,plan.end_time,plan.survey_ids,plan.show_report,plan.plan_by,plan.create_time,group_concat(student.member_id) as member_ids')
                        ->where($w)
                        ->with(['surveys'])
                        ->group('plan.id')
                        ->order('plan.id desc')
                        ->select();
                    $w['sur.survey_type'] = ['in',[26,27,28]];
                    $w['relation.status'] = 0;
                    $w['plan.status'] = 0;
                    $relation_info = $this->PlanModel->alias('plan')
                        ->join('student student','find_in_set(student.class_id,plan.class_ids) and find_in_set(student.member_id,plan.member_ids)')
                        ->join('teacher teacher','find_in_set(student.class_id,teacher.class_ids)')
                        ->join('psychassessment_plan_survey_relation relation','relation.plan_id = plan.id and relation.member_id = student.member_id')
                        ->join('survey_user_session session','relation.session_id = session.session_id')
                        ->join('survey sur','session.survey_id = sur.id')
                        ->field('plan.id as plan_id,session.survey_id,sur.title,sur.survey_type,session.survey_id,session.session_id,relation.member_id')
                        ->where($w)
                        ->group('plan.id,session.survey_id,student.member_id')
                        ->order('plan.id')
                        ->select();
                    break;
            }
            $result = to_arr($result);

            $relation_info = to_arr($relation_info);

            $new_data = [];
            foreach ($relation_info as $key => $value){
                $new_data[$value['plan_id']][$value['survey_id']]['member_id'][] = $value['member_id'];
            }

            foreach ($new_data as $ke => $val){
                foreach ($val as $vk => $vv){
                    $new_data[$ke][$vk]['finish_num'] = count($vv['member_id']);
                }
            }

            foreach ($result as $k => $v){
                $member_id_arr = explode(',',$v['member_ids']);
                $plan_num = count($member_id_arr);
                $result[$k]['plan_num'] = $plan_num;
                foreach ($v['surveys'] as $ks => $vs){
                    $result[$k]['surveys'][$ks]['title'] = $sur_title[$vs['survey_id']]['title'];
                    $result[$k]['surveys'][$ks]['survey_type'] = $sur_title[$vs['survey_id']]['survey_type'];
                    $finish_num = $new_data[$v['id']][$vs['survey_id']]['finish_num'] ?? 0;
                    $result[$k]['surveys'][$ks]['finish_num'] = $finish_num;
                    $result[$k]['surveys'][$ks]['progress'] = round($finish_num * 100/ $plan_num) > 100 ? 100 : round($finish_num * 100/ $plan_num);
                }
                $is_finish = 0;//初始值设为未完成
                $i = 0;
                foreach ($member_id_arr as $ik => $iv){
                    foreach ($v['surveys'] as $ks => $vs){
                        if(isset($new_data[$v['id']][$vs['survey_id']]['member_id']) && in_array($iv,$new_data[$v['id']][$vs['survey_id']]['member_id'])){
                            $is_finish = 1;//当前测评完成就置为1
                        }else{
                            $is_finish = 0;//只要有一个测评没完成就置为0，并跳出当前循环外面一层循环
                            continue 2;
                        }
                    }
                    if($is_finish == 1) $i++;//1代表计划中的测评都已完成
                }
                $result[$k]['plan_num'] = $plan_num;
                $result[$k]['finish_num'] = $i;
                $result[$k]['progress'] = round($i * 100/ $plan_num);
                if(date('Y-m-d H:i:s') < $v['start_time']){
                    $result[$k]['plan_state'] = 0;
                }elseif(date('Y-m-d H:i:s') > $v['end_time']){
                    $result[$k]['plan_state'] = 2;
                }else{
                    $result[$k]['plan_state'] = 1;
                }
            }

            return pageing($result,$pageSize,$pageNumber);
        }


    }

    /**
     * @return \app\psychassessment\logic\psychassessment_plan
     */
    public function assess_list()
    {
        return $this->PlanModel->assess_list();
    }

    /**
     * @return \app\psychassessment\logic\get_psychassessment_class_member
     */
    public function get_psychassessment_class_member()
    {
        $level = input('level');
        $where['cl.step'] = 0;
        $where['stu.step'] = 0;
        $where['tea.member_id'] = $this->user['id'];
        $sch_id = $this->user['school_id'];
        //升年级后只保留最新的年级
        $sql = "SELECT g.id grade_id,g.name as grade_year,g.grade_name,g.create_time,cl.id as class_id,cl.name as class_name,stu.member_id,stu.name
                FROM
                (
                    select id,name,grade_name,school_id,school_district,create_time
                    from  ysy_grade  t1
                    where id = (
                        select max(id)
                        from ysy_grade t2
                        where t1.name = t2.name and t2.school_id = ".$sch_id." and step >= 0
                    )
                    and t1.school_id = ".$sch_id." and step >= 0
                ) g
                LEFT JOIN ysy_class cl ON g.id = cl.grade_id
                LEFT JOIN ysy_student stu ON stu.class_id = cl.id
                LEFT JOIN ysy_teacher tea ON find_in_set(cl.id,tea.class_ids)
                WHERE cl.step = 0 and stu.step = 0 and tea.member_id = ".$this->user['id'];
        $lists = Db::query($sql);
        //过滤学年制年份外的数据
        $school_year = db('grade')->where(['school_id' => $this->user['school_id'],'step'=>['>=',0],'school_year'=>['>=',3]])->value('school_year');
        $school_year = $school_year ?? 3;
        $year  = date('Y');
        $month = date('m');
        if($month > 8){
            $time = $year - $school_year + 1;
        }else{
            $time = $year - $school_year;
        }
        $temp  = [];
        foreach($lists as $k => $v){
            if($v['grade_year'] >= $time){
                $temp[]= $v;
            }
        }
        $new_data = [];
        //这么写是因为前端希望根据level来展示到几级
        switch ($level){
            case 2:
                foreach ($temp as $k => $v) {
                    $new_data[$v['class_id']]['label'] = $v['grade_year'].'-'.$v['grade_name'].$v['class_name'];
                    $new_data[$v['class_id']]['value'] = $v['class_id'];
                    $new_data[$v['class_id']]['level'] = 2;
                }
                break;
            default:
                foreach ($temp as $k => $v){
                    $new_data[$v['class_id']]['label'] = $v['grade_year'].'-'.$v['grade_name'].$v['class_name'];
                    $new_data[$v['class_id']]['value'] = $v['class_id'];
                    $new_data[$v['class_id']]['level'] = 2;
                    $new_data[$v['class_id']]['children'][$v['member_id']]['label'] = $v['name'];
                    $new_data[$v['class_id']]['children'][$v['member_id']]['value'] = $v['member_id'];
                    $new_data[$v['class_id']]['children'][$v['member_id']]['level'] = 3;
                }
                break;
        }
        return array_values($this->tree1($new_data));
    }

    /**
     * 格式化数组键名
     * @param $data
     * @return array
     */
    public function tree1($data){
        foreach ($data as $k =>$v){
            if(isset($v['children'])){
                $data[$k]['children'] = array_values($this->tree1($v['children']));
            }
        }
        return array_values($data);
    }

    public function get_member_complete_status(){
        $plan_id = input('id');
        $status = input('status');
        $members = $this->PlanModel->alias('plan')
            ->join('student stu','find_in_set(stu.class_id,plan.class_ids) and find_in_set(stu.member_id,plan.member_ids)')
            ->join('class cl','stu.class_id = cl.id')
            ->join('grade gr','cl.grade_id = gr.id')
            ->field('plan.id as plan_id,stu.student_no,stu.name,stu.gender,gr.name as grade_year,cl.name as class_name,stu.member_id')
            ->where(['plan.id'=>$plan_id])
            ->select();
        $members = to_arr($members);
        $surveys = $this->PlanDetailModel->alias('detail')
            ->join('survey sur','detail.survey_id = sur.id')
            ->field('sur.id,sur.survey_type')
            ->where(['detail.plan_id'=>$plan_id,'detail.status'=>0])
            ->select();
        $surveys = to_arr($surveys);
        $data = $this->PlanModel->alias('plan')
            ->join('psychassessment_plan_survey_relation relation','relation.plan_id = plan.id and find_in_set(relation.member_id,plan.member_ids)')
            ->join('survey_user_session session','relation.session_id = session.session_id')
            ->field('plan.id as plan_id,session.survey_id,session.session_id,relation.member_id')
            ->where(['plan.id'=>$plan_id])
            ->select();
        $data = to_arr($data);
        foreach ($data as $val){
            $new_data[$val['survey_id'].'-'.$val['member_id']] = $val;
        }
        $num = count($surveys);
        foreach ($members as $k => $v){
            $i = 0;
            foreach ($surveys as $vs){
                switch ($vs['survey_type']){
                    case 26:
                        $psy_key = 'confidence';
                        break;
                    case 27:
                        $psy_key = 'awareness';
                        break;
                    case 28:
                        $psy_key = 'anxiety';
                        break;
                    default:
                        $psy_key = 'nothing';
                }
                if(isset($new_data[$vs['id'].'-'.$v['member_id']])){
                    $i++;
                    $members[$k][$psy_key] = '已完成';
                }else{
                    $members[$k][$psy_key] = '未完成';
                }
            }
            if($status == 2 && $i == $num) unset($members[$k]);//说明都完成了就不返回了
        }
        return $members;
    }

    //查询
    public function get_student_psychassess()
    {
        $survey_type_id = $this->SurModel
            ->field('survey_type,id')
            ->where(['survey_type'=>['in',[26,27,28]]])
            ->select();
        $survey_type_id = to_arr($survey_type_id);

        $survey_conf = Config::get('psych');
        foreach ($survey_type_id as $k =>$v){
            $survey_rebuild[$v['id']] = $survey_conf[$v['survey_type']];
        }
        $pageNumber = input('page', 1); // 获取页码，默认为1
        $pageSize = input('pagesize', 10); // 获取每页显示的记录数，默认为10
        $result = $this->PlanModel->where('status',0)->where('find_in_set('.$this->user['id'].',member_ids)')->order('end_time desc')->with(['surveys'])->select();
        $result = to_arr($result);
        $relation_info = $this->RelationModel->alias('relation')
            ->join('survey_user_session session','session.session_id = relation.session_id')
            ->join('survey sur','session.survey_id = sur.id')
            ->field('relation.plan_id,relation.member_id,session.survey_id,sur.title')
            ->where(['sur.survey_type'=>['in',[26,27,28]],'relation.status'=>0,'relation.member_id'=>$this->user['id']])
            ->select();
        $relation_info = to_arr($relation_info);
        $new_data = [];
        foreach ($relation_info as $key => $value){
            $new_data[$value['plan_id']][$value['survey_id']] = $value['member_id'];
        }

        foreach ($result as $k => $v){
            $num = count($v['surveys']);
            $i = 0;
            foreach ($v['surveys'] as $ks => $vs){
                $result[$k]['surveys'][$ks] = array_merge($vs,$survey_rebuild[$vs['survey_id']]);
                if(isset($new_data[$v['id']][$vs['survey_id']])) {
                    $result[$k]['surveys'][$ks]['is_finish'] = 1;
                    $i++;
                }
                if(isset($new_data[$v['id']][$vs['survey_id']])) {
                    $result[$k]['surveys'][$ks]['state'] = '查看报告';
                }elseif(date('Y-m-d H:i:s') >= $v['start_time'] && date('Y-m-d H:i:s') <= $v['end_time']){
                    $result[$k]['surveys'][$ks]['state'] = '开始测评';
                }elseif(date('Y-m-d H:i:s') > $v['end_time']){
                    $result[$k]['surveys'][$ks]['state'] = '已结束';
                }
            }
            $result[$k]['is_finish'] = $num == $i ? 1 : 0;
            if(date('Y-m-d H:i:s') < $v['start_time']){
                $result[$k]['state'] = '暂未开始';
            }elseif(date('Y-m-d H:i:s') >= $v['start_time'] && date('Y-m-d H:i:s') <= $v['end_time']){
                $result[$k]['state'] = '进行中';
            }elseif(date('Y-m-d H:i:s') > $v['end_time']){
                $result[$k]['state'] = '已结束';
            }

        }
        return pageing($result,$pageSize,$pageNumber);
    }

}