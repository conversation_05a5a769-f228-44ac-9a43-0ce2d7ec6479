<?php

namespace App\Models\School\Assessment\Question;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssessmentPsychologyQuestion extends Model
{
    use HasFactory;

    protected $table = 'assessment_psychology_questions';
    
    protected $fillable = [
        'assessment_id',
        'old_question_id',
        'is_normal',
        'content',
        'number',
        'dimension_name',
        'dimension_code',
        'options'
    ];

    protected $casts = [
        'options' => 'json',
        'is_normal' => 'boolean'
    ];

    public $timestamps = false;
}
