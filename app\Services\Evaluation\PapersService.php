<?php

namespace App\Services\Evaluation;

use App\Models\Evaluation\Papers;
use App\Models\Evaluation\Question;
use App\Models\Evaluation\PaperQuestion;
use App\Models\Evaluation\EvaluationAnswer;
use App\Models\Evaluation\EvaluationLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

/**
 * 试卷管理服务类
 */
class PapersService
{
    protected $paperModel;
    protected $questionModel;
    protected $paperQuestionModel;
    protected $evaluationAnswerModel;
    protected $evaluationLogModel;
    protected $user;

    public function __construct(
        Papers $paperModel,
        Question $questionModel,
        PaperQuestion $paperQuestionModel,
        EvaluationAnswer $evaluationAnswerModel,
        EvaluationLog $evaluationLogModel
    ) {
        $this->paperModel = $paperModel;
        $this->questionModel = $questionModel;
        $this->paperQuestionModel = $paperQuestionModel;
        $this->evaluationAnswerModel = $evaluationAnswerModel;
        $this->evaluationLogModel = $evaluationLogModel;
        $this->user = Auth::user();
    }

    /**
     * 获取试卷列表
     * 
     * @param array $params
     * @return array
     */
    public function getPapersList(array $params): array
    {
        if (!empty($params['id'])) {
            $data = $this->paperModel->where('id', $params['id'])->first();
            return $data ? $data->toArray() : [];
        }

        $query = $this->paperModel->query();

        // 应用筛选条件
        $this->applyFilters($query, $params);

        $page = $params['page'] ?? 1;
        $pagesize = $params['pagesize'] ?? 15;

        $total = $query->count();
        $papers = $query->orderBy('id', 'desc')
            ->offset(($page - 1) * $pagesize)
            ->limit($pagesize)
            ->get();

        return [
            'list' => $papers,
            'total' => $total,
            'page' => $page,
            'pagesize' => $pagesize
        ];
    }

    /**
     * 创建试卷
     * 
     * @param array $data
     * @return Papers
     */
    public function createPaper(array $data): Papers
    {
        return DB::transaction(function () use ($data) {
            // 处理题目ID
            $questionIds = $this->processQuestionIds($data['question_ids'] ?? '');
            
            // 计算试卷信息
            $paperInfo = $this->calculatePaperInfo($questionIds);

            $paperData = [
                'paper_name' => $data['paper_name'],
                'course_id' => $data['course_id'],
                'scenario_id' => $data['scenario_id'],
                'grade_id' => $data['grade_id'],
                'question_ids' => implode(',', $questionIds),
                'total_score' => $paperInfo['total_score'],
                'question_count' => $paperInfo['question_count'],
                'has_subjective' => $paperInfo['has_subjective'],
                'description' => $data['description'] ?? '',
                'creator_id' => $this->user->id,
                'school_id' => $this->user->school_id ?? 0,
            ];

            $paper = $this->paperModel->create($paperData);

            // 创建试卷题目关联
            $this->createPaperQuestions($paper->id, $questionIds);

            return $paper;
        });
    }

    /**
     * 更新试卷
     * 
     * @param int $id
     * @param array $data
     * @return Papers
     */
    public function updatePaper(int $id, array $data): Papers
    {
        return DB::transaction(function () use ($id, $data) {
            $paper = $this->paperModel->findOrFail($id);

            // 处理题目ID
            if (isset($data['question_ids'])) {
                $questionIds = $this->processQuestionIds($data['question_ids']);
                $paperInfo = $this->calculatePaperInfo($questionIds);
                
                $data['question_ids'] = implode(',', $questionIds);
                $data['total_score'] = $paperInfo['total_score'];
                $data['question_count'] = $paperInfo['question_count'];
                $data['has_subjective'] = $paperInfo['has_subjective'];

                // 更新试卷题目关联
                $this->updatePaperQuestions($paper->id, $questionIds);
            }

            $paper->update($data);

            return $paper;
        });
    }

    /**
     * 删除试卷
     * 
     * @param int $id
     * @return bool
     */
    public function deletePaper(int $id): bool
    {
        return DB::transaction(function () use ($id) {
            $paper = $this->paperModel->findOrFail($id);

            // 检查是否有答题记录
            $hasAnswers = $this->evaluationAnswerModel->where('paper_id', $id)->exists();
            $hasLogs = $this->evaluationLogModel->where('paper_id', $id)->exists();

            if ($hasAnswers || $hasLogs) {
                throw new \Exception('该试卷已有答题记录，无法删除');
            }

            // 删除试卷题目关联
            $this->paperQuestionModel->where('paper_id', $id)->delete();

            return $paper->delete();
        });
    }

    /**
     * 获取试卷详情
     * 
     * @param int $id
     * @return array
     */
    public function getPaperDetail(int $id): array
    {
        $paper = $this->paperModel->with(['questions.options', 'questions.categoryPortions.category'])
            ->findOrFail($id);

        return $paper->toArray();
    }

    /**
     * 复制试卷
     * 
     * @param array $data
     * @return Papers
     */
    public function copyPaper(array $data): Papers
    {
        return DB::transaction(function () use ($data) {
            $originalPaper = $this->paperModel->findOrFail($data['paper_id']);

            $newPaperData = $originalPaper->toArray();
            unset($newPaperData['id'], $newPaperData['created_at'], $newPaperData['updated_at']);
            
            $newPaperData['paper_name'] = $data['paper_name'] ?? $originalPaper->paper_name . '_副本';
            $newPaperData['creator_id'] = $this->user->id;

            $newPaper = $this->paperModel->create($newPaperData);

            // 复制试卷题目关联
            $originalQuestions = $this->paperQuestionModel->where('paper_id', $originalPaper->id)->get();
            foreach ($originalQuestions as $question) {
                $questionData = $question->toArray();
                unset($questionData['id'], $questionData['created_at'], $questionData['updated_at']);
                $questionData['paper_id'] = $newPaper->id;
                $this->paperQuestionModel->create($questionData);
            }

            return $newPaper;
        });
    }

    /**
     * 试卷预览
     * 
     * @param int $id
     * @return array
     */
    public function previewPaper(int $id): array
    {
        $paper = $this->paperModel->findOrFail($id);
        
        $questions = $this->questionModel
            ->with(['options', 'children.options'])
            ->whereIn('id', explode(',', $paper->question_ids))
            ->orderByRaw("FIELD(id, {$paper->question_ids})")
            ->get();

        return [
            'paper' => $paper->toArray(),
            'questions' => $questions->toArray()
        ];
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 应用筛选条件
     * 
     * @param $query
     * @param array $params
     */
    private function applyFilters($query, array $params): void
    {
        $query->where('status', 0);

        if (isset($this->user->school_id) && $this->user->school_id) {
            $query->where('school_id', $this->user->school_id);
        }

        if (!empty($params['paper_name'])) {
            $query->where('paper_name', 'like', '%' . $params['paper_name'] . '%');
        }

        if (!empty($params['course_id'])) {
            $query->where('course_id', $params['course_id']);
        }

        if (!empty($params['scenario_id'])) {
            $query->where('scenario_id', $params['scenario_id']);
        }

        if (!empty($params['grade_id'])) {
            $query->where('grade_id', $params['grade_id']);
        }

        if (!empty($params['creator_id'])) {
            $query->where('creator_id', $params['creator_id']);
        }
    }

    /**
     * 处理题目ID
     * 
     * @param string $questionIds
     * @return array
     */
    private function processQuestionIds(string $questionIds): array
    {
        if (empty($questionIds)) {
            return [];
        }

        $ids = explode(',', $questionIds);
        return array_filter(array_map('intval', $ids));
    }

    /**
     * 计算试卷信息
     * 
     * @param array $questionIds
     * @return array
     */
    private function calculatePaperInfo(array $questionIds): array
    {
        if (empty($questionIds)) {
            return [
                'total_score' => 0,
                'question_count' => 0,
                'has_subjective' => 0
            ];
        }

        $questions = $this->questionModel->whereIn('id', $questionIds)->get();

        $totalScore = $questions->sum('score');
        $questionCount = $questions->count();
        $hasSubjective = $questions->whereIn('type_id', [3, 4, 5])->count() > 0 ? 1 : 0; // 主观题类型

        return [
            'total_score' => $totalScore,
            'question_count' => $questionCount,
            'has_subjective' => $hasSubjective
        ];
    }

    /**
     * 创建试卷题目关联
     * 
     * @param int $paperId
     * @param array $questionIds
     */
    private function createPaperQuestions(int $paperId, array $questionIds): void
    {
        $questions = $this->questionModel->whereIn('id', $questionIds)->get()->keyBy('id');

        $paperQuestions = [];
        foreach ($questionIds as $index => $questionId) {
            if (isset($questions[$questionId])) {
                $paperQuestions[] = [
                    'paper_id' => $paperId,
                    'question_id' => $questionId,
                    'sort' => $index + 1,
                    'score' => $questions[$questionId]->score,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }

        if (!empty($paperQuestions)) {
            $this->paperQuestionModel->insert($paperQuestions);
        }
    }

    /**
     * 更新试卷题目关联
     * 
     * @param int $paperId
     * @param array $questionIds
     */
    private function updatePaperQuestions(int $paperId, array $questionIds): void
    {
        // 删除原有关联
        $this->paperQuestionModel->where('paper_id', $paperId)->delete();
        
        // 创建新关联
        $this->createPaperQuestions($paperId, $questionIds);
    }
}
