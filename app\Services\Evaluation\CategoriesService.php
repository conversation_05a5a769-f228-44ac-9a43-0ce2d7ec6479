<?php

namespace App\Services\Evaluation;

use App\Models\Evaluation\Categories;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

/**
 * 素养类别管理服务类
 */
class CategoriesService
{
    protected $categoriesModel;
    protected $user;

    public function __construct(Categories $categoriesModel)
    {
        $this->categoriesModel = $categoriesModel;
        $this->user = Auth::user();
    }

    /**
     * 获取类别列表
     * 
     * @param array $params
     * @return array
     */
    public function getCategoriesList(array $params): array
    {
        if (!empty($params['id'])) {
            $data = $this->categoriesModel->where('id', $params['id'])->first();
            return $data ? $data->toArray() : [];
        }

        $query = $this->categoriesModel->query();

        // 应用筛选条件
        $this->applyFilters($query, $params);

        $data = $query->with(['children' => function ($query) use ($params) {
                if (!empty($params['course_id'])) {
                    $query->where('course_id', $params['course_id']);
                }
                $query->where('status', 0);
            }])
            ->select('id', 'category_name', 'parent_id', 'course_id', 'content')
            ->get();

        return $data->toArray();
    }

    /**
     * 创建类别
     * 
     * @param array $data
     * @return Categories
     */
    public function createCategory(array $data): Categories
    {
        $categoryData = [
            'category_name' => $data['category_name'],
            'parent_id' => $data['parent_id'] ?? 0,
            'course_id' => $data['course_id'],
            'content' => $data['content'] ?? '',
            'status' => 0,
        ];

        return $this->categoriesModel->create($categoryData);
    }

    /**
     * 更新类别
     * 
     * @param int $id
     * @param array $data
     * @return Categories
     */
    public function updateCategory(int $id, array $data): Categories
    {
        $category = $this->categoriesModel->findOrFail($id);

        $updateData = [
            'category_name' => $data['category_name'] ?? $category->category_name,
            'parent_id' => $data['parent_id'] ?? $category->parent_id,
            'course_id' => $data['course_id'] ?? $category->course_id,
            'content' => $data['content'] ?? $category->content,

        ];

        $category->update($updateData);

        return $category;
    }

    /**
     * 删除类别（软删除）
     * 
     * @param int $id
     * @return bool
     */
    public function deleteCategory(int $id): bool
    {
        $category = $this->categoriesModel->findOrFail($id);

        // 检查是否有子类别
        $hasChildren = $this->categoriesModel->where('parent_id', $id)->where('status', 0)->exists();
        if ($hasChildren) {
            throw new \Exception('该类别下还有子类别，无法删除');
        }

        // 检查是否被题目使用
        $isUsed = DB::table('evaluation_category_portions')
            ->where('category_id', $id)
            ->exists();
        if ($isUsed) {
            throw new \Exception('该类别已被题目使用，无法删除');
        }

        return $category->update(['status' => -1]);
    }

    /**
     * 获取类别树形结构
     * 
     * @param array $params
     * @return array
     */
    public function getCategoryTree(array $params): array
    {
        $categories = $this->categoriesModel
            ->where('status', 0)
            ->when(!empty($params['course_id']), function ($query) use ($params) {
                $query->where('course_id', $params['course_id']);
            })
            ->orderBy('id')
            ->get()
            ->toArray();

        return $this->buildTree($categories);
    }

    /**
     * 获取指定父级下的子类别
     * 
     * @param int $parentId
     * @param int|null $courseId
     * @return array
     */
    public function getChildrenCategories(int $parentId, ?int $courseId = null): array
    {
        $query = $this->categoriesModel
            ->where('parent_id', $parentId)
            ->where('status', 0);

        if ($courseId) {
            $query->where('course_id', $courseId);
        }

        return $query->orderBy('id')
            ->get()
            ->toArray();
    }

    /**
     * 批量更新排序
     * 注意：由于数据库表中没有 sort 字段，此方法暂时返回 true
     *
     * @param array $data
     * @return bool
     */
    public function updateSort(array $data): bool
    {
        if (empty($data['categories'])) {
            throw new \InvalidArgumentException('排序数据不能为空');
        }

        // 由于数据库表中没有 sort 字段，暂时返回 true
        // 如果需要排序功能，需要先在数据库中添加 sort 字段
        return true;
    }

    /**
     * 移动类别到指定父级
     * 
     * @param int $id
     * @param int $parentId
     * @return bool
     */
    public function moveCategory(int $id, int $parentId): bool
    {
        $category = $this->categoriesModel->findOrFail($id);

        // 检查是否会形成循环引用
        if ($this->wouldCreateCircularReference($id, $parentId)) {
            throw new \Exception('不能移动到自己的子类别下');
        }

        return $category->update(['parent_id' => $parentId]);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 应用筛选条件
     * 
     * @param $query
     * @param array $params
     */
    private function applyFilters($query, array $params): void
    {
        $query->where('status', 0)->where('parent_id', 0);

        if (!empty($params['course_id'])) {
            $query->where('course_id', $params['course_id']);
        }

        if (!empty($params['category_name'])) {
            $query->where('category_name', 'like', '%' . $params['category_name'] . '%');
        }
    }

    /**
     * 构建树形结构
     * 
     * @param array $categories
     * @param int $parentId
     * @return array
     */
    private function buildTree(array $categories, int $parentId = 0): array
    {
        $tree = [];

        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $children = $this->buildTree($categories, $category['id']);
                if (!empty($children)) {
                    $category['children'] = $children;
                }
                $tree[] = $category;
            }
        }

        return $tree;
    }

    /**
     * 检查是否会形成循环引用
     * 
     * @param int $categoryId
     * @param int $parentId
     * @return bool
     */
    private function wouldCreateCircularReference(int $categoryId, int $parentId): bool
    {
        if ($parentId == 0) {
            return false;
        }

        if ($parentId == $categoryId) {
            return true;
        }

        $parent = $this->categoriesModel->find($parentId);
        if (!$parent) {
            return false;
        }

        return $this->wouldCreateCircularReference($categoryId, $parent->parent_id);
    }
}
