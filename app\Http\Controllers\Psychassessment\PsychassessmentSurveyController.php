<?php

namespace App\Http\Controllers\Psychassessment;

use App\Http\Controllers\Controller;
use App\Services\Psychassessment\SurveyService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * 心理评估-测评计划控制器 - 基于原 ThinkPHP Survey 控制器重新实现
 */
class PsychassessmentSurveyController extends Controller
{
    protected $surveyService;

    public function __construct(SurveyService $surveyService)
    {
        $this->surveyService = $surveyService;
    }

    /**
     * 测评计划添加/修改/列表/删除
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function survey(Request $request): JsonResponse
    {
        try {
            $method = $request->method();
            
            switch ($method) {
                case 'POST':
                    // 测评计划添加
                    $data = $this->surveyService->create($request->all(), auth()->user());
                    return $this->success($data, '添加成功');
                    
                case 'PUT':
                    // 测评计划修改
                    $id = $request->route('id') ?: $request->input('id');
                    $data = $this->surveyService->update($id, $request->all(), auth()->user());
                    return $this->success($data, '修改成功');
                    
                case 'GET':
                    // 测评计划列表
                    $data = $this->surveyService->getList($request->all());
                    return $this->success($data, '获取成功');
                    
                case 'DELETE':
                    // 测评计划删除
                    $id = $request->route('id') ?: $request->input('id');
                    $data = $this->surveyService->delete($id);
                    return $this->success($data, '删除成功');
                    
                default:
                    return $this->error('不支持的请求方法');
            }
        } catch (\Exception $e) {
            return $this->error('操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 心理评估列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function assessList(Request $request): JsonResponse
    {
        try {
            $data = $this->surveyService->getAssessList();
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取老师辅导的学生列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPsychassessmentClassMember(Request $request): JsonResponse
    {
        try {
            $data = $this->surveyService->getPsychassessmentClassMember(auth()->user());
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取未完成的学生列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getMemberCompleteStatus(Request $request): JsonResponse
    {
        try {
            $data = $this->surveyService->getMemberCompleteStatus($request->all());
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 学生端测评计划列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudentPsychassess(Request $request): JsonResponse
    {
        try {
            $data = $this->surveyService->getStudentPsychassess(auth()->user());
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取测评计划详情
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getDetail(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $data = $this->surveyService->getDetail($id);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量删除测评计划
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchDelete(Request $request): JsonResponse
    {
        try {
            $ids = $request->input('ids');
            $data = $this->surveyService->batchDelete($ids);
            return $this->success($data, '批量删除成功');
        } catch (\Exception $e) {
            return $this->error('批量删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新测评计划状态
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateStatus(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $status = $request->input('status');
            $data = $this->surveyService->updateStatus($id, $status);
            return $this->success($data, '状态更新成功');
        } catch (\Exception $e) {
            return $this->error('状态更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 复制测评计划
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function copy(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $data = $this->surveyService->copy($id, auth()->user());
            return $this->success($data, '复制成功');
        } catch (\Exception $e) {
            return $this->error('复制失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取测评统计数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStatistics(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $data = $this->surveyService->getStatistics($id);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出测评结果
     * 
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|JsonResponse
     */
    public function export(Request $request)
    {
        try {
            $id = $request->input('id');
            return $this->surveyService->export($id);
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取可用的心理测评类型
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getSurveyTypes(Request $request): JsonResponse
    {
        try {
            $data = $this->surveyService->getSurveyTypes();
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取班级学生列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getClassMembers(Request $request): JsonResponse
    {
        try {
            $classIds = $request->input('class_ids');
            $data = $this->surveyService->getClassMembers($classIds);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }
}
