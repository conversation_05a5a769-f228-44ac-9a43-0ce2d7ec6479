<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2024/02/26
 * Time: 16:13
 */
namespace app\psychassessment\controller;
use think\Config;
use app\common\controller\Authentication;

class Page extends Authentication {
    public function __construct() {

        parent::__construct();
    }
    public function index(){
        return $this->view->fetch('page/index', ['data' =>$this->data]);
    }
    //报告详情页
    public function report_detail(){
        $this->view->engine->layout(false);
        return $this->view->fetch('page/report_detail', ['data' =>$this->data]);
    }

}