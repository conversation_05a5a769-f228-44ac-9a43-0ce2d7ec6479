<?php

namespace App\Services\School\Assessment\Score\Career;

use App\Services\School\Assessment\Score\Career\AbstractScoreService;
use App\Models\School\Assessment\AssessmentTaskAssignment;

/**
 * 学习生活适应性评估服务
 * 
 * 用于计算学生的学习生活适应性评估结果
 */
class AdaptationService extends AbstractScoreService
{
    /**
     * 计算学习生活适应性评估结果
     * 
     * @param array $params 包含评估所需参数的数组
     * @return array 学习生活适应性评估结果数组
     */
    public function calculate($params): array
    {
        $dimension_scores = $this->calculateScores($params);

        return $this->convertToStandardScores(
            $params['assessment_task_assignment_id'],
            $dimension_scores
        );
    }

    /**
     * 将原始分数转换为标准分数
     * 
     * @param int $assessment_task_assignment_id 任务分配ID
     * @param array $dimension_scores 维度分数数组
     * @return array 标准分数结果数组
     */
    private function convertToStandardScores(int $assessment_task_assignment_id, array $dimension_scores): array
    {
        $conversion_table = config('assessment.career.adaptation_raw_to_standard_score_conversion');
        if (!$conversion_table) {
            throw new \Exception('未找到分数转换配置');
        }

        $grade_id = AssessmentTaskAssignment::where('assessment_task_assignments.id', $assessment_task_assignment_id)
            ->join('student_classes', 'student_classes.id', '=', 'assessment_task_assignments.student_class_id')
            ->join('classes', 'classes.id', '=', 'student_classes.class_id')
            ->value('grade_id');

        if (!$grade_id) $grade_id = '10';//非学生用户默认调用高一的转换数据
        
        $dimension_normal_scores = [];
        $total_score = 0;

        foreach ($dimension_scores as $key => $dimension) {
            $dimension_normal_scores[] = [
                'score' => $conversion_table[$grade_id][$key][$dimension['score']] ?? 0,
                'name' => $dimension['name'],
                'code' => $dimension['code']
            ];
            $total_score += $dimension['score'];
        }

        return [
            'dimensions' => $dimension_normal_scores,
            'total_score' => $conversion_table[$grade_id][8][$total_score] ?? 0,
            'dimension_raw' => $dimension_scores,
        ];
    }
}