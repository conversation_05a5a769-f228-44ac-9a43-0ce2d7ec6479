<?php

namespace App\Services\School\System;

use App\Exceptions\BusinessException;
use App\Models\Role;
use App\Models\School\System\Teacher;
use App\Models\User;
use App\Services\BaseService;
use App\Enums\SystemRoleTypeEnum;
use App\Services\UserService;
use Bus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class TeacherService extends BaseService
{
    protected ClassService $classService;
    protected UserService $userService;

    public function __construct(ClassService $classService, UserService $userService)
    {
        $this->classService = $classService;
        $this->userService = $userService;
    }

    /**
     * 构建教师列表查询
     * 
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function listBuilder(Request $request)
    {
        $school_id = $this->classService->getSchoolId($request);
        $school_campus_id = $request->input('school_campus_id');
        $grade_id = $request->input('grade_id');
        $class_id = $request->input('class_id');
        $course_id = $request->input('course_id');
        $teacher_name = $request->input('teacher_name');

        $query = Teacher::where('school_id', $school_id)
            ->when($school_campus_id, fn($query) => $query->where('school_campus_id', $school_campus_id))
            ->when($grade_id, fn($query) => $query->hasByNonDependentSubquery('viewClasses',
                fn($query) => $query->where('grade_id', $grade_id))
            )
            ->when($class_id, fn($query) => $query->hasByNonDependentSubquery('viewClasses',
                fn($query) => $query->where('class_id', $class_id))
            )
//            ->when($course_id, fn($query) => $query->hasByNonDependentSubquery('courses',
//                fn($query) => $query->where('course_id', $course_id))
//            )
            ->when($teacher_name, fn($query) => $query->where('teacher_name', 'like', "%$teacher_name%"))
//            ->with('school:id,name', 'schoolCampus:id,campus_name', 'user:id,username', 'classes.grade', 'viewClasses.grade', 'courses:id,course_name', 'viewCourses:id,course_name')
            ->with('school:id,name', 'schoolCampus:id,campus_name', 'user:id,username', 'viewClasses.grade')
            ->select('teachers.id','teachers.teacher_name','teachers.school_id','teachers.school_campus_id','teachers.user_id',
                'teachers.creator','teachers.updater','teachers.created_at','teachers.updated_at')
        ;
        
        return $query;
    }

    /**
     * 新增教师
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): ?Teacher
    {
        $school_id = $this->classService->getSchoolId($request);
        $school_campus_id = $request->input('school_campus_id');
        $teacher_name = $request->input('teacher_name');
        $username = $request->input('username');


        // 验证用户名是否已存在
        if ($this->isUsernameExists($username)) {
            $this->throwBusinessException('老师账号用户名已存在');
        }

        try {
            DB::beginTransaction();

            $real_name = $request->user()->real_name;
            $organization_id = $request->user()->organization_id;
            
            // 创建用户
            $user = $this->createUser([
                'organization_id' => $organization_id,
                'real_name' => $teacher_name,
                'username' => $username,
                'password' => $request->input('password', '888888'),
                'creator' => $real_name
            ]);
            
            // 保存用户所属角色
            $user->roles()->sync($request->input('roles', []));

            // 创建教师
            $teacher = Teacher::create([
                'user_id' => $user->id,
                'school_id' => $school_id,
                'school_campus_id' => $school_campus_id,
                'teacher_name' => $teacher_name,
                'creator' => $real_name
            ]);

            DB::commit();

            return $teacher;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("新增教师逻辑错误", 500, $e->getMessage());
        }
    }
    
    /**
     * 批量新增教师
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchStore(Request $request): array
    {
        $organization_id = $request->input('organization_id', $request->user()->organization_id);
       
        $school_id = $this->classService->getSchoolId($request);
        $school_campus_id = $request->input('school_campus_id');
        $teacher_list = $request->input('teachers', []);

        if (empty($teacher_list)) {
            $this->throwBusinessException('教师数据不能为空');
        }

        // 取出老师集合的所有账号值
        $username_list = array_filter(array_unique(array_column($teacher_list, 'username')));
        
        // 判断账号是否已存在
        $existing_users = User::whereIn('username', $username_list)->get();
        if ($existing_users->isNotEmpty()) {
            $this->throwBusinessException('导入数据中老师账号在数据库中已存在：'. $existing_users->implode('username', ','));
        }

        // 取出老师集合的所有角色
        $role_name_list = array_filter(array_unique(array_column($teacher_list, 'role_name')));
        
        // 验证角色是否存在
        $valid_roles = $this->validateRoles($organization_id, $role_name_list);
        if (!$valid_roles['valid']) {
            $this->throwBusinessException('导入数据中角色名称在数据库中不存在：' . implode(',', $valid_roles['missing']));
        }

        try {
            DB::beginTransaction();

            $real_name = $request->user()->real_name;
            $teacher_result_list = [];

            foreach ($teacher_list as $teacher) {
                // 创建用户
                $user = $this->createUser([
                    'organization_id' => $organization_id,
                    'real_name' => $teacher['teacher_name'],
                    'username' => $teacher['username'],
                    'password' => '888888',
                    'gender' => isset($teacher['gender']) ? ($teacher['gender'] == '男' ? 1 : 2) : 0,
                    'creator' => $real_name
                ]);

                // 根据角色名称获取角色id并分配
                $role = Role::where('organization_id', $organization_id)
                    ->where('status', 1)
                    ->whereIn('type', [2, 3]) // 角色类型为2教务3老师
                    ->where('name', $teacher['role_name'])
                    ->first();
                
                if ($role) {
                    $user->roles()->sync($role->id);
                }

                // 创建教师
                $teacherResult = Teacher::create([
                    'user_id' => $user->id,
                    'school_id' => $school_id,
                    'school_campus_id' => $school_campus_id,
                    'teacher_name' => $teacher['teacher_name'],
                    'creator' => $real_name
                ]);
                
                $teacher_result_list[] = $teacherResult;
            }

            DB::commit();

            return $teacher_result_list;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("批量新增教师逻辑错误", 500, $e->getMessage());
        }
    }

    /**
     * 更新教师信息
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id): ?Teacher
    {
        $record = Teacher::find($id);
        if (!$record) {
            $this->throwBusinessException('更新对象不存在');
        }

        try {
            DB::beginTransaction();

            $real_name = $request->user()->real_name;
            $user = User::find($record->user_id);
            
            if (!$user) {
                $this->throwBusinessException('教师关联的用户不存在');
            }
            
            // 更新用户信息
            $user->update([
                'real_name' => $request->input('teacher_name'),
                'updater' => $real_name
            ]);

            // 更新角色
            if ($request->has('roles')) {
                $user->roles()->sync($request->input('roles', []));
            }

            // 更新教师信息
            $record->update([
                'teacher_name' => $request->input('teacher_name'),
                'updater' => $real_name
            ]);

            DB::commit();

            return Teacher::find($record->id);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("更新教师信息逻辑错误", 500, $e->getMessage());
        }
    }

    /**
     * 删除教师
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id): void
    {
        $record = Teacher::find($id);
        if (!$record) {
            $this->throwBusinessException('删除对象不存在');
        }

        $user = User::find($record->user_id);
        if (!$user) {
            $this->throwBusinessException('删除老师的账号对象不存在');
        }

        try {
            DB::beginTransaction();

            $record->delete();

            // 删除老师对应的用户
            $this->userService->destroy($request, $record->user_id);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("删除教师", 500, $e->getMessage());
        }
    }

    /**
     * 检查教师姓名是否已存在
     * 
     * @param int $school_id
     * @param int $school_campus_id
     * @param string $teacher_name
     * @return bool
     */
    protected function isTeacherNameExists($school_id, $school_campus_id, $teacher_name)
    {
        return Teacher::where('school_id', $school_id)
            ->where('school_campus_id', $school_campus_id)
            ->where('teacher_name', $teacher_name)
            ->exists();
    }

    /**
     * 检查用户名是否已存在
     * 
     * @param string $username
     * @return bool
     */
    protected function isUsernameExists($username)
    {
        return User::where('username', $username)->exists();
    }

    /**
     * 创建用户
     * 
     * @param array $userData
     * @return User
     */
    protected function createUser(array $userData)
    {
        return User::create([
            'organization_id' => $userData['organization_id'],
            'real_name' => $userData['real_name'],
            'username' => $userData['username'],
            'password' => bcrypt($userData['password'] ?? '888888'),
            'gender' => $userData['gender'] ?? 1,
            'status' => 1,
            'creator' => $userData['creator']
        ]);
    }

    /**
     * 验证角色是否存在
     * 
     * @param int $organization_id
     * @param array $role_names
     * @return array
     */
    protected function validateRoles($organization_id, array $role_names)
    {
        $existing_roles = Role::where('organization_id', $organization_id)
            ->where('status', 1)
            ->whereIn('type', [2, 3]) // 角色类型为2教务3老师
            ->whereIn('name', $role_names)
            ->pluck('name')
            ->toArray();
        
        $missing_roles = array_diff($role_names, $existing_roles);
        
        return [
            'valid' => count($missing_roles) === 0,
            'missing' => $missing_roles
        ];
    }



    /**
     * 批量更新教师（如果需要的话）
     *
     * @param Request $request
     * @return array
     */
    public function batchUpdate(Request $request): array
    {
        $teacher_ids = $request->input('teacher_ids', []);
        $update_data = $request->input('update_data', []);

        if (empty($teacher_ids)) {
            $this->throwBusinessException('教师ID列表不能为空');
        }

        try {
            DB::beginTransaction();

            $real_name = $request->user()->real_name;
            $updated_teachers = [];

            foreach ($teacher_ids as $teacher_id) {
                $teacher = Teacher::find($teacher_id);
                if (!$teacher) {
                    continue;
                }

                $user = User::find($teacher->user_id);
                if (!$user) {
                    continue;
                }

                // 更新用户信息
                if (isset($update_data['teacher_name'])) {
                    $user->update([
                        'real_name' => $update_data['teacher_name'],
                        'updater' => $real_name
                    ]);
                }

                // 更新角色
                if (isset($update_data['roles'])) {
                    $user->roles()->sync($update_data['roles']);
                }

                // 更新教师信息
                if (isset($update_data['teacher_name'])) {
                    $teacher->update([
                        'teacher_name' => $update_data['teacher_name'],
                        'updater' => $real_name
                    ]);
                }

                $updated_teachers[] = $teacher;
            }

            DB::commit();

            return $updated_teachers;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("批量更新教师逻辑错误", 500, $e->getMessage());
        }
    }




}
