<?php

namespace App\Http\Middleware;

use App\Models\Permission;
use App\Services\Admin\UserAccessLogService;
use App\Traits\ApiResponse;
use Closure;
use Illuminate\Http\Request;

class StoreAccessLog
{
    use ApiResponse;
    const  EXPIRE_DATE = 14;

    protected $userAccessLogService;

    // 构造函数注入 UserAccessLogService
    public function __construct(UserAccessLogService $userAccessLogService)
    {
        $this->userAccessLogService = $userAccessLogService;
    }

    public function handle(Request $request, Closure $next)
    {
        $routeName = $request->route()->getName();
        // 这里记录用户访问日志，只记录配置权限的访问日志。
//        if (!app()->environment('local')) {
//            $permission = Permission::where('name', $routeName)->first();
//            $this->userAccessLogService->store($request, $permission);
//        }
        $permission = new Permission();
        $permission->name = $routeName;
        $this->userAccessLogService->store($request, $permission);

        return $next($request);

    }
}
