<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 *
 *
 * @property int $id
 * @property string $name 角色名称
 * @property int $organization_id 机构id
 * @property string $guard_name
 * @property int $type 角色类型1学生2教务3老师
 * 4教育局
 * 5家长
 * 999管理员
 * @property int $status 状态1启用2禁用
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $creator 添加人
 * @property string $updater 最后更新人
 * @method static \Illuminate\Database\Eloquent\Builder|Role newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Role newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Role query()
 * @method static \Illuminate\Database\Eloquent\Builder|Role whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Role whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Role whereGuardName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Role whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Role whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Role whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Role whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Role whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Role whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Role whereUpdater($value)
 * @mixin \Eloquent
 */
class Role extends BaseModel
{
    protected $hidden = [
        'pivot',
        'role_id'
    ];

    public function users(): BelongsToMany
    {
        return $this->morphedByMany(
            "App\Models\User",
            'model',
            'model_has_roles',
            'role_id',
            'model_id'
        );
    }

    public function model()
    {
        return $this->morphTo();
    }
}
