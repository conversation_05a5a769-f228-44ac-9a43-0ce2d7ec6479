<?php

namespace App\Traits;

use Symfony\Component\HttpFoundation\Response as FoundationResponse;
use Response;

trait ApiResponse
{
    /**
     * @var int
     */
    protected int $statusCode = FoundationResponse::HTTP_OK;

    /**
     * @return mixed
     */
    private function getStatusCode()
    {
        return $this->statusCode;
    }

    /**
     * @param $statusCode
     * @return $this
     */
    private function setStatusCode($statusCode): static
    {
        $this->statusCode = $statusCode;
        return $this;
    }

    /**
     * @param $data
     * @param array $header
     * @return mixed
     */
    private function respond($data, $header = [])
    {
        return Response::json($data, $this->getStatusCode(), $header);
    }

    /**
     * @param $status
     * @param array $data
     * @param int|null $code
     * @return mixed
     */
    private function status($status, array $data, $code = null)
    {
        if ($code) {
            $this->setStatusCode($code);
        }
        $status = [
            'status' => $status,
            'code' => $this->statusCode
        ];

        $data = array_merge($status, $data);
        return $this->respond($data);
    }

    /**
     * 返回成功获取数据
     * 
     * @param $data
     * @param string $message
     * @return mixed
     */
    public function success($data, $message = "操作成功")
    {
        return $this->status("success", compact('data', 'message'));
    }

    /**
     * 返回分页数据
     *
     * @param $list 数据列表
     * @param $cnt 数据总数
     * @param string $message
     * @return mixed
     */
    public function paginateSuccess($list, $cnt, $message = "操作成功")
    {
        $data = [
            'list' => $list,
            'cnt' => $cnt
        ];
        return $this->success($data, $message);
    }

    /**
     * 返回错误信息
     *
     * @param $message
     * @param int $code
     * @return mixed
     */
    public function error($message, int $code = FoundationResponse::HTTP_INTERNAL_SERVER_ERROR)
    {
        return $this->status('error', [
            'message' => $message,
            'code' => $code
        ]);
    }
    
    /**
     * 返回消息
     *
     * @param $message
     * @param int $code
     * @param string $status
     * @return mixed
     */
    public function message($message, int $code = FoundationResponse::HTTP_OK, string $status = "success")
    {
        return $this->status($status, [
            'message' => $message,
            'code' => $code
        ]);
    }
    
    /**
     * 返回404错误
     *
     * @param string $message
     * @return mixed
     */
    public function notFound(string $message = 'Not Found!')
    {
        return $this->error($message, Foundationresponse::HTTP_NOT_FOUND);
    }
    
    /**
     * 授权成功
     *
     * @param $data
     * @param int $code
     * @param string $status
     * @return mixed
     */
    public function oauthSuccess($data, int $code = FoundationResponse::HTTP_OK, string $status = "success")
    {
        return $this->status($status, $data, $code);
    }
}
