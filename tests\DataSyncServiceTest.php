<?php

/**
 * 数据同步服务测试脚本
 * 
 * 用于测试DataSyncService与各个专门同步服务的集成
 */

// 模拟Laravel环境
require_once __DIR__ . '/../vendor/autoload.php';

class DataSyncServiceTest
{
    private $dataSyncService;

    public function __construct()
    {
        echo "=== 数据同步服务测试开始 ===\n\n";
        
        try {
            // 初始化DataSyncService
            $this->dataSyncService = app(\App\Services\DataSync\DataSyncService::class);
            echo "✅ DataSyncService 初始化成功\n";
        } catch (\Exception $e) {
            echo "❌ DataSyncService 初始化失败: " . $e->getMessage() . "\n";
            return;
        }
    }

    /**
     * 测试服务状态
     */
    public function testServicesStatus()
    {
        echo "\n--- 测试服务状态 ---\n";
        
        try {
            $status = $this->dataSyncService->getServicesStatus();
            
            echo "服务状态检查结果:\n";
            foreach ($status as $key => $value) {
                if ($key === 'message') {
                    echo "消息: {$value}\n";
                } else {
                    $statusText = $value ? '✅ 可用' : '❌ 不可用';
                    echo "{$key}: {$statusText}\n";
                }
            }
            
            return true;
        } catch (\Exception $e) {
            echo "❌ 服务状态检查失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试学校同步方法
     */
    public function testSchoolSyncMethods()
    {
        echo "\n--- 测试学校同步方法 ---\n";
        
        $methods = [
            'syncSchool' => '同步学校数据',
            'syncBatchSchools' => '批量同步学校数据',
            'getCampusInfo' => '获取校区信息'
        ];
        
        foreach ($methods as $method => $description) {
            if (method_exists($this->dataSyncService, $method)) {
                echo "✅ {$description} ({$method}) - 方法存在\n";
            } else {
                echo "❌ {$description} ({$method}) - 方法不存在\n";
            }
        }
    }

    /**
     * 测试班级同步方法
     */
    public function testClassSyncMethods()
    {
        echo "\n--- 测试班级同步方法 ---\n";
        
        $methods = [
            'syncClass' => '同步班级数据',
            'syncBatchClasses' => '批量同步班级数据'
        ];
        
        foreach ($methods as $method => $description) {
            if (method_exists($this->dataSyncService, $method)) {
                echo "✅ {$description} ({$method}) - 方法存在\n";
            } else {
                echo "❌ {$description} ({$method}) - 方法不存在\n";
            }
        }
    }

    /**
     * 测试教师同步方法
     */
    public function testTeacherSyncMethods()
    {
        echo "\n--- 测试教师同步方法 ---\n";
        
        $methods = [
            'syncSingleTeacher' => '同步单个教师数据',
            'syncTeacher' => '同步教师数据到ysy_member和ysy_teacher表',
            'syncBatchTeachers' => '批量同步教师数据',
            'syncBatchTeachersUpdate' => '批量同步教师更新',
            'syncSingleTeacherUpdate' => '同步单个教师更新'
        ];
        
        foreach ($methods as $method => $description) {
            if (method_exists($this->dataSyncService, $method)) {
                echo "✅ {$description} ({$method}) - 方法存在\n";
            } else {
                echo "❌ {$description} ({$method}) - 方法不存在\n";
            }
        }
    }

    /**
     * 测试学生同步方法
     */
    public function testStudentSyncMethods()
    {
        echo "\n--- 测试学生同步方法 ---\n";
        
        $methods = [
            'syncSingleStudent' => '同步单个学生数据',
            'syncStudent' => '同步学生数据到ysy_member和ysy_student表',
            'syncBatchStudents' => '批量同步学生数据'
        ];
        
        foreach ($methods as $method => $description) {
            if (method_exists($this->dataSyncService, $method)) {
                echo "✅ {$description} ({$method}) - 方法存在\n";
            } else {
                echo "❌ {$description} ({$method}) - 方法不存在\n";
            }
        }
    }

    /**
     * 测试通用方法
     */
    public function testCommonMethods()
    {
        echo "\n--- 测试通用方法 ---\n";
        
        $methods = [
            'getServicesStatus' => '获取所有同步服务的状态',
            'logSyncOperation' => '记录同步操作日志'
        ];
        
        foreach ($methods as $method => $description) {
            if (method_exists($this->dataSyncService, $method)) {
                echo "✅ {$description} ({$method}) - 方法存在\n";
            } else {
                echo "❌ {$description} ({$method}) - 方法不存在\n";
            }
        }
    }

    /**
     * 测试依赖注入
     */
    public function testDependencyInjection()
    {
        echo "\n--- 测试依赖注入 ---\n";
        
        try {
            $reflection = new \ReflectionClass($this->dataSyncService);
            $properties = $reflection->getProperties();
            
            $expectedServices = [
                'schoolSyncService' => 'SchoolSyncService',
                'classSyncService' => 'ClassSyncService', 
                'teacherSyncService' => 'TeacherSyncService',
                'studentSyncService' => 'StudentSyncService'
            ];
            
            foreach ($expectedServices as $property => $serviceName) {
                if ($reflection->hasProperty($property)) {
                    echo "✅ {$serviceName} 依赖注入成功\n";
                } else {
                    echo "❌ {$serviceName} 依赖注入失败\n";
                }
            }
            
        } catch (\Exception $e) {
            echo "❌ 依赖注入测试失败: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        $this->testServicesStatus();
        $this->testDependencyInjection();
        $this->testSchoolSyncMethods();
        $this->testClassSyncMethods();
        $this->testTeacherSyncMethods();
        $this->testStudentSyncMethods();
        $this->testCommonMethods();
        
        echo "\n=== 数据同步服务测试完成 ===\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new DataSyncServiceTest();
    $test->runAllTests();
}
