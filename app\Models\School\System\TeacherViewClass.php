<?php

namespace App\Models\School\System;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;

class TeacherViewClass extends BaseModel
{
    use SoftDeletes;

    protected $table = 'teacher_view_classes';

    protected $fillable = [
        'teacher_id',
        'class_id',
        'school_year',
        'creator',
        'updater'
    ];

    // 关联教师
    public function teacher()
    {
        return $this->belongsTo(Teacher::class, 'teacher_id', 'id');
    }

    // 关联班级
    public function class()
    {
        return $this->belongsTo(Claass::class, 'class_id', 'id');
    }
}