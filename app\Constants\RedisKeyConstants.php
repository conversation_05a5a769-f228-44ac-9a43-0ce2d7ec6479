<?php

namespace App\Constants;

/*
|--------------------------------------------------------------------------
| 公用缓存键常量库
|--------------------------------------------------------------------------
|
| This script returns the application instance. The instance is given to
| the calling script we can separate the building of the instances
| from the actual running of the application and sending responses.
|
*/

// 通用缓存键前缀
class RedisKeyConstants
{
    /**
     * 系统相关键组
     */
    public const SYSTEM = [
        'MAX_ERROR_COUNT' => 3, // 最大登录错误次数
        'LOGIN_ERROR_COUNT' => 'login:error_count', // 登录错误次数
        'LOGIN_LOCK_ACCOUNT' => 'login:lock_account', // 锁定账号
        'ROLE_MENUS' => 'role_menus', // 角色菜单
        'ORG_MENUS' => 'org_menus', // 机构已购买拥有的菜单
    ];

    /**
     * 后台管理端相关键组
     */
    public const ADMIN = [
        'BASE_MENUS' => 'admin_base_menus',
        // 其他后台管理键...
    ];

    /**
     * 教育局管端相关键组
     */
    public const PARTNER = [
        // 其他教育局管理键...
    ];

    /**
     * 学校端相关键组
     */
    public const SCHOOL = [
        // 其他学校管理键...
        'TEACHER_VIEW_CLASSES' => 'teacher_view_classes', // 教师可查看班级数据
        'CURRENT_SCHOOL_YEAR' => 'current_school_year', // 当前学年
    ];
}




