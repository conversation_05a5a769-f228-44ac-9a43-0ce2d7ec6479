# 教师同步功能集成说明

## 概述

根据您的要求，我已经将教师同步功能重构为专门的同步接口模式，类似于学校同步的实现方式。TeacherController负责传参，专门的DataSyncController负责处理同步逻辑。

## 架构设计

### 1. 职责分离
- **TeacherController**: 负责教师业务逻辑和参数传递
- **DataSyncService**: 负责专门的同步逻辑处理
- **同步方法**: `DataSyncService::syncTeacherToYsy()`

### 2. 调用流程
```
TeacherController (创建/批量创建教师)
    ↓
准备教师数据
    ↓
调用 DataSyncService::syncTeacherToYsy()
    ↓
同步到 ysy_member 和 ysy_teacher 表
```

## 实现详情

### 1. TeacherController 修改

#### store方法 (单个教师创建)
```php
public function store(Request $request)
{
    // 创建教师
    $teacher = $this->teacherService->store($request);
    
    // 调用同步接口
    try {
        $this->syncSingleTeacher($teacher);
    } catch (\Exception $e) {
        Log::warning('教师创建后同步失败', [
            'teacher_id' => $teacher->id ?? null,
            'error' => $e->getMessage()
        ]);
    }
    
    return $this->message('创建成功');
}
```

#### batchStore方法 (批量教师创建)
```php
public function batchStore(Request $request)
{
    // 批量创建教师
    $created_teachers = $this->teacherService->batchStore($request);
    
    // 调用同步接口（批量创建后同步）
    try {
        if (!empty($created_teachers)) {
            $this->syncBatchTeachers($created_teachers);
        }
    } catch (\Exception $e) {
        Log::warning('批量教师创建后同步失败', [
            'error' => $e->getMessage()
        ]);
    }
    
    return $this->success($created_teachers, '批量创建成功');
}
```

#### 新增的同步方法
- `syncSingleTeacher($teacher)`: 同步单个教师
- `syncBatchTeachers(array $teachers)`: 批量同步教师

### 2. DataSyncController 增强

#### syncTeacher方法支持两种调用模式

**模式1: 批量同步（从TeacherController调用）**
```php
POST /api/admin/datasync/sync-teacher
{
    "teachers_data": [
        {
            "id": 1,
            "user_id": 10,
            "teacher_name": "张老师",
            "username": "teacher001",
            "password": "encrypted_password",
            "phone": "13800138000",
            "gender": 1,
            "school_id": 1,
            "school_campus_id": 1,
            "is_psychology_teacher": 0,
            "roles": [3]
        }
    ]
}
```

**模式2: 单个同步（直接API调用）**
```php
POST /api/admin/datasync/sync-teacher
{
    "id": 1,
    "name": "张老师",
    "teacher_number": "T001",
    "gender": 1,
    "phone": "13800138000",
    "email": "<EMAIL>",
    "school_id": 1,
    "campus_id": 1,
    "department": "数学组",
    "position": "教师",
    "subject": "数学",
    "status": 1
}
```

#### 新增的syncTeachersData方法
专门处理批量教师数据同步到ysy_member和ysy_teacher表的逻辑。

### 3. 同步逻辑

#### 角色判断逻辑
```php
$rolesTypes = $teacherData['roles'];

// 所有教师都同步到ysy_member表
$sync_connection->table('ysy_member')->updateOrInsert(
    ['id' => $teacherData['user_id']],
    $member_data
);

// 如果角色类型包含教师(type=3)，同步到ysy_teacher表
if (in_array(3, $rolesTypes)) {
    $sync_connection->table('ysy_teacher')->updateOrInsert(
        ['id' => $teacherData['id']],
        $teacher_data
    );
}
```

#### 数据映射

**ysy_member表字段映射:**
- `id` → `user_id`
- `name` → `teacher_name`
- `username` → `username`
- `password` → `password` (已加密)
- `mobile` → `phone`
- `gender` → `gender` (1男2女)
- `school_id` → `school_id`
- `role_id` → `'0,' + roles.join(',') + ',0'`

**ysy_teacher表字段映射:**
- `id` → `teacher.id`
- `member_id` → `user_id`
- `name` → `teacher_name`
- `school_id` → `school_id`
- `school_district` → `school_campus_id`
- `is_psych` → `is_psychology_teacher`

## 使用示例

### 1. 创建单个教师（自动同步）
```bash
POST /api/school/system/teacher
{
    "school_campus_id": 1,
    "teacher_name": "张老师",
    "username": "teacher001",
    "password": "123456",
    "roles": [3]
}
```

### 2. 批量创建教师（自动同步）
```bash
POST /api/school/system/teacher/batch
{
    "school_campus_id": 1,
    "teachers": [
        {
            "teacher_name": "李老师",
            "username": "teacher002",
            "role_name": "教师"
        },
        {
            "teacher_name": "王老师",
            "username": "teacher003",
            "role_name": "教务"
        }
    ]
}
```

### 3. 手动批量同步已有教师
```bash
POST /api/school/system/teacher/sync
{
    "teacher_ids": [1, 2, 3, 4, 5]
}
```

## 优势

1. **职责分离**: 业务逻辑和同步逻辑分离
2. **统一接口**: 所有同步都通过专门的同步接口处理
3. **灵活性**: 支持创建时自动同步和手动批量同步
4. **错误隔离**: 同步失败不影响教师创建
5. **日志记录**: 完整的同步日志记录
6. **事务保证**: 同步过程使用数据库事务

## 注意事项

1. **异步处理**: 同步调用是异步的，不会阻塞教师创建流程
2. **错误处理**: 同步失败只记录日志，不影响主业务
3. **数据一致性**: 使用数据库事务保证同步数据的一致性
4. **角色判断**: 根据用户实际角色智能判断同步策略
