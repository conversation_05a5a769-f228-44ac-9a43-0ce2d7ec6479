<?php
namespace App\Services\School\Assessment\IndividualReport\Competency;

use App\Services\School\Assessment\IndividualReport\AbstractIndividualReportService;
use App\Models\School\Assessment\Question\AssessmentCompetencyQuestion;
use App\Models\School\Assessment\Answer\AssessmentCompetencyAnswer;

/**
 * 能力评估个人报告抽象服务类
 * 
 * 该类为各种能力评估个人报告提供基础功能，包括维度分析、等级计算和改进建议
 */
abstract class AbstractCompetencyReportService extends AbstractIndividualReportService
{
    /**
     * 问题得分阈值，高于此值视为高分题
     * 
     * @var int
     */
    protected const SCORE_THRESHOLD = 3;
    
    /**
     * 默认低分阈值
     * 
     * @var float
     */
    protected const DEFAULT_LOW_THRESHOLD = 6;
    
    /**
     * 默认高分阈值
     * 
     * @var float
     */
    protected const DEFAULT_HIGH_THRESHOLD = 8;
    
    /**
     * 需要改进的等级列表
     * 
     * @var array
     */
    protected const LOW_LEVELS = ['L1', 'L2'];
    
    /**
     * 配置信息
     * 
     * @var array
     */
    protected array $config;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->config = config('assessment.competency.' . $this->getConfigKey());
    }

    /**
     * 获取配置键名
     * 
     * @return string 配置键名
     */
    abstract protected function getConfigKey(): string;
    
    /**
     * 获取改进指数限制
     * 
     * @return int 改进指数限制
     */
    abstract protected function getImprovementIndexLimit(): int;

    /**
     * 生成个人报告
     * 
     * @param array $params 包含测评任务分配ID和测评ID的参数数组
     * @return array 个人报告数据
     */
    public function generateReport(array $params): array
    {
        $assignment_id = $params['assessment_task_assignment_id'];
        $assignment_info = $this->getAssignmentInfo($assignment_id);
        $standard_results = $assignment_info['standard_results'];
        $dimension_scores = $this->extractDimensionScores($standard_results);
        $assessment_info = $this->generateCompetencyReport($dimension_scores, $params);
        return array_merge($assignment_info, $assessment_info);
    }

    /**
     * 从标准结果中提取维度分数
     * 
     * @param array|string $standardResults 标准结果数据
     * @return array 维度分数数组
     */
    protected function extractDimensionScores($standard_results): array
    {
        if (is_string($standard_results)) {
            $standard_results = json_decode($standard_results, true);
        }
        $dimensions = $standard_results['dimensions'] ?? [];
        $dimension_scores = [];
        foreach ($dimensions as $dimension) {
            $dimension_scores[] = $dimension['score'];
        }
        $dimension_scores[] = $standard_results['total_score'];
        return $dimension_scores;
    }

    /**
     * 生成能力评估报告
     * 
     * @param array $dimensionScores 维度分数数组
     * @param array $params 参数数组
     * @return array 报告内容
     */
    protected function generateCompetencyReport(array $dimensionScores, array $params): array
    {
        // 是否需要获取问题和答案
        if ($this->shouldProcessQuestions()) {
            $questions = $this->getQuestions($params['assessment_id']);
            $answers = $this->getAnswers($params);
            $answerScores = $this->calculateScores($questions, $answers);
            
            return [
                'content' => $this->processReportContent($dimensionScores, $answerScores)
            ];
        }
        
        return [
            'content' => $this->processReportContent($dimensionScores)
        ];
    }

    /**
     * 是否需要处理问题和答案
     * 
     * @return bool 是否需要处理
     */
    protected function shouldProcessQuestions(): bool
    {
        return true;
    }

    /**
     * 处理报告内容
     * 
     * @param array $dimensionScores 维度分数数组
     * @param array|null $answerScores 答案分数数组
     * @return array 处理后的报告内容
     */
    protected function processReportContent(array $dimension_scores, ?array $answer_scores = null): array
    {
        $content = [];
        $improvement_areas = [];
        $categories = $this->config['category'];

        foreach($categories as $index => $category){
            $high_score_questions = [];
            $low_score_questions = [];
            
            if ($answer_scores) {
                [$high_score_questions, $low_score_questions] = $this->getDimensionQuestionsByScore($answer_scores, $category['name']);
            }
            
            $level_info = $this->calculateLevel($dimension_scores[$index], $index, $category['low'], $category['high']);

            if($this->shouldImprove($index, $level_info[0])) {
                $improvement_areas[] = $category['name'];
            }

            $content[$index] = $this->formatCategoryContent(
                $category,
                $dimension_scores[$index],
                $level_info,
                $high_score_questions,
                $low_score_questions,
                $improvement_areas,
                $index
            );
        }

        return $content;
    }

    /**
     * 获取维度的高分题和低分题
     * 
     * @param array $answerScores 答案分数数组
     * @param string $dimensionName 维度名称
     * @return array 包含高分题和低分题的数组
     */
    protected function getDimensionQuestionsByScore(array $answer_scores, string $dimension_name): array
    {
        $high_score_questions = [];
        $low_score_questions = [];

        foreach ($answer_scores as $answer) {
            if ($dimension_name === $answer['dimension_name']) {
                if($answer['score'] > static::SCORE_THRESHOLD){
                    $high_score_questions[] = $answer['content'];
                } else {
                    $low_score_questions[] = $answer['content'];
                }
            }
        }

        return [$high_score_questions, $low_score_questions];
    }

    /**
     * 计算维度等级
     * 
     * @param float $score 分数
     * @param int $levelType 等级类型
     * @param float $low 低分阈值
     * @param float $high 高分阈值
     * @return array 等级信息数组
     */
    protected function calculateLevel(
        float $score, 
        int $levelType, 
        float $low = self::DEFAULT_LOW_THRESHOLD, 
        float $high = self::DEFAULT_HIGH_THRESHOLD
    ): array {
        if($score < $low){
            return $this->config[$levelType][0];
        }
        if($score < $high){
            return $this->config[$levelType][1];
        }
        return $this->config[$levelType][2];
    }

    /**
     * 判断是否需要改进
     * 
     * @param int $index 维度索引
     * @param string $level 等级
     * @return bool 是否需要改进
     */
    protected function shouldImprove(int $index, string $level): bool
    {
        return $index < $this->getImprovementIndexLimit() && in_array($level, static::LOW_LEVELS);
    }

    /**
     * 格式化分类内容
     * 
     * @param array $category 分类信息
     * @param float $score 分数
     * @param array $levelInfo 等级信息
     * @param array $highScores 高分题目
     * @param array $lowScores 低分题目
     * @param array $improvementAreas 需要改进的领域
     * @param int $index 索引
     * @return array 格式化后的分类内容
     */
    protected function formatCategoryContent(
        array $category,
        float $score,
        array $level_info,
        array $high_scores,
        array $low_scores,
        array $improvement_areas,
        int $index
    ): array {
        $content = [
            'name' => $category['name'],
            'score' => $score,
            'level' => $level_info[0],
            'level_desc' => $level_info[1],
            'level_content' => $this->formatLevelContent($level_info[2], $improvement_areas, $index, $level_info[0]),
            'advice' => $level_info[3],
        ];
        
        if (!empty($high_scores)) {
            $content['high_problem'] = $high_scores;
        }
        
        if (!empty($low_scores)) {
            $content['low_problem'] = $low_scores;
        }
        
        return $content;
    }

    /**
     * 格式化等级内容
     * 
     * @param string $content 内容
     * @param array $improvementAreas 需要改进的领域
     * @param int $index 索引
     * @param string $level 等级
     * @return string 格式化后的等级内容
     */
    protected function formatLevelContent(string $content, array $improvement_areas, int $index, string $level): string
    {
        if ($improvement_areas && $index == $this->getImprovementIndexLimit() && in_array($level, static::LOW_LEVELS)) {
            return sprintf($content, implode(',', $improvement_areas));
        }
        return $content;
    }

    /**
     * 获取问题列表
     * 
     * @param int $assessmentId 测评ID
     * @return array 问题列表
     */
    protected function getQuestions(int $assessment_id): array
    {
        $questions = AssessmentCompetencyQuestion::where('assessment_id', $assessment_id)
            ->select('id', 'content', 'options', 'correct', 'dimension_code', 'dimension_name')
            ->get()
            ->toArray();
        return array_column($questions, null, 'id');
    }
    
    /**
     * 获取答案列表
     * 
     * @param array $params 参数数组
     * @return array 答案列表
     */
    protected function getAnswers(array $params): array
    {
        return AssessmentCompetencyAnswer::where('assessment_task_assignment_id', $params['assessment_task_assignment_id'])
            ->where('assessment_id', $params['assessment_id'])
            ->orderBy('assessment_competency_question_id')
            ->select('answer', 'assessment_competency_question_id')
            ->get()
            ->toArray();
    }

    /**
     * 计算分数
     * 
     * @param array $questions 问题列表
     * @param array $answers 答案列表
     * @return array 计算后的分数
     */
    public function calculateScores(array $questions, array $answers): array
    {
        foreach ($answers as $key => $answer) {
            $question_id = $answer['assessment_competency_question_id'];
            $options = $questions[$question_id]['options'];
            $score = 0;
            if($questions[$question_id]['correct']){
                $correct_strlen = strlen($questions[$question_id]['correct']);
                if($correct_strlen == 1 && $answer['answer'] == $questions[$question_id]['correct']){
                    $score = 1;
                }elseif($correct_strlen > 1){
                    $score = $this->calculateMultiChoiceScore($answer['answer'], $questions[$question_id]['correct']);
                }
            }else{
                $score = $options[$answer['answer']]['score'];
            }
            $answers[$key] = array_merge($answer, [
                'score' => $score,
                'dimension_name' => $questions[$question_id]['dimension_name'],
                'content' => $questions[$question_id]['content']
            ]);
        }
        return $answers;
    }

    /**
     * 计算多选题分数
     */
    private function calculateMultiChoiceScore(string $answer, string $correct_answer): int
    {
        $answer_arr = str_split($answer);
        $score = 0;

        foreach ($answer_arr as $selected_option) {
            if (strpos($correct_answer, $selected_option) === false) {
                return 0;
            }
            $score++;
        }

        return $score;
    }

}