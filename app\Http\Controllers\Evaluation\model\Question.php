<?php
/**
 * 题库模型
 */

namespace app\evaluation\model;

use think\Db;

class Question extends BaseModel
{
    protected $table = 'ysy_evaluation_questions';

    public function children()
    {
        return $this->hasMany('Question', 'parent_id', 'id')->where('status', 0)->order('sort,id');
    }

    public function typeStr()
    {
        return $this->hasOne('QuestionType', 'id', 'type_id')->where('status', 0);
    }


    public function getCategoryIdsAttr($value)
    {
        $arr = explode(',', $value);
        if (!empty($arr)) {
            return Db::name('evaluation_category')->field('id,category_name,parent_id')->whereIn('id', $arr)->select();
        } else {
            return [];
        }
    }

    public function optionList()
    {
        return $this->hasMany('QuestionOption', 'question_id', 'id')->where('status', 0);
    }

    public function getKnowlegeIdsAttr($value)
    {
        $arr = explode(',', $value);
        if (!empty($arr)) {
            return Db::name('evaluation_knowledges')->field('id,name,parent_id')->whereIn('id', $arr)->select();
        } else {
            return [];
        }
    }

    public function categoryPortion()
    {
        return $this->hasMany('CategoryPortion', 'question_id', 'id')->field('id,parent_id,name,question_id,category_id,percentage')->where('status', 0);
    }
}