<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 16:01
 */

namespace app\evaluation\service;

class Report
{
    protected $ReportLogic;

    public function __construct()
    {
        $this->user        = get_user();
        $this->ReportLogic = new \app\evaluation\logic\Report();
    }

    public function personal_report()
    {
        $member_id = input('member_id') ?? $this->user['id'];
        $distribution_id = input('distribution_id');
        $data = $this->ReportLogic->personal_report($member_id,$distribution_id);
        return $data;
    }

    public function settlement()
    {
        $data = $this->ReportLogic->settlement();
        return $data;
    }

    public function all_settlement()
    {
        $data = $this->ReportLogic->all_settlement();
        return $data;
    }

}