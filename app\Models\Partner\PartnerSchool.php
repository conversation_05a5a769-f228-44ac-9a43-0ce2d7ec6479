<?php

namespace App\Models\Partner;

use App\Models\BaseModel;
use App\Models\School\System\School;
use App\Models\School\System\SchoolCampus;

class PartnerSchool extends BaseModel
{

    // 归属于伙伴
    public function partner()
    {
        return $this->belongsTo(Partner::class, 'partner_id', 'id');
    }

    // 归属于学校
    public function school()
    {
        return $this->belongsTo(School::class, 'school_id', 'id')->where('status', 1);
    }

    // 归属于校区
    public function schoolCampus()
    {
        return $this->belongsTo(SchoolCampus::class, 'school_campus_id', 'id')->where('status', 1);
    }

    /**
     * 批量插入伙伴学校数据，避免重复插入
     *
     * @param array $data
     * @return int 插入的记录数量
     */
    public static function batchInsertIfNotExists(array $data): int
    {
        if (empty($data)) {
            return 0;
        }

        // 提取 partner_id, school_id, school_campus_id 组合用于查重
        $schoolIds = array_column($data, 'school_id');
        $partnerIds = array_column($data, 'partner_id');
        $campusIds = array_column($data, 'school_campus_id');


        // 查询已存在的记录
        $existingRecords = self::whereIn('partner_id', $partnerIds)
            ->whereIn('school_id', $schoolIds)
            ->whereIn('school_campus_id', $campusIds)
            ->get(['partner_id', 'school_id', 'school_campus_id'])
            ->keyBy(fn($item) => "{$item->partner_id}-{$item->school_id}-{$item->school_campus_id}");

        // 过滤出不存在的记录
        $filteredData = array_filter($data, function ($item) use ($existingRecords) {
            $key = "{$item['partner_id']}-{$item['school_id']}-{$item['school_campus_id']}";
            return !isset($existingRecords[$key]);
        });

        // 批量插入新数据
        if (!empty($filteredData)) {
            self::insert($filteredData);
        }

        return count($filteredData);
    }

}
