<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 数据同步配置
    |--------------------------------------------------------------------------
    |
    | 此配置文件用于配置数据同步功能的各项参数
    |
    */

    // 是否启用数据同步
    'enabled' => env('DATA_SYNC_ENABLED', false),

    // 同步数据库连接名称
    'sync_connection' => env('DATA_SYNC_CONNECTION', 'sync_mysql'),

    // 主数据库连接名称
    'main_connection' => env('DATA_SYNC_MAIN_CONNECTION', 'mysql'),

    // 同步模式
    'sync_mode' => env('DATA_SYNC_MODE', 'async'), // sync: 同步, async: 异步

    // 队列配置
    'queue' => [
        'connection' => env('DATA_SYNC_QUEUE_CONNECTION', 'redis'),
        'queue' => env('DATA_SYNC_QUEUE_NAME', 'data-sync'),
    ],

    // 重试配置
    'retry' => [
        'max_attempts' => env('DATA_SYNC_MAX_ATTEMPTS', 3),
        'delay' => env('DATA_SYNC_RETRY_DELAY', 60), // 秒
    ],

    // 排除同步的ID列表
    'exclude_ids' => [
        'school' => [],
        'student' => [],
        'teacher' => [],
        'admin' => [],
    ],

    // 字段映射配置
    'field_mapping' => [
        'ysy_school' => [
            // 学校字段映射：原始字段名 => 同步数据库字段名
            'school_name' => 'name',           // 学校名称        // 学校类型
        ],
        'student' => [
            // 学生字段映射
            'student_name' => 'name',          // 学生姓名
            'student_no' => 'student_number',  // 学号
            'sex' => 'gender',                 // 性别
            'mobile' => 'phone',               // 手机号
            'student_status' => 'status',      // 状态
        ],
        'teacher' => [
            // 教师字段映射
            'teacher_name' => 'name',          // 教师姓名
            'teacher_no' => 'teacher_number',  // 教师编号
            'sex' => 'gender',                 // 性别
            'mobile' => 'phone',               // 手机号
            'dept' => 'department',            // 部门
            'job_title' => 'position',         // 职位
            'teacher_status' => 'status',      // 状态
        ],
        'admin' => [
            // 管理员字段映射
            'admin_name' => 'name',            // 管理员姓名
            'user_name' => 'username',         // 用户名
            'sex' => 'gender',                 // 性别
            'mobile' => 'phone',               // 手机号
            'dept' => 'department',            // 部门
            'job_title' => 'position',         // 职位
            'user_role' => 'role',             // 角色
            'admin_status' => 'status',        // 状态
        ],
        'grade' => [
            // 年级字段映射
            'grade_name' => 'name',            // 年级名称
            'grade_code' => 'code',            // 年级代码
            'grade_level' => 'level',          // 年级等级
            'grade_sort' => 'sort',            // 排序
            'grade_status' => 'status',        // 状态
        ],
        'class' => [
            // 班级字段映射
            'class_name' => 'name',            // 班级名称
            'class_code' => 'code',            // 班级代码
            'class_teacher_id' => 'teacher_id', // 班主任ID
            'student_num' => 'student_count',   // 学生数量
            'class_sort' => 'sort',            // 排序
            'class_status' => 'status',        // 状态
        ],
    ],

    // 同步表名映射
    'table_mapping' => [
        'school' => 'ysy_school',
        'campus' => 'campuses',
        'student' => 'students',
        'teacher' => 'teachers',
        'admin' => 'admins',
        'role' => 'roles',
        'grade' => 'grades',
        'class' => 'classes',
    ],

    // 日志配置
    'logging' => [
        'enabled' => env('DATA_SYNC_LOGGING_ENABLED', true),
        'channel' => env('DATA_SYNC_LOG_CHANNEL', 'daily'),
        'level' => env('DATA_SYNC_LOG_LEVEL', 'info'),
    ],

    // 监控配置
    'monitoring' => [
        'enabled' => env('DATA_SYNC_MONITORING_ENABLED', false),
        'webhook_url' => env('DATA_SYNC_WEBHOOK_URL'),
        'alert_on_failure' => env('DATA_SYNC_ALERT_ON_FAILURE', true),
    ],

    // 数据验证配置
    'validation' => [
        'enabled' => env('DATA_SYNC_VALIDATION_ENABLED', true),
        'strict_mode' => env('DATA_SYNC_STRICT_MODE', false),
    ],

    // 批量操作配置
    'batch' => [
        'chunk_size' => env('DATA_SYNC_BATCH_CHUNK_SIZE', 100),
        'max_execution_time' => env('DATA_SYNC_MAX_EXECUTION_TIME', 300), // 秒
    ],

    // 同步策略配置
    'strategy' => [
        // 冲突解决策略: overwrite, skip, merge
        'conflict_resolution' => env('DATA_SYNC_CONFLICT_RESOLUTION', 'overwrite'),
        
        // 是否保留软删除记录
        'preserve_soft_deletes' => env('DATA_SYNC_PRESERVE_SOFT_DELETES', true),
        
        // 是否同步时间戳
        'sync_timestamps' => env('DATA_SYNC_TIMESTAMPS', true),
    ],

    // 性能配置
    'performance' => [
        // 是否使用事务
        'use_transactions' => env('DATA_SYNC_USE_TRANSACTIONS', true),
        
        // 连接池大小
        'connection_pool_size' => env('DATA_SYNC_CONNECTION_POOL_SIZE', 10),
        
        // 查询超时时间（秒）
        'query_timeout' => env('DATA_SYNC_QUERY_TIMEOUT', 30),
    ],

    // 安全配置
    'security' => [
        // 是否加密敏感数据
        'encrypt_sensitive_data' => env('DATA_SYNC_ENCRYPT_SENSITIVE_DATA', false),
        
        // 敏感字段列表
        'sensitive_fields' => [
            'password',
            'phone',
            'email',
            'id_card',
        ],
        
        // 是否记录操作审计
        'audit_logging' => env('DATA_SYNC_AUDIT_LOGGING', true),
    ],
];
