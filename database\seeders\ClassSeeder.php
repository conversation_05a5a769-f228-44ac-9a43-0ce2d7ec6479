<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;

/**
 * 学校班级数据填充
 */
class ClassSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school_id = $this->school_id;
        // 同学校不同校区
        DB::table('school_campuses')->where('school_id',$school_id)
            ->get()
            ->each(function ($item) use ($school_id){
                // 查询年级列表
                $grade_list = DB::connection($this->connect)->table('grade')
//                    ->where('name','>=',2021)// 2021年以后
                    ->where('school_id',$school_id)
                    ->where('school_district',$item->id)
//                    ->where('step',0)
                    ->groupBy('grade_sort')
                    ->select('grade_sort',DB::raw("GROUP_CONCAT(id) AS grade_ids"))
                    ->get()
                    ->each(function ($item_grade) use ($item){
                        // 查询年级对应的班级列表，比如：高一，高二
                        $grade_ids = explode(',',$item_grade->grade_ids);
                        $class_list = DB::connection($this->connect)->table('class')
                            ->whereIn('grade_id',$grade_ids)
                            ->where('school_id',$item->school_id)
                            ->where('school_district',$item->id)
//                            ->where('step',0)
                            ->pluck('name')->toArray();
                        if(!empty($class_list)){
                            $grade_sort =$item_grade->grade_sort;
                            if($grade_sort === 6.5){
                                $grade_sort = 6;
                            }else{
                                $grade_sort = intval($grade_sort);
                            }
                            // 对班级数据分割处理去重
                            $new_class_list = $this->change_class($class_list);
                            $class_data = [];
                            foreach ($new_class_list as $class_name){
                                $class_data[] = [
                                    'class_name' => $class_name,
                                    'school_id' => $item->school_id,
                                    'school_campus_id' => $item->id,
                                    'grade_id' => $grade_sort,
//                                    'created_at' => date('Y-m-d H:i:s',time()),
//                                    'updated_at' => date('Y-m-d H:i:s',time()),
                                ];
                            }

                            // 先将原始数据插入到classes_old表中，再通过sql处理班级名称不规范问题
                            DB::table('classes_old')->insert($class_data);
                        }
                    });
        });

        // 执行班级名称处理
        $this->processClassNames($school_id);
    }

    /**
     * 处理班级名称
     */
    private function processClassNames($schoolId)
    {
        echo "开始处理班级名称...\n";

        try {
            // 调用班级名称处理命令
            Artisan::call('class:process-names', [
                '--school_id' => $schoolId
            ]);

            echo "班级名称处理完成！\n";
        } catch (\Exception $e) {
            echo "班级名称处理失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }

    public function change_class($class_list): array
    {
//        $data = [];
//        foreach ($class_list as $value){
//            // 匹配获取括号里的数字并且拼上班级
//            preg_match('/\((\d+)\)/', $value, $matches);
//            // 没有匹配到括号，则保留原班级名称
//            if(isset($matches[1])){
//                $data[] = $matches[1].'班';
//            }else{
//                // 匹配获取数字
//                preg_match('/(\d+)/', $value, $matches_second);
//                $data[] = isset($matches_second[1])?$matches_second[1].'班':$value;
//            }
//            // todo:但根据不同的学校，可能需要设置不同的班级名称提取规则
//        }
//
//        return  array_unique($data);

        return  array_unique($class_list);
    }
}
