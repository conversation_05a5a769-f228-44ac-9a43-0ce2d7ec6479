<?php

namespace App\Services;

use App\Exceptions\BusinessException;
use App\Traits\ApiResponse;
use App\Traits\ValidatorTrait;
use GuzzleHttp\Client;

class BaseService
{
    use ApiResponse;
    use ValidatorTrait;
    
    /**
     * 抛出业务异常
     * 
     * @param string $message 异常信息
     * @param int|null $code 异常代码
     * @throws BusinessException
     */
    protected function throwBusinessException(string $message, ?int $code = null, mixed $log_data = null, ?string $log_type = null)
    {
        throw new BusinessException($message, $code, $log_data, $log_type);
    }

    protected function request_post(string $url, string $data, array $headers = []): string
    {
        try {
            $client = new Client();
            $defaultHeaders = [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ];
            
            $response = $client->post($url, [
                'headers' => array_merge($defaultHeaders, $headers),
                'body' => $data,
                'timeout' => 30,
            ]);

            return $response->getBody()->getContents();
        } catch (\Exception $e) {
            throw new BusinessException("BaseService逻辑错误", 500, $e->getMessage());
        }
    }
}
