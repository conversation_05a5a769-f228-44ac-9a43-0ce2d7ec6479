<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 16:01
 */
namespace app\evaluation\service;

use think\Loader;
use think\Config;
use app\evaluation\logic\TenthGradeReport;

class Answer{
    protected $AnswerLogic;
    protected $tenthGradeReport;
    public function __construct()
    {
        $this->AnswerLogic = new \app\evaluation\logic\Answer();
        $this->tenthGradeReport = new TenthGradeReport();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://查询

                return $this->AnswerLogic->get_list();
                break;
            case 'post'://增加

                return $this->AnswerLogic->add();
                break;
            case 'put'://修改

                return $this->AnswerLogic->edit();
                break;
            case 'delete'://删除
                return $this->AnswerLogic->del();
                break;
            default:
                return false;
        }
    }

    public function change_review_teacher(){
        $data = $this->AnswerLogic->change_review_teacher();
        return $data;
    }

    public function correction_score(){
        $data = $this->AnswerLogic->correction_score();
        return $data;
    }

    public function get_question_score(){
        $data = $this->AnswerLogic->get_question_score();
        apiReturn($data);
    }

    public function submit_subjective(){
        $data = $this->AnswerLogic->submit_subjective();
        apiReturn($data);
    }

    public function log_papers(){
        $data = $this->AnswerLogic->log_papers();
        apiReturn($data);
    }

    public function insert_answer_single(){
        $question_id = input('question_id');
        $grading_method = 2;
        $data = db('evaluation_temp_yuwen')->field('student_no,question_'.$question_id)->select();
        $question_options = db('evaluation_question_options')
            ->where(['question_id'=>$question_id,'status'=>0])
            ->column('title,score');
//        print_r($question_options);die;
        $student_info = db('student stu')
            ->join('school_district dis','stu.school_district = dis.id')
            ->where(['stu.school_id'=>987,'stu.step'=>0])
            ->column('stu.student_no,stu.id,stu.member_id,stu.name,stu.school_district');
//        print_r($student_info);die;
//计算好客观题分数后，存入表中
        foreach ($data as $v){
            $row[] = [
                'paper_id'=>95,
                'distribution_id'=>242,
                'student_id' => $student_info[$v['student_no']]['id'],
                'question_id'=>$question_id,
                'answer'=>$v['question_'.$question_id],
                'score'=>$question_options[$v['question_'.$question_id]] ?? 0,
                'grading_method'=>$grading_method,
                'school_id' => 987,
                'school_district_id' => $student_info[$v['student_no']]['school_district'],
                'member_id' => $student_info[$v['student_no']]['member_id'],
            ];
        }
//            print_r($row);die;
        $res = db('evaluation_student_answer')->insertAll($row);
        return $res;
    }

    public function insert_answer_mult(){
        $question_id = input('question_id');
        $grading_method = 2;
        $data = db('evaluation_temp_yuwen')->field('student_no,question_'.$question_id)->select();
        $question_answer = db('evaluation_questions')
            ->where(['id'=>$question_id])
            ->value('answer');
//        print_r($question_answer);die;
        $student_info = db('student stu')
            ->join('school_district dis','stu.school_district = dis.id')
            ->where(['stu.school_id'=>987,'stu.step'=>0])
            ->column('stu.student_no,stu.id,stu.member_id,stu.name,stu.school_district');
//计算好客观题分数后，存入表中
        foreach ($data as $v){
            $row[] = [
                'paper_id'=>95,
                'distribution_id'=>242,
                'student_id' => $student_info[$v['student_no']]['id'],
                'question_id'=>$question_id,
                'answer'=>$v['question_'.$question_id],
                'score'=>$this->get_mult_score(str_split($v['question_'.$question_id]),explode(',',$question_answer)),
                'grading_method'=>$grading_method,
                'school_id' => 987,
                'school_district_id' => $student_info[$v['student_no']]['school_district'],
                'member_id' => $student_info[$v['student_no']]['member_id'],
            ];
        }
//            print_r($row);die;
        $res = db('evaluation_student_answer')->insertAll($row);
        apiReturn($res);
    }
    public function get_mult_score($student_answers, $correct_answers) {
        $n = count($correct_answers);
        $score = 0;

        foreach ($student_answers as $answer) {
            if (in_array($answer, $correct_answers)) {
                $score += 8 / $n;
            } else {
                $score -= 10 / $n;
            }
        }

        return max(0, ceil($score)); // Ensure the score is not less than 0 and round up to the nearest integer
    }

    public function insert_answer_sub(){
        $question_id = input('question_id');
        $course_id = input('course_id');
        $grading_method = input('grading_method') ?? 0;
        $paper_id = input('paper_id');
        if($course_id == 1){
            $table = 'evaluation_temp_yuwen';
        }elseif($course_id == 2){
            $table = 'evaluation_temp_shuxue';
        }elseif($course_id == 3){
            $table = 'evaluation_temp_kexue';
        }
//        $grading_method = 1;
//        $data = db($table)->field('student_no,question_'.$question_id.',score_'.$question_id)->select();
        $data = db($table)->field('student_no,score_'.$question_id)->select();
        $student_info = db('student stu')
            ->join('school_district dis','stu.school_district = dis.id')
            ->where(['stu.school_id'=>987,'stu.step'=>0])
            ->column('stu.student_no,stu.id,stu.member_id,stu.name,stu.school_district');
        $all = db('evaluation_category_portion')->alias('a')

            ->join('evaluation_category b','a.category_id = b.id')

            ->join('evaluation_questions c','a.question_id = c.id')

            ->field('a.question_id,a.category_id,b.category_name,a.percentage')

            ->where(['b.course_id'=>$course_id,'a.status'=>0,'b.status'=>0,'c.status'=>0,'c.id'=>$question_id])

            ->select();
//        print_r($all);die;
        $competence_shine = competence_shine();

//        print_r($competence_shine);die;

//计算好客观题分数后，存入表中
        $rows = [];
        foreach ($data as $v){
            $row = [
                'paper_id'=>$paper_id,
                'distribution_id'=>242,
                'student_id' => $student_info[$v['student_no']]['id'],
                'question_id'=>$question_id,
                'answer'=>$v['question_'.$question_id] ?? '',
                'score'=>$v['score_'.$question_id],
                'grading_method'=>$grading_method,
                'school_id' => 987,
                'school_district_id' => $student_info[$v['student_no']]['school_district'],
                'member_id' => $student_info[$v['student_no']]['member_id'],
            ];
            foreach ($all as $kc => $vc){

                if(isset($competence_shine[$vc['category_name']])){

                    $score = $v['score_'.$question_id] ? $v['score_'.$question_id] * $vc['percentage'] / 100 : '';

                }

                $competence_key = $competence_shine[ $vc['category_name'] ];

                $row[ $competence_key ] = $score;

            }
//            print_r($row);die;
            $rows[] = $row;
        }
//            print_r($rows);die;
        $res = db('evaluation_student_answer')->insertAll($rows);
        apiReturn($res);
    }

    public function insert_log()
    {
        $course_id = input('course_id');
        $paper_id = input('paper_id');
        $where['stu.school_id'] = 987;
        $where['stu.step'] = 0;
        $where['answer.paper_id'] = $paper_id;
        $field = '';
        if($course_id == 1){
            $table = 'evaluation_temp_yuwen';
            $field .= ',sum(language) as language,sum(thinking) as thinking,sum(appreciation) as appreciation,sum(culture) as culture';
        }elseif($course_id == 2){
            $table = 'evaluation_temp_shuxue';
            $field .= ',sum(reasoning) as reasoning,sum(modeling) as modeling,sum(operation) as operation,sum(imagine) as imagine,sum(analysis) as analysis,sum(abstract) as abstract';
        }elseif($course_id == 3){
            $table = 'evaluation_temp_kexue';
            $field .= ',sum(concept) as concept,sum(thought) as thought,sum(explore) as explore,sum(attitude) as attitude';
        }
        $data = db($table.' yuwen')
            ->join('student stu','yuwen.student_no = stu.student_no')
            ->join('evaluation_student_answer answer','stu.id = answer.student_id')
            ->field('stu.grade_id,stu.class_id,stu.id,stu.member_id,stu.school_district,sum(score) as sum_score'.$field)
            ->where($where)
            ->group('answer.student_id')
            ->select();
//        print_r(db($table.' yuwen')->getLastSql());die;
//        print_r($data);die;
        $competence_shine = competence_shine();
        if($course_id == 1){
            $competence_shine = array_slice($competence_shine,0,4);
        }elseif($course_id == 2){
            $competence_shine = array_slice($competence_shine,4,6);
        }elseif($course_id == 3){
            $competence_shine = array_slice($competence_shine,10,4);
        }

//        print_r($competence_shine);die;
        $infos = [];
        foreach ($data as $k => $v){
            $info = [
                'paper_id'=>$paper_id,
                'grade_id'=>$v['grade_id'],
                'class_id'=>$v['class_id'],
                'student_id' => $v['id'],
                'member_id' => $v['member_id'],
                'distribution_id'=>242,
                'used_time'=>'',
                'check_member_id'=>'',
                'score'=>$v['sum_score'],
                'check_status'=>1,//有主观题置为待批阅，无主观题置为已批阅
                'school_id' =>987,
                'school_district_id' => $v['school_district'],
            ];
            foreach ($competence_shine as $compe){

                $info[$compe] = $v[$compe];

            }
            $infos[] = $info;
        }
//        print_r($infos);die;
        $res = db('evaluation_student_log')->insertAll($infos);
        return $res;
    }

    public function update_log_competence()
    {
        $paper_id = input('paper_id');
        $distribution_id = input('distribution_id');
        $school_id = input('school_id');
        $run_half = input('run_half');//跑一半，跑多了会卡死
        $paper_info = db('evaluation_papers')->field('course_id,question_ids')->where('id',$paper_id)->find();
        $course_id = $paper_info['course_id'];
        $paper_question_ids = $paper_info['question_ids'];
        $question_ids = db('evaluation_questions')
            ->where('is_common',1)
            ->where('status',0)
            ->where('id in ('.$paper_question_ids.') or parent_id in ('.$paper_question_ids.')')
            ->value('group_concat(id)');
//        print_r($question_ids);die;
//        $question_ids = input('question_id');
        $where['paper_id'] = $paper_id;

        $where['school_id'] = $school_id;

        $where['distribution_id'] = $distribution_id;

        $where['question_id'] = ['in',$question_ids];
        $field = '';
        if($course_id == 1){
            $field .= ',sum(language) as language,sum(thinking) as thinking,sum(appreciation) as appreciation,sum(culture) as culture';
        }elseif($course_id == 2){
            $field .= ',sum(reasoning) as reasoning,sum(modeling) as modeling,sum(operation) as operation,sum(imagine) as imagine,sum(analysis) as analysis,sum(abstract) as abstract';
        }elseif($course_id == 3){
            $field .= ',sum(concept) as concept,sum(thought) as thought,sum(explore) as explore,sum(attitude) as attitude';
        }

        $score_list = db('evaluation_student_answer')

            ->field('member_id'.$field)

            ->where($where)

            ->group('member_id')

            ->select();
//        if($run_half == 1){
//            $score_list = array_slice($score_list,0,710);
//        }elseif($run_half == 2){
//            $score_list = array_slice($score_list,710);
//        }
//        print_r(db('evaluation_student_answer')->getLastSql());die;
//        print_r($score_list);die;

        $where1['paper_id'] = $paper_id;

        $where1['distribution_id'] = $distribution_id;

        $all = db('evaluation_student_log')

            ->field('id,score,member_id,distribution_id')

            ->where($where1)

            ->select();

//        print_r($all);die;

        //计算好客观题分数后，存入表中

        $competence_shine = competence_shine();
        if($course_id == 1){
            $competence_shine = array_slice($competence_shine,0,4);
        }elseif($course_id == 2){
            $competence_shine = array_slice($competence_shine,4,6);
        }elseif($course_id == 3){
            $competence_shine = array_slice($competence_shine,10,4);
        }

//        $competence_shine = array_values($competence_shine);

//        print_r($competence_shine);die;

        foreach ($all as $k => $v){
            $row = [];

            //结算各科维度

            foreach ($score_list as $kc => $vc){

                if($v['member_id'] == $vc['member_id']){

                    foreach ($competence_shine as $compe){

                        $row[$compe] = $vc[$compe];

                    }

                }

            }

//            print_r($row);die;

            if($row) {
                $row['id'] = $v['id'];
                $rows[] = $row;
            }

        }

//        print_r($rows);die;
        $evaluationLogModel = new \app\evaluation\model\EvaluationLog();

        $res = $evaluationLogModel->saveAll($rows);

        return $res;

    }

    public function update_answer_competence()
    {
        $paper_id = input('paper_id');
        $run_half = input('run_half');//跑一半，跑多了会卡死
        $paper_info = db('evaluation_papers')->field('course_id,question_ids')->where('id',$paper_id)->find();
        $course_id = $paper_info['course_id'];
        $paper_question_ids = $paper_info['question_ids'];
        $question_ids = db('evaluation_questions')
            ->where('is_common',1)
            ->where('status',0)
            ->where('id in ('.$paper_question_ids.') or parent_id in ('.$paper_question_ids.')')
            ->value('group_concat(id)');
//        print_r($question_ids);die;
//        $question_ids = input('question_id');
        $where['paper_id'] = $paper_id;

//        $where['grading_method'] = 2;//1主观2客观题

        $where['distribution_id'] = input('distribution_id');

        $where['question_id'] = ['in',$question_ids];

        $score_list = db('evaluation_student_answer')

            ->field('id,score,member_id,distribution_id,question_id')

            ->where($where)
            ->order('id')
            ->select();
        if($run_half == 1){
            $score_list = array_slice($score_list,0,710);
        }elseif($run_half == 2){
            $score_list = array_slice($score_list,710);
        }

//print_r($score_list);die;

        //course_id,'阅读' => '1','数学' => '2','科学' => '3',

        $all = db('evaluation_category_portion')->alias('a')

            ->join('evaluation_category b','a.category_id = b.id')

            ->join('evaluation_questions c','a.question_id = c.id')

            ->field('a.question_id,a.category_id,b.category_name,a.percentage')

            ->where(['b.course_id'=>$course_id,'a.status'=>0,'b.status'=>0,'c.status'=>0,'c.id'=>['in',$question_ids]])

            ->select();

//        print_r($all);die;

        //计算好客观题分数后，存入表中

        $competence_shine = competence_shine();

//        print_r($competence_shine);die;

        foreach ($score_list as $k => $v){

            $row = [];

            //结算各科维度

            foreach ($all as $kc => $vc){

                if($v['question_id'] == $vc['question_id']){

                    if(isset($competence_shine[$vc['category_name']])){

                        $score = $v['score'] ? $v['score'] * $vc['percentage'] / 100 : '';

                    }

                    $competence_key = $competence_shine[ $vc['category_name'] ];

                    $row[ $competence_key ] = $score;

                }

            }

            $row['id'] = $v['id'];
            $rows[] = $row;
        }

//        print_r($rows);die;

        $evaluationAnswerModel = new \app\evaluation\model\EvaluationAnswer();
        $res = $evaluationAnswerModel->saveAll($rows);

        return $res;

    }

    public function get_question_score_with_competence()
    {
        $school_id = input('school_id');
        $paper_id = input('paper_id');
        $course_id = input('course_id');
//        $distribution_id = input('distribution_id');
//        $distribution_id = [251,252,253,254];
        $distribution_id = [250,256,257];
//        $campus_name = input('campus_name');

//        $paper_question_ids = db('evaluation_papers')->where('id',$paper_id)->value('question_ids');
//        $question_ids_arr = db('evaluation_questions')
//            ->where('is_common',1)
//            ->where('status',0)
//            ->where('id in ('.$paper_question_ids.') or parent_id in ('.$paper_question_ids.')')
//            ->column('id');
//        $objective = [309,312,313,315,316,417,420,331,332,335,336,338,339,342];
//        $subjectivity = [418,421,422,423,343,424,425,426];


//        print_r($question_ids_arr);die;

        $field = '';
        if($course_id == 1){
            $filename = '阅读';
            $question_ids_arr = [309,312,313,315,316,417,420,331,332,335,336,338,339,342,418,421,422,423,343,424,425,426];//95阅读1排好序的
            $field .= ',language,thinking,appreciation,culture';
        }elseif($course_id == 2){
            $filename = '数学';
            $question_ids_arr = [286,252,267,274,287,268,288,269,253,264,258,281,280,304,303,291,307,302,296,305];//94数学2排好序的
            $field .= ',reasoning,modeling,operation,imagine,analysis,abstract';
        }elseif($course_id == 3){
            $filename = '科学';
            $question_ids_arr = [394,395,428,397,398,399,400,402,427,411,413,414,401,403,404,406,408,409,410,412,415];//92科学3排好序的
            $field .= ',concept,thought,explore,attitude';
        }
        $search = [
            'school_id'=>$school_id,
            'paper_id'=>$paper_id,
            'status'=>0,
            'distribution_id'=>['in',$distribution_id]
        ];
        $student_ids = db('evaluation_student_answer')->where($search)->group('student_id')->column('student_id');
//        print_r($student_ids);die;
//        if($campus_name) $where['district.campus_name'] = $campus_name;
        $where['student.school_id'] = $school_id;
        $where['student.step'] = 0;
        $where['student.id'] = ['in',$student_ids];
        $student_list = db('student student')
            ->join('school school','school.id = student.school_id')
            ->join('grade grade','grade.id = student.grade_id')
            ->join('class class','class.id = student.class_id')
            ->join('school_district district','student.school_district = district.id')
            ->field('student.id as student_id,student.student_no,student.name,district.campus_name,grade.name as grade_year,grade.grade_name,class.name as class_name')
            ->where($where)
            ->select();
//        print_r(db('student student')->getLastSql());die;
//        print_r($student_list);die;
        $where['paper_id'] = $paper_id;
        $question_list = db('evaluation_student_answer')
            ->field('student_id,question_id,answer,score'.$field)
            ->where($search)
            ->select();
//        print_r($question_list);die;
        $question_list_map = [];
        foreach ($question_list as $k => $v){
            $question_list_map[$v['student_id'].'-'.$v['question_id']] = $v;
        }

        $log_list = db('evaluation_student_log')
            ->where($search)
            ->column('student_id,score as total,used_time'.$field);
//        print_r(db('evaluation_student_log')->getLastSql());die;
//        print_r($question_list_map);die;

        if($course_id == 1){
            foreach ($student_list as $key => $value){
                foreach ($question_ids_arr as $k => $v) {
                    $student_list[$key]['用时(s)'] = $log_list[$value['student_id']]['used_time'];
                    $student_list[$key]['总分'] = $log_list[$value['student_id']]['total'];
                    $student_list[$key]['语言建构与运用'] = $log_list[$value['student_id']]['language'];
                    $student_list[$key]['思维发展与提升'] = $log_list[$value['student_id']]['thinking'];
                    $student_list[$key]['审美鉴赏与创造'] = $log_list[$value['student_id']]['appreciation'];
                    $student_list[$key]['文化传承与理解'] = $log_list[$value['student_id']]['culture'];
                    $question_name = $this->transform_yuwen($question_list_map[$value['student_id'].'-'.$v]['question_id']);
                    $student_list[$key][$question_name.'-score' ] = $question_list_map[$value['student_id'].'-'.$v]['score'];
                    $student_list[$key][$question_name.'-answer' ] = $question_list_map[$value['student_id'].'-'.$v]['answer'];
                    $student_list[$key][$question_name.'-语言建构与运用' ] = $question_list_map[$value['student_id'].'-'.$v]['language'];
                    $student_list[$key][$question_name.'-思维发展与提升' ] = $question_list_map[$value['student_id'].'-'.$v]['thinking'];
                    $student_list[$key][$question_name.'-审美鉴赏与创造' ] = $question_list_map[$value['student_id'].'-'.$v]['appreciation'];
                    $student_list[$key][$question_name.'-文化传承与理解' ] = $question_list_map[$value['student_id'].'-'.$v]['culture'];
                }
            }
        }elseif($course_id == 2){
            foreach ($student_list as $key => $value){
                foreach ($question_ids_arr as $k => $v) {
                    $student_list[$key]['用时(s)'] = $log_list[$value['student_id']]['used_time'];
                    $student_list[$key]['总分'] = $log_list[$value['student_id']]['total'];
                    $student_list[$key]['数学推理'] = $log_list[$value['student_id']]['reasoning'];
                    $student_list[$key]['数学建模'] = $log_list[$value['student_id']]['modeling'];
                    $student_list[$key]['数学运算'] = $log_list[$value['student_id']]['operation'];
                    $student_list[$key]['直观想象'] = $log_list[$value['student_id']]['imagine'];
                    $student_list[$key]['数据分析'] = $log_list[$value['student_id']]['analysis'];
                    $student_list[$key]['数学抽象'] = $log_list[$value['student_id']]['abstract'];
                    $question_name = $this->transform_shuxue($question_list_map[$value['student_id'].'-'.$v]['question_id']);
                    $student_list[$key][$question_name.'-score' ] = $question_list_map[$value['student_id'].'-'.$v]['score'];
                    $student_list[$key][$question_name.'-answer' ] = $question_list_map[$value['student_id'].'-'.$v]['answer'];
                    $student_list[$key][$question_name.'-数学推理' ] = $question_list_map[$value['student_id'].'-'.$v]['reasoning'];
                    $student_list[$key][$question_name.'-数学建模' ] = $question_list_map[$value['student_id'].'-'.$v]['modeling'];
                    $student_list[$key][$question_name.'-数学运算' ] = $question_list_map[$value['student_id'].'-'.$v]['operation'];
                    $student_list[$key][$question_name.'-直观想象' ] = $question_list_map[$value['student_id'].'-'.$v]['imagine'];
                    $student_list[$key][$question_name.'-数据分析' ] = $question_list_map[$value['student_id'].'-'.$v]['analysis'];
                    $student_list[$key][$question_name.'-数学抽象' ] = $question_list_map[$value['student_id'].'-'.$v]['abstract'];
                }
            }
        }elseif($course_id == 3){
            foreach ($student_list as $key => $value){
                foreach ($question_ids_arr as $k => $v) {
                    $student_list[$key]['用时(s)'] = $log_list[$value['student_id']]['used_time'];
                    $student_list[$key]['总分'] = $log_list[$value['student_id']]['total'];
                    $student_list[$key]['科学观念'] = $log_list[$value['student_id']]['concept'];
                    $student_list[$key]['科学思维'] = $log_list[$value['student_id']]['thought'];
                    $student_list[$key]['探究实践'] = $log_list[$value['student_id']]['explore'];
                    $student_list[$key]['态度责任'] = $log_list[$value['student_id']]['attitude'];
                    $question_name = $this->transform_kexue($question_list_map[$value['student_id'].'-'.$v]['question_id']);
                    $student_list[$key][$question_name.'-score' ] = $question_list_map[$value['student_id'].'-'.$v]['score'];
                    $student_list[$key][$question_name.'-answer' ] = $question_list_map[$value['student_id'].'-'.$v]['answer'];
                    $student_list[$key][$question_name.'-科学观念' ] = $question_list_map[$value['student_id'].'-'.$v]['concept'];
                    $student_list[$key][$question_name.'-科学思维' ] = $question_list_map[$value['student_id'].'-'.$v]['thought'];
                    $student_list[$key][$question_name.'-探究实践' ] = $question_list_map[$value['student_id'].'-'.$v]['explore'];
                    $student_list[$key][$question_name.'-态度责任' ] = $question_list_map[$value['student_id'].'-'.$v]['attitude'];
                }
            }
        }

//        print_r($student_list);die;

        Loader::import("phpexcel/Classes/PHPExcel",VENDOR_PATH);
        $objPHPExcel  = new \PHPExcel();
        foreach ($student_list as $key => $value) {
            $i = 0;
            foreach ($value as $k => $v) {
                if($key == 0){
                    $objPHPExcel->setActiveSheetIndex(0)->setCellValue(\PHPExcel_Cell::stringFromColumnIndex($i).($key+1),$k);
                }
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue(\PHPExcel_Cell::stringFromColumnIndex($i).($key+2),$v);
                $i++;
            }
        }

        //发送标题强制用户下载文件
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        ob_end_clean();//这一步非常关键，用来清除缓冲区防止导出的excel乱码
        // 下载这个表格，在浏览器输出
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl;charset=utf-8");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");
        header('Content-Disposition:attachment;filename='.$filename.'各题分数及维度_'  . date("YmdHis").'.xlsx');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');
        exit();
    }

    public function transform_yuwen($key)
    {
        $data = [
            '309'=>'单选1_答案（B）',
            '312'=>'单选2_答案（A）',
            '313'=>'单选3_答案（C）',
            '315'=>'单选4_答案（A）',
            '316'=>'单选5_答案（B）',
            '331'=>'单选8-2_答案（B）',
            '332'=>'单选8-3_答案（A）',
            '335'=>'多选8-4_答案（ABE）',
            '336'=>'多选8-5_答案（ABD）',
            '338'=>'多选9-1_答案（CDE）',
            '339'=>'多选9-2_答案（CD）',
            '342'=>'单选10-1_答案（D）',
            '343'=>'10-2（8.0分）',
            '417'=>'单选6-1_答案（A）',
            '418'=>'6-2（3.0分）',
            '420'=>'单选7-1_答案（B）',
            '421'=>'7-2（4.0分）',
            '422'=>'8-1（3.0分）',
            '423'=>'9-3（10.0分）',
            '424'=>'10-3（10.0分）',
            '425'=>'11-1（6.0分）',
            '426'=>'11-2（8.0分）',
        ];
        return $data[$key];
    }

    public function transform_shuxue($key)
    {
        $data = [
            '286'=>'单选1（2.0分）',
            '252'=>'单选2（2.0分）',
            '267'=>'单选3（2.0分）',
            '274'=>'单选4（3.0分）',
            '287'=>'单选5（3.0分）',
            '268'=>'单选6（3.0分）',
            '288'=>'单选7（2.0分）',
            '269'=>'单选8（3.0分）',
            '253'=>'单选9（2.0分）',
            '264'=>'单选10（2.0分）',
            '258'=>'单选11（2.0分）',
            '281'=>'单选12（2.0分）',
            '280'=>'单选13（1.0分）',
            '304'=>'14（3.0分）',
            '303'=>'15（3.0分）',
            '291'=>'16（3.0分）',
            '307'=>'17（3.0分）',
            '302'=>'18（3.0分）',
            '296'=>'19（3.0分）',
            '305'=>'20（3.0分）',
        ];
        return $data[$key];
    }

    public function transform_kexue($key)
    {

        $data = [
            '394'=>'单选1-1（1.0分）',
            '395'=>'单选1-2（2.0分）',
            '428'=>'多选1-3（2.0分）',
            '397'=>'单选1-4（1.0分）',
            '398'=>'单选1-5（1.0分）',
            '399'=>'单选1-6（1.0分）',
            '400'=>'单选1-7（1.0分）',
            '402'=>'单选1-9（3.0分）',
            '427'=>'多选2-2（2.0分）',
            '411'=>'单选2-6（1.0分）',
            '413'=>'单选2-8（1.0分）',
            '414'=>'单选2-9（2.0分）',
            '401'=>'1-8（2.0分）',
            '403'=>'1-10（3.0分）',
            '404'=>'1-11（3.0分）',
            '406'=>'2-1（2.0分）',
            '408'=>'2-3（2.0分）',
            '409'=>'2-4（3.0分）',
            '410'=>'2-5（2.0分）',
            '412'=>'2-7（2.0分）',
            '415'=>'2-10（3.0分）',
        ];
        return $data[$key];
    }

    public function get_student_exam()
    {
        $school_id = input('school_id');
        $where['student.school_id'] = $school_id;
        $where['student.step'] = 0;
        $where['student.class_id'] = 38430;
        $student_list = db('student student')
            ->join('school school','school.id = student.school_id')
            ->join('grade grade','grade.id = student.grade_id')
            ->join('class class','class.id = student.class_id')
            ->join('school_district district','student.school_district = district.id')
            ->field('student.id as student_id,student.student_no,student.name,district.campus_name,grade.name as grade_year,grade.grade_name,class.name as class_name')
            ->where($where)
            ->order('student.student_no')
            ->select();
//        print_r(db('student student')->getLastSql());die;
//        print_r($student_list);die;
        //语文阅读素养A，paper_id=95
        $log_yuwen = db('evaluation_student_log')
            ->where(['school_id'=>$school_id,'paper_id'=>95,'status'=>0,'distribution_id'=>261])
            ->order('student_id')
            ->column('student_id,score,language,thinking,appreciation,culture');
//        print_r($log_yuwen);die;
        //数学素养A，paper_id=94
        $log_shuxue = db('evaluation_student_log')
            ->where(['school_id'=>$school_id,'paper_id'=>94,'status'=>0,'distribution_id'=>261])
            ->order('student_id')
            ->column('student_id,score,reasoning,modeling,operation,imagine,analysis,abstract');
        //科学素养A，paper_id=92
        $log_kexue = db('evaluation_student_log')
            ->where(['school_id'=>$school_id,'paper_id'=>92,'status'=>0,'distribution_id'=>261])
            ->order('student_id')
            ->column('student_id,score,concept,thought,explore,attitude');

        //生涯//->join('student as student','result.student_no = student.student_no')
        $log_shengya = db('evaluation_temp_shengya_result result')
            ->join('student student','result.member_id = student.member_id')
            ->where(['student.school_id'=>$school_id,'student.step'=>0])
            ->order('student.member_id')
            ->column('student.id as student_id,result.*');
//        print_r($log_shengya);die;

        foreach ($student_list as $key => $value){
            if(isset($log_yuwen[$value['student_id']]) && isset($log_shuxue[$value['student_id']]) && isset($log_kexue[$value['student_id']]) && isset($log_shengya[$value['student_id']]) && $log_yuwen[$value['student_id']]['score']>0 && $log_shuxue[$value['student_id']]['score']>0 && $log_kexue[$value['student_id']]['score']>0){
                $student_list[$key]['shuxue'] = $log_shuxue[$value['student_id']]['score'] ?? '';
                $student_list[$key]['reasoning'] = $log_shuxue[$value['student_id']]['reasoning'] ?? '';
                $student_list[$key]['modeling'] = $log_shuxue[$value['student_id']]['modeling'] ?? '';
                $student_list[$key]['operation'] = $log_shuxue[$value['student_id']]['operation'] ?? '';
                $student_list[$key]['imagine'] = $log_shuxue[$value['student_id']]['imagine'] ?? '';
                $student_list[$key]['analysis'] = $log_shuxue[$value['student_id']]['analysis'] ?? '';
                $student_list[$key]['abstract'] = $log_shuxue[$value['student_id']]['abstract'] ?? '';

                $student_list[$key]['yuwen'] = $log_yuwen[$value['student_id']]['score'] ?? '';
                $student_list[$key]['language'] = $log_yuwen[$value['student_id']]['language'] ?? '';
                $student_list[$key]['thinking'] = $log_yuwen[$value['student_id']]['thinking'] ?? '';
                $student_list[$key]['appreciation'] = $log_yuwen[$value['student_id']]['appreciation'] ?? '';
                $student_list[$key]['culture'] = $log_yuwen[$value['student_id']]['culture'] ?? '';

                $student_list[$key]['kexue'] = $log_kexue[$value['student_id']]['score'] ?? '';
                $student_list[$key]['concept'] = $log_kexue[$value['student_id']]['concept'] ?? '';
                $student_list[$key]['thought'] = $log_kexue[$value['student_id']]['thought'] ?? '';
                $student_list[$key]['explore'] = $log_kexue[$value['student_id']]['explore'] ?? '';
                $student_list[$key]['attitude'] = $log_kexue[$value['student_id']]['attitude'] ?? '';

                $student_list[$key]['shengya_guanzhu'] = $log_shengya[$value['student_id']]['shengya_guanzhu'] ?? '';
                $student_list[$key]['shengya_kongzhi'] = $log_shengya[$value['student_id']]['shengya_kongzhi'] ?? '';
                $student_list[$key]['shengya_haoqi'] = $log_shengya[$value['student_id']]['shengya_haoqi'] ?? '';
                $student_list[$key]['shengya_zixin'] = $log_shengya[$value['student_id']]['shengya_zixin'] ?? '';

                $student_list[$key]['zhineng1'] = $log_shengya[$value['student_id']]['zhineng1'] ?? '';
                $student_list[$key]['zhineng2'] = $log_shengya[$value['student_id']]['zhineng2'] ?? '';
                $student_list[$key]['zhineng3'] = $log_shengya[$value['student_id']]['zhineng3'] ?? '';
                $student_list[$key]['zhineng4'] = $log_shengya[$value['student_id']]['zhineng4'] ?? '';
                $student_list[$key]['zhineng5'] = $log_shengya[$value['student_id']]['zhineng5'] ?? '';
                $student_list[$key]['zhineng6'] = $log_shengya[$value['student_id']]['zhineng6'] ?? '';
                $student_list[$key]['zhineng7'] = $log_shengya[$value['student_id']]['zhineng7'] ?? '';
                $student_list[$key]['zhineng8'] = $log_shengya[$value['student_id']]['zhineng8'] ?? '';

                $student_list[$key]['yuedu1'] = $log_shengya[$value['student_id']]['yuedu1'] * 17.08 / 100;
                $student_list[$key]['yuedu2'] = $log_shengya[$value['student_id']]['yuedu2'] * 27.13 / 100;
                $student_list[$key]['yuedu3'] = $log_shengya[$value['student_id']]['yuedu3'] * 31.94 / 100;
                $student_list[$key]['yuedu4'] = $log_shengya[$value['student_id']]['yuedu4'] * 23.85 / 100;
                $student_list[$key]['yuedu5'] = $student_list[$key]['yuedu1'] + $student_list[$key]['yuedu2'] + $student_list[$key]['yuedu3'] + $student_list[$key]['yuedu4'];

                $student_list[$key]['shuxue1'] = $log_shengya[$value['student_id']]['shuxue1'] * 17.08 / 100;
                $student_list[$key]['shuxue2'] = $log_shengya[$value['student_id']]['shuxue2'] * 27.13 / 100;
                $student_list[$key]['shuxue3'] = $log_shengya[$value['student_id']]['shuxue3'] * 31.94 / 100;
                $student_list[$key]['shuxue4'] = $log_shengya[$value['student_id']]['shuxue4'] * 23.85 / 100;
                $student_list[$key]['shuxue5'] = $student_list[$key]['shuxue1'] + $student_list[$key]['shuxue2'] + $student_list[$key]['shuxue3'] + $student_list[$key]['shuxue4'];

                $student_list[$key]['kexue1'] = $log_shengya[$value['student_id']]['kexue1'] * 17.08 / 100;
                $student_list[$key]['kexue2'] = $log_shengya[$value['student_id']]['kexue2'] * 27.13 / 100;
                $student_list[$key]['kexue3'] = $log_shengya[$value['student_id']]['kexue3'] * 31.94 / 100;
                $student_list[$key]['kexue4'] = $log_shengya[$value['student_id']]['kexue4'] * 23.85 / 100;
                $student_list[$key]['kexue5'] = $student_list[$key]['kexue1'] + $student_list[$key]['kexue2'] + $student_list[$key]['kexue3'] + $student_list[$key]['kexue4'];

            }else{
                unset($student_list[$key]);
            }
        }
        $student_list = array_values($student_list);
//        print_r($student_list);die;
        Loader::import("phpexcel/Classes/PHPExcel",VENDOR_PATH);
        $objPHPExcel  = new \PHPExcel();
        foreach ($student_list as $key => $value) {
            $i = 0;
            foreach ($value as $k => $v) {
                if($key == 0){
                    $objPHPExcel->setActiveSheetIndex(0)->setCellValue(\PHPExcel_Cell::stringFromColumnIndex($i).($key+1),$k);
                }
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue(\PHPExcel_Cell::stringFromColumnIndex($i).($key+2),$v);
                $i++;
            }
        }

        //发送标题强制用户下载文件
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        ob_end_clean();//这一步非常关键，用来清除缓冲区防止导出的excel乱码
        // 下载这个表格，在浏览器输出
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl;charset=utf-8");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");
        header('Content-Disposition:attachment;filename=测评三科都参与不含0分学生列表_'  . date("YmdHis").'.xlsx');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');
        exit();
    }

    public function get_student_excel()
    {
        $school_id = input('school_id');
        $where['student.school_id'] = $school_id;
        $where['student.step'] = 0;
        $where['student.class_id'] = 38430;
        $student_list = db('student student')
            ->join('school school','school.id = student.school_id')
            ->join('grade grade','grade.id = student.grade_id')
            ->join('class class','class.id = student.class_id')
            ->join('school_district district','student.school_district = district.id')
            ->field('student.id as student_id,student.student_no,student.name,district.campus_name,grade.name as grade_year,grade.grade_name,class.name as class_name')
            ->where($where)
            ->order('student.student_no')
            ->select();
//        print_r(db('student student')->getLastSql());die;
//        print_r($student_list);die;
        //语文阅读素养A，paper_id=95
        $log_yuwen = db('evaluation_student_log')
            ->where(['school_id'=>$school_id,'paper_id'=>95,'status'=>0,'distribution_id'=>261])
            ->order('student_id')
            ->column('student_id,used_time,score,language,thinking,appreciation,culture');
//        print_r($log_yuwen);die;
        //数学素养A，paper_id=94
        $log_shuxue = db('evaluation_student_log')
            ->where(['school_id'=>$school_id,'paper_id'=>94,'status'=>0,'distribution_id'=>261])
            ->order('student_id')
            ->column('student_id,used_time,score,reasoning,modeling,operation,imagine,analysis,abstract');
        //科学素养A，paper_id=92
        $log_kexue = db('evaluation_student_log')
            ->where(['school_id'=>$school_id,'paper_id'=>92,'status'=>0,'distribution_id'=>261])
            ->order('student_id')
            ->column('student_id,used_time,score,concept,thought,explore,attitude');

        //生涯//->join('student as student','result.student_no = student.student_no')
        $log_shengya = db('survey_user_session session')
            ->join('student student','session.member_id = student.member_id')
            ->where(['student.school_id'=>$school_id,'student.step'=>0,'session.school_id'=>$school_id,'session.class_id'=>38430,'session.survey_id'=>464])
            ->order('student.member_id')
            ->column('student.id as student_id,session.result');
//        var_dump($log_shengya);die;

        foreach ($student_list as $key => $value){
            if(isset($log_yuwen[$value['student_id']]) && isset($log_shuxue[$value['student_id']]) && isset($log_kexue[$value['student_id']]) && isset($log_shengya[$value['student_id']]) && $log_yuwen[$value['student_id']]['score']>0 && $log_shuxue[$value['student_id']]['score']>0 && $log_kexue[$value['student_id']]['score']>0){
                $student_list[$key]['shuxue'] = $log_shuxue[$value['student_id']]['score'] ?? '';
                $student_list[$key]['shuxue_used_time'] = $log_shuxue[$value['student_id']]['used_time'] ?? '';
                $student_list[$key]['shuxue_level'] = $this->total_score_to_level($log_shuxue[$value['student_id']]['score'],[19,27,36,44,50]);
                $student_list[$key]['reasoning'] = $log_shuxue[$value['student_id']]['reasoning'] ?? '';
                $student_list[$key]['reasoning_level'] = $this->score_to_level(round($log_shuxue[$value['student_id']]['reasoning'],1),[0,3.3,3.4,4.9,5,7,7.1,8.9,9]);
                $student_list[$key]['modeling'] = $log_shuxue[$value['student_id']]['modeling'] ?? '';
                $student_list[$key]['modeling_level'] = $this->score_to_level(round($log_shuxue[$value['student_id']]['modeling'],1),[0,4.3,4.4,6.1,6.2,8.3,8.4,10.1,10.1]);
                $student_list[$key]['operation'] = $log_shuxue[$value['student_id']]['operation'] ?? '';
                $student_list[$key]['operation_level'] = $this->score_to_level(round($log_shuxue[$value['student_id']]['operation'],1),[0,2,2.1,3.3,3.4,4.7,4.8,5.4,5.5]);
                $student_list[$key]['imagine'] = $log_shuxue[$value['student_id']]['imagine'] ?? '';
                $student_list[$key]['imagine_level'] = $this->score_to_level(round($log_shuxue[$value['student_id']]['imagine'],1),[0,3.5,3.6,5.3,5.4,7.7,7.8,9.7,9.8]);
                $student_list[$key]['analysis'] = $log_shuxue[$value['student_id']]['analysis'] ?? '';
                $student_list[$key]['analysis_level'] = $this->score_to_level(round($log_shuxue[$value['student_id']]['analysis'],1),[0,0.8,0.9,2,2.1,2.6,2.7,2.9,3.0]);
                $student_list[$key]['abstract'] = $log_shuxue[$value['student_id']]['abstract'] ?? '';
                $student_list[$key]['abstract_level'] = $this->score_to_level(round($log_shuxue[$value['student_id']]['abstract'],1),[0,3.3,3.4,5,5.1,6.8,6.9,8.4,8.5]);

                $student_list[$key]['yuwen'] = $log_yuwen[$value['student_id']]['score'] ?? '';
                $student_list[$key]['yuwen_used_time'] = $log_yuwen[$value['student_id']]['used_time'] ?? '';
                $student_list[$key]['yuwen_level'] = $this->total_score_to_level($log_yuwen[$value['student_id']]['score'],[50,61,70,82,120]);
                $student_list[$key]['language'] = $log_yuwen[$value['student_id']]['language'] ?? '';
                $student_list[$key]['language_level'] = $this->score_to_level(round($log_yuwen[$value['student_id']]['language'],1),[0,18.4,18.5,22.3,22.4,25.8,25.9,30.2,30.3]);
                $student_list[$key]['thinking'] = $log_yuwen[$value['student_id']]['thinking'] ?? '';
                $student_list[$key]['thinking_level'] = $this->score_to_level(round($log_yuwen[$value['student_id']]['thinking'],1),[0,12.1,12.2,14.6,14.7,17.2,17.3,20.9,21]);
                $student_list[$key]['appreciation'] = $log_yuwen[$value['student_id']]['appreciation'] ?? '';
                $student_list[$key]['appreciation_level'] = $this->score_to_level(round($log_yuwen[$value['student_id']]['appreciation'],1),[0,5.7,5.8,7.8,7.9,9.4,9.5,11.8,11.9]);
                $student_list[$key]['culture'] = $log_yuwen[$value['student_id']]['culture'] ?? '';
                $student_list[$key]['culture_level'] = $this->score_to_level(round($log_yuwen[$value['student_id']]['culture'],1),[0,13.2,13.3,16.1,16.2,18.3,18.4,21.2,21.3]);

                $student_list[$key]['kexue'] = $log_kexue[$value['student_id']]['score'] ?? '';
                $student_list[$key]['kexue_used_time'] = $log_kexue[$value['student_id']]['used_time'] ?? '';
                $student_list[$key]['kexue_level'] = $this->total_score_to_level($log_kexue[$value['student_id']]['score'],[18,23,26,32,40]);
                $student_list[$key]['concept'] = $log_kexue[$value['student_id']]['concept'] ?? '';
                $student_list[$key]['concept_level'] = $this->score_to_level(round($log_kexue[$value['student_id']]['concept'],1),[0,3.5,3.6,4.5,4.6,5.4,5.5,6.5,6.6]);
                $student_list[$key]['thought'] = $log_kexue[$value['student_id']]['thought'] ?? '';
                $student_list[$key]['thought_level'] = $this->score_to_level(round($log_kexue[$value['student_id']]['thought'],1),[0,9,9.1,11.2,11.3,12.8,12.9,15.8,15.9]);
                $student_list[$key]['explore'] = $log_kexue[$value['student_id']]['explore'] ?? '';
                $student_list[$key]['explore_level'] = $this->score_to_level(round($log_kexue[$value['student_id']]['explore'],1),[0,3.7,3.8,5,5.1,6.1,6.2,7.5,7.6]);
                $student_list[$key]['attitude'] = $log_kexue[$value['student_id']]['attitude'] ?? '';
                $student_list[$key]['attitude_level'] = $this->score_to_level(round($log_kexue[$value['student_id']]['attitude'],1),[0,1.2,1.3,2.1,2.2,2.7,2.8,3.6,3.7]);

                if(isset($log_shengya[$value['student_id']])) {
                    $shengya_arr = json_decode($log_shengya[$value['student_id']], true);

                    $student_list[$key]['shengya_guanzhu'] = $shengya_arr[0][0];
                    $student_list[$key]['shengya_kongzhi'] = $shengya_arr[0][1];
                    $student_list[$key]['shengya_haoqi'] = $shengya_arr[0][2];
                    $student_list[$key]['shengya_zixin'] = $shengya_arr[0][3];

                    $student_list[$key]['yuedu1'] = $shengya_arr[2][0] * 17.08 / 100;
                    $student_list[$key]['yuedu2'] = $shengya_arr[2][1] * 27.13 / 100;
                    $student_list[$key]['yuedu3'] = $shengya_arr[2][2] * 31.94 / 100;
                    $student_list[$key]['yuedu4'] = $shengya_arr[2][3] * 23.85 / 100;
                    $student_list[$key]['yuedu5'] = ($student_list[$key]['yuedu1'] + $student_list[$key]['yuedu2'] + $student_list[$key]['yuedu3'] + $student_list[$key]['yuedu4']) * 4;
                    $student_list[$key]['yuedu5_level'] = $this->interest_level($student_list[$key]['yuedu5']);

                    $student_list[$key]['shuxue1'] = $shengya_arr[3][0] * 17.08 / 100;
                    $student_list[$key]['shuxue2'] = $shengya_arr[3][1] * 27.13 / 100;
                    $student_list[$key]['shuxue3'] = $shengya_arr[3][2] * 31.94 / 100;
                    $student_list[$key]['shuxue4'] = $shengya_arr[3][3] * 23.85 / 100;
                    $student_list[$key]['shuxue5'] = ($student_list[$key]['shuxue1'] + $student_list[$key]['shuxue2'] + $student_list[$key]['shuxue3'] + $student_list[$key]['shuxue4']) * 4;
                    $student_list[$key]['shuxue5_level'] = $this->interest_level($student_list[$key]['shuxue5']);

                    $student_list[$key]['kexue1'] = $shengya_arr[4][0] * 17.08 / 100;
                    $student_list[$key]['kexue2'] = $shengya_arr[4][1] * 27.13 / 100;
                    $student_list[$key]['kexue3'] = $shengya_arr[4][2] * 31.94 / 100;
                    $student_list[$key]['kexue4'] = $shengya_arr[4][3] * 23.85 / 100;
                    $student_list[$key]['kexue5'] = ($student_list[$key]['kexue1'] + $student_list[$key]['kexue2'] + $student_list[$key]['kexue3'] + $student_list[$key]['kexue4']) * 4;
                    $student_list[$key]['kexue5_level'] = $this->interest_level($student_list[$key]['kexue5']);
                }
            }else{
                unset($student_list[$key]);
            }
        }
        $student_list = array_values($student_list);
//        print_r($student_list);die;
        Loader::import("phpexcel/Classes/PHPExcel",VENDOR_PATH);
        $objPHPExcel  = new \PHPExcel();
        $title = [
            '学生id','学号','姓名','校区','学年','年级','班级',
            '数学','数学测评用时','数学星级','数学推理','数学推理星级','数学建模','数学建模星级','数学运算','数学运算星级','直观想象','直观想象星级','数据分析','数据分析星级','数学抽象','数学抽象星级',
            '语文','语文测评用时','语文星级','语言建构与运用','语言建构与运用星级','思维发展与提升','思维发展与提升星级','审美鉴赏与创造','审美鉴赏与创造星级','文化传承与理解','文化传承与理解星级',
            '科学','科学测评用时','科学星级','科学观念','科学观念星级','科学思维','科学思维星级','探究实践','探究实践星级','态度责任','态度责任星级',
            '生涯关注','生涯控制','生涯好奇','生涯自信',
            '情感体验','价值认识','知识获取','自主投入','阅读分','阅读水平',
            '情感体验','价值认识','知识获取','自主投入','数学分','数学水平',
            '情感体验','价值认识','知识获取','自主投入','科学分','科学水平',
            ];
        foreach ($student_list as $key => $value) {
            $i = 0;
            foreach ($value as $k => $v) {
                if($key == 0){
                    $objPHPExcel->setActiveSheetIndex(0)->setCellValue(\PHPExcel_Cell::stringFromColumnIndex($i).($key+1),$title[$i]);
                }
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue(\PHPExcel_Cell::stringFromColumnIndex($i).($key+2),$v);
                $i++;
            }
        }

        //发送标题强制用户下载文件
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        ob_end_clean();//这一步非常关键，用来清除缓冲区防止导出的excel乱码
        // 下载这个表格，在浏览器输出
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl;charset=utf-8");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");
        header('Content-Disposition:attachment;filename=六3班得分原始数据_'  . date("YmdHis").'.xlsx');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');
        exit();
    }

    public function total_score_to_level($score,$norm){
        if($score < $norm[0]){
            $level = '☆';
        }elseif($score >= $norm[0] && $score < $norm[1]){
            $level = '☆☆';
        }elseif($score >= $norm[1] && $score < $norm[2]){
            $level = '☆☆☆';
        }elseif($score >= $norm[2] && $score < $norm[3]){
            $level = '☆☆☆☆';
        }elseif($score >= $norm[3]){
            $level = '☆☆☆☆☆';
        }
        return $level;
    }

    public function score_to_level($score,$norm){
        if($score <= $norm[1]){
            $level = '☆';
        }elseif($score >= $norm[2] && $score <= $norm[3]){
            $level = '☆☆';
        }elseif($score >= $norm[4] && $score <= $norm[5]){
            $level = '☆☆☆';
        }elseif($score >= $norm[6] && $score <= $norm[7]){
            $level = '☆☆☆☆';
        }elseif($score >= $norm[8]){
            $level = '☆☆☆☆☆';
        }
        return $level;
    }

    public function interest_level($rank){
        if($rank <= 12){
            $level = '低水平';
        }elseif($rank > 12 && $rank <= 48){
            $level = '中水平';
        }elseif($rank > 48){
            $level = '高水平';
        }
        return $level;
    }

    public function get_student_excel_tenth()
    {
        $school_id = input('school_id');
        $distribution_id = input('distribution_id');
        $where['student.school_id'] = $school_id;
        $where['student.step'] = 0;
        $class_id = '40098,40099,40100,40101,40102,40103,40104,40105,40106,40107,40108,40084,40085,40086,40087,40088,40089,40090,40091,40092,40093,40094,40095,40096,40097';
        $where['student.class_id'] = ['in',$class_id];
        $student_list = db('student student')
            ->join('school school','school.id = student.school_id')
            ->join('grade grade','grade.id = student.grade_id')
            ->join('class class','class.id = student.class_id')
            ->join('school_district district','student.school_district = district.id')
            ->field('student.id as student_id,student.student_no,student.name,district.campus_name,grade.name as grade_year,grade.grade_name,class.name as class_name')
            ->where($where)
            ->order('student.student_no')
            ->select();
        //语文阅读素养A，paper_id=95
        $log_yuwen = db('evaluation_student_log')
            ->where(['school_id'=>$school_id,'paper_id'=>98,'status'=>0,'distribution_id'=>$distribution_id])
            ->order('student_id')
            ->column('student_id,used_time,score,language,thinking,appreciation,culture');
        //数学素养A，paper_id=94
        $log_shuxue = db('evaluation_student_log')
            ->where(['school_id'=>$school_id,'paper_id'=>97,'status'=>0,'distribution_id'=>$distribution_id])
            ->order('student_id')
            ->column('student_id,used_time,score,reasoning,modeling,operation,imagine,analysis,abstract');
        //科学素养A，paper_id=92
        $log_kexue = db('evaluation_student_log')
            ->where(['school_id'=>$school_id,'paper_id'=>96,'status'=>0,'distribution_id'=>$distribution_id])
            ->order('student_id')
            ->column('student_id,used_time,score,concept,thought,explore,attitude');

        //生涯//->join('student as student','result.student_no = student.student_no')
        $log_shengya = db('survey_user_session session')
            ->join('student student','session.member_id = student.member_id')
            ->where(['student.school_id'=>$school_id,'student.step'=>0,'session.school_id'=>$school_id,'session.class_id'=>['in',$class_id],'session.survey_id'=>479])
            ->order('student.member_id')
            ->column('student.id as student_id,session.result');

        foreach ($student_list as $key => $value){
            $student_list[$key]['shuxue'] = $log_shuxue[$value['student_id']]['score'] ?? '';
            $student_list[$key]['shuxue_used_time'] = $log_shuxue[$value['student_id']]['used_time'] ?? '';
            $student_list[$key]['reasoning'] = $log_shuxue[$value['student_id']]['reasoning'] ?? '';
            $student_list[$key]['modeling'] = $log_shuxue[$value['student_id']]['modeling'] ?? '';
            $student_list[$key]['operation'] = $log_shuxue[$value['student_id']]['operation'] ?? '';
            $student_list[$key]['imagine'] = $log_shuxue[$value['student_id']]['imagine'] ?? '';
            $student_list[$key]['analysis'] = $log_shuxue[$value['student_id']]['analysis'] ?? '';
            $student_list[$key]['abstract'] = $log_shuxue[$value['student_id']]['abstract'] ?? '';


            $student_list[$key]['yuwen'] = $log_yuwen[$value['student_id']]['score'] ?? '';
            $student_list[$key]['yuwen_used_time'] = $log_yuwen[$value['student_id']]['used_time'] ?? '';
            $student_list[$key]['language'] = $log_yuwen[$value['student_id']]['language'] ?? '';
            $student_list[$key]['thinking'] = $log_yuwen[$value['student_id']]['thinking'] ?? '';
            $student_list[$key]['appreciation'] = $log_yuwen[$value['student_id']]['appreciation'] ?? '';
            $student_list[$key]['culture'] = $log_yuwen[$value['student_id']]['culture'] ?? '';

            $student_list[$key]['kexue'] = $log_kexue[$value['student_id']]['score'] ?? '';
            $student_list[$key]['kexue_used_time'] = $log_kexue[$value['student_id']]['used_time'] ?? '';
            $student_list[$key]['concept'] = $log_kexue[$value['student_id']]['concept'] ?? '';
            $student_list[$key]['thought'] = $log_kexue[$value['student_id']]['thought'] ?? '';
            $student_list[$key]['explore'] = $log_kexue[$value['student_id']]['explore'] ?? '';
            $student_list[$key]['attitude'] = $log_kexue[$value['student_id']]['attitude'] ?? '';


            if(isset($log_shengya[$value['student_id']])) {
                $shengya_arr = json_decode($log_shengya[$value['student_id']], true);

                $student_list[$key]['Low_Motivation'] = $shengya_arr['Learning_Motivation'][0];
                $student_list[$key]['High_Motivation'] = $shengya_arr['Learning_Motivation'][1];
                $student_list[$key]['Academic_Efficacy1'] = $shengya_arr['Academic_Efficacy'][0];
                $student_list[$key]['Academic_Efficacy2'] = $shengya_arr['Academic_Efficacy'][1];
                $student_list[$key]['Academic_Efficacy3'] = $shengya_arr['Academic_Efficacy'][2];
                $student_list[$key]['Academic_Efficacy4'] = $shengya_arr['Academic_Efficacy'][3];
                $student_list[$key]['Learning_Ability1'] = $shengya_arr['Learning_Ability'][0];
                $student_list[$key]['Learning_Ability2'] = $shengya_arr['Learning_Ability'][1];
                $student_list[$key]['Learning_Ability3'] = $shengya_arr['Learning_Ability'][2];
                $student_list[$key]['Learning_Ability4'] = $shengya_arr['Learning_Ability'][3];
                $student_list[$key]['Career_Goals1'] = $shengya_arr['Career_Goals'][0];
                $student_list[$key]['Career_Goals2'] = $shengya_arr['Career_Goals'][1];
                $student_list[$key]['Career_Goals3'] = $shengya_arr['Career_Goals'][2];
                $student_list[$key]['Career_Strengths1'] = $shengya_arr['Career_Strengths'][0];
                $student_list[$key]['Career_Strengths2'] = $shengya_arr['Career_Strengths'][1];
                $student_list[$key]['Career_Strengths3'] = $shengya_arr['Career_Strengths'][2];
                $student_list[$key]['Career_Strengths4'] = $shengya_arr['Career_Strengths'][3];
                $student_list[$key]['Career_Strengths5'] = $shengya_arr['Career_Strengths'][4];
                $student_list[$key]['Interest1'] = $shengya_arr['Interest']['R'];
                $student_list[$key]['Interest2'] = $shengya_arr['Interest']['I'];
                $student_list[$key]['Interest3'] = $shengya_arr['Interest']['A'];
                $student_list[$key]['Interest4'] = $shengya_arr['Interest']['S'];
                $student_list[$key]['Interest5'] = $shengya_arr['Interest']['E'];
                $student_list[$key]['Interest6'] = $shengya_arr['Interest']['C'];
            }
        }
        $student_list = array_values($student_list);

        Loader::import("phpexcel/Classes/PHPExcel",VENDOR_PATH);
        $objPHPExcel  = new \PHPExcel();
        $title = [
            '学生id','学号','姓名','校区','学年','年级','班级',
            '数学','数学测评用时','数学推理','数学建模','数学运算','直观想象','数据分析','数学抽象',
            '语文','语文测评用时','语言建构与运用','思维发展与提升','审美鉴赏与创造','文化传承与理解',
            '科学','科学测评用时','科学观念','科学思维','探究实践','态度责任',
            '弱动机','强动机',
            '学习效能感','掌握效能感','挑战效能感','抗挫效能感',
            '计划与目标设定','认知策略','资源管理','自我监督',
            '成就竞争目标','成就回避目标','成就掌握目标',
            '工程类职业优势','研究类职业优势','艺体类职业优势','社会类职业优势','行政类职业优势',
            '实用型（R）','研究型（I）','艺术型（A）','社会型（S）','企业型（E）', '事务型（C）',
        ];
        foreach ($student_list as $key => $value) {
            $i = 0;
            foreach ($value as $k => $v) {
                if($key == 0){
                    $objPHPExcel->setActiveSheetIndex(0)->setCellValue(\PHPExcel_Cell::stringFromColumnIndex($i).($key+1),$title[$i]);
                }
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue(\PHPExcel_Cell::stringFromColumnIndex($i).($key+2),$v);
                $i++;
            }
        }

        //发送标题强制用户下载文件
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        ob_end_clean();//这一步非常关键，用来清除缓冲区防止导出的excel乱码
        // 下载这个表格，在浏览器输出
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl;charset=utf-8");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");
        header('Content-Disposition:attachment;filename=徐汇三校十年级全部学生得分原始数据_'  . date("YmdHis").'.xlsx');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');
        exit();
    }

    public function get_student_excel_level_tenth()
    {
        $school_id = input('school_id');
        $distribution_id = input('distribution_id');
        $where['student.school_id'] = $school_id;
        $where['student.step'] = 0;
        $class_id = '40098,40099,40100,40101,40102,40103,40104,40105,40106,40107,40108,40084,40085,40086,40087,40088,40089,40090,40091,40092,40093,40094,40095,40096,40097';
        $where['student.class_id'] = ['in',$class_id];
        $student_list = db('student student')
            ->join('school school','school.id = student.school_id')
            ->join('grade grade','grade.id = student.grade_id')
            ->join('class class','class.id = student.class_id')
            ->join('school_district district','student.school_district = district.id')
            ->field('student.id as student_id,student.student_no,student.name,district.campus_name,grade.name as grade_year,grade.grade_name,class.name as class_name')
            ->where($where)
            ->order('student.student_no')
            ->select();
        //语文阅读素养A，paper_id=95
        $log_yuwen = db('evaluation_student_log')
            ->where(['school_id'=>$school_id,'paper_id'=>98,'status'=>0,'distribution_id'=>$distribution_id])
            ->order('student_id')
            ->column('student_id,used_time,score,language,thinking,appreciation,culture');
        //数学素养A，paper_id=94
        $log_shuxue = db('evaluation_student_log')
            ->where(['school_id'=>$school_id,'paper_id'=>97,'status'=>0,'distribution_id'=>$distribution_id])
            ->order('student_id')
            ->column('student_id,used_time,score,reasoning,modeling,operation,imagine,analysis,abstract');
        //科学素养A，paper_id=92
        $log_kexue = db('evaluation_student_log')
            ->where(['school_id'=>$school_id,'paper_id'=>96,'status'=>0,'distribution_id'=>$distribution_id])
            ->order('student_id')
            ->column('student_id,used_time,score,concept,thought,explore,attitude');

        //生涯//->join('student as student','result.student_no = student.student_no')
        $log_shengya = db('survey_user_session session')
            ->join('student student','session.member_id = student.member_id')
            ->where(['student.school_id'=>$school_id,'student.step'=>0,'session.school_id'=>$school_id,'session.class_id'=>['in',$class_id],'session.survey_id'=>479])
            ->order('student.member_id')
            ->column('student.id as student_id,session.result');

        Config::load(APP_PATH.'evaluation/config_tenth.php');
        $tenth_grade_motivation = Config::get('tenth_grade_motivation');
        $tenth_grade_efficiency = Config::get('tenth_grade_efficacy');
        $tenth_grade_ability = Config::get('tenth_grade_ability');
        $tenth_grade_goal = Config::get('tenth_grade_goal');
        
        foreach ($student_list as $key => $value){
            $student_list[$key]['shuxue'] = isset($log_shuxue[$value['student_id']]['score']) ? $this->total_score_to_level($log_shuxue[$value['student_id']]['score'],[32,51,71,90,100]) : '';
            $student_list[$key]['reasoning'] = isset($log_shuxue[$value['student_id']]['reasoning']) ? $this->score_to_level($log_shuxue[$value['student_id']]['reasoning'],[0,7.5,7.6,12.9,13.0,17.8,17.9,21.7,21.8]) : '';
            $student_list[$key]['modeling'] = isset($log_shuxue[$value['student_id']]['modeling']) ? $this->score_to_level($log_shuxue[$value['student_id']]['modeling'],[0,7.7,7.8,12.5,12.6,19.7,19.8,28.1,28.2]) : '';
            $student_list[$key]['operation'] = isset($log_shuxue[$value['student_id']]['operation']) ? $this->score_to_level($log_shuxue[$value['student_id']]['operation'],[0,10.4,10.5,16.2,16.3,23.0,23.1,29.4,29.5]) : '';
            $student_list[$key]['imagine'] = isset($log_shuxue[$value['student_id']]['imagine']) ? $this->score_to_level($log_shuxue[$value['student_id']]['imagine'],[0,4.2,4.3,8.5,8.6,10.7,10.8,12.7,12.8]) : '';

            $student_list[$key]['yuwen'] = isset($log_yuwen[$value['student_id']]['score']) ? $this->total_score_to_level($log_yuwen[$value['student_id']]['score'],[52,60,68,80,100]) : '';
            $student_list[$key]['language'] = isset($log_yuwen[$value['student_id']]['language']) ? $this->score_to_level($log_yuwen[$value['student_id']]['language'],[0,17.0,17.1,19.7,19.8,22.4,22.5,26,26.1]) : '';
            $student_list[$key]['thinking'] = isset($log_yuwen[$value['student_id']]['thinking']) ? $this->score_to_level($log_yuwen[$value['student_id']]['thinking'],[0,12.1,12.2,15.2,15.3,18,18.1,21.9,22]) : '';
            $student_list[$key]['appreciation'] = isset($log_yuwen[$value['student_id']]['appreciation']) ? $this->score_to_level($log_yuwen[$value['student_id']]['appreciation'],[0,4.3,4.4,5.3,5.4,6.8,6.9,8.8,8.9]) : '';
            $student_list[$key]['culture'] = isset($log_yuwen[$value['student_id']]['culture']) ? $this->score_to_level($log_yuwen[$value['student_id']]['culture'],[0,16.5,16.6,18.1,18.2,20.2,20.3,23.2,23.3]) : '';
        
            $student_list[$key]['kexue'] = isset($log_kexue[$value['student_id']]['score']) ? $this->total_score_to_level($log_kexue[$value['student_id']]['score'],[52,60,68,80,100]) : '';
            $student_list[$key]['concept'] = isset($log_kexue[$value['student_id']]['concept']) ? $this->score_to_level($log_kexue[$value['student_id']]['concept'],[0,14.39,14.4,20.09,20.1,35.19,35.2,49.29,49.3]) : '';
            $student_list[$key]['thought'] = isset($log_kexue[$value['student_id']]['thought']) ? $this->score_to_level($log_kexue[$value['student_id']]['thought'],[0,11.29,11.3,17.14,17.15,28.99,29,46.19,46.2]) : '';
            $student_list[$key]['explore'] = isset($log_kexue[$value['student_id']]['explore']) ? $this->score_to_level($log_kexue[$value['student_id']]['explore'],[0,5.79,5.8,8.54,8.55,13.64,13.65,17.54,17.55]) : '';
            $student_list[$key]['attitude'] = isset($log_kexue[$value['student_id']]['attitude']) ? $this->score_to_level($log_kexue[$value['student_id']]['attitude'],[0,1.99,2,2.99,3,5.39,5.4,9.64,9.65]) : '';

            if(isset($log_shengya[$value['student_id']])) {
                $shengya_arr = json_decode($log_shengya[$value['student_id']], true);

                $weak_motivation = $this->career_score_to_level($shengya_arr['Learning_Motivation'][0],[5,10,11,19,20,25]);
                $strong_motivation = $this->career_score_to_level($shengya_arr['Learning_Motivation'][1],[5,10,11,19,20,25]);
                $student_list[$key]['motivation'] = $tenth_grade_motivation[$this->tenthGradeReport->motivation_type($weak_motivation,$strong_motivation)]['type'];
                
                $student_list[$key]['Low_Motivation'] = $weak_motivation;
                $student_list[$key]['High_Motivation'] = $strong_motivation;

                $total_efficacy = $this->career_score_to_level(round(array_sum($shengya_arr['Academic_Efficacy'])),[0,33,34,44,45,55]);
                $student_list[$key]['efficacy'] = $tenth_grade_efficiency[$this->tenthGradeReport->efficacy_type($total_efficacy,$shengya_arr['Academic_Efficacy'])]['type'];
                
                $student_list[$key]['Academic_Efficacy1'] = round($shengya_arr['Academic_Efficacy'][0] * 100 / 15) . '%';
                $student_list[$key]['Academic_Efficacy2'] = round($shengya_arr['Academic_Efficacy'][1] * 100 / 20) . '%';
                $student_list[$key]['Academic_Efficacy3'] = round($shengya_arr['Academic_Efficacy'][2] * 100 / 10) . '%';
                $student_list[$key]['Academic_Efficacy4'] = round($shengya_arr['Academic_Efficacy'][3] * 100 / 10) . '%';

                $learning_ability_level = $this->tenthGradeReport->learning_ability_level(array_sum($shengya_arr['Learning_Ability']),[20,39,40,59,60,79,80,100]);
                $student_list[$key]['ability'] = $tenth_grade_ability['level'][$learning_ability_level]['type'];
                
                $student_list[$key]['Learning_Ability1'] = $shengya_arr['Learning_Ability'][0];
                $student_list[$key]['Learning_Ability2'] = $shengya_arr['Learning_Ability'][1];
                $student_list[$key]['Learning_Ability3'] = $shengya_arr['Learning_Ability'][2];
                $student_list[$key]['Learning_Ability4'] = $shengya_arr['Learning_Ability'][3];


                $competition_goal = $this->career_score_to_level($shengya_arr['Career_Goals'][0],[0,18,19,24,25,30]);
                $avoid_goal = $this->career_score_to_level($shengya_arr['Career_Goals'][1],[0,18,19,24,25,30]);
                $master_goal = $this->career_score_to_level($shengya_arr['Career_Goals'][2],[0,18,19,24,25,30]);

                $student_list[$key]['goal'] = $tenth_grade_goal[$this->tenthGradeReport->goal_type($competition_goal,$avoid_goal,$master_goal)]['type'];
                $student_list[$key]['Career_Goals1'] = $this->career_score_to_level($shengya_arr['Career_Goals'][0],[0,18,19,24,25,30]);
                $student_list[$key]['Career_Goals2'] = $this->career_score_to_level($shengya_arr['Career_Goals'][1],[0,18,19,24,25,30]);
                $student_list[$key]['Career_Goals3'] = $this->career_score_to_level($shengya_arr['Career_Goals'][2],[0,18,19,24,25,30]);

                list($strength_area,$potential_area,$development_area) = $this->tenthGradeReport->strengths_interest_type($shengya_arr['Career_Strengths'],$shengya_arr['Interest']);
                if(!empty($strength_area)){
                    $strength_interest = implode(',',array_column($strength_area,'type'));
                }elseif(!empty($potential_area)){
                    $strength_interest = implode(',',array_column($potential_area,'type'));
                }elseif(!empty($development_area)){
                    $strength_interest = '无突出优势和兴趣';
                }
                $student_list[$key]['strengths'] = $strength_interest;
                $student_list[$key]['Career_Strengths1'] = $shengya_arr['Career_Strengths'][0];
                $student_list[$key]['Career_Strengths2'] = $shengya_arr['Career_Strengths'][1];
                $student_list[$key]['Career_Strengths3'] = $shengya_arr['Career_Strengths'][2];
                $student_list[$key]['Career_Strengths4'] = $shengya_arr['Career_Strengths'][3];
                $student_list[$key]['Career_Strengths5'] = $shengya_arr['Career_Strengths'][4];
                $student_list[$key]['Interest1'] = $shengya_arr['Interest']['R'];
                $student_list[$key]['Interest2'] = $shengya_arr['Interest']['I'];
                $student_list[$key]['Interest3'] = $shengya_arr['Interest']['A'];
                $student_list[$key]['Interest4'] = $shengya_arr['Interest']['S'];
                $student_list[$key]['Interest5'] = $shengya_arr['Interest']['E'];
                $student_list[$key]['Interest6'] = $shengya_arr['Interest']['C'];
            }
        }
        $student_list = array_values($student_list);

        Loader::import("phpexcel/Classes/PHPExcel",VENDOR_PATH);
        $objPHPExcel  = new \PHPExcel();
        $title = [
            '学生id','学号','姓名','校区','学年','年级','班级',
            '数学','数学推理','数学建模','数学运算','直观想象',
            '语文','语言建构与运用','思维发展与提升','审美鉴赏与创造','文化传承与理解',
            '科学','科学观念','科学思维','探究实践','态度责任',
            '学习动机','弱动机','强动机',
            '学业效能','学习效能感','掌握效能感','挑战效能感','抗挫效能感',
            '学习能力','计划与目标设定','认知策略','资源管理','自我监督',
            '生涯目标','成就竞争目标','成就回避目标','成就掌握目标',
            '生涯优势及生涯兴趣','工程类职业优势','研究类职业优势','艺体类职业优势','社会类职业优势','行政类职业优势',
            '实用型（R）','研究型（I）','艺术型（A）','社会型（S）','企业型（E）', '事务型（C）',
        ];
        foreach ($student_list as $key => $value) {
            $i = 0;
            foreach ($value as $k => $v) {
                if($key == 0){
                    $objPHPExcel->setActiveSheetIndex(0)->setCellValue(\PHPExcel_Cell::stringFromColumnIndex($i).($key+1),$title[$i]);
                }
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue(\PHPExcel_Cell::stringFromColumnIndex($i).($key+2),$v);
                $i++;
            }
        }

        //发送标题强制用户下载文件
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        ob_end_clean();//这一步非常关键，用来清除缓冲区防止导出的excel乱码
        // 下载这个表格，在浏览器输出
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl;charset=utf-8");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");
        header('Content-Disposition:attachment;filename=徐汇三校十年级全部学生得分和等级_'  . date("YmdHis").'.xlsx');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');
        exit();
    }

    public function career_score_to_level($score,$norm){
        if($score >= $norm[0] && $score <= $norm[1]){
            $level = '低';
        }elseif($score >= $norm[2] && $score <= $norm[3]){
            $level = '中';
        }elseif($score >= $norm[4] && $score <= $norm[5]){
            $level = '高';
        }else{
            return '低';
        }
        return $level;
    }
}