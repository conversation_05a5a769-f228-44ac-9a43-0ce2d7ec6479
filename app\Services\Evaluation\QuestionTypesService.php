<?php

namespace App\Services\Evaluation;

use App\Models\Evaluation\QuestionType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

/**
 * 题目类型管理服务类
 */
class QuestionTypesService
{
    protected $questionTypeModel;
    protected $user;

    public function __construct(QuestionType $questionTypeModel)
    {
        $this->questionTypeModel = $questionTypeModel;
        $this->user = Auth::user();
    }

    /**
     * 获取题目类型列表
     * 
     * @param array $params
     * @return array
     */
    public function getQuestionTypesList(array $params): array
    {
        if (!empty($params['id'])) {
            $data = $this->questionTypeModel->where('id', $params['id'])->first();
            return $data ? $data->toArray() : [];
        }

        $query = $this->questionTypeModel->query();

        // 应用筛选条件
        $this->applyFilters($query, $params);

        $page = $params['page'] ?? 1;
        $pagesize = $params['pagesize'] ?? 15;

        if (isset($params['page'])) {
            $total = $query->count();
            $questionTypes = $query->orderBy('id', 'desc')
                ->offset(($page - 1) * $pagesize)
                ->limit($pagesize)
                ->get();

            return [
                'list' => $questionTypes,
                'total' => $total,
                'page' => $page,
                'pagesize' => $pagesize
            ];
        } else {
            return $query->orderBy('id', 'desc')->get()->toArray();
        }
    }

    /**
     * 创建题目类型
     * 
     * @param array $data
     * @return QuestionType
     */
    public function createQuestionType(array $data): QuestionType
    {
        $questionTypeData = [
            'type_name' => $data['type_name'],
            'is_subjective' => $data['is_subjective'] ?? 0,
            'description' => $data['description'] ?? '',
            'code' => $data['code'] ?? '',
            'sort' => $data['sort'] ?? 0,
            'status' => 0,
        ];

        return $this->questionTypeModel->create($questionTypeData);
    }

    /**
     * 更新题目类型
     * 
     * @param int $id
     * @param array $data
     * @return QuestionType
     */
    public function updateQuestionType(int $id, array $data): QuestionType
    {
        $questionType = $this->questionTypeModel->findOrFail($id);

        $updateData = [
            'type_name' => $data['type_name'] ?? $questionType->type_name,
            'is_subjective' => $data['is_subjective'] ?? $questionType->is_subjective,
            'description' => $data['description'] ?? $questionType->description,
            'code' => $data['code'] ?? $questionType->code,
            'sort' => $data['sort'] ?? $questionType->sort,
        ];

        $questionType->update($updateData);

        return $questionType;
    }

    /**
     * 删除题目类型（软删除）
     * 
     * @param int $id
     * @return bool
     */
    public function deleteQuestionType(int $id): bool
    {
        $questionType = $this->questionTypeModel->findOrFail($id);

        // 检查是否被题目使用
        $isUsed = DB::table('evaluation_questions')
            ->where('type_id', $id)
            ->where('status', 0)
            ->exists();
        if ($isUsed) {
            throw new \Exception('该题目类型已被题目使用，无法删除');
        }

        return $questionType->update(['status' => -1]);
    }

    /**
     * 获取题目类型统计
     * 
     * @return array
     */
    public function getQuestionTypeStatistics(): array
    {
        $total = $this->questionTypeModel->where('status', 0)->count();
        $subjective = $this->questionTypeModel->where('status', 0)->where('is_subjective', 1)->count();
        $objective = $this->questionTypeModel->where('status', 0)->where('is_subjective', 0)->count();

        // 获取每种类型的题目数量
        $usageStats = DB::table('evaluation_questions as q')
            ->join('evaluation_question_types as qt', 'q.type_id', '=', 'qt.id')
            ->where('q.status', 0)
            ->where('qt.status', 0)
            ->selectRaw('qt.id, qt.type_name, COUNT(q.id) as question_count')
            ->groupBy('qt.id', 'qt.type_name')
            ->get()
            ->toArray();

        return [
            'total' => $total,
            'subjective' => $subjective,
            'objective' => $objective,
            'usage_stats' => $usageStats
        ];
    }

    /**
     * 批量更新排序
     * 
     * @param array $data
     * @return bool
     */
    public function updateSort(array $data): bool
    {
        if (empty($data['question_types'])) {
            throw new \InvalidArgumentException('排序数据不能为空');
        }

        return DB::transaction(function () use ($data) {
            foreach ($data['question_types'] as $typeData) {
                if (isset($typeData['id']) && isset($typeData['sort'])) {
                    $this->questionTypeModel->where('id', $typeData['id'])
                        ->update(['sort' => $typeData['sort']]);
                }
            }
            return true;
        });
    }

    /**
     * 获取主观题类型
     * 
     * @return array
     */
    public function getSubjectiveTypes(): array
    {
        return $this->questionTypeModel
            ->where('status', 0)
            ->where('is_subjective', 1)
            ->orderBy('sort')
            ->orderBy('id')
            ->get()
            ->toArray();
    }

    /**
     * 获取客观题类型
     * 
     * @return array
     */
    public function getObjectiveTypes(): array
    {
        return $this->questionTypeModel
            ->where('status', 0)
            ->where('is_subjective', 0)
            ->orderBy('sort')
            ->orderBy('id')
            ->get()
            ->toArray();
    }

    /**
     * 复制题目类型
     * 
     * @param int $id
     * @param string $newName
     * @return QuestionType
     */
    public function copyQuestionType(int $id, string $newName = null): QuestionType
    {
        $originalType = $this->questionTypeModel->findOrFail($id);

        $newTypeData = $originalType->toArray();
        unset($newTypeData['id'], $newTypeData['created_at'], $newTypeData['updated_at']);
        
        $newTypeData['type_name'] = $newName ?: $originalType->type_name . '_副本';
        $newTypeData['code'] = $originalType->code ? $originalType->code . '_copy' : '';

        return $this->questionTypeModel->create($newTypeData);
    }

    /**
     * 启用/禁用题目类型
     * 
     * @param int $id
     * @param int $status
     * @return bool
     */
    public function toggleStatus(int $id, int $status): bool
    {
        $questionType = $this->questionTypeModel->findOrFail($id);
        
        if ($status == -1) {
            // 禁用前检查是否被使用
            $isUsed = DB::table('evaluation_questions')
                ->where('type_id', $id)
                ->where('status', 0)
                ->exists();
            if ($isUsed) {
                throw new \Exception('该题目类型已被题目使用，无法禁用');
            }
        }

        return $questionType->update(['status' => $status]);
    }

    /**
     * 批量操作
     * 
     * @param string $action
     * @param array $ids
     * @return array
     */
    public function batchOperation(string $action, array $ids): array
    {
        $successCount = 0;
        $errors = [];

        foreach ($ids as $id) {
            try {
                switch ($action) {
                    case 'delete':
                        $this->deleteQuestionType($id);
                        break;
                    case 'enable':
                        $this->toggleStatus($id, 0);
                        break;
                    case 'disable':
                        $this->toggleStatus($id, -1);
                        break;
                    default:
                        throw new \Exception('不支持的操作类型');
                }
                $successCount++;
            } catch (\Exception $e) {
                $errors[] = "ID {$id}: " . $e->getMessage();
            }
        }

        return [
            'success_count' => $successCount,
            'error_count' => count($errors),
            'errors' => $errors
        ];
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 应用筛选条件
     * 
     * @param $query
     * @param array $params
     */
    private function applyFilters($query, array $params): void
    {
        $query->where('status', 0);

        if (!empty($params['type_name'])) {
            $query->where('type_name', 'like', '%' . $params['type_name'] . '%');
        }

        if (isset($params['is_subjective']) && is_numeric($params['is_subjective'])) {
            $query->where('is_subjective', $params['is_subjective']);
        }

        if (!empty($params['code'])) {
            $query->where('code', 'like', '%' . $params['code'] . '%');
        }
    }
}
