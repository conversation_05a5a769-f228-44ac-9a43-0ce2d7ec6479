<?php

namespace App\Models\School\System;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;

class Course extends BaseModel
{
    use SoftDeletes;

    // 归属于学校
    public function school()
    {
        return $this->belongsTo(School::class, 'school_id', 'id');
    }

    // 归属于校区
    public function schoolCampus()
    {
        return $this->belongsTo(SchoolCampus::class, 'school_campus_id', 'id');
    }

    // 归属于年级
    public function grade()
    {
        return $this->belongsTo(Grade::class, 'grade_id', 'id');
    }

    // 课程有多个老师
    public function teachers()
    {
        return $this->belongsToMany(Teacher::class, 'teacher_courses', 'course_id', 'teacher_id');
    }
}
