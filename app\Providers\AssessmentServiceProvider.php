<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\School\Assessment\AssessmentServiceFactory;
use App\Http\Controllers\School\Assessment\AssessmentProcessController;
use App\Http\Controllers\School\Assessment\PdfHtmlController;

class AssessmentServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     */
    public function register()
    {
        // 绑定测评控制器
        $this->app->when(AssessmentProcessController::class)
            ->needs('$dynamicService')
            ->give(function ($app) {
                $request = $app->make('request');
                $factory = $app->make(AssessmentServiceFactory::class);

                $assessment_details = $this->getAssessmentDetails($request);

                $action_name = $request->route()->getActionMethod();

                $type = '';
                $module = '';

                switch ($action_name) {
                    case 'saveAnswer':
                    case 'teacherSubmitAndReport':
                        $type = $assessment_details['module'] . 'Answer';
                        $module = 'Assessment\Answer';
                        break;
                    case 'report':
                        $type = $assessment_details['type'];
                        $module = 'Assessment\IndividualReport\\' . $assessment_details['module'];
                        break;
                    case 'groupReport':
                        $type = $assessment_details['type'];
                        $module = 'Assessment\GroupReport\\' . $assessment_details['module'];
                        break;
                    default:
                        throw new \RuntimeException("无法为当前方法 [{$action_name}] 确定 type 和 module");
                }

                return $factory->getService($type, $module);
            });

        // 绑定生成pdf控制器
        $this->app->when(PdfHtmlController::class)
            ->needs('$dynamicService')
            ->give(function ($app) {
                $request = $app->make('request');
                $factory = $app->make(AssessmentServiceFactory::class);

                $assessment_details = $this->getAssessmentDetails($request);

                $type = $assessment_details['type'];
                $module = 'Assessment\IndividualReport\\' . $assessment_details['module'];

                return $factory->getService($type, $module);
            });
    }

    /**
     * 获取测评配置详情
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    private function getAssessmentDetails($request)
    {
        $assessment_id = $request->input('assessment_id');
        $assessment_config = config('assessment.assessment_id_type_module');

        if (!$assessment_id || !isset($assessment_config[$assessment_id])) {
            throw new \InvalidArgumentException('assessment_id 缺失或配置不存在');
        }

        return $assessment_config[$assessment_id];
    }

    /**
     * 引导服务
     */
    public function boot(): void
    {
        //
    }
}