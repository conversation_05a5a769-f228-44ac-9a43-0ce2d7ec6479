<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */
namespace app\evaluation\controller;
use app\login\controller\ApiAuth;


/**
 * Class Questions
 * @package app\evaluation\controller
 */
class Categories extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->Categories = new \app\evaluation\service\Categories();
    }
    /**
     * 模块：素养测评-素养类别管理
     * @SWG\Post(path="/evaluation/categories",
     *   tags={"素养测评-素养类别设置:categories"},
     *   summary="素养类别添加",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="category_name",
     *     type="string",
     *     description="素养名称",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="course_id",
     *     type="integer",
     *     description="科目ID",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="content",
     *     type="string",
     *     description="备注，内容",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="parent_id",
     *     type="integer",
     *     description="默认为0，上一级ID",
     *     required=true,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-素养类别管理
     * @SWG\Put(path="/evaluation/categories/{id}",
     *   tags={"素养测评-素养类别设置:categories"},
     *   summary="素养类别修改",
     *  @SWG\Parameter(
     *     in="path",
     *     name="id",
     *     type="integer",
     *     description="id",
     *     required=true,
     *  ),
     *   @SWG\Parameter(
     *     in="body",
     *     name="data",
     *     description="更新的数据",
     *     required=true,
     *     @SWG\Schema(
     *       type="object",
     *       @SWG\Property(property="category_name", type="string", description="素养名称"),
     *       @SWG\Property(property="course_id", type="integer", description="科目ID"),
     *       @SWG\Property(property="content", type="integer", description="备注"),
     *       @SWG\Property(property="parent_id", type="integer", description="上一级ID")
     *     )
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-素养类别管理
     * @SWG\Delete(path="/evaluation/categories/{id}",
     *   tags={"素养测评-素养类别设置:categories"},
     *   summary="素养类别删除",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="素养类别ID",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    /**
     * 模块：素养测评-素养类别管理
     * @SWG\Get(path="/evaluation/categories",
     *   tags={"素养测评-素养类别设置:categories"},
     *   summary="素养类别查询",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="course_id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    
    public function categories(){

        $data = $this->Categories->hand_out();
        apiReturn($data);
    }

}