<?php

namespace App\Http\Controllers\School\Assessment;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\Assessment\ScheduleRequest;
use App\Exports\QuestionStatisticsExport;
use App\Http\Resources\School\Assessment\QuestionStatisticsResource;
use App\Services\School\Assessment\ScheduleService;
use App\Services\School\Assessment\AssessmentTeacherService;
use App\Traits\PaginationTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class ScheduleController extends Controller
{
    use PaginationTrait;

    public function __construct(protected ScheduleService $scheduleService,protected AssessmentTeacherService $assessmentTeacherService)
    {
        
    }

    /**
     * 获取计划列表 教务端
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = $this->scheduleService->getSchedules($request);
        $total = $query->count();
        $list = $this->scopePagination($query)->get();
        foreach ($list as $item) {
            // 计算计划下学生数量
            $item->total_students = $item->tasks[0]->assignments->count();
            // 计算计划下已完成任务数量
            $item->completed_students = $this->scheduleService->getCompletedStudentCount($item->tasks->pluck('id')->toArray());
            // 完成率
            $item->completed_rate = $item->total_students > 0 ? round($item->completed_students / $item->total_students * 100) : 0;
            // 计算计划下每个任务的完成情况
            foreach ($item->tasks as $task) {
                $task->total_students = $item->total_students;
                $task->completed_students = $this->scheduleService->getCompletedStudentCount([$task->id]);
                $task->completed_rate = $task->total_students >0 ? round($task->completed_students / $task->total_students * 100) : 0;

                unset($task->assignments);
            }
        }

        return $this->paginateSuccess($list, $total);
    }

    /**
     * 创建新计划
     *
     * @param ScheduleRequest $request
     * @return JsonResponse
     */
    public function store(ScheduleRequest $request): JsonResponse
    {
        $this->scheduleService->createSchedule($request->validated());
        return $this->message('计划创建成功');
    }

    /**
     * 更新计划
     *
     * @param ScheduleRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(ScheduleRequest $request, int $id): JsonResponse
    {
        $this->scheduleService->updateSchedule($request->validated(), $id);
        return $this->message('计划更新成功');
    }

    /**
     * 删除计划
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $this->scheduleService->destroy($id);
        return $this->message('计划删除成功');
    }

    /**
     * 获取测评计划详情
     *
     * @param int $id 计划ID
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $schedule = $this->scheduleService->getScheduleDetail($id);
        return $this->success($schedule);
    }

    /**
     * 获取计划未完成学生名单
     *
     * @param Request $request
     * @param int $id 计划ID
     * @return JsonResponse
     */
    public function incompleteStudents(Request $request, int $id): JsonResponse
    {
        $filters = $request->only(['keyword', 'class_id']);

        // 添加完成状态筛选，1=所有名单（包含已完成和未完成的），2=未完成，默认为2（未完成），3=已完成的
        $filters['completion_status'] = (int)$request->input('completion_status', 2);

        $students = $this->scheduleService->getIncompleteStudents($id, $filters);
        $total = $students->count();

        // 将集合转换为数组，然后使用array_slice进行分页
        $page = (int)$request->input('page', 1);
        $pageSize = (int)$request->input('page_size', 10);
        $skip = ($page - 1) * $pageSize;

        $studentsArray = $students->values()->all();
        $list = array_slice($studentsArray, $skip, $pageSize);

        return $this->paginateSuccess($list, $total);
    }

    /**
     * 获取测评计划统计详情
     *
     * @param int $id 计划ID
     * @return JsonResponse
     */
    public function statDetail(Request $request): JsonResponse
    {
        $schedule = $this->scheduleService->getScheduleStatDetail($request);
        return $this->success($schedule);
    }

    /**
     * 获取计划下班级名单
     *
     * @param Request $request
     * @param int $id 计划ID
     * @return JsonResponse
     */
    public function scheduleClasses(int $id): JsonResponse
    {
        $data = $this->scheduleService->getScheduleClasses($id);
        return $this->success($data);
    }

    /**
     * 获取测评题目统计数据
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function questionStat(Request $request): JsonResponse
    {
        /** @var \Illuminate\Database\Eloquent\Builder $builder */
        $builder = $this->scheduleService->questionBuilder($request);
        $cnt = $builder->count();
        $list = $this->scopePagination($builder)->orderBy("id", "desc")->get();
        // 给每个问题添加统计信息
        $list = $this->scheduleService->questionStatisticsByTask($request, $list);

        // 使用资源类处理输出格式
        $formattedList = QuestionStatisticsResource::collection($list)->toArray($request);

        return $this->paginateSuccess($formattedList, $cnt);
    }

    /**
     * 导出测评题目统计数据
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function questionExport(Request $request)
    {
        // 获取所有题目数据（不分页）
        $builder = $this->scheduleService->questionBuilder($request);
        $list = $builder->orderBy("id", "asc")->get();

        // 给每个问题添加统计信息
        $list = $this->scheduleService->questionStatisticsByTask($request, $list);

        // 获取测评信息
        $assessmentId = $request->input('assessment_id');
        $assessmentTaskId = $request->input('assessment_task_id');
        $assessment = $this->scheduleService->getAssessmentInfo($assessmentId);
        $fileName = $assessment->name . '题目统计_' . date('YmdHis') . '.xlsx';

        // 准备导出数据
        $exportData = [];

        foreach ($list as $question) {
            // 基本题目信息
            $questionData = [
                'content' => $question->content ?? $question->title ?? '未知题目',
                'avg_score' => $question->statistics['average_score'] ?? 0,
            ];

            // 添加选项、小计和百分比
            if (isset($question->statistics['options']) && !empty($question->statistics['options'])) {
                foreach ($question->statistics['options'] as $option) {
                    $optionKey = $option['option'] ?? '';
                    if (!empty($optionKey)) {
                        $questionData['option_' . $optionKey] = $option['name'] ?? '';
                        $questionData['count_' . $optionKey] = $option['count'] ?? 0;
                        $questionData['percent_' . $optionKey] = ($option['percentage'] ?? 0) . '%';
                    }
                }
            }

            // 添加有效填写量
            $questionData['valid_count'] = $question->statistics['valid_count'] ?? 0;
            $questionData['valid_percent'] = '100%';

            $exportData[] = $questionData;
        }

        // 创建Excel导出服务
        return Excel::download(
            new QuestionStatisticsExport($exportData, $assessment->name),
            $fileName
        );
    }

    /**
     * 获取计划下所有学生的得分情况
     *
     * @param Request $request 请求对象
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStudentsScore(Request $request)
    {
        // 获取学生得分情况查询构建器
        $builder = $this->assessmentTeacherService->getStudentsScoreBuilder($request);
        
        // 计算总数
        $total = $builder->count();
        
        // 应用分页并获取数据
        $assignments = $this->scopePagination($builder)->get();
        
        // 处理分页后的数据
        $list = $this->assessmentTeacherService->processStudentsScore($assignments, $request);
        
        return $this->paginateSuccess($list, $total);
    }
}