<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\DataSync\DataSyncService;
use App\Events\DataSync\SchoolCreated;
use App\Events\DataSync\StudentCreated;
use App\Events\DataSync\TeacherCreated;
use App\Events\DataSync\AdminCreated;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 数据同步控制器
 */
class DataSyncController extends Controller
{
    protected $dataSyncService;

    public function __construct(DataSyncService $dataSyncService)
    {
        $this->dataSyncService = $dataSyncService;
    }

    /**
     * 手动同步学校数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function syncSchool(Request $request): JsonResponse
    {
        try {
            $schoolData = $request->validate([
                'id' => 'required|integer',
                'name' => 'required|string|max:255',
         
            ]);

            $result = $this->dataSyncService->syncSchool($schoolData);
          
            if ($result['success']) {
                return $this->success($result, '学校数据同步成功');
            } else {
                return $this->error($result['message']);
            }
        } catch (\Exception $e) {
            Log::error('手动同步学校数据失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('同步失败: ' . $e->getMessage());
        }
    }

    /**
     * 手动同步学生数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function syncStudent(Request $request): JsonResponse
    {
        try {
            $studentData = $request->validate([
                'id' => 'required|integer',
                'name' => 'required|string|max:255',
                'student_number' => 'nullable|string|max:50',
                'gender' => 'nullable|integer',
                'phone' => 'nullable|string|max:20',
                'email' => 'nullable|email',
                'school_id' => 'required|integer',
                'campus_id' => 'nullable|integer',
                'grade_id' => 'nullable|integer',
                'class_id' => 'nullable|integer',
                'status' => 'nullable|integer'
            ]);

            $result = $this->dataSyncService->syncStudent($studentData);

            if ($result['success']) {
                return $this->success($result, '学生数据同步成功');
            } else {
                return $this->error($result['message']);
            }
        } catch (\Exception $e) {
            Log::error('手动同步学生数据失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('同步失败: ' . $e->getMessage());
        }
    }

    /**
     * 手动同步教师数据
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function syncTeacher(Request $request): JsonResponse
    {
        try {
            // 支持单个教师和批量教师同步
            if ($request->has('teachers_data')) {
                // 批量同步（从TeacherController调用）
                $validated = $request->validate([
                    'teachers_data' => 'required|array',
                    'teachers_data.*.id' => 'required|integer',
                    'teachers_data.*.user_id' => 'required|integer',
                    'teachers_data.*.teacher_name' => 'required|string|max:255',
                    'teachers_data.*.username' => 'required|string',
                    'teachers_data.*.password' => 'required|string',
                    'teachers_data.*.phone' => 'nullable|string|max:20',
                    'teachers_data.*.gender' => 'nullable|integer',
                    'teachers_data.*.school_id' => 'required|integer',
                    'teachers_data.*.school_campus_id' => 'required|integer',
                    'teachers_data.*.is_psychology_teacher' => 'nullable|integer',
                    'teachers_data.*.roles' => 'required|array'
                ]);

                $result = $this->syncTeachersData($validated['teachers_data']);
            } else {
                // 单个教师同步（直接API调用）
                $teacherData = $request->validate([
                    'id' => 'required|integer',
                    'name' => 'required|string|max:255',
                    'teacher_number' => 'nullable|string|max:50',
                    'gender' => 'nullable|integer',
                    'phone' => 'nullable|string|max:20',
                    'email' => 'nullable|email',
                    'school_id' => 'required|integer',
                    'campus_id' => 'nullable|integer',
                    'department' => 'nullable|string|max:100',
                    'position' => 'nullable|string|max:100',
                    'subject' => 'nullable|string|max:100',
                    'status' => 'nullable|integer'
                ]);

                $result = $this->dataSyncService->syncTeacher($teacherData);
            }

            if ($result['success']) {
                return $this->success($result, '教师数据同步成功');
            } else {
                return $this->error($result['message']);
            }
        } catch (\Exception $e) {
            Log::error('手动同步教师数据失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('同步失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量同步教师数据到ysy_member和ysy_teacher表
     *
     * @param array $teachersData
     * @return array
     */
    private function syncTeachersData(array $teachersData): array
    {
        try {
            DB::beginTransaction();

            $sync_results = [];
            $sync_connection = DB::connection('sync_mysql');

            foreach ($teachersData as $teacherData) {
                try {
                    // 获取角色类型
                    $rolesTypes = $teacherData['roles'];

                    // 准备同步到ysy_member表的数据
                    $member_data = [
                        'id' => $teacherData['user_id'],
                        'name' => $teacherData['teacher_name'],
                        'username' => $teacherData['username'],
                        'password' => $teacherData['password'],
                        'mobile' => $teacherData['phone'] ?? '',
                        'gender' => $teacherData['gender'] == 1 ? 1 : 2,
                        'school_id' => $teacherData['school_id'],
                        'role_id' => '0,' . implode(',', $rolesTypes) . ',0',
                        'step' => 0,
                        'created_at' => now(),
                        'updated_at' => now()
                    ];

                    // 同步到ysy_member表
                    $sync_connection->table('ysy_member')->updateOrInsert(
                        ['id' => $teacherData['user_id']],
                        $member_data
                    );

                    // 如果角色类型包含教师(type=3)，同步到ysy_teacher表
                    if (in_array(3, $rolesTypes)) {
                        $teacher_data = [
                            'id' => $teacherData['id'],
                            'member_id' => $teacherData['user_id'],
                            'name' => $teacherData['teacher_name'],
                            'school_id' => $teacherData['school_id'],
                            'school_district' => $teacherData['school_campus_id'],
                            'is_psych' => $teacherData['is_psychology_teacher'] ?? 0,
                            'step' => 0,
                            'created_at' => now(),
                            'updated_at' => now()
                        ];

                        // 同步到ysy_teacher表
                        $sync_connection->table('ysy_teacher')->updateOrInsert(
                            ['id' => $teacherData['id']],
                            $teacher_data
                        );
                    }

                    $sync_results[] = [
                        'teacher_id' => $teacherData['id'],
                        'success' => true,
                        'message' => '同步成功',
                        'synced_to_member' => true,
                        'synced_to_teacher' => in_array(3, $rolesTypes),
                        'role_types' => $rolesTypes
                    ];

                } catch (\Exception $e) {
                    $sync_results[] = [
                        'teacher_id' => $teacherData['id'],
                        'success' => false,
                        'message' => '同步失败: ' . $e->getMessage()
                    ];

                    Log::error('单个教师数据同步失败', [
                        'teacher_data' => $teacherData,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            DB::commit();

            return [
                'success' => true,
                'data' => [
                    'sync_results' => $sync_results,
                    'total_count' => count($teachersData),
                    'success_count' => count(array_filter($sync_results, fn($r) => $r['success'])),
                    'message' => '教师数据批量同步完成'
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('教师数据批量同步失败', [
                'teachers_data' => $teachersData,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '教师数据批量同步失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 手动同步教务人员数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function syncAdmin(Request $request): JsonResponse
    {
        try {
            $adminData = $request->validate([
                'id' => 'required|integer',
                'name' => 'required|string|max:255',
                'username' => 'nullable|string|max:100',
                'gender' => 'nullable|integer',
                'phone' => 'nullable|string|max:20',
                'email' => 'nullable|email',
                'school_id' => 'required|integer',
                'campus_id' => 'nullable|integer',
                'department' => 'nullable|string|max:100',
                'position' => 'nullable|string|max:100',
                'role' => 'nullable|string|max:50',
                'permissions' => 'nullable|array',
                'status' => 'nullable|integer'
            ]);

            $result = $this->dataSyncService->syncAdmin($adminData);

            if ($result['success']) {
                return $this->success($result, '教务人员数据同步成功');
            } else {
                return $this->error($result['message']);
            }
        } catch (\Exception $e) {
            Log::error('手动同步教务人员数据失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('同步失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量同步数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchSync(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'type' => 'required|string|in:school,student,teacher,admin',
                'data' => 'required|array',
                'data.*' => 'required|array'
            ]);

            $results = [];
            $successCount = 0;
            $failCount = 0;

            foreach ($data['data'] as $item) {
                try {
                    switch ($data['type']) {
                        case 'school':
                            $result = $this->dataSyncService->syncSchool($item);
                            break;
                        case 'student':
                            $result = $this->dataSyncService->syncStudent($item);
                            break;
                        case 'teacher':
                            $result = $this->dataSyncService->syncTeacher($item);
                            break;
                        case 'admin':
                            $result = $this->dataSyncService->syncAdmin($item);
                            break;
                        default:
                            throw new \InvalidArgumentException('不支持的数据类型');
                    }

                    if ($result['success']) {
                        $successCount++;
                    } else {
                        $failCount++;
                    }

                    $results[] = [
                        'id' => $item['id'],
                        'success' => $result['success'],
                        'message' => $result['message']
                    ];
                } catch (\Exception $e) {
                    $failCount++;
                    $results[] = [
                        'id' => $item['id'] ?? 'unknown',
                        'success' => false,
                        'message' => $e->getMessage()
                    ];
                }
            }

            return $this->success([
                'total' => count($data['data']),
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'results' => $results
            ], '批量同步完成');

        } catch (\Exception $e) {
            Log::error('批量同步数据失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('批量同步失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除同步数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteSyncData(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'type' => 'required|string|in:school,student,teacher,admin',
                'original_id' => 'required|integer'
            ]);

            $result = $this->dataSyncService->deleteSyncData($data['type'], $data['original_id']);

            if ($result['success']) {
                return $this->success($result, '同步数据删除成功');
            } else {
                return $this->error($result['message']);
            }
        } catch (\Exception $e) {
            Log::error('删除同步数据失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新同步数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateSyncData(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'type' => 'required|string|in:school,student,teacher,admin',
                'original_id' => 'required|integer',
                'update_data' => 'required|array'
            ]);

            $result = $this->dataSyncService->updateSyncData(
                $data['type'], 
                $data['original_id'], 
                $data['update_data']
            );

            if ($result['success']) {
                return $this->success($result, '同步数据更新成功');
            } else {
                return $this->error($result['message']);
            }
        } catch (\Exception $e) {
            Log::error('更新同步数据失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取同步状态
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getSyncStatus(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'type' => 'required|string|in:school,student,teacher,admin',
                'original_id' => 'required|integer'
            ]);

            // 检查同步数据库中是否存在对应记录
            $tableName = match($data['type']) {
                'school' => 'schools',
                'student' => 'students',
                'teacher' => 'teachers',
                'admin' => 'admins'
            };

            $syncRecord = $this->dataSyncService->getSyncConnection()
                ->table($tableName)
                ->where('original_id', $data['original_id'])
                ->first();

            return $this->success([
                'is_synced' => !is_null($syncRecord),
                'sync_record' => $syncRecord,
                'sync_time' => $syncRecord ? $syncRecord->created_at : null
            ], '获取同步状态成功');

        } catch (\Exception $e) {
            Log::error('获取同步状态失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('获取同步状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取字段映射配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getFieldMapping(Request $request): JsonResponse
    {
        try {
            $type = $request->input('type');

            if ($type) {
                $mapping = config("datasync.field_mapping.{$type}", []);
            } else {
                $mapping = config('datasync.field_mapping', []);
            }

            return $this->success([
                'field_mapping' => $mapping,
                'available_types' => ['school', 'student', 'teacher', 'admin']
            ], '获取字段映射成功');

        } catch (\Exception $e) {
            Log::error('获取字段映射失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('获取字段映射失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试字段映射
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function testFieldMapping(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'type' => 'required|string|in:school,student,teacher,admin',
                'test_data' => 'required|array',
                'custom_mapping' => 'nullable|array'
            ]);

            // 如果提供了自定义映射，临时使用它
            if (!empty($data['custom_mapping'])) {
                config(["datasync.field_mapping.{$data['type']}" => $data['custom_mapping']]);
            }

            // 根据类型应用字段映射
            $mappedData = $this->applyTestMapping($data['type'], $data['test_data']);

            return $this->success([
                'original_data' => $data['test_data'],
                'mapped_data' => $mappedData,
                'mapping_rules' => config("datasync.field_mapping.{$data['type']}", [])
            ], '字段映射测试成功');

        } catch (\Exception $e) {
            Log::error('字段映射测试失败', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return $this->error('字段映射测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 应用测试映射
     */
    private function applyTestMapping(string $type, array $data): array
    {
        $configMapping = config("datasync.field_mapping.{$type}", []);
        $mappedData = [];

        foreach ($data as $originalField => $value) {
            if (isset($configMapping[$originalField])) {
                $mappedField = $configMapping[$originalField];
                $mappedData[$mappedField] = $value;
            } else {
                $mappedData[$originalField] = $value;
            }
        }

        return $mappedData;
    }

    /**
     * 获取数据同步配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getConfig(Request $request): JsonResponse
    {
        try {
            $config = [
                'enabled' => config('datasync.enabled', false),
                'sync_connection' => config('datasync.sync_connection', 'sync_mysql'),
                'sync_mode' => config('datasync.sync_mode', 'async'),
                'queue' => config('datasync.queue', []),
                'retry' => config('datasync.retry', []),
                'field_mapping' => config('datasync.field_mapping', []),
                'table_mapping' => config('datasync.table_mapping', []),
                'logging' => config('datasync.logging', []),
                'performance' => config('datasync.performance', [])
            ];

            return $this->success($config, '获取配置成功');

        } catch (\Exception $e) {
            Log::error('获取数据同步配置失败', [
                'error' => $e->getMessage()
            ]);

            return $this->error('获取配置失败: ' . $e->getMessage());
        }
    }
}
