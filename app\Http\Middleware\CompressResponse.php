<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class CompressResponse
{
    public function handle($request, Closure $next)
    {
        $response = $next($request);
        // 确保$response是一个响应对象
        if (is_array($response)) {
            $response = new Response(json_encode($response));
        }
        // 确保$response是Symfony响应对象
        if (!$response instanceof SymfonyResponse) {
            return $response;
        }

        if (config('app.compress_response') && $this->shouldCompress($request, $response)) {
            // 确定是否压缩
            $compress = true;

            // 例如，只压缩 API 接口的响应
            if ($request->is('api/applet/*')||$request->is('api/upload_image')
                ||$request->is('api/upload_file')
                ||$request->is('api/upload_tinymce_picture')) {
                // 不压缩
                $compress = false;
            }

            // 根据条件压缩响应
            if ($compress) {
                // 检查响应内容是否有效
                $content = $response->getContent();
                if ($this->isValidContent($content)) {
                    $response->headers->set('Content-Encoding', 'gzip');
                    $response->setContent(gzencode($content, 9));
                }
//                $response->header('Content-Encoding', 'gzip');
//                $response->setContent(gzencode($response->getContent(), 9));
            }
        }

        return $response;
    }

    protected function shouldCompress($request, $response)
    {
        // Check if the client accepts gzip encoding
        if (!$request->accepts('gzip')) {
            return false;
        }

        // Check the response type, only compress JSON responses
        if ($response->headers->get('Content-Type') !== 'application/json') {
            return false;
        }

        return true;
    }
    protected function isValidContent($content)
    {
        // 检查内容是否为空或格式是否正确
        return !empty($content) && is_string($content);
    }
}
