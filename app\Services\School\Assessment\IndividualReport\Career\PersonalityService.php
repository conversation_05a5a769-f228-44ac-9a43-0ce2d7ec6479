<?php
namespace App\Services\School\Assessment\IndividualReport\Career;

use App\Models\School\Assessment\Template\AssessmentCareerMbtiTemplate;
use App\Services\School\Assessment\IndividualReport\AbstractIndividualReportService;

class PersonalityService extends AbstractIndividualReportService
{
    /**
     * 解析性格评估结果
     */
    private const MBTI_DIMENSIONS = [
        ['e' => '外倾', 'i' => '内倾'],
        [
            's' => [
                '感觉',
                ['t' => '掌握型', 'f' => '人际型']
            ],
            'n' => [
                '直觉',
                ['t' => '理解型', 'f' => '自我表达型']
            ]
        ],
        ['t' => '思维', 'f' => '情感'],
        ['j' => '判断', 'p' => '知觉']
    ];

    public function generateReport(array $params): array
    {
        $assignmentId = $params['assessment_task_assignment_id'];
        // 获取分发信息
        $assignmentInfo = $this->getAssignmentInfo($assignmentId);

        // 生成报告
        $results = $assignmentInfo['standard_results'];

        // 检查结果数据
        if (!$results || !is_array($results)) {
            throw new \Exception('测评结果数据格式错误');
        }

        $assessmentInfo = $this->personalityInfo($results);

        // 合并结果
        return array_merge($assignmentInfo, $assessmentInfo);
    }

    public function personalityInfo($result): array
    {
        // 初始化结果
        $text = '';

        // E/I维度
        $text .= self::MBTI_DIMENSIONS[0][$this->getScore($result, 'E') > $this->getScore($result, 'I') ? 'e' : 'i'];

        // S/N维度
        $text .= self::MBTI_DIMENSIONS[1][$this->getScore($result, 'S') > $this->getScore($result, 'N') ? 's' : 'n'][0];

        // T/F维度
        $text .= self::MBTI_DIMENSIONS[2][$this->getScore($result, 'T') > $this->getScore($result, 'F') ? 't' : 'f'];

        // J/P维度
        $text .= self::MBTI_DIMENSIONS[3][$this->getScore($result, 'J') > $this->getScore($result, 'P') ? 'j' : 'p'];

        // 从数据库获取模板数据
        $template = AssessmentCareerMbtiTemplate::where('code', $result['code'])->first();

        // 为template添加personality_dimension数据
        if ($template) {
            $template->personality_dimension = $this->generatePersonalityDimension($result);
            $template->you_learning_style = $this->generateLearningStyle($template->code);
        }

        // 返回结果
        return [
            // 'result' => $result,
            'dimension_statistics' => $this->generateDimensionStatistics($result),
            'type' => $result['code'],
            'description' => $text,
            'style' => $this->getMbtiStyle($result),
            'template' => $template
        ];
    }


    private function getMbtiStyle($result): string
    {
        $isThinking = $this->getScore($result, 'T') > $this->getScore($result, 'F');
        if ($this->getScore($result, 'S') > $this->getScore($result, 'N')) {
            return $isThinking ? '掌握型' : '人际型';
        }
        return $isThinking ? '理解型' : '自我表达型';
    }

    /**
     * 从standard_results的dimensions数组中获取指定维度的分数
     *
     * @param array|null $result standard_results数据
     * @param string $code 维度代码
     * @return int 分数
     */
    private function getScore(?array $result, string $code): int
    {
        if ($result && isset($result['dimensions']) && is_array($result['dimensions'])) {
            foreach ($result['dimensions'] as $dimension) {
                if (isset($dimension['code']) && $dimension['code'] === $code) {
                    return $dimension['score'] ?? 0;
                }
            }
        }
        return 0;
    }

    /**
     * 生成性格维度统计图数据
     *
     * @param array|null $result standard_results数据
     * @return array 四个维度的统计数据
     */
    private function generateDimensionStatistics(?array $result): array
    {
        // 获取各维度分数
        $eScore = $this->getScore($result, 'E');
        $iScore = $this->getScore($result, 'I');
        $sScore = $this->getScore($result, 'S');
        $nScore = $this->getScore($result, 'N');
        $tScore = $this->getScore($result, 'T');
        $fScore = $this->getScore($result, 'F');
        $jScore = $this->getScore($result, 'J');
        $pScore = $this->getScore($result, 'P');

        return [
            // E/I维度 - 能量来源
            'energy_source' => [
                [
                    'code' => 'E',
                    'name' => '外倾',
                    'score' => $eScore,
                    'percentage' => $this->calculatePercentage($eScore, $iScore)
                ],
                [
                    'code' => 'I',
                    'name' => '内倾',
                    'score' => $iScore,
                    'percentage' => $this->calculatePercentage($iScore, $eScore)
                ]
            ],
            // S/N维度 - 信息处理
            'information_processing' => [
                [
                    'code' => 'S',
                    'name' => '实感',
                    'score' => $sScore,
                    'percentage' => $this->calculatePercentage($sScore, $nScore)
                ],
                [
                    'code' => 'N',
                    'name' => '直觉',
                    'score' => $nScore,
                    'percentage' => $this->calculatePercentage($nScore, $sScore)
                ]
            ],
            // T/F维度 - 决策方式
            'decision_making' => [
                [
                    'code' => 'T',
                    'name' => '思维',
                    'score' => $tScore,
                    'percentage' => $this->calculatePercentage($tScore, $fScore)
                ],
                [
                    'code' => 'F',
                    'name' => '情感',
                    'score' => $fScore,
                    'percentage' => $this->calculatePercentage($fScore, $tScore)
                ]
            ],
            // J/P维度 - 生活方式
            'way_of_life' => [
                [
                    'code' => 'J',
                    'name' => '判断',
                    'score' => $jScore,
                    'percentage' => $this->calculatePercentage($jScore, $pScore)
                ],
                [
                    'code' => 'P',
                    'name' => '知觉',
                    'score' => $pScore,
                    'percentage' => $this->calculatePercentage($pScore, $jScore)
                ]
            ]
        ];
    }

    /**
     * 计算百分比
     *
     * @param int $score1 当前维度分数
     * @param int $score2 对应维度分数
     * @return int 百分比（四舍五入取整数）
     */
    private function calculatePercentage(int $score1, int $score2): int
    {
        $total = $score1 + $score2;
        if ($total === 0) {
            return 50; // 如果总分为0，返回50%
        }
        return round(($score1 / $total) * 100);
    }

    /**
     * 生成性格维度数据
     *
     * @param array|null $result standard_results数据
     * @return array 性格维度数据
     */
    private function generatePersonalityDimension(?array $result): array
    {
        // 获取各维度分数
        $eScore = $this->getScore($result, 'E');
        $iScore = $this->getScore($result, 'I');
        $sScore = $this->getScore($result, 'S');
        $nScore = $this->getScore($result, 'N');
        $tScore = $this->getScore($result, 'T');
        $fScore = $this->getScore($result, 'F');
        $jScore = $this->getScore($result, 'J');
        $pScore = $this->getScore($result, 'P');

        $personalityDimension = [];

        // 能量来源 (I内倾 vs E外倾) - 取分数高的
        if ($iScore > $eScore) {
            $personalityDimension['energy_source'] = [
                [
                    'describe' => "你的内倾指数(I)为{$iScore}/100，属于“选择性社交”类型：",
                    'info' => [
                        '喜欢独处或与少数亲友相处，在人群中容易感到疲惫',
                        '不常主动表达想法，但可能内心世界丰富',
                        '更愿意倾听而非主导谈话，在熟悉环境中才会完全放松',
                        '需要独处时间来恢复精力，比如看书、听音乐或散步'
                    ]
                ]
            ];
        } else {
            $personalityDimension['energy_source'] = [
                [
                    'describe' => "你的外倾指数(E)为{$eScore}/100，属于“主动社交”类型：",
                    'info' => [
                        '在社交活动中充满活力，人越多反而越感到兴奋',
                        '喜欢主动结识新朋友，乐于分享自己的想法和经历',
                        '通过与人交谈来理清思路，边说边想是常见方式',
                        '行动快速直接，喜欢尝试新鲜事物，容易带动气氛'
                    ]
                ]
            ];
        }

        // 决策方式 (F情感 vs T思维) - 取分数高的
        if ($tScore > $fScore) {
            $personalityDimension['decision_making'] = [
                [
                    'describe' => "你的思维偏好(T)为{$tScore}/100，是一个“理性做决定”的人：",
                    'info' => [
                        '做决定时优先考虑逻辑和客观因素，追求合理性',
                        '重视公平原则，认为规则应该对所有人都一样',
                        '表达直接坦率，倾向于指出问题而非安慰情绪',
                        '遇到冲突时，习惯先分析问题再考虑人际关系'
                    ]
                ]
            ];
        } else {
            $personalityDimension['decision_making'] = [
                [
                    'describe' => "你的情感偏好(F)为{$fScore}/100，是一个“用心做决定”的人：",
                    'info' => [
                        '决策时重视情感因素和人际关系的影响',
                        '天生善解人意，能敏锐察觉他人的情绪变化',
                        '说话委婉体贴，常常先考虑对方的感受',
                        '追求人际和谐，有时会为照顾他人而妥协'
                    ]
                ]
            ];
        }

        // 信息处理 (S实感 vs N直觉) - 取分数高的
        if ($sScore > $nScore) {
            $personalityDimension['information_processing'] = [
                [
                    'describe' => "你的实感倾向(S)为{$sScore}/100，是一个“眼见为实”的学习者：",
                    'info' => [
                        '注重实际经验和具体事实，更相信亲身验证的信息',
                        '擅长记忆细节，对颜色、声音等感官信息敏感',
                        '喜欢按部就班地做事，重视传统和已验证的方法',
                        '更关注当下现实，善于处理具体而实际的问题'
                    ]
                ]
            ];
        } else {
            $personalityDimension['information_processing'] = [
                [
                    'describe' => "你的直觉倾向(N)为{$nScore}/100，是一个“洞察本质”的学习者：",
                    'info' => [
                        '更关注未来可能性，常常思考“如果...会怎样”的问题',
                        '喜欢探讨抽象概念和创新想法，对理论感兴趣',
                        '容易看到事物间的联系，思维具有跳跃性特点',
                        '有时会忽略现实细节，更重视整体意义和象征'
                    ]
                ]
            ];
        }

        // 生活方式 (P感知 vs J判断) - 取分数高的
        if ($jScore > $pScore) {
            $personalityDimension['way_of_life'] = [
                [
                    'describe' => "你的判断倾向(J)为{$jScore}/100，是一个“计划先行”的人：",
                    'info' => [
                        '喜欢提前制定计划，做事有条不紊',
                        '重视截止日期，通常会提前完成任务，避免赶工',
                        '日常生活有规律，物品摆放井然有序',
                        '总是有明确的目标，做决定后很少反复改变'
                    ]
                ]
            ];
        } else {
            $personalityDimension['way_of_life'] = [
                [
                    'describe' => "你的感知倾向(P)为{$pScore}/100，是一个“灵活应变”的人：",
                    'info' => [
                        '喜欢保持灵活性，讨厌被严格的时间表束缚',
                        '常常根据当下情况改变主意，享受随性而为的轻松感',
                        '做事风格比较随性，但总能截止日期前爆发出高效率',
                        '对新奇事物充满探索欲，兴趣爱好广泛却容易转移'
                    ]
                ]
            ];
        }

        return $personalityDimension;
    }

    /**
     * 生成学习风格数据
     *
     * @param string $code MBTI性格代码（如INFJ）
     * @return array 学习风格数据
     */
    private function generateLearningStyle(string $code): array
    {
        // 从MBTI代码中取中间两位字符作为学习风格维度
        // 例如：INFJ -> NF, ESTP -> ST
        $learningStyleDimension = substr($code, 1, 2);

        // 根据维度组合返回对应的学习风格数据
        switch ($learningStyleDimension) {
            case 'SF':
                return [
                    'personality_dimension' => 'SF',
                    'learning_style' => '人际型学习者',
                    'learning_style_info' => '你属于人际型学习者，通过与人互动和动手实践能获得最佳学习效果。你的学习特点包括：',
                    'learning_characteristics' => [
                        '和同学一起学习时效率更高，喜欢小组讨论',
                        '通过实际案例（如实验、情景模拟）理解更快',
                        '需要老师、同学的鼓励来保持动力',
                        '对死记硬背容易厌烦，要用图表、视频等生动方式'
                    ],
                    'learning_method' => [
                        '组建学习小组',
                        '用彩笔做笔记',
                        '把知识编成故事'
                    ]
                ];

            case 'ST':
                return [
                    'personality_dimension' => 'ST',
                    'learning_style' => '掌握型学习者',
                    'learning_style_info' => '你属于掌握型学习者，通过逻辑分析和实践操作来掌握知识。你的学习特点包括：',
                    'learning_characteristics' => [
                        '喜欢有条理、结构化的学习内容',
                        '擅长通过练习和重复来巩固知识',
                        '重视事实和细节，对理论的应用有浓厚兴趣',
                        '在解决问题时倾向于使用已知的方法和步骤'
                    ],
                    'learning_method' => [
                        '将知识表格化',
                        '错题整理分类本',
                        '使用记忆卡片'
                    ]
                ];

            case 'NF':
                return [
                    'personality_dimension' => 'NF',
                    'learning_style' => '自我表达型学习者',
                    'learning_style_info' => '你属于自我表达型学习者，通过表达自己的想法和感受来深化理解。你的学习特点包括：',
                    'learning_characteristics' => [
                        '喜欢通过写作、演讲或艺术创作来表达所学',
                        '对抽象概念和理论有浓厚兴趣，喜欢探索其深层含义',
                        '在学习中寻求个人成长和自我实现',
                        '需要自由和空间来发挥创造力'
                    ],
                    'learning_method' => [
                        '学习手账记录',
                        '主题演讲分享',
                        '绘制思维导图'
                    ]
                ];

            case 'NT':
                return [
                    'personality_dimension' => 'NT',
                    'learning_style' => '理解型学习者',
                    'learning_style_info' => '你属于理解型学习者，通过深入思考和逻辑推理来理解复杂概念。你的学习特点包括：',
                    'learning_characteristics' => [
                        '喜欢挑战性的学习任务，追求知识的深度和广度',
                        '擅长分析问题，找出规律和模式',
                        '对理论和模型有浓厚兴趣，喜欢探究其背后的原理',
                        '在学习中注重逻辑性和系统性'
                    ],
                    'learning_method' => [
                        '参加竞赛辩论',
                        '设计知识框架图',
                        '做同学的小老师'
                    ]
                ];

            default:
                // 默认返回SF类型
                return [
                    'personality_dimension' => 'SF',
                    'learning_style' => '人际型学习者',
                    'learning_style_info' => '你属于人际型学习者，通过与人互动和动手实践能获得最佳学习效果。你的学习特点包括：',
                    'learning_characteristics' => [
                        '和同学一起学习时效率更高，喜欢小组讨论',
                        '通过实际案例（如实验、情景模拟）理解更快',
                        '需要老师、同学的鼓励来保持动力',
                        '对死记硬背容易厌烦，要用图表、视频等生动方式'
                    ],
                    'learning_method' => [
                        '组建学习小组',
                        '用彩笔做笔记',
                        '把知识编成故事'
                    ]
                ];
        }
    }

}