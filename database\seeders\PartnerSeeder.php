<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * 代理商、教育局等合作伙伴
 */
class PartnerSeeder extends Seeder
{
    protected string $connect = 'mysql_demo';
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 查询原有的代理商数据
        $list = DB::connection($this->connect)->table('agent')
            ->where('is_delete', 0)
            ->orderBy('id', 'asc')
            ->get();
        // 循环数据,组装新的结构数据
        $data = [];
        foreach ($list as $item){
            //  提取站点配置信息
            // 特殊配置信息
            $special_config = [
                'style' => $item->style,
                'logo' => $item->logo,
                'logo_white' => $item->logo_white,
                'logo_red' => $item->logo_red,
                'logout_url' => $item->logout_url,
                'logo_base64' => $item->logo_base64,
                'show' => $item->show,
                'skip_url' => $item->skip_url,
            ];
            // 第三方配置信息，比如：天喻
            $third_config = [
                'import' => $item->import,
                'import_type' => $item->import_type,
            ];
            // 登出配置信息
            $logout_config = [
                'show_logout' => $item->show_logout,
                'logout_url' => $item->logout_url,
            ];
            $siteConfig = [
                'name' => $item->name,
                'title' => $item->title,
                'icon' => $item->icon,
                'special_config' => json_encode($special_config),
                'third_config' => json_encode($third_config),
                'logout_config' => json_encode($logout_config),
                'created_at' => date('Y-m-d H:i:s',time()),
                'updated_at' => date('Y-m-d H:i:s',time()),
            ];
            // 保存站点配置信息，生成站点ID
            $siteConfigId = DB::table("site_configs")->insertGetId($siteConfig);
            // 合作伙伴数据
            $data[] = [
                'agent_id' => $item->id,
                'name' => $item->name,
                'province' => $item->province,
                'city' => $item->city,
                'district' => $item->district,
                'type' => $item->type,
                'address' => $item->agent_address,
                'date_start' => $item->add_time,
                'date_due' => $item->end_time,
                'status' => $item->status,
                'site_config_id' => $siteConfigId,
                'created_at' => date('Y-m-d H:i:s',time()),
                'updated_at' => date('Y-m-d H:i:s',time()),
            ];
        }

        // 保存(合作伙伴)数据
        DB::table("partners")->insert($data);
        dd('done!');
    }
}
