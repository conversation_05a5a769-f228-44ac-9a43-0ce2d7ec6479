<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organization_has_menus', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('organization_id')->nullable(false)->comment('机构id');
            $table->unsignedInteger('menu_id')->nullable(false)->comment('菜单id');
            $table->string('menu_code', 20)->default('')->comment('菜单编码（留存）');
            $table->unsignedInteger('parent_id')->nullable(false)->comment('菜单父级id');
            $table->string('menu_alias', 20)->default('')->comment('菜单别名');
            $table->integer('sort')->default(1)->comment('排序');
            $table->date('date_start')->comment('菜单启用日期');
            $table->date('date_due')->comment('菜单到期日期');
            $table->tinyInteger('status')->default(1)->comment('状态：1正常2禁用');
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
            $table->timestamps();
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `organization_has_menus` comment '机构拥有的所有菜单表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organization_has_menus');
    }
};
