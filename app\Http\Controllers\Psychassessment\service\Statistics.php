<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON>
 * Date: 2024/01/19
 * Time: 17:52
 */
namespace app\psychassessment\service;

class Statistics{

    public function __construct()
    {
        $this->statistics = new \app\psychassessment\logic\Statistics();
    }

    public function get_member_score_list()
    {
        $data = $this->statistics->get_member_score_list();
        apiReturn($data);
    }

    public function get_statistic_analysis()
    {
        $data = $this->statistics->get_statistic_analysis();
        apiReturn($data);
    }

    public function batch_download_pdf()
    {
        $data = $this->statistics->batch_download_pdf();
        apiReturn($data);
    }
}