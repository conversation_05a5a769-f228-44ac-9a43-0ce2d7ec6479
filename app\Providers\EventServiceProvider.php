<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        // 数据同步事件监听器
        \App\Events\DataSync\SchoolCreated::class => [
            \App\Listeners\DataSync\SyncSchoolData::class,
        ],
        \App\Events\DataSync\StudentCreated::class => [
            \App\Listeners\DataSync\SyncStudentData::class,
        ],
        \App\Events\DataSync\TeacherCreated::class => [
            \App\Listeners\DataSync\SyncTeacherData::class,
        ],
        \App\Events\DataSync\AdminCreated::class => [
            \App\Listeners\DataSync\SyncAdminData::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
