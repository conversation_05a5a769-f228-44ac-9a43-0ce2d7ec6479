<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DataSync\DataSyncService;
use App\Helpers\DataSyncHelper;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TestClassSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:class-sync {--campus_id=} {--school_id=} {--grade=10}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试班级数据同步功能';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $campusId = $this->option('campus_id');
        $schoolId = $this->option('school_id');
        $grade = (int)$this->option('grade');
        
        $this->info("开始测试班级数据同步功能");
        
        // 1. 测试配置
        $this->testConfig();
        
        // 2. 测试数据库连接
        $this->testDatabaseConnection();
        
        // 3. 测试班级同步
        $this->testClassSync($campusId, $schoolId, $grade);
        
        return 0;
    }
    
    /**
     * 测试配置
     */
    private function testConfig()
    {
        $this->info("\n=== 测试配置 ===");
        
        $enabled = config('datasync.enabled');
        $this->info("数据同步开关: " . ($enabled ? '启用' : '禁用'));
    }
    
    /**
     * 测试数据库连接
     */
    private function testDatabaseConnection()
    {
        $this->info("\n=== 测试数据库连接 ===");
        
        try {
            // 测试主数据库连接
            $mainResult = DB::connection('mysql')->select('SELECT 1 as test');
            $this->info("✅ 主数据库连接正常");
            
            // 测试同步数据库连接
            $syncResult = DB::connection('sync_mysql')->select('SELECT 1 as test');
            $this->info("✅ 同步数据库连接正常");
            
        } catch (\Exception $e) {
            $this->error("❌ 数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 测试班级同步
     */
    private function testClassSync($campusId, $schoolId, $grade)
    {
        $this->info("\n=== 测试班级同步 ===");
        
        try {
            $syncHelper = new DataSyncHelper();
            
            // 如果提供了校区ID，先查询校区信息
            if ($campusId) {
                $campus = DB::connection('mysql')->table('school_campuses')
                    ->where('id', $campusId)
                    ->where('status', 1)
                    ->first();
                
                if ($campus) {
                    $this->info("找到校区信息:");
                    $this->info("  校区ID: {$campus->id}");
                    $this->info("  校区名称: {$campus->campus_name}");
                    $this->info("  学校ID: {$campus->school_id}");
                    $schoolId = $campus->school_id;
                } else {
                    $this->error("❌ 未找到校区ID: {$campusId}");
                    return;
                }
            }
            
            if (!$schoolId) {
                $this->error("❌ 请提供学校ID或校区ID");
                return;
            }
            
            // 测试班级数据
            $testClassData = [
                'id' => 9999,
                'name' => '测试班级_' . date('YmdHis'),
                'grade' => $grade,
                'school_id' => $schoolId,
                'campus_id' => $campusId
            ];
            
            $this->info("测试班级数据: " . json_encode($testClassData, JSON_UNESCAPED_UNICODE));
            
            // 调用同步
            $result = $syncHelper->syncClass($testClassData);
            
            if ($result['success']) {
                $this->info("✅ 班级数据同步成功");
                $this->info("同步结果: " . json_encode($result, JSON_UNESCAPED_UNICODE));
                
                // 验证年级数据
                $gradeData = DB::connection('sync_mysql')
                    ->table('ysy_grade')
                    ->where('id', $result['grade_id'])
                    ->first();
                
                if ($gradeData) {
                    $this->info("✅ 年级数据验证成功:");
                    $this->info("  年级ID: {$gradeData->id}");
                    $this->info("  年份: {$gradeData->name}");
                    $this->info("  年级名称: {$gradeData->grade_name}");
                    $this->info("  年级排序: {$gradeData->grade_sort}");
                }
                
                // 验证班级数据
                $classData = DB::connection('sync_mysql')
                    ->table('ysy_class')
                    ->where('id', $testClassData['id'])
                    ->first();
                
                if ($classData) {
                    $this->info("✅ 班级数据验证成功:");
                    $this->info("  班级ID: {$classData->id}");
                    $this->info("  班级名称: {$classData->name}");
                    $this->info("  学校ID: {$classData->school_id}");
                    $this->info("  校区ID: {$classData->school_district}");
                    $this->info("  年级ID: {$classData->grade_id}");
                }
                
            } else {
                $this->error("❌ 班级数据同步失败: " . $result['message']);
            }
            
        } catch (\Exception $e) {
            $this->error("❌ 测试班级同步异常: " . $e->getMessage());
            $this->error("错误详情: " . $e->getTraceAsString());
        }
    }
}
