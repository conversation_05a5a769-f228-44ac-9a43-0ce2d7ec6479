<?php

namespace App\Services\School\Assessment\GroupReport\Capability;

/**
 * 沟通与合作评估团体报告服务类
 * 
 * 该类用于生成沟通与合作评估的团体报告
 */
class CommunicationCollaborationService extends AbstractCapabilityService
{
    /**
     * 获取配置键名
     * 
     * @return string 配置键名
     */
    protected function getConfigKey(): string
    {
        return 'communication_collaboration';
    }

    /**
     * 处理维度分类
     * 
     * 移除第一个分类
     * 
     * @param array $category 分类数据
     * @return array 处理后的分类数据
     */
    protected function processDimensionCategory(array $category): array
    {
        return array_slice($category, 1);
    }

    /**
     * 是否移除总分
     * 
     * @return bool 是否移除
     */
    protected function shouldRemoveTotal(): bool
    {
        return false; // 沟通与合作不需要移除总分
    }
}