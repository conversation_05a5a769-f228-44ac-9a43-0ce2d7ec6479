<?php

namespace App\Models\Admin;

use App\Models\BaseModel;

class Menu extends BaseModel
{
    public function childrenCategories()
    {
        return $this->hasMany(self::class, 'parent_id', 'id')
            ->where('status', 1)
            ->orderBy('sort');
    }

    public function children()
    {
        return $this->childrenCategories()->with('children');
    }

    //重构 crowd get 方法
    // public function getCrowdAttribute($value)
    // {
    //     return json_decode($value, true);
    // }
        //重构 crowd get 方法
    public function getCrowdAttribute($value)
    {
        if (empty($value)) {
            return [];
        }

        $decoded = json_decode($value, true);
        if (!is_array($decoded)) {
            return [];
        }

        // 确保返回的数组按数字顺序排序
        sort($decoded, SORT_NUMERIC);
        return $decoded;
    }

    // set
    public function setCrowdAttribute($value)
    {
        $this->attributes['crowd'] = json_encode($value, JSON_UNESCAPED_UNICODE);
    }
}
