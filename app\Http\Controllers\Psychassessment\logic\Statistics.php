<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2024/01/22
 * Time: 15:16
 */
namespace app\psychassessment\logic;
use ZipArchive;

class Statistics{

    public function __construct()
    {
        $this->relation = new \app\psychassessment\model\PlanSurveyRelation();
        $this->plan = new \app\psychassessment\model\Plan();
        $this->FocusModel = new \app\psychassessment\model\Focus();
        $this->survey_user_model = new \app\survey\model\SurveyUser();
        $this->user = get_user();
    }

    public function get_member_score_list()
    {
        $survey_type   = input('survey_type');
        $plan_id       = input('plan_id');
        $is_complete   = input('is_complete');//已完成未完成
        $warning_level = input('warning_level');//预警等级
        $abnormal      = input('abnormal');//异常项目
        $pageNumber    = input('page', 1); // 获取页码，默认为1
        $pageSize      = input('pagesize', 10); // 获取每页显示的记录数，默认为10
        $survey_id = db('survey')->where('survey_type',$survey_type)->value('id');

        $w['plan.id'] = $plan_id;
        switch ($this->user['role_source_id']){
            case 2:
                $member_ids = $this->plan->alias('plan')
                    ->join('student student','find_in_set(student.class_id,plan.class_ids) and find_in_set(student.member_id,plan.member_ids)')
                    ->join('grade grade','student.grade_id = grade.id')
                    ->join('class class','student.class_id = class.id')
                    ->field('student.id as student_id,student.member_id,case when student.gender=1 then "男" else "女" end as sex,grade.name as grade_year,grade.grade_name,class.name as class_name,student.student_no,student.name as student_name')
                    ->where($w)
                    ->select();
                break;
            case 3:
                $w['teacher.member_id'] = $this->user['id'];
                $member_ids = $this->plan->alias('plan')
                    ->join('student student','find_in_set(student.class_id,plan.class_ids) and find_in_set(student.member_id,plan.member_ids)')
                    ->join('grade grade','student.grade_id = grade.id')
                    ->join('class class','student.class_id = class.id')
                    ->join('teacher teacher','find_in_set(student.class_id,teacher.class_ids)')
                    ->field('student.id as student_id,student.member_id,case when student.gender=1 then "男" else "女" end as sex,grade.name as grade_year,grade.grade_name,class.name as class_name,student.student_no,student.name as student_name')
                    ->where($w)
                    ->select();
                break;
        }
        $member_ids = to_arr($member_ids);
        $view_memberid = array_column($member_ids,'member_id');
        $focus_arr = $this->FocusModel->field('member_id,id as focus_id')->where(['member_id'=>['in',$view_memberid],'status'=>0])->group('member_id')->select();
        $focus_arr = to_arr($focus_arr);
        $focus_memberid = array_column($focus_arr,'member_id');
        $focusid_memberid_map = array_column($focus_arr,'focus_id','member_id');

        $where['relation.plan_id'] = $plan_id;
        $where['session.survey_id'] = $survey_id;
        $data = $this->relation->alias('relation')
            ->join('survey_user_session session','session.session_id = relation.session_id')
            ->join('survey survey','survey.id = session.survey_id')
            ->field('session.survey_id,session.session_id,session.member_id,session.result,session.used_time,session.pdf_url')
            ->where($where)
            ->select();
        $data = to_arr($data);
        $data = array_column($data,null,'member_id');

        foreach ($member_ids as $key => $value){
            if(isset($data[$value['member_id']])){
                $result = json_decode($data[$value['member_id']]['result'],true);
                $member_ids[$key]['is_complete'] = '已完成';
                $member_ids[$key]['used_time']   = $data[$value['member_id']]['used_time'];
                $member_ids[$key]['pdf_url']     = $data[$value['member_id']]['pdf_url'];
                $member_ids[$key]['session_id']  = $data[$value['member_id']]['session_id'];
            }else{
                $result = [];
                $member_ids[$key]['is_complete'] = '未完成';
                $member_ids[$key]['used_time']   = '';
                $member_ids[$key]['pdf_url']     = '';
                $member_ids[$key]['session_id']  = '';
            }
            if(in_array($value['member_id'],$focus_memberid)){
                $member_ids[$key]['care_level'] = 1;//等于1代表已加入重点关注
                $member_ids[$key]['focus_id'] = $focusid_memberid_map[$value['member_id']];//等于1代表已加入重点关注
            }else{
                $member_ids[$key]['care_level'] = 0;
                $member_ids[$key]['focus_id'] = '';//等于1代表已加入重点关注
            }
            switch ($survey_type){
                case 26:
                    //此处是自信[[44,90,60,44,84,60],64],6个维度加一个总分
                    $member_ids[$key]['warning_level'] = $result ? confidence_situation($result) : '';
                    $member_ids[$key]['scale']         = $result ? $result[1] : '';

                    $member_ids[$key]['jiazhizixin']   = $result ? $result[0][0] : '';
                    $member_ids[$key]['pindezixin']    = $result ? $result[0][1] : '';
                    $member_ids[$key]['renjizixin']    = $result ? $result[0][2] : '';
                    $member_ids[$key]['nenglizixin']   = $result ? $result[0][3] : '';
                    $member_ids[$key]['xueyezixin']    = $result ? $result[0][4] : '';
                    $member_ids[$key]['waibiaozixin']  = $result ? $result[0][5] : '';

                    $member_ids[$key]['scale_level']        = $result ? confidence_abnormal($result[1]) : '';
                    $member_ids[$key]['jiazhizixin_level']  = $result ? confidence_abnormal($result[0][0]) : '';
                    $member_ids[$key]['pindezixin_level']   = $result ? confidence_abnormal($result[0][1]) : '';
                    $member_ids[$key]['renjizixin_level']   = $result ? confidence_abnormal($result[0][2]) : '';
                    $member_ids[$key]['nenglizixin_level']  = $result ? confidence_abnormal($result[0][3]) : '';
                    $member_ids[$key]['xueyezixin_level']   = $result ? confidence_abnormal($result[0][4]) : '';
                    $member_ids[$key]['waibiaozixin_level'] = $result ? confidence_abnormal($result[0][5]) : '';
                    break;
                case 27:
                    //此处是自我意识$arr=[[44,77,60,44,84,60],64]，[['行为意识','学业意识','身体意识','情绪意识','人际意识','幸福意识'],'总分'];
                    $member_ids[$key]['warning_level'] = $result ? awareness_situation($result) : '';
                    $member_ids[$key]['scale']         = $result ? $result[1] : '';

                    $member_ids[$key]['xingweiyishi']  = $result ? $result[0][0] : '';
                    $member_ids[$key]['xueyeyishi']    = $result ? $result[0][1] : '';
                    $member_ids[$key]['shentiyishi']   = $result ? $result[0][2] : '';
                    $member_ids[$key]['qingxuyishi']   = $result ? $result[0][3] : '';
                    $member_ids[$key]['renjiyishi']    = $result ? $result[0][4] : '';
                    $member_ids[$key]['xingfuyishi']   = $result ? $result[0][5] : '';

                    $member_ids[$key]['scale_level']        = $result ? awareness_abnormal($result[1]) : '';
                    $member_ids[$key]['xingweiyishi_level'] = $result ? awareness_abnormal($result[0][0]) : '';
                    $member_ids[$key]['xueyeyishi_level']   = $result ? awareness_abnormal($result[0][1]) : '';
                    $member_ids[$key]['shentiyishi_level']  = $result ? awareness_abnormal($result[0][2]) : '';
                    $member_ids[$key]['qingxuyishi_level']  = $result ? awareness_abnormal($result[0][3]) : '';
                    $member_ids[$key]['renjiyishi_level']   = $result ? awareness_abnormal($result[0][4]) : '';
                    $member_ids[$key]['xingfuyishi_level']  = $result ? awareness_abnormal($result[0][5]) : '';
                    break;
                case 28:
                    //此处是焦虑$arr=[50,[37.5,43.75,43.75,50,50]],['焦虑程度',['学习焦虑','考试焦虑','家庭焦虑','人际焦虑','形象焦虑']];
                    $member_ids[$key]['warning_level']   = $result ? anxiety_situation($result) : '';
                    $member_ids[$key]['scale']           = $result ? $result[0] : '';

                    $member_ids[$key]['xuexijiaolv']     = $result ? $result[1][0] : '';
                    $member_ids[$key]['kaoshijiaolv']    = $result ? $result[1][1] : '';
                    $member_ids[$key]['jiatingjiaolv']   = $result ? $result[1][2] : '';
                    $member_ids[$key]['renjijiaolv']     = $result ? $result[1][3] : '';
                    $member_ids[$key]['xingxiangjiaolv'] = $result ? $result[1][4] : '';

                    $member_ids[$key]['scale_level']           = $result ? anxiety_abnormal($result[0]) : '';
                    $member_ids[$key]['xuexijiaolv_level']     = $result ? anxiety_abnormal($result[1][0]) : '';
                    $member_ids[$key]['kaoshijiaolv_level']    = $result ? anxiety_abnormal($result[1][1]) : '';
                    $member_ids[$key]['jiatingjiaolv_level']   = $result ? anxiety_abnormal($result[1][2]) : '';
                    $member_ids[$key]['renjijiaolv_level']     = $result ? anxiety_abnormal($result[1][3]) : '';
                    $member_ids[$key]['xingxiangjiaolv_level'] = $result ? anxiety_abnormal($result[1][4]) : '';
                    break;
                default:
                    //默认
            }
        }

        //过滤条件
        if($is_complete){
            foreach ($member_ids as $km => $vm){
                if ($is_complete != $vm['is_complete']) {
                    unset($member_ids[$km]);
                }
            }
        }
        if($warning_level){
            foreach ($member_ids as $kw => $vw){
                if ($warning_level != $vw['warning_level']) {
                    unset($member_ids[$kw]);
                }
            }
        }
        if($abnormal){
            $code_psych_competence = code_psych_competence($survey_type);
            $code_psych_competence['scale'] = '全量表分';
            $code_psych_competence = array_flip($code_psych_competence);
            foreach ($member_ids as $ka => $va){
                if ($va[$code_psych_competence[$abnormal].'_level'] != '异常') {
                    unset($member_ids[$ka]);
                }
            }
        }

        return pageing($member_ids,$pageSize,$pageNumber);
    }

    public function get_statistic_analysis()
    {
        $survey_type   = input('survey_type');
        $plan_id       = input('plan_id');
        $survey_id = db('survey')->where('survey_type',$survey_type)->value('id');

        $w['plan.id'] = $plan_id;
        switch ($this->user['role_source_id']){
            case 2:
                $member_ids = $this->plan->alias('plan')->where($w)->value('plan.member_ids');
                $member_ids = explode(',',$member_ids);
                break;
            case 3:
                $w['teacher.member_id'] = $this->user['id'];
                $member_ids = $this->plan->alias('plan')
                    ->join('student student','find_in_set(student.class_id,plan.class_ids) and find_in_set(student.member_id,plan.member_ids)')
                    ->join('teacher teacher','find_in_set(student.class_id,teacher.class_ids)')
                    ->where($w)
                    ->column('student.member_id');
                $member_ids = to_arr($member_ids);
                break;
        }

        $total = count($member_ids);
//        $member_ids = to_arr($member_ids);
//        print_r($member_ids);die;
        $where['relation.plan_id'] = $plan_id;
        $where['session.survey_id'] = $survey_id;
        $data = $this->relation->alias('relation')
            ->join('survey_user_session session','session.session_id = relation.session_id')
            ->join('survey survey','survey.id = session.survey_id')
            ->field('session.survey_id,session.member_id,session.result,session.used_time')
            ->where($where)
            ->group('session.member_id')
            ->select();
        $data = to_arr($data);
        $complete_num = count($data);
        $data = array_column($data,null,'member_id');
//        print_r($data);die;
        $i1 = $i2 = $i3 = $i4 = 0;
        foreach ($member_ids as $key => $value){
            if(isset($data[$value])){
                $result = json_decode($data[$value]['result'],true);
            }else{
                $result = [];
            }
            switch ($survey_type){
                case 26:
                    //此处是自信[[44,90,60,44,84,60],64],[['价值自信','品德自信','人际自信','能力自信','学业自信','外表自信'],'总分']
                    if($result){
                        switch($result[1]){
                            case $result[1] < 45:
                                $confidence_situation = '信心不足';
                                $i1++;
                                break;
                            case 45 <= $result[1] && $result[1] < 70:
                                $confidence_situation = '一般自信';
                                $i2++;
                                break;
                            case 70 <= $result[1] && $result[1] < 85:
                                $confidence_situation = '比较自信';
                                $i3++;
                                break;
                            case $result[1] >= 85:
                                $confidence_situation = '非常自信';
                                $i4++;
                                break;
                        }
                    }
                    break;
                case 27:
                    //此处是自我意识$arr=[[44,77,60,44,84,60],64]，[['行为意识','学业意识','身体意识','情绪意识','人际意识','幸福意识'],'总分'];
                    if($result){
                        switch($result[1]){
                            case $result[1] < 45:
                                $awareness_situation = '较低水平';
                                $i1++;
                                break;
                            case $result[1] >= 45:
                                $awareness_situation = '正常水平';
                                $i2++;
                                break;
                        }
                    }
                    break;
                case 28:
                    //此处是焦虑$arr=[50,[37.5,43.75,43.75,50,50]],['焦虑程度',['学习焦虑','考试焦虑','家庭焦虑','人际焦虑','形象焦虑']];
                    if($result){
                        switch($result[0]){
                            case $result[0] < 50:
                                $anxiety_situation = '无焦虑';
                                $i1++;
                                break;
                            case 50 <= $result[0] && $result[0] < 60:
                                $anxiety_situation = '轻度焦虑';
                                $i2++;
                                break;
                            case 60 <= $result[0] && $result[0] < 70:
                                $anxiety_situation = '中度焦虑';
                                $i3++;
                                break;
                            case $result[0] >= 70:
                                $anxiety_situation = '重度焦虑';
                                $i4++;
                                break;
                        }
                    }
                    break;
                default:
                    //默认
            }
        }
        $code_psych_competence = code_psych_competence($survey_type);
        switch ($survey_type){
            case 26:
                //此处是自信[[44,90,60,44,84,60],64],[['价值自信','品德自信','人际自信','能力自信','学业自信','外表自信'],'总分']
                $back = [
                    [
                        ['total'=>$total,'complete_num'=>$complete_num,'percentage'=>round($complete_num * 100/$total) . '%',]
                    ],
                    [
                        ['name'=>'非常自信','num'=>$i4,],
                        ['name'=>'比较自信','num'=>$i3,],
                        ['name'=>'一般自信','num'=>$i2,],
                        ['name'=>'信心不足','num'=>$i1,],
                    ],
                    $code_psych_competence
                ];
                break;
            case 27:
                //此处是自我意识$arr=[[44,77,60,44,84,60],64]，[['行为意识','学业意识','身体意识','情绪意识','人际意识','幸福意识'],'总分'];
                $back = [
                    [
                        ['total'=>$total,'complete_num'=>$complete_num,'percentage'=>round($complete_num * 100/$total) . '%',]
                    ],
                    [
                        ['name'=>'正常水平','num'=>$i2,],
                        ['name'=>'较低水平','num'=>$i1,],
                    ],
                    $code_psych_competence
                ];
                break;
            case 28:
                //此处是焦虑$arr=[50,[37.5,43.75,43.75,50,50]],['焦虑程度',['学习焦虑','考试焦虑','家庭焦虑','人际焦虑','形象焦虑']];
                $back = [
                    [
                        ['total'=>$total,'complete_num'=>$complete_num,'percentage'=>round($complete_num * 100/$total) . '%',]
                    ],
                    [
                        ['name'=>'无焦虑','num'=>$i1,],
                        ['name'=>'轻度焦虑','num'=>$i2,],
                        ['name'=>'中度焦虑','num'=>$i3,],
                        ['name'=>'重度焦虑','num'=>$i4,],
                    ],
                    [$code_psych_competence]
                ];
                break;
            default:
                //默认
        }
        return $back;

    }

    public function batch_download_pdf(){
        $session_ids = input('session_ids');
        $pdf_url_arr = $this->survey_user_model->alias('session')
            ->join('survey survey','session.survey_id = survey.id')
            ->join('student student','session.class_id = student.class_id and session.member_id = student.member_id')
            ->field('session.pdf_url,survey.title,student.name')
            ->where(['session_id'=>['in',$session_ids],'session.pdf_url'=>['<>','null']])
            ->select();
        $pdf_url_arr = to_arr($pdf_url_arr);
        $url = $this->pack_zip($pdf_url_arr);
        apiReturn($url);
    }

    public function pack_zip($pdf_url_arr){
        $sftp = new \app\common\controller\Sftp();
        $filename = date('YmdHis')."_psych.zip"; // 最终生成的文件名（含路径）
        $zip = new ZipArchive (); // 使用本类，linux需开启zlib，windows需取消php_zip.dll前的注释
        if ($zip->open("../runtime/sftp/".$filename, ZIPARCHIVE::CREATE) !== TRUE) {
            exit ('无法打开文件，或者文件创建失败');
        }

        foreach ($pdf_url_arr as $val) {
            $pdf_name = pathinfo($val['pdf_url']);
            $sftp->downftp("/saas_upload/survey/pdf/psychassessment/".$pdf_name['basename'],"../runtime/sftp/".$pdf_name['basename']);
            $zip->addFile("../runtime/sftp/".$pdf_name['basename'], iconv("UTF-8", "GBK", $val['title'].'_'.$val['name'].'.pdf')); // 第二个参数是放在压缩包中的文件名称，如果文件可能会有重复，就需要注意一下
        }

        $zip->close(); // 关闭
        $sftp->upftp("../runtime/sftp/".$filename,"/saas_upload/survey/pdf/psychassessment/".$filename);
        //需要将zip包关闭后，才能删除下载文件,不然会报错
        foreach ($pdf_url_arr as $val) {
            $pdf_name = pathinfo($val['pdf_url']);
            unlink("../runtime/sftp/".$pdf_name['basename']);
        }
        //将压缩包地址存入数据库
        $file_url = 'https://s.yishengya.cn/saas_upload/survey/pdf/psychassessment/'.$filename;
        unlink("../runtime/sftp/".$filename);
        return $file_url;
    }
}