<?php

namespace App\Services\School\Assessment\Score\Career;

use App\Services\School\Assessment\Score\Career\AbstractScoreService;

/**
 * 智能评估服务
 * 
 * 用于计算和分析学生的多元智能评估结果
 */
class IntelligenceService extends AbstractScoreService
{
    /**
     * 计算智能评估结果
     * 
     * @param array $params 包含评估所需参数的数组
     * @return array 智能评估结果数组
     */
    public function calculate($params): array
    {
        $dimension_scores = $this->calculateScores($params);
        
        if ($params['assessment_id'] == 2) {
            $threshold = 24;
        } else {
            foreach ($dimension_scores as $key => $value) {
                $dimension_scores[$key]['score'] = round($value['score'] * 10 / 3);
            }
            $threshold = 40;
        }

        $scores = array_column($dimension_scores, 'score');
        
        list($advantages_count, $neutral_count, $disadvantages_count, $under_threshold_dimension_count) = 
            $this->analyzeIntelligenceScores($scores, $threshold);

        return [
            'dimensions' => $dimension_scores, 
            'each_level_count' => [
                'advantages' => $advantages_count, 
                'neutral' => $neutral_count, 
                'disadvantages' => $disadvantages_count
            ], 
            'number_below_threshold' => $under_threshold_dimension_count
        ];
    }

    /**
     * 分析智能评估分数
     * 
     * @param array $scores 分数数组
     * @param int $threshold 阈值
     * @return array 分析结果数组
     */
    public function analyzeIntelligenceScores(array $scores, int $threshold): array
    {
        // 计算维度得分小于阈值的个数，高中24，初中40
        $under_threshold_dimension_count = 0;
        foreach ($scores as $score) {
            if ($score <= $threshold) {
                $under_threshold_dimension_count++;
            }
        }

        // 如果维度得分小于等于阈值的个数为8，返回特殊结果
        if ($under_threshold_dimension_count === 8) {
            return [0, 0, 0, 8]; // 所有维度得分较低
        }

        rsort($scores);
        $consecutive_matches = 0; // 连续相同得分的次数

        for($i = 1 ; $i < count($scores) ; $i++){
            if($scores[($i-1)] == $scores[$i]){
                $consecutive_matches++;
            }
        }

        if($consecutive_matches>=5){
            throw new \Exception('6个及以上相同为异常');//6个及以上相同(既5个相等) 为异常
        }

        // 多元智能一共8项智能，当不存在并列第三和并列第六时，选择其中得分最高的3项，为优势智能；
        // 得分依次往下的3项为中性智能；最后2项为相对劣势智能。
        
        // 计算优势智能的个数，考虑并列第三的情况
        $advantage_count = $this->advantageCount($scores);
        // 计算劣势智能的个数，考虑并列第六的情况
        $disadvantage_count = $this->disadvantageCount($scores);
        $neutral_count = count($scores) - $advantage_count - $disadvantage_count; // 2、中性智能
        if($neutral_count < 0){
            throw new \Exception('中性智能数量不能小于0'); // 算法错误
        }
        return array($advantage_count, $neutral_count, $disadvantage_count, $under_threshold_dimension_count);
    }

    /**
     * 计算优势智能数量
     * 
     * @param array $scores 排序后的分数数组
     * @return int 优势智能数量
     */
    public function advantageCount(array $scores): int
    {
        $advantage_count = 3;

        // 检查是否并列第三
        if ($scores[2] == $scores[3]) {
            // 第二和第三不相等，且第四和第五不相等
            if ($scores[1] != $scores[2] && $scores[3] != $scores[4]) {
                $diff_top = $scores[1] - $scores[2]; // 第二和第三的差值
                $diff_bottom = $scores[3] - $scores[4]; // 第四和第五的差值

                if ($diff_top <= $diff_bottom) {
                    $advantage_count = 4;
                } else {
                    $advantage_count = 2;
                }
            }
            // 第二和第三不相等，且第四和第五相等
            elseif ($scores[1] != $scores[2] && $scores[3] == $scores[4]) {
                $advantage_count = 2;
            }
            // 第二和第三相等，且第四和第五不相等
            elseif ($scores[1] == $scores[2] && $scores[3] != $scores[4]) {
                $advantage_count = 4;
            }
            // 第二和第三相等，且第四和第五相等
            elseif ($scores[1] == $scores[2] && $scores[3] == $scores[4]) {
                // 检查第一和第二是否相等，以及第五和第六是否相等
                if ($scores[0] == $scores[1] && $scores[4] != $scores[5]) {
                    $advantage_count = 5;
                } elseif ($scores[0] != $scores[1] && $scores[4] == $scores[5]) {
                    $advantage_count = 1;
                } elseif ($scores[0] != $scores[1] && $scores[4] != $scores[5]) {
                    $diff_top = $scores[0] - $scores[1];
                    $diff_bottom = $scores[4] - $scores[5];
                    if ($diff_top <= $diff_bottom) {
                        $advantage_count = 5;
                    } else {
                        $advantage_count = 1;
                    }
                }
            }
        }

        return $advantage_count;
    }

    /**
     * 计算劣势智能数量
     * 
     * @param array $scores 排序后的分数数组
     * @return int 劣势智能数量
     */
    public function disadvantageCount(array $scores): int
    {
        $disadvantage_count = 2;

        // 检查是否并列第六
        if ($scores[5] == $scores[6]) {
            // 第五和第六相等，且第七和第八相等
            if ($scores[4] == $scores[5] && $scores[6] == $scores[7]) {
                // 第四和第五相等
                if ($scores[3] == $scores[4]) {
                    // 第三和第四不相等
                    if ($scores[2] != $scores[3]) {
                        $disadvantage_count = 5;
                    }
                } else {
                    $disadvantage_count = 4;
                }
            }
            // 第五和第六相等，且第七和第八不相等
            elseif ($scores[4] == $scores[5] && $scores[6] != $scores[7]) {
                $disadvantage_count = 1;
            }
            // 第五和第六不相等，且第七和第八相等
            elseif ($scores[4] != $scores[5] && $scores[6] == $scores[7]) {
                $disadvantage_count = 3;
            }
            // 第五和第六不相等，且第七和第八不相等
            elseif ($scores[4] != $scores[5] && $scores[6] != $scores[7]) {
                $diff_top = $scores[4] - $scores[5]; // 第五和第六的差值
                $diff_bottom = $scores[6] - $scores[7]; // 第七和第八的差值

                if ($diff_top <= $diff_bottom) {
                    $disadvantage_count = 1;
                } else {
                    $disadvantage_count = 3;
                }
            }
        }

        return $disadvantage_count;
    }
}