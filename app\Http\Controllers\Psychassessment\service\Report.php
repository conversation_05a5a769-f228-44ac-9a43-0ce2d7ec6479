<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON>
 * Date: 2024/01/19
 * Time: 17:52
 */
namespace app\psychassessment\service;

class Report{

    public function __construct()
    {
        $this->ReportLogic = new \app\psychassessment\logic\Report();
    }

    public function report()
    {
        $plan_id       = input('plan_id');
        $survey_type   = input('survey_type');
        $member_id     = input('member_id');
        $data = $this->ReportLogic->report($plan_id,$survey_type,$member_id);
        apiReturn($data);
    }

}