<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */
namespace app\evaluation\logic;
use app\evaluation\model\Categories as CategoriesModel;
use think\Loader;

class Categories{
    protected $user;
    public function __construct()
    {

        $this->user = get_user();
    }

    public function add()
    {
        $model = new CategoriesModel();
        $model->data([
            'category_name'  =>  input('category_name'),
            'parent_id' =>  input('parent_id'),
            'course_id' => input('course_id'),
            'content' =>  input('content'),
            'create_at' => date('Y-m-d H:i:s'),
        ]);
        $model->save();
        $id=$model->id;
        apiReturn($id);
    }

    public function edit()
    {

        $model = new CategoriesModel();
        $model->save([
            'category_name'  =>  input('category_name'),
            'parent_id' =>  input('parent_id'),
            'course_id' => input('course_id'),
            'content' =>  input('content'),
            'update_at' =>  date('Y-m-d H:i:s'),
        ],['id' => input('id')]);
        apiReturn(input('id'));
    }

    public function del()
    {
        $model = new CategoriesModel();
        $model->save([
            'status'  => '-1',
        ],['id' => input('id')]);
        //软删除
        apiReturn(input('id'));
    }

    public function get_list()
    {
        $id=input('id');

        $course_id=input('course_id');
        $model = new CategoriesModel();

            $data = $model->field('id, category_name, parent_id,course_id,content')
                ->where(function ($query) {
                    $query->where('status', 0)->where('parent_id', 0);
                    if(!empty(input('course_id'))) {
                        $query->where('course_id', input('course_id'));
                    }
                    if(!empty(input('id'))) {
                        $query->where('id', input('id'));
                    }
                })
                ->with(['children'=>function($query) use ($course_id){

                    if (!empty($course_id)) {

                        $query->where('course_id', $course_id);
                    }

                }])
                ->select();

        apiReturn($data);

    }

}