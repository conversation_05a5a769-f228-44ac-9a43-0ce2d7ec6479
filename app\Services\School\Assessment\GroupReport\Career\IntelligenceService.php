<?php

namespace App\Services\School\Assessment\GroupReport\Career;

use App\Services\School\Assessment\GroupReport\AbstractGroupReportService;

/**
 * 多元智能评估团体报告服务类
 * 
 * 该类用于生成多元智能评估的团体报告，包括智能类型分布和优势智能人数分布
 */
class IntelligenceService extends AbstractGroupReportService
{
    /**
     * 维度名称列表
     * 
     * @var array
     */
    protected $dimension_names = [
        '言语智能', '逻辑—数学智能', '空间智能', '身体—运动智能',
        '音乐智能', '人际智能', '自我认知智能', '自然观察智能'
    ];

    /**
     * 生成评估报告
     * 
     * @param array $params 请求参数
     * @param int $school_id 学校ID
     * @return array 评估报告数据
     */
    public function generateReport(array $params, int $school_id): array
    {
        // 获取评估数据
        $filtered_assessments = $this->getAssessmentData($params, $school_id, $this->getFilterConditions($params));
        $filtered_assessment_with_results = array_filter($filtered_assessments, fn($v) => !empty($v['standard_results']));
        
        // 统计数据
        $class_statistics = $this->calculateClassStatistics($filtered_assessments);
        
        // 由子类实现具体的报告生成逻辑
        return $this->generateReportData($filtered_assessment_with_results, $class_statistics, $params);
    }
    
    /**
     * 生成报告数据
     * 
     * @param array $all_assessment_with_results 所有有结果的评估数据
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @param array $params 请求参数
     * @return array 报告数据
     */
    protected function generateReportData(
        array $filtered_assessment_with_results, 
        array $class_statistics,
        array $params
    ): array {
        return [
            'participation_count' => $this->getMemberCount($class_statistics),
            'type_distribution' => $this->calculateDimensionDistribution(
                $filtered_assessment_with_results,
                $params['assessment_id'],
                $class_statistics
            ),
            'advantage_count' => $this->calculateAdvantageDistribution(
                $filtered_assessment_with_results,
                $class_statistics
            )
        ];
    }

    /**
     * 计算优势分布
     * 
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @return array 优势分布数据
     */
    protected function calculateAdvantageDistribution(array $filtered_assessment_with_results, array $class_statistics): array
    {
        $selected_data = $this->calculateAdvantageCount($filtered_assessment_with_results);
        $dimension_scores = array_combine($this->dimension_names, $selected_data);
        arsort($dimension_scores);

        $report_data = [
            'sort' => implode(',', array_slice(array_keys($dimension_scores), 0, 3)),
            'axis' => $this->dimension_names,
            'legend' => ['本次测评'],
            'series' => [$selected_data]
        ];

        // 处理班级统计
        $class_data = [
            'axis' => [],
            'legend' => $this->dimension_names,
            'series' => []
        ];

        foreach ($class_statistics['stats'] as $class_name => $stats) {
            $class_data['axis'][] = $stats['class_name'];
            $class_data['series'][$class_name] = !empty($class_statistics['category_data'][$class_name]) 
                ? $this->calculateAdvantageCount($class_statistics['category_data'][$class_name]) 
                : array_fill(0, 8, 0);
        }

        return [
            'header'=>[
                'number' => 3,
                'name' => '优势智能人数分布',
            ],
            'default' => $report_data,
            'class' => $class_data
        ];
    }

    /**
     * 计算维度分布
     * 
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param int $assessment_id 评估ID
     * @param array $class_statistics 班级统计数据
     * @return array 维度分布数据
     */
    protected function calculateDimensionDistribution(
        array $filtered_assessment_with_results, 
        int $assessment_id, 
        array $class_statistics
    ): array
    {
        $selected_averages = $this->calculateDimensionAverages($filtered_assessment_with_results);
        
        $dimension_scores = array_combine($this->dimension_names, $selected_averages);
        arsort($dimension_scores);

        $norm_scores = $this->getNormScores($assessment_id);

        $report_data = [
            'sort' => implode(',', array_keys($dimension_scores)),
            'axis' => $this->dimension_names,
            'legend' => ['常模对比', '本次测评'],
            'series' => [$norm_scores, $selected_averages]
        ];

        // 处理班级统计
        $class_data = [
            'axis' => [],
            'legend' => $this->dimension_names,
            'series' => []
        ];

        foreach ($class_statistics['stats'] as $class_name => $stats) {
            $class_data['axis'][] = $stats['class_name'];
            $class_data['series'][$class_name] = !empty($class_statistics['category_data'][$class_name])
                ? $this->calculateDimensionAverages($class_statistics['category_data'][$class_name])
                : array_fill(0, 8, 0);
        }

        return [
            'header'=>[
                'number' => 2,
                'name' => '多元智能类型分布',
            ],
            'default' => $report_data,
            'class' => $class_data
        ];
    }

    /**
     * 计算优势数量
     * 
     * @param array $assessments 评估数据
     * @return array 优势数量数据
     */
    private function calculateAdvantageCount(array $assessments): array
    {
        $dimension_counts = array_fill(0, 8, 0);
        
        foreach ($assessments as $assessment) {
            $result = is_array($assessment['standard_results']) 
                ? $assessment['standard_results'] 
                : json_decode($assessment['standard_results'], true);
            
            // 获取维度分数
            $scores = [];
            foreach ($result['dimensions'] as $dimension) {
                $scores[] = $dimension['score'];
            }
            
            // 获取优势维度数量
            $advantage_count = $result['each_level_count']['advantages'] ?? 0;
            
            // 创建分数映射并排序
            $score_map = array_combine($this->dimension_names, $scores);
            arsort($score_map);
            $advantage_dimensions = array_slice(array_keys($score_map), 0, $advantage_count);

            // 统计优势维度
            foreach ($scores as $index => $score) {
                if (in_array($this->dimension_names[$index], $advantage_dimensions)) {
                    $dimension_counts[$index]++;
                }
            }
        }

        return $dimension_counts;
    }

    /**
     * 计算维度平均值
     * 
     * @param array $assessments 评估数据
     * @return array 维度平均值数据
     */
    private function calculateDimensionAverages(array $assessments): array
    {
        $dimension_sums = array_fill(0, 8, 0);
        $count = count($assessments);

        if ($count === 0) {
            return array_fill(0, 8, 0);
        }

        foreach ($assessments as $assessment) {
            $result = is_array($assessment['standard_results']) 
                ? $assessment['standard_results'] 
                : json_decode($assessment['standard_results'], true);
            
            // 从维度数组中提取分数
            foreach ($result['dimensions'] as $index => $dimension) {
                $dimension_sums[$index] += $dimension['score'];
            }
        }

        return array_map(
            fn($sum) => round($sum / $count, 2),
            $dimension_sums
        );
    }

    /**
     * 获取常模分数
     * 
     * @param int $assessment_id 评估ID
     * @return array 常模分数数据
     */
    private function getNormScores(int $assessment_id): array
    {
        return $assessment_id == 4
            ? [39.46, 36.79, 41.8, 38.73, 39.56, 45.35, 44.64, 37.31]  // 高中常模
            : [78.73, 75.72, 76.98, 78.01, 75.1, 79.92, 79.96, 75.73]; // 初中常模
    }
}