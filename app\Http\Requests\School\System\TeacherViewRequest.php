<?php

namespace App\Http\Requests\School\System;

use App\Http\Requests\BaseRequest;

class TeacherViewRequest extends BaseRequest
{
    public function rules(): array
    {
        $method = $this->route()->getActionMethod();

        return match ($method) {
            'setViewClasses' => $this->setViewClassesRules(),
            'getViewClasses' => $this->getViewClassesRules(),
            default => []
        };
    }

    private function setViewClassesRules(): array
    {
        return [
            'teacher_id' => 'required|integer|exists:teachers,id',
            'school_year' => 'required|integer',
            'class_ids' => 'required|array',
            'class_ids.*' => 'integer|exists:classes,id'
        ];
    }

    private function getViewClassesRules(): array
    {
        return [
            'school_year' => 'required|integer',
            'teacher_id' => 'required|integer|exists:teachers,id'
        ];
    }

    public function messages(): array
    {
        return [
            'teacher_id.required' => '教师ID不能为空',
            'teacher_id.integer' => '教师ID必须为整数',
            'school_year.required' => '学年不能为空',
            'school_year.integer' => '学年必须为整数',
            'teacher_id.exists' => '教师不存在',
            'class_ids.required' => '班级ID列表不能为空',
            'class_ids.array' => '班级ID列表必须为数组',
            'class_ids.*.integer' => '班级ID必须为整数',
            'class_ids.*.exists' => '班级不存在'
        ];
    }
}