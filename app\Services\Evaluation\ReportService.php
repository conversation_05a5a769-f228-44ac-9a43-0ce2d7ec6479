<?php

namespace App\Services\Evaluation;

use App\Models\Evaluation\EvaluationLog;
use App\Models\Evaluation\EvaluationAnswer;
use App\Models\Evaluation\Distribution;
use App\Models\Evaluation\Papers;
use App\Models\School\System\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

/**
 * 报告管理服务类
 */
class ReportService
{
    protected $evaluationLogModel;
    protected $evaluationAnswerModel;
    protected $distributionModel;
    protected $paperModel;
    protected $studentModel;
    protected $user;

    public function __construct(
        EvaluationLog $evaluationLogModel,
        EvaluationAnswer $evaluationAnswerModel,
        Distribution $distributionModel,
        Papers $paperModel,
        Student $studentModel
    ) {
        $this->evaluationLogModel = $evaluationLogModel;
        $this->evaluationAnswerModel = $evaluationAnswerModel;
        $this->distributionModel = $distributionModel;
        $this->paperModel = $paperModel;
        $this->studentModel = $studentModel;
        $this->user = Auth::user();
    }

    /**
     * 获取报告列表
     * 
     * @param array $params
     * @return array
     */
    public function getReportList(array $params): array
    {
        $query = $this->evaluationLogModel->query();

        // 应用筛选条件
        $this->applyFilters($query, $params);

        $page = $params['page'] ?? 1;
        $pagesize = $params['pagesize'] ?? 15;

        $total = $query->count();
        $reports = $query->with(['paper', 'distribution', 'student', 'grade', 'class'])
            ->orderBy('id', 'desc')
            ->offset(($page - 1) * $pagesize)
            ->limit($pagesize)
            ->get();

        return [
            'list' => $reports,
            'total' => $total,
            'page' => $page,
            'pagesize' => $pagesize
        ];
    }

    /**
     * 获取学生报告
     * 
     * @param array $params
     * @return array
     */
    public function getStudentReport(array $params): array
    {
        $memberId = $params['member_id'] ?? $this->user->id;
        $distributionId = $params['distribution_id'] ?? null;

        // 获取学生基本信息
        $memberInfo = $this->getStudentBasicInfo($memberId);
        
        // 获取生涯测评数据
        $careerData = $this->getCareerData($memberId);
        
        // 获取考试数据
        $examData = $this->getExamData($memberId, $distributionId);
        
        // 构建报告数据
        $reportData = [
            'info' => $memberInfo,
            'portrait' => $this->buildPortrait($careerData, $examData),
            'career' => $this->buildCareerData($careerData),
            'literacy' => $this->buildLiteracyData($examData),
            'suggestions' => $this->getPersonalizedSuggestions($memberId, $distributionId)
        ];

        return $reportData;
    }

    /**
     * 获取班级报告
     * 
     * @param array $params
     * @return array
     */
    public function getClassReport(array $params): array
    {
        $classId = $params['class_id'];
        $distributionId = $params['distribution_id'] ?? null;

        // 获取班级基本信息
        $classInfo = $this->getClassBasicInfo($classId);
        
        // 获取班级学生列表
        $students = $this->getClassStudents($classId);
        
        // 获取班级统计数据
        $statistics = $this->getClassStatistics($classId, $distributionId);
        
        // 获取班级排名数据
        $rankings = $this->getClassRankings($classId, $distributionId);

        return [
            'class_info' => $classInfo,
            'students' => $students,
            'statistics' => $statistics,
            'rankings' => $rankings,
            'analysis' => $this->getClassAnalysis($classId, $distributionId)
        ];
    }

    /**
     * 获取学校报告
     * 
     * @param array $params
     * @return array
     */
    public function getSchoolReport(array $params): array
    {
        $schoolId = $params['school_id'] ?? $this->user->school_id;
        $distributionId = $params['distribution_id'] ?? null;

        // 获取学校基本信息
        $schoolInfo = $this->getSchoolBasicInfo($schoolId);
        
        // 获取学校统计数据
        $statistics = $this->getSchoolStatistics($schoolId, $distributionId);
        
        // 获取年级统计
        $gradeStatistics = $this->getGradeStatistics($schoolId, $distributionId);
        
        // 获取班级统计
        $classStatistics = $this->getSchoolClassStatistics($schoolId, $distributionId);

        return [
            'school_info' => $schoolInfo,
            'statistics' => $statistics,
            'grade_statistics' => $gradeStatistics,
            'class_statistics' => $classStatistics,
            'trends' => $this->getSchoolTrends($schoolId, $distributionId)
        ];
    }

    /**
     * 生成报告
     * 
     * @param array $params
     * @return array
     */
    public function generateReport(array $params): array
    {
        $reportType = $params['report_type']; // student, class, school
        $targetId = $params['target_id'];
        $distributionId = $params['distribution_id'] ?? null;

        switch ($reportType) {
            case 'student':
                $data = $this->getStudentReport(['member_id' => $targetId, 'distribution_id' => $distributionId]);
                break;
            case 'class':
                $data = $this->getClassReport(['class_id' => $targetId, 'distribution_id' => $distributionId]);
                break;
            case 'school':
                $data = $this->getSchoolReport(['school_id' => $targetId, 'distribution_id' => $distributionId]);
                break;
            default:
                throw new \Exception('不支持的报告类型');
        }

        // 生成PDF或其他格式的报告文件
        $reportFile = $this->generateReportFile($data, $reportType, $params);

        return [
            'report_data' => $data,
            'report_file' => $reportFile,
            'generated_at' => now()
        ];
    }

    /**
     * 下载报告
     * 
     * @param int $reportId
     * @return mixed
     */
    public function downloadReport(int $reportId)
    {
        // 实现报告下载逻辑
        // 这里需要根据实际需求实现
        throw new \Exception('报告下载功能待实现');
    }

    /**
     * 个人报告
     * 
     * @param int $memberId
     * @param int|null $distributionId
     * @return array
     */
    public function personalReport(int $memberId, ?int $distributionId = null): array
    {
        return $this->getStudentReport([
            'member_id' => $memberId,
            'distribution_id' => $distributionId
        ]);
    }

    /**
     * 结算报告
     * 
     * @param array $params
     * @return array
     */
    public function settlement(array $params = []): array
    {
        // 实现结算逻辑
        $distributionId = $params['distribution_id'] ?? null;
        $schoolId = $params['school_id'] ?? $this->user->school_id;

        $query = $this->evaluationLogModel->query();
        
        if ($distributionId) {
            $query->where('distribution_id', $distributionId);
        }
        
        if ($schoolId) {
            $query->where('school_id', $schoolId);
        }

        $logs = $query->where('check_status', 1)->get();

        return [
            'total_count' => $logs->count(),
            'avg_score' => $logs->avg('score'),
            'max_score' => $logs->max('score'),
            'min_score' => $logs->min('score'),
            'score_distribution' => $this->getScoreDistribution($logs),
            'literacy_levels' => $logs->groupBy('literacy_level')->map->count()
        ];
    }

    /**
     * 全部结算
     * 
     * @param array $params
     * @return array
     */
    public function allSettlement(array $params = []): array
    {
        // 实现全部结算逻辑
        $schoolId = $params['school_id'] ?? $this->user->school_id;

        $query = $this->evaluationLogModel->query();
        
        if ($schoolId) {
            $query->where('school_id', $schoolId);
        }

        $logs = $query->where('check_status', 1)->get();

        $result = [
            'overview' => [
                'total_students' => $logs->count(),
                'total_papers' => $logs->groupBy('paper_id')->count(),
                'total_distributions' => $logs->groupBy('distribution_id')->count(),
                'avg_score' => round($logs->avg('score'), 2)
            ],
            'by_grade' => $logs->groupBy('grade_id')->map(function ($gradeLogs) {
                return [
                    'count' => $gradeLogs->count(),
                    'avg_score' => round($gradeLogs->avg('score'), 2),
                    'literacy_levels' => $gradeLogs->groupBy('literacy_level')->map->count()
                ];
            }),
            'by_paper' => $logs->groupBy('paper_id')->map(function ($paperLogs) {
                return [
                    'count' => $paperLogs->count(),
                    'avg_score' => round($paperLogs->avg('score'), 2),
                    'completion_rate' => 100 // 这里需要根据实际逻辑计算
                ];
            }),
            'trends' => $this->getScoreTrends($logs)
        ];

        return $result;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 应用筛选条件
     * 
     * @param $query
     * @param array $params
     */
    private function applyFilters($query, array $params): void
    {
        $query->where('status', 0);

        if (isset($this->user->school_id) && $this->user->school_id) {
            $query->where('school_id', $this->user->school_id);
        }

        if (!empty($params['distribution_id'])) {
            $query->where('distribution_id', $params['distribution_id']);
        }

        if (!empty($params['paper_id'])) {
            $query->where('paper_id', $params['paper_id']);
        }

        if (!empty($params['grade_id'])) {
            $query->where('grade_id', $params['grade_id']);
        }

        if (!empty($params['class_id'])) {
            $query->where('class_id', $params['class_id']);
        }

        if (!empty($params['student_id'])) {
            $query->where('student_id', $params['student_id']);
        }

        if (isset($params['check_status']) && is_numeric($params['check_status'])) {
            $query->where('check_status', $params['check_status']);
        }

        if (!empty($params['literacy_level'])) {
            $query->where('literacy_level', $params['literacy_level']);
        }

        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $query->whereBetween('created_at', [$params['start_time'], $params['end_time']]);
        }
    }

    /**
     * 获取学生基本信息
     * 
     * @param int $memberId
     * @return array
     */
    private function getStudentBasicInfo(int $memberId): array
    {
        $student = DB::table('student as stu')
            ->join('school', 'stu.school_id', '=', 'school.id')
            ->select([
                'stu.name',
                DB::raw('CASE WHEN stu.gender = 1 THEN "男" WHEN stu.gender = 2 THEN "女" ELSE "未知" END as gender'),
                'stu.student_no',
                'school.name as school_name'
            ])
            ->where('stu.member_id', $memberId)
            ->first();

        return $student ? (array)$student : [];
    }

    /**
     * 获取生涯数据
     * 
     * @param int $memberId
     * @return array
     */
    private function getCareerData(int $memberId): array
    {
        // 这里需要根据实际的生涯测评表结构来实现
        // 暂时返回空数组
        return [];
    }

    /**
     * 获取考试数据
     * 
     * @param int $memberId
     * @param int|null $distributionId
     * @return array
     */
    private function getExamData(int $memberId, ?int $distributionId = null): array
    {
        $query = $this->evaluationLogModel->where('member_id', $memberId);
        
        if ($distributionId) {
            $query->where('distribution_id', $distributionId);
        }

        return $query->get()->toArray();
    }

    /**
     * 构建画像数据
     * 
     * @param array $careerData
     * @param array $examData
     * @return array
     */
    private function buildPortrait(array $careerData, array $examData): array
    {
        // 根据生涯数据和考试数据构建学生画像
        return [
            'career' => [],
            'interest' => [],
            'literacy' => []
        ];
    }

    /**
     * 构建生涯数据
     * 
     * @param array $careerData
     * @return array
     */
    private function buildCareerData(array $careerData): array
    {
        return [];
    }

    /**
     * 构建素养数据
     * 
     * @param array $examData
     * @return array
     */
    private function buildLiteracyData(array $examData): array
    {
        return [];
    }

    /**
     * 获取个性化建议
     * 
     * @param int $memberId
     * @param int|null $distributionId
     * @return array
     */
    private function getPersonalizedSuggestions(int $memberId, ?int $distributionId = null): array
    {
        return [];
    }

    /**
     * 获取班级基本信息
     * 
     * @param int $classId
     * @return array
     */
    private function getClassBasicInfo(int $classId): array
    {
        return [];
    }

    /**
     * 获取班级学生
     * 
     * @param int $classId
     * @return array
     */
    private function getClassStudents(int $classId): array
    {
        return [];
    }

    /**
     * 获取班级统计
     * 
     * @param int $classId
     * @param int|null $distributionId
     * @return array
     */
    private function getClassStatistics(int $classId, ?int $distributionId = null): array
    {
        return [];
    }

    /**
     * 获取班级排名
     * 
     * @param int $classId
     * @param int|null $distributionId
     * @return array
     */
    private function getClassRankings(int $classId, ?int $distributionId = null): array
    {
        return [];
    }

    /**
     * 获取班级分析
     * 
     * @param int $classId
     * @param int|null $distributionId
     * @return array
     */
    private function getClassAnalysis(int $classId, ?int $distributionId = null): array
    {
        return [];
    }

    /**
     * 获取学校基本信息
     * 
     * @param int $schoolId
     * @return array
     */
    private function getSchoolBasicInfo(int $schoolId): array
    {
        return [];
    }

    /**
     * 获取学校统计
     * 
     * @param int $schoolId
     * @param int|null $distributionId
     * @return array
     */
    private function getSchoolStatistics(int $schoolId, ?int $distributionId = null): array
    {
        return [];
    }

    /**
     * 获取年级统计
     * 
     * @param int $schoolId
     * @param int|null $distributionId
     * @return array
     */
    private function getGradeStatistics(int $schoolId, ?int $distributionId = null): array
    {
        return [];
    }

    /**
     * 获取学校班级统计
     * 
     * @param int $schoolId
     * @param int|null $distributionId
     * @return array
     */
    private function getSchoolClassStatistics(int $schoolId, ?int $distributionId = null): array
    {
        return [];
    }

    /**
     * 获取学校趋势
     * 
     * @param int $schoolId
     * @param int|null $distributionId
     * @return array
     */
    private function getSchoolTrends(int $schoolId, ?int $distributionId = null): array
    {
        return [];
    }

    /**
     * 生成报告文件
     * 
     * @param array $data
     * @param string $reportType
     * @param array $params
     * @return string
     */
    private function generateReportFile(array $data, string $reportType, array $params): string
    {
        // 实现报告文件生成逻辑
        return '';
    }

    /**
     * 获取分数分布
     * 
     * @param $logs
     * @return array
     */
    private function getScoreDistribution($logs): array
    {
        $distribution = [
            '0-20' => 0,
            '21-40' => 0,
            '41-60' => 0,
            '61-80' => 0,
            '81-100' => 0
        ];

        foreach ($logs as $log) {
            $score = $log->score;
            if ($score <= 20) {
                $distribution['0-20']++;
            } elseif ($score <= 40) {
                $distribution['21-40']++;
            } elseif ($score <= 60) {
                $distribution['41-60']++;
            } elseif ($score <= 80) {
                $distribution['61-80']++;
            } else {
                $distribution['81-100']++;
            }
        }

        return $distribution;
    }

    /**
     * 获取分数趋势
     * 
     * @param $logs
     * @return array
     */
    private function getScoreTrends($logs): array
    {
        return $logs->groupBy(function ($log) {
            return $log->created_at->format('Y-m-d');
        })->map(function ($dayLogs) {
            return [
                'count' => $dayLogs->count(),
                'avg_score' => round($dayLogs->avg('score'), 2)
            ];
        })->toArray();
    }
}
