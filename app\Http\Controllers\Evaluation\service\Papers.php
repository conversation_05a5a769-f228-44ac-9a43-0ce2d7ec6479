<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 16:01
 */
namespace app\evaluation\service;

class Papers{
    protected $PapersLogic;
    public function __construct()
    {
          $this->PapersLogic = new \app\evaluation\logic\Papers();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://查询

                return $this->PapersLogic->get_list();
                break;
            case 'post'://增加

                return $this->PapersLogic->add();
                break;
            case 'put'://修改

                return $this->PapersLogic->edit();
                break;
            case 'delete'://删除
                return $this->PapersLogic->del();
                break;
            default:
                return false;
        }
    }

}