<?php

namespace App\Services\DataSync;

use App\Services\BaseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 教师带班信息同步服务
 */
class TeacherClassSyncService extends BaseService
{
    protected $syncConnection;

    public function __construct()
    {
        $this->syncConnection = DB::connection('sync_mysql');
    }

    /**
     * 同步教师带班信息到 ysy_teacher 表
     * 
     * @param array $data 带班数据
     * @return array
     */
    public function syncTeacherClasses(array $data): array
    {
        try {
            $teacher_id = $data['teacher_id'];
            $class_ids = $data['class_ids'] ?? [];
            $school_year = $data['school_year'];

            // 1. 通过 teacher_id 查询账号 username
            $teacher = \App\Models\School\System\Teacher::with('user:id,username')->find($teacher_id);
            
            if (!$teacher || !$teacher->user) {
                return [
                    'success' => false,
                    'message' => '找不到教师或用户信息'
                ];
            }
            
            $username = $teacher->user->username;
            
            // 2. 通过 username 查询 ysy_teacher 里的老师
            $syncTeacher = $this->syncConnection->table('ysy_teacher')
                ->where('username', $username)
                ->first();
                
            if (!$syncTeacher) {
                return [
                    'success' => false,
                    'message' => '在同步数据库中找不到对应教师'
                ];
            }
           
            // 3. 处理班级信息
            $syncClassIds = [];
            foreach ($class_ids as $class_id) {
                $syncClassId = $this->getSyncClassId($class_id, $school_year);
                if ($syncClassId) {
                    $syncClassIds[] = $syncClassId;
                }
            }
          
            // 4. 更新 ysy_teacher 表的带班信息
            if (!empty($syncClassIds)) {
                $classIdsString = implode(',', $syncClassIds);
                $this->syncConnection->table('ysy_teacher')
                    ->where('id', $syncTeacher->id)
                    ->update([
                        'class_ids' => $classIdsString,
                        'view_class_ids' => $classIdsString,
                    ]);

                // 先删除该教师的旧关联记录
                $this->syncConnection->table('ysy_teacher_viewclass_relation')
                    ->where('teacher_id', $syncTeacher->id)
                    ->delete();

                // 一条一条插入新的关联记录
                foreach ($syncClassIds as $syncClassId) {
                    $this->syncConnection->table('ysy_teacher_viewclass_relation')->insert([
                        'teacher_id' => $syncTeacher->id,
                        'viewclass_id' => $syncClassId,
                        'create_time' => now()
                    ]);
                }
            } else {
                // 如果没有班级，清空带班信息
                $this->syncConnection->table('ysy_teacher')
                    ->where('id', $syncTeacher->id)
                    ->update([
                        'class_ids' => '',
                        'view_class_ids' => '',
                        'update_time' => now()
                    ]);

                // 删除该教师的所有关联记录
                $this->syncConnection->table('ysy_teacher_viewclass_relation')
                    ->where('teacher_id', $syncTeacher->id)
                    ->delete();
            }
            
            return [
                'success' => true,
                'message' => '教师带班信息同步成功',
                'sync_class_ids' => $syncClassIds,
                'teacher_name' => $teacher->teacher_name,
                'username' => $username
            ];
            
        } catch (\Exception $e) {
            $this->throwBusinessException('带班同步失败');
        }
    }

    /**
     * 获取同步数据库中的班级ID
     * 
     * @param int $class_id 本地班级ID
     * @param string $school_year 学年
     * @return int|null 同步数据库中的班级ID
     */
    private function getSyncClassId($class_id, $school_year): ?int
    {
        try {
             
           
            // 通过 class_id 查询班级信息
            $class = \App\Models\School\System\Claass::find($class_id);
            if (!$class) {
                return null;
            }

            $class_name = $class->class_name;
            $grade_id = $class->grade_id;
            $school_campus_id = $class->school_campus_id;
            // 通过 grade_id + school_year + school_campus_id 推算同步数据库中的年级ID
            $query = $this->syncConnection->table('ysy_grade')
                ->where('name', $school_year)
                ->where('grade_sort', $grade_id)
                ->where('school_district', $school_campus_id);

           
            $syncGrade = $query->first(); 
            if (!$syncGrade) {
                return null;
            }
             
            // 通过年级ID + class_name 推算同步数据库中的班级ID
            $classQuery = $this->syncConnection->table('ysy_class')
                ->where('grade_id', $syncGrade->id)
                ->where('name', $class_name);
            $syncClass = $classQuery->first();
            return $syncClass ? $syncClass->id : null;
            
        } catch (\Exception $e) {
           $this->throwBusinessException('带班同步失败');
        }
    }
}
