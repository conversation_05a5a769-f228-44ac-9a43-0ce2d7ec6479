<?php

namespace App\Models\School\Assessment\Answer;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssessmentPsychologyAnswer extends Model
{
    use HasFactory;
    
    protected $table = 'assessment_psychology_answers';
    
    protected $fillable = [
        'assessment_task_assignment_id',
        'school_id',
        'student_id',
        'assessment_psychology_question_id',
        'answer',
        'assessment_id'
    ];

    protected $casts = [
        'assessment_task_assignment_id' => 'integer',
        'school_id' => 'integer',
        'student_id' => 'integer',
        'assessment_psychology_question_id' => 'integer',
        'assessment_id' => 'integer'
    ];
}