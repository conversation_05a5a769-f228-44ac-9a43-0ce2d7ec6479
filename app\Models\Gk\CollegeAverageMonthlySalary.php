<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CollegeAverageMonthlySalary extends BaseModel
{
    use HasFactory;

    protected $table = 'College_AverageMonthlySalary';

    // 指定连接
    protected $connection = 'sqlsrv_gk';

    //主键
    protected $primaryKey = 'ID';

    // 隐藏字段
    protected $hidden = [];
    
    // 是否使用时间戳
    public $timestamps = false;

    // 可批量赋值的字段
    protected $fillable = [
        'CollegeId',
        'CollegeName',
        'WorkingAge',
        'NominalPrice',
        'MonthlySalary',
        'CountrySal',
        'Year',
    ];

    /**
     * 获取关联的学校信息
     */
    public function college(): BelongsTo
    {
        return $this->belongsTo(College::class, 'CollegeId', 'ID');
    }
}