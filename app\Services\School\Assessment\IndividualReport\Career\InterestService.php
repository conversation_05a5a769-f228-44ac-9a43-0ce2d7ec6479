<?php
namespace App\Services\School\Assessment\IndividualReport\Career;

use App\Services\School\Assessment\IndividualReport\AbstractIndividualReportService;
use App\Models\School\Assessment\Template\AssessmentCareerHollandTemplate;

class InterestService extends AbstractIndividualReportService
{

    public function generateReport(array $params): array
    {
        $assessmentId = $params['assessment_id'];
        $assessmentTaskAssignmentId = $params['assessment_task_assignment_id'];
        // 获取分发信息
        $assignmentInfo = $this->getAssignmentInfo($assessmentTaskAssignmentId);

        // 生成报告
        // 只使用standard_results字段
        $standardResults = $assignmentInfo['standard_results'];
        if (is_string($standardResults)) {
            $results = json_decode($standardResults, true);
        } else {
            $results = $standardResults;
        }

        // 将assignmentInfo中的student_id和school_id传递给interestInfo方法
        $params = [
            'student_id' => $assignmentInfo['student_id'] ?? null
        ];

        // 检查school_id是否存在
        if (isset($assignmentInfo['school_id'])) {
            $params['school_id'] = $assignmentInfo['school_id'];
        }

        $assessmentInfo = $this->interestInfo($results, $assessmentId, $params);

        // 合并结果
        return array_merge($assignmentInfo, $assessmentInfo);
    }

    private function interestInfo($result, int $assessmentId, array $params = []): array
    {
        // 获取霍兰德模板数据
        return $this->getHollandTemplateData($result, $assessmentId, $params);
    }

    /**
     * 获取霍兰德模板数据
     *
     * @param mixed $result 测评结果（可以是数组或JSON字符串）
     * @param int $assessmentId 测评ID
     * @param array $params 额外参数，包含学生ID和学校ID
     * @return array 模板数据
     */
    private function getHollandTemplateData($result, int $assessmentId, array $params = []): array
    {
        // 解析结果数据
        $parsedResult = [];
        $sixLetterCode = '';

        // 检查结果是否为数组格式
        if (is_array($result)) {
            // 检查是否有code字段（新格式）
            if (isset($result['code'])) {
                $sixLetterCode = $result['code'];
                $parsedResult = $result;
            }
            // 检查是否为旧格式数组
            elseif (isset($result[0]) && isset($result[1])) {
                // 尝试解析JSON格式的结果
                if (is_string($result[0])) {
                    $parsedResult = json_decode($result[0], true);
                }

                // 如果不是JSON格式或解析失败，使用原始格式
                if (!is_array($parsedResult) || empty($parsedResult)) {
                    $parsedResult = [
                        'dimensions' => $this->parseSurveyData($result[0])
                    ];
                }

                $sixLetterCode = $result[1];
            }
        }
        // 如果是字符串，尝试解析为JSON
        elseif (is_string($result)) {
            $parsedResult = json_decode($result, true);
            if (isset($parsedResult['code'])) {
                $sixLetterCode = $parsedResult['code'];
            }
        }

        // 获取前三位码
        $threeLetterCode = substr($sixLetterCode, 0, 3);

        $templateData = [];

        // 1. 获取各维度的单个代码信息
        $templateData['dimensions'] = []; // 初始化为空数组
        if (isset($parsedResult['dimensions']) && is_array($parsedResult['dimensions'])) {
            foreach ($parsedResult['dimensions'] as $dimension) {
                $code = '';
                $score = 0;

                // 处理不同格式的维度数据
                if (isset($dimension['code'])) {
                    $code = $dimension['code'];
                    $score = $dimension['score'] ?? 0;
                } elseif (isset($dimension['type'])) {
                    $code = $dimension['type'];
                    $score = $dimension['score'] ?? 0;
                }

                if (!empty($code)) {
                    $template = $this->getDimensionTemplate($code, $score);
                    if ($template) {
                        $templateData['dimensions'][] = [
                            'code' => $code,
                            'content' => $template->content,
                            'appellation' => $template->appellation,
                            'level' => $template->level,
                            'name' => $dimension['name'] ?? '',
                            'score' => $score
                        ];
                    }
                }
            }
        }

        // 判断是初中还是高中版本
        $gradeType = getGradeType($assessmentId);
        // 2. 获取六字码的个人优势与成长挑战分析
        if (!empty($sixLetterCode)) {

            $query = AssessmentCareerHollandTemplate::where('code', $sixLetterCode)
                ->where('type', '个人优势与成长挑战分析');

            // 如果有年级类型，添加条件
            if ($gradeType !== null) {
                $query->where('grade_type', $gradeType);
            }

            $template = $query->first();

            if ($template) {
                // 确保personal_analysis是JSON格式
                if (is_string($template->content)) {
                    $templateData['personal_analysis'] = json_decode($template->content, true);
                } else {
                    $templateData['personal_analysis'] = $template->content;
                }
            }
        }

        // 3. 获取前三位码的相关数据
        if (!empty($threeLetterCode)) {
            // 兴趣组合分析
            $interestAnalysis = AssessmentCareerHollandTemplate::where('code', $threeLetterCode)
                ->where('type', '你的兴趣组合分析')
                ->first();

            // $templateData['interest_analysis'] = $interestAnalysis->content;
            if ($interestAnalysis) {
                // 确保interest_analysis是JSON格式
                if (is_string($interestAnalysis->content)) {
                    $templateData['interest_analysis'] = json_decode($interestAnalysis->content, true);
                } else {
                    $templateData['interest_analysis'] = $interestAnalysis->content;
                }
            }

            // 未来专业方向推荐
            $majorRecommendation = AssessmentCareerHollandTemplate::where('code', $threeLetterCode)
                ->where('type', '未来专业方向推荐')
                ->first();

            if ($majorRecommendation) {
                // 确保major_recommendation是JSON格式
                if (is_string($majorRecommendation->content)) {
                    $templateData['major_recommendation'] = json_decode($majorRecommendation->content, true);
                } else {
                    $templateData['major_recommendation'] = $majorRecommendation->content;
                }
            }

            // 兴趣延伸实践活动建议
            $query = AssessmentCareerHollandTemplate::where('code', $threeLetterCode)
                ->where('type', '兴趣延伸实践活动建议');

            // 如果有年级类型，添加条件
            if ($gradeType !== null) {
                $query->where('grade_type', $gradeType);
            }

            $activitySuggestion = $query->first();

            if ($activitySuggestion) {
                // 确保activity_suggestion是JSON格式
                if (is_string($activitySuggestion->content)) {
                    $templateData['activity_suggestion'] = json_decode($activitySuggestion->content, true);
                } else {
                    $templateData['activity_suggestion'] = $activitySuggestion->content;
                }
            }

            // 结束语
            $query = AssessmentCareerHollandTemplate::where('code', $sixLetterCode)
                ->where('type', '结束语');

            // 如果有年级类型，添加条件
            if ($gradeType !== null) {
                $query->where('grade_type', $gradeType);
            }

            // 结束语
            $conclusion = $query->first();
            if ($conclusion) {
                $templateData['conclusion'] = $conclusion->content;
            }


        }

        $templateData['peer_data'] = [
            [
                'code' => 'R',
                'score' => 47.36
            ],
            [
                'code' => 'I',
                'score' => 48.93
            ],
            [
                'code' => 'A',
                'score' => 50.40
            ],
            [
                'code' => 'S',
                'score' => 49.58
            ],
            [
                'code' => 'E',
                'score' => 47.15
            ],
            [
                'code' => 'C',
                'score' => 46.57
            ]
        ];

        // 获取学生历年测评趋势数据
        $templateData['trend'] = $this->getStudentAssessmentTrend($params['student_id'], $params['school_id'], $assessmentId);
        
        return $templateData;
    }

    /**
     * 获取维度模板
     *
     * @param string $code 维度代码
     * @param int $score 维度分数
     * @return AssessmentCareerHollandTemplate|null 模板对象
     */
    private function getDimensionTemplate(string $code, int $score)
    {
        // 根据代码和分数范围查询模板
        $template = AssessmentCareerHollandTemplate::where('code', $code)
            ->where(function($query) use ($score) {
                $query->where(function($q) use ($score) {
                    $q->whereNotNull('lowest_score')
                      ->whereNotNull('highest_score')
                      ->where('lowest_score', '<=', $score)
                      ->where('highest_score', '>=', $score);
                })->orWhere(function($q) {
                    $q->whereNull('lowest_score')
                      ->whereNull('highest_score');
                });
            })
            ->first();

        // 如果找不到特定分数范围的模板，尝试查找不限分数范围的模板
        if (!$template || $template->content === null) {
            $fallbackTemplate = AssessmentCareerHollandTemplate::where('code', $code)
                ->whereNull('lowest_score')
                ->whereNull('highest_score')
                ->first();

            if ($fallbackTemplate && $fallbackTemplate->content !== null) {
                return $fallbackTemplate;
            }
        }

        return $template;
    }


    private function parseSurveyData($data): array
    {
        $survey = [];
        foreach ($data as $key => $value) {
            $survey[$key] = [
                'name' => $value[2],
                'type' => $value[1],
                'score' => $value[0]
            ];
        }
        return $survey;
    }

    /**
     * 获取学生历年测评趋势数据
     *
     * @param int $studentId 学生ID
     * @param int $schoolId 学校ID
     * @param int $assessmentId 测评ID
     * @return array 学生历年测评趋势数据
     */
    private function getStudentAssessmentTrend(int $studentId, int $schoolId, int $assessmentId): array
    {
        // 获取学生历年的测评数据
        $assessmentResults = \DB::table('assessment_task_assignments')
            ->select(
                'assessment_task_assignments.id',
                'assessment_task_assignments.standard_results',
                'assessment_task_assignments.created_at',
                'classes.grade_id',
                'grades.grade_name'
            )
            ->join('student_classes', function($join) use ($studentId) {
                $join->on('assessment_task_assignments.student_class_id', '=', 'student_classes.id')
                    ->where('student_classes.student_id', '=', $studentId);
            })
            ->join('classes', 'student_classes.class_id', '=', 'classes.id')
            ->join('grades', 'classes.grade_id', '=', 'grades.id')
            ->where('assessment_task_assignments.student_id', $studentId)
            ->where('assessment_task_assignments.school_id', $schoolId)
            ->where('assessment_task_assignments.assessment_id', $assessmentId)
            ->whereNotNull('assessment_task_assignments.standard_results')
            ->orderBy('assessment_task_assignments.created_at', 'asc')
            ->get();

        // 如果没有历史数据，返回空数组
        if ($assessmentResults->isEmpty()) {
            return [];
        }

        // 处理测评结果，按年级分组
        $trendData = [];
        $processedGrades = []; // 用于跟踪已处理的年级

        foreach ($assessmentResults as $result) {
            $gradeName = $result->grade_name;

            // 如果已经处理过这个年级，跳过
            if (in_array($gradeName, $processedGrades)) {
                continue;
            }

            $processedGrades[] = $gradeName;

            // 解析测评结果
            $standardResults = is_string($result->standard_results)
                ? json_decode($result->standard_results, true)
                : $result->standard_results;

            if (empty($standardResults) || empty($standardResults['dimensions'])) {
                continue;
            }

            // 创建年级数据
            $gradeData = [
                'grade' => $gradeName
            ];

            // 添加各维度分数
            foreach ($standardResults['dimensions'] as $dimension) {
                $code = $dimension['code'] ?? '';
                $score = $dimension['score'] ?? 0;

                if (!empty($code)) {
                    $gradeData[$code] = (int)$score;
                }
            }

            // 确保包含所有六个维度
            foreach (['R', 'I', 'A', 'S', 'E', 'C'] as $code) {
                if (!isset($gradeData[$code])) {
                    $gradeData[$code] = 0;
                }
            }

            $trendData[] = $gradeData;
        }

        return $trendData;
    }

}
