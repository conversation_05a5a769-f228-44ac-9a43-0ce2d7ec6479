<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */
namespace app\evaluation\logic;
use app\evaluation\model\DistributionTeachersStudents as DistributionTeachersStudentsModel;
use app\evaluation\model\EvaluationAnswer as EvaluationAnswerModel;
use app\evaluation\model\EvaluationLog as EvaluationLogModel;
use app\evaluation\model\Question as question_model;
use app\evaluation\model\Distribution as Distributionmodel;
use app\evaluation\model\Papers as PapersModel;
use think\Db;


class Answer{
    protected $user;
    protected $evaluationAnswerModel;
    protected $evaluationLogModel;
    protected $question_model;
    protected $distributionModel;
    protected $paperModel;
    public function __construct()
    {
        $this->evaluationAnswerModel = new EvaluationAnswerModel();
        $this->evaluationLogModel = new EvaluationLogModel();
        $this->question_model = new question_model();
        $this->distributionModel = new Distributionmodel();
        $this->paperModel = new PapersModel();
        $this->distributionTeachersStudents = new DistributionTeachersStudentsModel();
        $this->user = get_user();
    }

    public function add()
    {
        //录入试卷表
        $data = input('post.');
        //没有分发就不能做
        $member_ids = $this->distributionModel->where('id',$data['distribution_id'])->value('member_ids');
        if(!in_array($this->user['id'],explode(',',$member_ids))) apiReturn([],'你没有权限',-5);
        $student = db('student')->field('id,grade_id,class_id,school_district')->where('member_id',$this->user['id'])->order('id desc')->find();
        $submit_arr = json_decode($data['answer_arr'],true);

        //提交拦截，不能二次提交
        $where['distribution_id'] = $data['distribution_id'];
        $where['paper_id']        = $data['paper_id'];
        $where['member_id']       = $this->user['id'];
        $where['status']          = 0;
        //查询answer表和log表已经有的记录
        $answer = $this->evaluationAnswerModel->field('id')->where($where)->find();
        $log    = $this->evaluationLogModel->field('id')->where($where)->find();
        if( !empty($answer) || !empty($log) ) apiReturn([],'请勿重复提交答案',-8);

        //获取问题id拼接成的字符串
        $question_ids= '';
        $input_data = [];
        foreach ($submit_arr as $v){
            $question_ids .= ','. $v[0];
            $question_ids = ltrim($question_ids,',');
            $input_data[$v[0]] = $v[1];
        }
        //根据问题id拼接成的字符串查询问题对应的option
        $question_option_detail = $this->question_model->with(['typeStr', 'optionList'])
            ->where(function ($query) use ($question_ids) {
                $query->where('status', 0)->where(['id'=>['in',$question_ids]]);
            })
            ->with(['children'=>function($query){
                $query->with(['typeStr', 'optionList']);
            }])
            ->select();
        $question_option_detail = to_arr($question_option_detail);

        $get_score_arr = [];
        foreach ($question_option_detail as $key => $value){
            //常规题答案存在question表的answer字段里，答案匹配，此题得分
            switch ($value['type_id']) {
                //选择题,选择正确选项才有分数
                case 1:
                    if($value['answer'] == $input_data[$value['id']]){
                        $get_score_arr[$value['id']] = $value['score'];
                    }else{
                        foreach ($value['option_list'] as $k => $v) {
                            if ($v['title'] == $input_data[$value['id']]) {
                                $get_score_arr[$value['id']] = $v['score'];
                            }
                        }
                    }
                    break;
                //多选题
                case 2:
                    $get_score_arr[$value['id']] = $this->get_score(explode(',',$input_data[$value['id']]),explode(',',$value['answer']));
                    break;
                //判断题，评分规则：全答对满分，答错1题扣2分，答错2题扣4分，全答错或未作答得：0分
                case 6:
                    //普通题也就是常规题
                    if($value['is_common'] == 1 && $value['parent_id'] == 0){
                        if($value['answer'] == $input_data[$value['id']]){
                            $get_score_arr[$value['id']] = $value['score'];
                        }else{
                            $get_score_arr[$value['id']] = 0;
                        }
                    }elseif($value['is_common'] == 1 && $value['parent_id'] != 0){
                        $answer_arr = explode(',',$input_data[$value['id']]);
                        $i = 0;
                        foreach ($value['option_list'] as $k => $v) {
                            if (isset($answer_arr[$k]) && $v['answer'] != $answer_arr[$k]) {
                                $i++;
                            }
                        }
                        if($i == count($value['option_list'])) {
                            $get_score_arr[$value['id']] = 0;
                        }elseif($i != count($value['option_list'])){
                            $get_score_arr[$value['id']] = $value['score'] - $i * 2;
                        }
                    }

                    break;
            }

        }
        //计算好客观题分数后，存入表中
        foreach ($submit_arr as $v){
            $row[] = [
                'paper_id'=>$data['paper_id'],
                'distribution_id'=>$data['distribution_id'],
                'student_id' => $student['id'],
                'question_id'=>$v[0],
                'answer'=>$v[1],
                'score'=>$get_score_arr[$v[0]] ?? null,
                'grading_method'=>$v[2],
                'school_id' => $this->user['school_id'],
                'school_district_id' => $student['school_district'],
                'member_id' => $this->user['id'],
            ];
        }
        $res = $this->evaluationAnswerModel->allowField(true)->saveAll($row);
        //是否有主观题，0无1有
        $has_subjective = $this->paperModel->where('id',$data['paper_id'])->value('has_subjective');
        //将分发试卷时指定批阅的平均分配的老师带入log表中
        $teacher_member_id = $this->distributionTeachersStudents->where('distribution_id = '.$data['distribution_id'].' and paper_id = '.$data['paper_id'].' and find_in_set('.$this->user['id'].',member_ids)')->value('teacher_member_id');
        $info = [
            'paper_id'=>$data['paper_id'],
            'grade_id'=>$student['grade_id'],
            'class_id'=>$student['class_id'],
            'student_id' => $student['id'],
            'member_id' => $this->user['id'],
            'distribution_id'=>$data['distribution_id'],
            'used_time'=>$data['used_time'] ?? '',
            'check_member_id'=>$teacher_member_id ?? 0,
            'score'=>array_sum(array_values($get_score_arr)),
            'check_status'=>$has_subjective == 1 ? 0 : 1,//有主观题置为待批阅，无主观题置为已批阅
            'school_id' => $this->user['school_id'],
            'school_district_id' => $student['school_district'],
        ];
        $res = $this->evaluationLogModel->allowField(true)->save($info);
        $this->ranking($data['distribution_id'],$data['paper_id'],$this->user['id'],$member_ids);
        apiReturn($res);
    }

    public function get_list()
    {
        $id = input('id');
        if (!empty($id)) {
            $data = $this->evaluationLogModel->where('id', $id)->find();
            apiReturn($data);
        } else {
            $pageNumber        = input('page', 1); // 获取页码，默认为1
            $pageSize          = input('pagesize', 10); // 获取每页显示的记录数，默认为10
            $distribution_name = input('distribution_name'); //测评名称
            $paper_name        = input('paper_name');
            $start_time        = input('start_time');
            $school_id         = input('school_id');
            $grade_id          = input('grade_id');
            $class_id          = input('class_id');
            $end_time          = input('end_time');
            $name              = input('name');
            $check_status      = input('check_status');
            $literacy_level    = input('literacy_level');
            $data = $this->evaluationLogModel->alias('a')
                ->field('a.*,p.paper_name,p.has_subjective,f.name as teacher_name')
                ->join('evaluation_papers p','a.paper_id = p.id')
                ->join('member f','a.check_member_id = f.id','left')
                ->where(function ($query) use($paper_name,$school_id,$grade_id,$class_id,$start_time,$end_time,$check_status,$literacy_level){
                    $query->where('a.status', 0);
                    if($this->user['role_source_id'] == 3) $query->where('a.check_member_id', $this->user['id']);
                    if(isset($this->user['school_id']) && $this->user['school_id']) {
                        $query->where('a.school_id', $this->user['school_id']);
                    }
                    if(!empty($paper_name)) {
                        $query->where('p.paper_name', 'like','%' . $paper_name . '%');
                    }
                    if(!empty($school_id)) {
                        $query->where(['a.school_id'=>['in',$school_id]]);
                    }
                    if(!empty($grade_id)) {
                        $query->where(['a.grade_id'=>['in',$grade_id]]);
                    }
                    if(!empty($class_id)) {
                        $query->where(['a.class_id'=>['in',$class_id]]);
                    }
                    if(!empty($start_time) && !empty($end_time)) {
                        $query->where(['a.create_at'=>['between', [$start_time, $end_time]]]);
                    }
                    if(is_numeric($check_status)) {
                        $query->where('a.check_status',$check_status);
                    }
                    if(!empty($literacy_level)) {
                        $query->where('a.literacy_level', $literacy_level);
                    }
                })
                ->order('a.id desc');

            $data = $data->select();
            $data = to_arr($data);
//            print_r($data);die;
            $evaluation_log_arr = $this->evaluationLogModel->alias('a')
                ->join('evaluation_distribution d','a.distribution_id = d.id','left')
                ->join('school sch','a.school_id = sch.id')
                ->join('school_district g','a.school_district_id = g.id','left')
                ->join('grade b','a.grade_id = b.id')
                ->join('class c','a.class_id = c.id')
                ->join('member e','a.member_id = e.id')
                ->field('d.title,d.status,a.distribution_id,a.member_id,b.grade_name,c.name as class_name,e.name as student_name,sch.name as school_name,g.campus_name as school_district_name')
                ->where(function ($query) use($distribution_name,$school_id,$grade_id,$class_id,$start_time,$end_time,$name,$check_status,$literacy_level){
                    $query->where('a.status', 0);
                    $query->where('d.status', 0);
                    if($this->user['role_source_id'] == 3) $query->where('a.check_member_id', $this->user['id']);
                    if(isset($this->user['school_id']) && $this->user['school_id']) {
                        $query->where('a.school_id', $this->user['school_id']);
                    }elseif(!empty($school_id)){
                        $query->where('a.school_id', $school_id);
                    }
                    if(!empty($distribution_name)) {
                        $query->where('d.title', 'like','%' . $distribution_name . '%');
                    }
                    if(!empty($grade_id)) {
                        $query->where(['a.grade_id'=>['in',$grade_id]]);
                    }
                    if(!empty($class_id)) {
                        $query->where(['a.class_id'=>['in',$class_id]]);
                    }
                    if(!empty($start_time) && !empty($end_time)) {
                        $query->where(['a.create_at'=>['between', [$start_time, $end_time]]]);
                    }
                    if(!empty($name)) {
                        $query->where('e.name like "%'.$name.'%"');
                    }
                    if(is_numeric($check_status)) {
                        $query->where('a.check_status',$check_status);
                    }
                    if(!empty($literacy_level)) {
                        $query->where('a.literacy_level', $literacy_level);
                    }
                })
                ->group('a.distribution_id,a.member_id')
                ->order('a.school_id desc,a.id desc')
                ->select();
//            print_r($this->evaluationLogModel->getLastSql());die;
            $evaluation_log_arr = to_arr($evaluation_log_arr);
            foreach ($evaluation_log_arr as $key => $value){
                foreach ($data as $k => $v){
                    if($value['distribution_id'] == $v['distribution_id'] && $value['member_id'] == $v['member_id']){
                        $evaluation_log_arr[$key]['papers'][] = $v;
                    }
                }
                //如果没有papers就unset掉
                if(!isset($evaluation_log_arr[$key]['papers']) || empty($evaluation_log_arr[$key]['papers'])) unset($evaluation_log_arr[$key]);
            }
            return pageing($evaluation_log_arr,$pageSize,$pageNumber);
        }
    }

    public function change_review_teacher(){
        $id = input('id');
        $check_member_id = input('check_member_id');
        //只允许改未批阅的
        $data = $this->evaluationLogModel->where(['id'=>$id,'check_status'=>0])->update(['check_member_id'=>$check_member_id]);
        return $data;
    }

    public function correction_score(){
        $record_id = input('record_id');
        $score = input('score');
        $data = $this->evaluationLogModel->where('id',$record_id)->update(['score'=>$score]);
        return $data;
    }

    public function get_question_score(){
        $distribution_id = input('distribution_id');
        $paper_id = input('paper_id');
        $member_id = input('member_id');
        $data = $this->evaluationAnswerModel->field('id,question_id,answer,score')->where(['distribution_id'=>$distribution_id,'paper_id'=>$paper_id,'member_id'=>$member_id,'status'=>0])->select();
        foreach ($data as $key => $value){
            if($value['score'] !== null) $data[$key]['score'] = floatval($value['score']);
        }
        return $data;
    }

    public function submit_subjective(){
        $data = input('put.');

        $where['distribution_id'] = $data['distribution_id'];
        $where['paper_id']        = $data['paper_id'];
        $where['member_id']       = $data['member_id'];
        $where['status']          = 0;
        //将前端传过来的questionid转换成answerid用于批量更新，因为批量更新需要主键id
        $questionid_to_answer = $this->evaluationAnswerModel->where($where)->column('question_id,id');

        $check_status = 1;
        $updateAnswerData = [];
        foreach ($data['answer_arr'] as $item) {
            if(isset($questionid_to_answer[$item[0]]) && $questionid_to_answer[$item[0]] > 0) {
                $updateAnswerData[] = [
                    'id' => $questionid_to_answer[$item[0]],
                    'score' => $item[1]
                ];
                if($check_status == 1 && $item[1] === null) $check_status = 0;//只要有一个null即可判定为中途提交
            }
        }
        //注意此处必须有id,只能更新不能新增，不然会出现重复提交的问题，且无法拦截
        $res = $this->evaluationAnswerModel->saveAll($updateAnswerData);

        //判断老师批改一半提交的情况，提交结果有null时说明没批改完，不再执行录入log表和计算排名的操作
        if($check_status == 1){
            //统计evaluation_answer中一个学生的一次分发的一张卷子的分数后更新evaluation_log表中的分数
            $score = $this->evaluationAnswerModel->where($where)->value('sum(score)');

            $updateLogData['score'] = $score;
            $updateLogData['check_status'] = 1;
//            $updateLogData['check_member_id']=$this->user['id'];//此处会导致教务核正分数后，老师就没权限查看的bug,故不能更改批改人

            $res = $this->evaluationLogModel->where($where)->update($updateLogData);
            $this->ranking($data['distribution_id'],$data['paper_id'],$data['member_id'],'');
        }
        apiReturn($res);
    }

    //计算排名
    //前25%优秀，中间25%良，后50%有待提高
    private function ranking($distribution_id,$paper_id,$current_id,$member_ids){
        if(!$member_ids){
            $member_ids = $this->distributionModel->where('id',$distribution_id)->value('member_ids');
        }
        //查询分发一套试卷，当前学生把分发的试卷都做完，此次测评才能算已完成
//        $paper_ids = $this->distributionDetailModel->where(['distribution_id'=>$distribution_id,'status'=>0])->group('paper_id')->column('paper_id');
//        $current_log = $this->evaluationLogModel
//            ->where(['distribution_id'=>$distribution_id,'paper_id'=>['in',$paper_ids],'member_id'=>$current_id,'status'=>0])
//            ->group('paper_id')
//            ->column('check_status');
//        //分发的试卷和已做的试卷id数量相等的话，说明都做了
//        if(count($paper_ids) == count($current_log)){
//            //还没想写什么
//        }
        $evaluation_log = $this->evaluationLogModel
            ->where(['distribution_id'=>$distribution_id,'paper_id'=>$paper_id,'member_id'=>['in',$member_ids],'status'=>0])
            ->field('id,score,check_status')
            ->group('member_id')
            ->select();
        $evaluation_log = to_arr($evaluation_log);
        $num = count($evaluation_log);//做了测评的学生
        $total = count(explode(',',$member_ids));//所有指定的学生
        if($num < $total) return;//有学生没做测评，就不计算优良

        $check_arr = array_column($evaluation_log,'check_status');
        if(in_array(0,$check_arr)) return;//有测评没有批阅，就不计算优良
        $score_arr = array_column($evaluation_log,'score');

        //根据分数倒序排序
        array_multisort($score_arr,SORT_DESC,$evaluation_log);

        $normal = ceil(0.25 * $total);
        $you_arr = array_slice($evaluation_log,0,$normal);//前25%
        $liang_arr = array_slice($evaluation_log,$normal,$normal);//中25%
        $youdaitigao_arr = array_slice($evaluation_log,2*$normal);//50%到最后
        //组装更新数据
        $you_data = $this->make_updata($you_arr,'优');
        $liang_data = $this->make_updata($liang_arr,'良');
        $youdaitigao_data = $this->make_updata($youdaitigao_arr,'有待提高');
        $updateData = array_merge($you_data,$liang_data,$youdaitigao_data);

        $res = $this->evaluationLogModel->saveAll($updateData);
        return $res;
    }

    private function make_updata($arr,$level = '优'){
        $updateData = [];
        foreach ($arr as $item) {
            $updateData[] = [
                //批量更新需要主键id
                'id' => $item['id'],
                'literacy_level' => $level
            ];
        }
        return $updateData;
    }

    public function log_papers(){
        $data = $this->evaluationLogModel->alias('a')
            ->join('evaluation_papers b','a.paper_id = b.id')
            ->field('a.paper_id,b.paper_name')
            ->where(['a.school_id'=>$this->user['school_id'],'a.status'=>0,'b.status'=>0])
            ->group('paper_id')
            ->select();
        return $data;
    }

    public function get_score($student_answers, $correct_answers) {
        $n = count($correct_answers);
        $score = 0;

        foreach ($student_answers as $answer) {
            if (in_array($answer, $correct_answers)) {
                $score += 8 / $n;
            } else {
                $score -= 10 / $n;
            }
        }

        return max(0, ceil($score)); // Ensure the score is not less than 0 and round up to the nearest integer
    }
}