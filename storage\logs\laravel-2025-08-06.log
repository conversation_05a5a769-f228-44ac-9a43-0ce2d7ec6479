[2025-08-06 09:57:21] local.INFO: SQL Query: select `organization_has_menus`.`id`, `organization_has_menus`.`menu_id`, `organization_has_menus`.`parent_id`, `organization_has_menus`.`date_start`, `organization_has_menus`.`date_due`, `menus`.`url`, `menus`.`icon`, `menus`.`sort`, COALESCE(NULLIF(organization_has_menus.menu_alias, ''), menus.menu_name) as menu_name from `organization_has_menus` inner join `menus` on `menus`.`id` = `organization_has_menus`.`menu_id` where `menus`.`status` = ? and `organization_has_menus`.`status` = ? and `organization_has_menus`.`organization_id` = ? and exists (select * from `roles` inner join `role_has_menus` on `roles`.`id` = `role_has_menus`.`role_id` where `organization_has_menus`.`id` = `role_has_menus`.`organization_menu_id` and 0 = 1) order by `organization_has_menus`.`sort` asc  
[2025-08-06 09:57:21] local.INFO: Bindings:  [1,1,53] 
