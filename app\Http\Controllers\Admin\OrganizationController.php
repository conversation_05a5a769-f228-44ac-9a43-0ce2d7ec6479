<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Partner\Partner;
use App\Models\School\System\School;
use App\Services\Admin\OrganizationService;
use App\Services\Partner\PartnerService;
use App\Services\RoleService;
use App\Services\School\System\SchoolService;
use App\Services\UserService;
use App\Services\DataSync\DataSyncService;
use App\Traits\PasswordTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class OrganizationController extends Controller
{
    use PasswordTrait;

    // 构造函数 使用构造函数属性提升，简化代码
    public function __construct(
        protected OrganizationService $organizationService,
        protected SchoolService $schoolService,
        protected PartnerService $partnerService,
        protected RoleService $roleService,
        protected UserService $userService
    ) {
    }

    /**
     * 获取学校列表
     */
    public function getSchools(Request $request)
    {
        return $this->getOrganizationList($request, 'schoolListBuilder');
    }

    /**
     * 获取合作伙伴列表
     */
    public function getPartners(Request $request)
    {
        return $this->getOrganizationList($request, 'partnerListBuilder');
    }

    /**
     * 获取学校校区
     */
    public function schoolCampuses($school_id)
    {
        $list = $this->schoolService->getSchoolCampus($school_id);
        return $this->success($list);
    }

    /**
     * 获取合作伙伴校区
     */
    public function partnerCampuses($partner_id)
    {
        $list = $this->partnerService->getPartnerCampus($partner_id);
        return $this->success($list);
    }

    /**
     * 设置合作伙伴校区
     */
    public function setPartnerCampuses($partner_id, Request $request)
    {
        $count = $this->partnerService->setPartnerCampuses($partner_id,$request);
        return $this->message("设置成功，共：{$count}");
    }

    /**
     * 创建学校
     */
    public function createSchool(Request $request)
    {
        // 获取模型类的别名
       
        $alias = (new School())->getMorphClass();
        $school = $this->organizationService->createOrgDefaultInfo($request, $alias);
       
        // 数据同步调用
        try {
            // 直接调用DataSyncService
            $dataSyncService = app(\App\Services\DataSync\DataSyncService::class);

            // 直接调用同步学校接口，传递学校数据和请求参数
            $syncResult = $dataSyncService->syncSchool($school->toArray(), $request);

            Log::info('学校数据同步结果', [
                'school_id' => $school->id,
                'success' => $syncResult['success'],
                'message' => $syncResult['message']
            ]);

        } catch (\Exception $e) {
            Log::error('学校数据同步失败', [
                'school_id' => $school->id ?? 'unknown',
               'error' => $e->getMessage()
            ]);
        }

        return $this->message("学校创建成功");
    }

    /**
     * 更新学校
     */
    public function updateSchool(Request $request, $id)
    {
        $this->schoolService->updateSchool($request, $id);
        return $this->message('学校更新成功');
    }

    public function createPartner(Request $request)
    {
        // 获取模型类的别名
        $alias = (new Partner())->getMorphClass();
        $this->organizationService->createOrgDefaultInfo($request, $alias);
        return $this->message("教育局创建成功");
    }

    public function updatePartner(Request $request, $id)
    {
        $this->partnerService->updatePartner($request, $id);
        return $this->message('教育局更新成功');
    }

    // 设置机构拥有的初始菜单
    public function setOrganizationHasMenus(Request $request, $organization_id)
    {
        $this->organizationService->setOrganizationHasMenus($request, $organization_id);
        return $this->message("设置成功");
    }

    // 获取机构拥有的初始菜单
    public function getOrganizationHasMenus($organization_id)
    {
        $orgInitMenus = $this->organizationService->getOrgInitMenus($organization_id);
        return $this->success($orgInitMenus);
    }

    //更新菜单别名
    public function updateMenuAlias(Request $request)
    {
        $this->organizationService->updateMenuAlias($request);
        return $this->message("设置成功");
    }

    public function roleList(RoleService $roleService, $organization_id)
    {
        $list = $roleService->listBuilder($organization_id);
        return $this->success($list);
    }

    public function userList(Request $request, UserService $userService, $organization_id)
    {
        $query = $userService->listBuilder($request, $organization_id);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get()->each(function ($item) {
            $item->role_names = $item->roles->pluck('name')->join(',');
        });
        // return $this->success(compact('list', 'cnt'));
        return $this->paginateSuccess($list, $cnt);
    }

    /**
     * 获取组织列表通用方法
     */
    private function getOrganizationList(Request $request, string $builderMethod)
    {
        $query = $this->organizationService->$builderMethod($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        return $this->paginateSuccess($list, $cnt);
    }

}
