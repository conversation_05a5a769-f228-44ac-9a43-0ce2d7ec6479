<?php

namespace App\Repositories;

use App\Models\Admin\Menu;
use App\Models\Admin\OrganizationHasMenu;

class MenuRepository
{
    /**
     * 获取适用人群的默认没有子级的菜单
     * 
     * @param string $crowd 适用人群
     * @return \Illuminate\Database\Eloquent\Collection 没有子级的菜单集合
     */
    public function getLeafMenus($crowd)
    {
        // 获取所有符合条件的菜单
        $allMenus = Menu::where('status', 1)
            ->whereNotNull('crowd')
            ->whereRaw("JSON_VALID(crowd)")
            ->whereRaw('JSON_CONTAINS(crowd, JSON_QUOTE(?))', [$crowd])
            ->select('id', 'parent_id')
            ->get();
        
        // 获取所有作为父级的菜单ID
        $parentIds = $allMenus->pluck('parent_id')->unique()->toArray();
        
        // 筛选出不作为其他菜单父级的菜单（即叶子节点）
        $leafMenus = $allMenus->filter(function ($menu) use ($parentIds) {
            return !in_array($menu->id, $parentIds);
        });
        
        return $leafMenus;
    }

    /**
     * 获取机构拥有的菜单中，属于这些菜单的ID列表
     * @param Request $request
     */
    public function orgMenus($organization_id, $allMenuIds)
    {
        $orgMenuIds = OrganizationHasMenu::where('organization_id', $organization_id)
            ->where('status', 1)
            ->whereIn('menu_id', $allMenuIds)
            ->pluck('id')
            ->toArray();
        return $orgMenuIds;
    }


}