<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON>
 * Date: 2024/01/19
 * Time: 17:52
 */
namespace app\psychassessment\service;

class CounselorRecord{
    protected $CounselorRecord;
    public function __construct()
    {
          $this->CounselorRecordlogic = new \app\psychassessment\logic\CounselorRecord();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://查询

                return $this->CounselorRecordlogic->get_list();
                break;
            case 'post'://增加

                return $this->CounselorRecordlogic->add();
                break;
            case 'put'://修改

                return $this->CounselorRecordlogic->edit();
                break;
            case 'delete'://删除
                return $this->CounselorRecordlogic->del();
                break;
            default:
                return false;
        }
    }

    public function get_appointment_student_list(){
        return $this->CounselorRecordlogic->get_appointment_student_list();
    }

    public function down_counselor_record(){
        return $this->CounselorRecordlogic->down_counselor_record();
    }

    public function counselor_head_info(){
        return $this->Counselor<PERSON><PERSON>ordlogic->counselor_head_info();
    }

    public function counselor_wait(){
        $data = $this->CounselorRecordlogic->counselor_wait();
        apiReturn($data);
    }
}