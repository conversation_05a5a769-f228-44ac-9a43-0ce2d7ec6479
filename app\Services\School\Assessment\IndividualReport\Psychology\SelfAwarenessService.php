<?php
namespace App\Services\School\Assessment\IndividualReport\Psychology;

class SelfAwarenessService extends AbstractPsychologyReportService
{
    /**
     * 获取自我意识评估的等级常量。
     *
     * @return array 返回包含总分和维度得分等级定义的数组。
     */
    protected function getLevelConstants(): array
    {
        return [
            'total' => [
                ['max' => 45, 'level' => '较低水平'], // 注意这里用 'max' 对应基类的实现
                ['max' => PHP_FLOAT_MAX, 'level' => '正常水平']
            ],
            'dimension' => [
                ['max' => 45, 'code' => 'low', 'text' => '表现较弱'], // 添加 text 属性用于报告生成
                ['max' => PHP_FLOAT_MAX, 'code' => 'normal', 'text' => '表现较强']
            ]
        ];
    }

    /**
     * 获取此服务对应的配置键名。
     *
     * @return string 返回配置键名 'awareness'。
     */
    protected function getConfigKey(): string
    {
        return 'awareness';
    }

    /**
     * 处理评估信息，生成报告所需的图表、描述、能力和建议。
     *
     * @param array $standard_results 标准化后的评估结果。
     * @param array $config 相关的配置信息。
     * @return array 包含图表、描述、能力和建议的数组。
     */
    protected function processAssessmentInfo(array $standard_results, array $config): array
    {
        $dimension_results = $this->analyzeDimensions($standard_results['dimensions'], $config);
        return [
            'charts'      => $this->generateCharts($standard_results),
            'discription' => $this->generateReportText($standard_results, $dimension_results, $config),
            'competence'  => $dimension_results['competence'],
            'advise'      => $this->getAdvice($standard_results['total_score'], $config)
        ];
    }

    /**
     * 分析维度得分，将其分类并生成能力描述。
     *
     * @param array $standard_results_dimensions 标准化结果中的维度数据。
     * @param array $config 相关的配置信息。
     * @return array 包含分类后的维度名称和能力描述的数组。
     */
    protected function analyzeDimensions(array $standard_results_dimensions, array $config): array
    {
        $categories = ['low' => [], 'normal' => []];
        $competence = [];

        foreach ($standard_results_dimensions as $index => $dimension_data) {
            $dimension_name = $config['dimension_codes'][$index] ?? $dimension_data['name']; 
            $level = $this->getScoreLevel($dimension_data['score'], 'dimension');
            $categories[$level][] = $dimension_name;
            $competence[$index] = $config[$index][$level];
        }

        return [
            'categories' => $categories,
            'competence' => $competence
        ];
    }

    /**
     * 生成报告的文本描述部分。
     *
     * @param array $standard_results 标准化后的评估结果。
     * @param array $dimension_results 分析后的维度结果。
     * @param array $config 相关的配置信息。
     * @return array 包含总结和详细描述的文本数组。
     */
    protected function generateReportText(array $standard_results, array $dimension_results, array $config): array
    {
        $total_level = $this->getScoreLevel($standard_results['total_score'], 'total');
        $summary = sprintf('在该评估中，你的总分为%s分，总体自我意识处于%s。', $standard_results['total_score'], $total_level);
        
        $text = '你';
        $parts = [];
        $dimension_level_texts = $this->getLevelConstants()['dimension'];
        $level_code_to_text_map = array_column($dimension_level_texts, 'text', 'code');

        if (!empty($dimension_results['categories']['normal'])) {
            $parts[] = sprintf('在%s方面的自我意识%s',
                implode('、', $dimension_results['categories']['normal']),
                $level_code_to_text_map['normal'] ?? '表现较强'
            );
        }
        if (!empty($dimension_results['categories']['low'])) {
            $parts[] = sprintf('在%s方面的自我意识%s',
                implode('、', $dimension_results['categories']['low']),
                $level_code_to_text_map['low'] ?? '表现较弱'
            );
        }
        
        return [$summary, $text . implode('，', $parts) . '。'];
    }

    /**
     * 根据总分获取建议。
     *
     * @param float $total_score 评估总分。
     * @param array $awareness_config 自我意识相关的配置信息。
     * @return array 返回对应分数水平的建议。
     */
    private function getAdvice(float $total_score, array $awareness_config): array
    {
        $level = $this->getScoreLevel($total_score, 'total');
        // 假设 LEVEL_THRESHOLDS total 的 level 与配置的 key 一致
        $advice_key = ($level === '较低水平') ? 'low' : 'normal'; 
        return $awareness_config['advise'][$advice_key];
    }
}