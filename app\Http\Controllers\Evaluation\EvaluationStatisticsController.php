<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use App\Services\Evaluation\StatisticsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * 统计分析控制器
 */
class EvaluationStatisticsController extends Controller
{
    protected $statisticsService;

    public function __construct(StatisticsService $statisticsService)
    {
        $this->statisticsService = $statisticsService;
    }

    /**
     * 统计查询
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $data = $this->statisticsService->getOverview($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取统计数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取统计概览
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getOverview(Request $request): JsonResponse
    {
        try {
            $data = $this->statisticsService->getOverview($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取统计概览失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取学生报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudentReport(Request $request): JsonResponse
    {
        try {
            $data = $this->statisticsService->getStudentReport($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取学生报告失败: ' . $e->getMessage());
        }
    }

    /**
     * 综合统计 - 学生维度
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function integratedStatisticsStudent(Request $request): JsonResponse
    {
        try {
            $data = $this->statisticsService->integratedStatisticsStudent($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取学生综合统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 综合统计 - 头部信息
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function integratedStatisticsHead(Request $request): JsonResponse
    {
        try {
            $data = $this->statisticsService->integratedStatisticsHead($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取统计头部信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 生涯统计
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function statisticsCareer(Request $request): JsonResponse
    {
        try {
            $data = $this->statisticsService->statisticsCareer($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取生涯统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取趋势分析
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getTrend(Request $request): JsonResponse
    {
        try {
            $data = $this->statisticsService->getTrend($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取趋势分析失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取对比分析
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getComparison(Request $request): JsonResponse
    {
        try {
            $data = $this->statisticsService->getComparison($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取对比分析失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量创建评估PDF
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchCreateEvaluationPdf(Request $request): JsonResponse
    {
        try {
            $data = $this->statisticsService->batchCreateEvaluationPdf($request->all());
            return $this->success($data, 'PDF批量创建完成');
        } catch (\Exception $e) {
            return $this->error('PDF批量创建失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量下载评估PDF
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchDownloadEvaluationPdf(Request $request): JsonResponse
    {
        try {
            $data = $this->statisticsService->batchDownloadEvaluationPdf($request->all());
            return $this->success($data, 'PDF批量下载准备完成');
        } catch (\Exception $e) {
            return $this->error('PDF批量下载失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出统计数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function exportStatistics(Request $request): JsonResponse
    {
        try {
            $exportType = $request->input('export_type', 'overview'); // overview, student, grade, class
            $format = $request->input('format', 'excel'); // excel, csv
            
            switch ($exportType) {
                case 'overview':
                    $data = $this->statisticsService->getOverview($request->all());
                    break;
                case 'student':
                    $data = $this->statisticsService->getStudentReport($request->all());
                    break;
                case 'integrated_student':
                    $data = $this->statisticsService->integratedStatisticsStudent($request->all());
                    break;
                case 'career':
                    $data = $this->statisticsService->statisticsCareer($request->all());
                    break;
                default:
                    throw new \Exception('不支持的导出类型');
            }
            
            // 这里需要实现具体的导出逻辑
            // 可以使用 Laravel Excel 等包来实现
            
            return $this->success([
                'download_url' => '',
                'file_name' => "statistics_{$exportType}_" . date('YmdHis') . ".{$format}"
            ], '统计数据导出成功');
        } catch (\Exception $e) {
            return $this->error('统计数据导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取实时统计
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getRealTimeStats(Request $request): JsonResponse
    {
        try {
            $schoolId = $request->input('school_id');
            $distributionId = $request->input('distribution_id');
            
            $data = [
                'current_online' => $this->getCurrentOnlineCount($schoolId),
                'today_completed' => $this->getTodayCompletedCount($schoolId, $distributionId),
                'recent_activities' => $this->getRecentActivities($schoolId, 10),
                'system_status' => $this->getSystemStatus()
            ];
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取实时统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取排行榜
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getRanking(Request $request): JsonResponse
    {
        try {
            $rankingType = $request->input('ranking_type', 'score'); // score, completion, participation
            $scope = $request->input('scope', 'school'); // school, grade, class
            $limit = $request->input('limit', 10);
            
            $data = $this->getRankingData($rankingType, $scope, $limit, $request->all());
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取排行榜失败: ' . $e->getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取当前在线人数
     */
    private function getCurrentOnlineCount(?int $schoolId): int
    {
        // 这里需要实现获取在线人数的逻辑
        // 可以通过 Redis 或其他方式实现
        return 0;
    }

    /**
     * 获取今日完成数量
     */
    private function getTodayCompletedCount(?int $schoolId, ?int $distributionId): int
    {
        // 实现获取今日完成数量的逻辑
        return 0;
    }

    /**
     * 获取最近活动
     */
    private function getRecentActivities(?int $schoolId, int $limit): array
    {
        // 实现获取最近活动的逻辑
        return [];
    }

    /**
     * 获取系统状态
     */
    private function getSystemStatus(): array
    {
        return [
            'status' => 'normal',
            'cpu_usage' => '15%',
            'memory_usage' => '60%',
            'disk_usage' => '45%'
        ];
    }

    /**
     * 获取排行榜数据
     */
    private function getRankingData(string $rankingType, string $scope, int $limit, array $params): array
    {
        // 实现获取排行榜数据的逻辑
        return [
            'ranking_type' => $rankingType,
            'scope' => $scope,
            'data' => []
        ];
    }
}
