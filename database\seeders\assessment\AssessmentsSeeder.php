<?php

namespace Database\Seeders\assessment;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AssessmentsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    protected string $connect = 'mysql_prod';
    public function run(): void
    {
        // 查询测评数据
        $categories = ['1','2', '3', '4','5','8'];
        $surveyData = DB::connection($this->connect)->table('survey')->whereIn('category', $categories)->orderBy('survey_type', 'asc')->get()->toArray();
        // 遍历数据，将数据插入到测评表中
        $assessmentData = [];
        foreach ($surveyData as $item) {
            // 准备每个测评的数据结构
            $survey_item_Data = [
                'name' => $item->title,
                'category' => $item->category,//1生涯,2五力
                'stage' => 2,
                'official_name' => '',
                'status' => 0,//状态
                'description'=> '',
                'created_at' => date('Y-m-d H:i:s',time()),
            ];
            $assessmentData[] = $survey_item_Data;
        }
        DB::table('assessments')->insert($assessmentData);
    }
}
