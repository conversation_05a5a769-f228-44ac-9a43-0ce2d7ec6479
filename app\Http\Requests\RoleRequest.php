<?php

namespace App\Http\Requests;

use App\Http\Requests\BaseRequest;

class RoleRequest extends BaseRequest
{
    public function rules(): array
    {
        $method = $this->route()->getActionMethod();

        return match ($method) {
            'crowdDefaultMenus' => $this->crowdDefaultMenusRules(),
            default => []
        };
    }

    private function crowdDefaultMenusRules(): array
    {
        return [
            'crowd' => 'required|integer'
        ];
    }

    public function messages(): array
    {
        return [
            'crowd.required' => '适用人群不能为空',
            'crowd.integer' => '适用人群必须为整数',
        ];
    }
}