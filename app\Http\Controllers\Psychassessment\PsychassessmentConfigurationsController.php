<?php

namespace App\Http\Controllers\Psychassessment;

use App\Http\Controllers\Controller;
use App\Services\Psychassessment\ConfigurationsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * 心理评估-可预约时间配置控制器 - 基于原 ThinkPHP Configurations 控制器重新实现
 */
class PsychassessmentConfigurationsController extends Controller
{
    protected $configurationsService;

    public function __construct(ConfigurationsService $configurationsService)
    {
        $this->configurationsService = $configurationsService;
    }

    /**
     * 可预约时间配置操作（添加/查询/删除/修改）
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function configurations(Request $request): JsonResponse
    {
        try {
            $method = $request->method();
            
            switch ($method) {
                case 'POST':
                    // 时间配置添加
                    $data = $this->configurationsService->createConfiguration($request->all(), auth()->user());
                    return $this->success($data, '配置成功');
                    
                case 'GET':
                    // 查询时间配置
                    $params = $request->all();
                    $data = $this->configurationsService->getConfigurationList($params);
                    return $this->success($data, '获取成功');
                    
                case 'PUT':
                    // 修改时间配置
                    $id = $request->route('id') ?: $request->input('id');
                    $data = $this->configurationsService->updateConfiguration($id, $request->all(), auth()->user());
                    return $this->success($data, '修改成功');
                    
                case 'DELETE':
                    // 删除时间配置
                    $id = $request->route('id') ?: $request->input('id');
                    $data = $this->configurationsService->deleteConfiguration($id, auth()->user());
                    return $this->success($data, '删除成功');
                    
                default:
                    return $this->error('不支持的请求方法');
            }
        } catch (\Exception $e) {
            return $this->error('操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取配置详情
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getConfigurationDetail(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $data = $this->configurationsService->getConfigurationDetail($id);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量删除配置
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchDelete(Request $request): JsonResponse
    {
        try {
            $ids = $request->input('ids');
            $data = $this->configurationsService->batchDelete($ids, auth()->user());
            return $this->success($data, '批量删除成功');
        } catch (\Exception $e) {
            return $this->error('批量删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取可用时间段
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getAvailableTimeSlots(Request $request): JsonResponse
    {
        try {
            $date = $request->input('date');
            $teacherId = $request->input('teacher_id');
            
            $data = $this->configurationsService->getAvailableTimeSlots($date, $teacherId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 复制配置到其他日期
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function copyConfiguration(Request $request): JsonResponse
    {
        try {
            $sourceId = $request->input('source_id');
            $targetDates = $request->input('target_dates');
            
            $data = $this->configurationsService->copyConfiguration($sourceId, $targetDates, auth()->user());
            return $this->success($data, '复制成功');
        } catch (\Exception $e) {
            return $this->error('复制失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取重复类型选项
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getRepeatTypes(Request $request): JsonResponse
    {
        try {
            $data = $this->configurationsService->getRepeatTypes();
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成重复配置
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function generateRepeatConfigurations(Request $request): JsonResponse
    {
        try {
            $configId = $request->input('config_id');
            $endDate = $request->input('end_date');
            
            $data = $this->configurationsService->generateRepeatConfigurations($configId, $endDate, auth()->user());
            return $this->success($data, '生成成功');
        } catch (\Exception $e) {
            return $this->error('生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取教师工作时间配置
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getTeacherWorkTime(Request $request): JsonResponse
    {
        try {
            $teacherId = $request->input('teacher_id');
            $data = $this->configurationsService->getTeacherWorkTime($teacherId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 设置教师工作时间
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function setTeacherWorkTime(Request $request): JsonResponse
    {
        try {
            $teacherId = $request->input('teacher_id');
            $workTime = $request->input('work_time');
            
            $data = $this->configurationsService->setTeacherWorkTime($teacherId, $workTime, auth()->user());
            return $this->success($data, '设置成功');
        } catch (\Exception $e) {
            return $this->error('设置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取节假日配置
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getHolidayConfigurations(Request $request): JsonResponse
    {
        try {
            $year = $request->input('year', date('Y'));
            $data = $this->configurationsService->getHolidayConfigurations($year);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 设置节假日
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function setHoliday(Request $request): JsonResponse
    {
        try {
            $data = $this->configurationsService->setHoliday($request->all(), auth()->user());
            return $this->success($data, '设置成功');
        } catch (\Exception $e) {
            return $this->error('设置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取预约统计
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getAppointmentStatistics(Request $request): JsonResponse
    {
        try {
            $params = $request->all();
            $data = $this->configurationsService->getAppointmentStatistics($params);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出配置
     * 
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|JsonResponse
     */
    public function exportConfigurations(Request $request)
    {
        try {
            $params = $request->all();
            return $this->configurationsService->exportConfigurations($params);
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 导入配置
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function importConfigurations(Request $request): JsonResponse
    {
        try {
            $file = $request->file('file');
            $data = $this->configurationsService->importConfigurations($file, auth()->user());
            return $this->success($data, '导入成功');
        } catch (\Exception $e) {
            return $this->error('导入失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取配置模板
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getConfigurationTemplates(Request $request): JsonResponse
    {
        try {
            $data = $this->configurationsService->getConfigurationTemplates();
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 应用配置模板
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function applyTemplate(Request $request): JsonResponse
    {
        try {
            $templateId = $request->input('template_id');
            $targetDates = $request->input('target_dates');
            
            $data = $this->configurationsService->applyTemplate($templateId, $targetDates, auth()->user());
            return $this->success($data, '应用成功');
        } catch (\Exception $e) {
            return $this->error('应用失败: ' . $e->getMessage());
        }
    }
}
