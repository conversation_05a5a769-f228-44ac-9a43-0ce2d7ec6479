<?php

namespace App\Repositories;

use App\Models\Gk\CollegeDistribution;
use App\Models\Gk\CollegeAverageMonthlySalary;

class CollegeRepository
{

    /**
     * employment
     * 获取学校就业相关数据
     * @param int $id 学校ID
     * @return array 就业相关数据
     */
    public function distributions($id)
    {
        
        // 获取最新年份
        $latestYearDistribution = CollegeDistribution::max('Year');
        
        // 获取学校就业分布数据（根据collegeName去重）
        $distributions = CollegeDistribution::select(
            'Post as post',
            'Work as work'
        )
        ->where('CollegeId', operator: $id)
        ->where('Year', $latestYearDistribution)
        ->first()
        ->toArray();

        return $distributions;
    }


    /**
     * salaries
     * 获取学校就业相关数据
     * @param int $id 学校ID
     * @return array 就业相关数据
     */
    public function salaries($id)
    {
        // 获取最新年份
        $latestYearSalary = CollegeAverageMonthlySalary::max('Year');
        
        // 获取该学校列表
        $salaries = CollegeAverageMonthlySalary::select(
            'Id as id',
            'CollegeId as college_id',
            'CollegeName as college_name',
            'WorkingAge as working_age',
            'NominalPrice as nominal_price',
            'MonthlySalary as monthly_salary',
            'CountrySal as country_salary',
            'Year as year'
        )
        ->where('CollegeId', $id)
        ->where('Year', $latestYearSalary)
        ->orderBy('WorkingAge')
        ->get()
        ->toArray();
        return $salaries;
    }

}