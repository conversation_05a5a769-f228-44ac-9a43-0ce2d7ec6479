<?php

namespace App\Services;

use App\Models\LoginLog;
use App\Models\User;
use Illuminate\Http\Request;

class LoginLogService
{
    /**
     * 记录登录失败日志
     */
    public function logFailure(Request $request, string $reason, string $username = null): void
    {
        LoginLog::createFailedLog(
            $username ?? $request->input('username'),
            $reason,
            $request->ip(),
            $request->userAgent()
        );
    }

    /**
     * 记录登录成功日志
     */
    public function logSuccess(Request $request, User $user): void
    {
        LoginLog::createSuccessLog(
            $user,
            $request->ip(),
            $request->userAgent()
        );
    }

    /**
     * 获取用户最近的登录记录
     */
    public function getRecentLogins(int $userId, int $limit = 10)
    {
        return LoginLog::getRecentLoginsByUser($userId, $limit);
    }

    /**
     * 获取登录统计数据
     */
    public function getLoginStats(string $startDate, string $endDate)
    {
        return LoginLog::getLoginStats($startDate, $endDate);
    }

    /**
     * 获取失败登录尝试（用于安全监控）
     */
    public function getFailedAttempts(string $ipAddress = null, string $username = null, int $hours = 24)
    {
        return LoginLog::getFailedAttempts($ipAddress, $username, $hours);
    }

    /**
     * 检查是否存在可疑的登录活动
     */
    public function checkSuspiciousActivity(string $username, int $hours = 1): bool
    {
        $failedAttempts = $this->getFailedAttempts(null, $username, $hours);
        return $failedAttempts->count() >= 5; // 1小时内失败5次以上认为可疑
    }

    /**
     * 获取IP地址的登录统计
     */
    public function getIpLoginStats(string $ipAddress, int $days = 7): array
    {
        $startDate = now()->subDays($days)->format('Y-m-d H:i:s');
        $endDate = now()->format('Y-m-d H:i:s');

        $logs = LoginLog::where('ip_address', $ipAddress)
            ->whereBetween('login_at', [$startDate, $endDate])
            ->selectRaw('
                status,
                COUNT(*) as count,
                COUNT(DISTINCT username) as unique_users
            ')
            ->groupBy('status')
            ->get();

        return [
            'success_count' => $logs->where('status', LoginLog::STATUS_SUCCESS)->first()->count ?? 0,
            'failed_count' => $logs->where('status', LoginLog::STATUS_FAILED)->first()->count ?? 0,
            'unique_users' => $logs->sum('unique_users'),
            'period_days' => $days
        ];
    }
}
