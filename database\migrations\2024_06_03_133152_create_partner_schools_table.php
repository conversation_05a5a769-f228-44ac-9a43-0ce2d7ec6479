<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 合作方学校关联表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('partner_schools', function (Blueprint $table) {
            $table->id();
            $table->integer('partner_id')->comment('合作方ID');
            $table->integer('school_id')->comment('学校ID');
            $table->integer('school_campus_id')->comment('校区ID');
            $table->timestamps();
            $table->string('creator', 20)->default('')->comment('添加人');
            $table->string('updater', 20)->default('')->comment('最后更新人');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('partner_schools');
    }
};
