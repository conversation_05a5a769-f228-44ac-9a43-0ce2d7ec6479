<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 16:01
 */
namespace app\evaluation\service;
use think\Loader;
class Knowledges{
    protected $Knowledges;
    public function __construct()
    {
          $this->KnowledgesLogic = new \app\evaluation\logic\Knowledges();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://获取
                return $this->KnowledgesLogic->get_list();
                break;
            case 'post'://增加

                return $this->KnowledgesLogic->add();
                break;
            case 'put'://修改

                return $this->KnowledgesLogic->edit();
                break;
            case 'delete'://删除
                return $this->KnowledgesLogic->del();
                break;
            default:
                return false;
        }
    }



}