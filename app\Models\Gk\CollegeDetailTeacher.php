<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CollegeDetailTeacher extends BaseModel
{
    use HasFactory;

    protected $table = 'CollegeDetailTeacher';

    // 指定连接
    protected $connection = 'sqlsrv_gk';

    //主键
    protected $primaryKey = 'ID';

    // 隐藏字段
    protected $hidden = [];

    //反向关联college BelongsTo
    public function college(): BelongsTo
    {
        return $this->belongsTo(College::class, 'CollegeID', 'ID');
    }

}
