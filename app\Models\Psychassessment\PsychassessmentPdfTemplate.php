<?php

namespace App\Models\Psychassessment;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 心理评估PDF模板模型
 */
class PsychassessmentPdfTemplate extends Model
{
    protected $table = 'psychassessment_pdf_template';

    protected $fillable = [
        'name',
        'description',
        'survey_type',
        'template_type',
        'template_content',
        'template_config',
        'status',
        'sort',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'survey_type' => 'integer',
        'status' => 'integer',
        'sort' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer'
    ];

    // 模板类型常量
    const TYPE_INDIVIDUAL = 'individual';  // 个人报告
    const TYPE_CLASS = 'class';           // 班级报告
    const TYPE_GRADE = 'grade';           // 年级报告
    const TYPE_SCHOOL = 'school';         // 学校报告

    /**
     * 关联心理评估类型
     */
    public function assessment(): BelongsTo
    {
        return $this->belongsTo(PsychassessmentAssessment::class, 'survey_type');
    }

    /**
     * 关联创建者
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Member::class, 'created_by');
    }

    /**
     * 关联更新者
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Member::class, 'updated_by');
    }

    /**
     * 获取模板配置数组
     */
    public function getTemplateConfigArrayAttribute()
    {
        return $this->template_config ? json_decode($this->template_config, true) : [];
    }

    /**
     * 设置模板配置
     */
    public function setTemplateConfigAttribute($value)
    {
        $this->attributes['template_config'] = is_array($value) ? json_encode($value) : $value;
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            0 => '禁用',
            1 => '启用',
            default => '未知'
        };
    }

    /**
     * 获取模板类型文本
     */
    public function getTemplateTypeTextAttribute()
    {
        return match($this->template_type) {
            self::TYPE_INDIVIDUAL => '个人报告',
            self::TYPE_CLASS => '班级报告',
            self::TYPE_GRADE => '年级报告',
            self::TYPE_SCHOOL => '学校报告',
            default => '未知'
        };
    }

    /**
     * 作用域：已启用
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：按评估类型
     */
    public function scopeBySurveyType($query, $surveyType)
    {
        return $query->where('survey_type', $surveyType);
    }

    /**
     * 作用域：按模板类型
     */
    public function scopeByTemplateType($query, $templateType)
    {
        return $query->where('template_type', $templateType);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort', 'desc')->orderBy('id', 'desc');
    }

    /**
     * 获取所有模板类型选项
     */
    public static function getTemplateTypeOptions(): array
    {
        return [
            self::TYPE_INDIVIDUAL => '个人报告',
            self::TYPE_CLASS => '班级报告',
            self::TYPE_GRADE => '年级报告',
            self::TYPE_SCHOOL => '学校报告'
        ];
    }
}
