<?php

namespace App\Http\Requests\School\System;

use App\Http\Requests\BaseRequest;

class CourseRequest extends BaseRequest
{

    public function rules(): array
    {
        $rules = [
            'school_campus_id' => 'required|integer',
            'grade_id' => 'required|integer'
        ];

        if ($this->has('course_name')) {
            $rules['course_name'] = 'required|string|max:255';
        }
        if ($this->has('courses')) {
            // 批量提交
            $rules['courses'] = 'required|array';
            $rules['courses.*.course_name'] = 'required|string|max:255';
            $rules['courses.*.type'] = 'required|string|max:255';
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'school_campus_id.integer' => 'school_campus_id 必须为整数',
            'grade_id.integer' => 'grade_id 必须为整数',
            'courses.array' => 'courses 必须为数组',
            'courses.*.course_name' => 'courses.*.course_name 必须为字符串',
            'courses.*.type' => 'courses.*.type 必须为字符串',
        ];
    }
}
