<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DataSync\DataSyncService;
use App\Models\School;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\Admin;
use Illuminate\Support\Facades\Log;

/**
 * 数据同步命令
 * 用于批量同步历史数据或手动触发同步
 */
class DataSyncCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'datasync:sync 
                            {type : 同步数据类型 (school|student|teacher|admin|all)}
                            {--id= : 指定同步的数据ID}
                            {--batch-size=100 : 批量处理大小}
                            {--force : 强制同步，忽略已同步的数据}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '数据同步命令 - 用于批量同步历史数据或手动触发同步';

    protected $dataSyncService;

    /**
     * Create a new command instance.
     */
    public function __construct(DataSyncService $dataSyncService)
    {
        parent::__construct();
        $this->dataSyncService = $dataSyncService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');
        $id = $this->option('id');
        $batchSize = (int) $this->option('batch-size');
        $force = $this->option('force');

        $this->info("开始数据同步 - 类型: {$type}");

        if ($id) {
            $this->syncSingleRecord($type, $id);
        } elseif ($type === 'all') {
            $this->syncAllData($batchSize, $force);
        } else {
            $this->syncByType($type, $batchSize, $force);
        }

        $this->info('数据同步完成');
    }

    /**
     * 同步单条记录
     */
    private function syncSingleRecord(string $type, int $id)
    {
        $this->info("同步单条记录 - 类型: {$type}, ID: {$id}");

        try {
            $model = $this->getModelByType($type);
            $record = $model::find($id);

            if (!$record) {
                $this->error("未找到ID为 {$id} 的 {$type} 记录");
                return;
            }

            $result = $this->syncRecord($type, $record->toArray());

            if ($result['success']) {
                $this->info("同步成功 - {$type} ID: {$id}");
            } else {
                $this->error("同步失败 - {$type} ID: {$id}, 错误: {$result['message']}");
            }
        } catch (\Exception $e) {
            $this->error("同步异常 - {$type} ID: {$id}, 错误: {$e->getMessage()}");
            Log::error('数据同步命令异常', [
                'type' => $type,
                'id' => $id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 同步所有类型的数据
     */
    private function syncAllData(int $batchSize, bool $force)
    {
        $types = ['school', 'student', 'teacher', 'admin'];

        foreach ($types as $type) {
            $this->info("开始同步 {$type} 数据");
            $this->syncByType($type, $batchSize, $force);
        }
    }

    /**
     * 按类型同步数据
     */
    private function syncByType(string $type, int $batchSize, bool $force)
    {
        try {
            $model = $this->getModelByType($type);
            $query = $model::query();

            // 如果不是强制同步，则排除已同步的数据
            if (!$force) {
                $query = $this->excludeSyncedRecords($query, $type);
            }

            $total = $query->count();
            $this->info("待同步 {$type} 记录总数: {$total}");

            if ($total === 0) {
                $this->info("没有需要同步的 {$type} 数据");
                return;
            }

            $bar = $this->output->createProgressBar($total);
            $bar->start();

            $successCount = 0;
            $failCount = 0;

            $query->chunk($batchSize, function ($records) use ($type, $bar, &$successCount, &$failCount) {
                foreach ($records as $record) {
                    try {
                        $data = $record->toArray();
                        
                        // 为学校类型添加额外数据
                        if ($type === 'school') {
                            $data = $this->addSchoolAdditionalData($record, $data);
                        }

                        $result = $this->syncRecord($type, $data);

                        if ($result['success']) {
                            $successCount++;
                        } else {
                            $failCount++;
                            Log::warning("数据同步失败", [
                                'type' => $type,
                                'id' => $record->id,
                                'error' => $result['message']
                            ]);
                        }
                    } catch (\Exception $e) {
                        $failCount++;
                        Log::error("数据同步异常", [
                            'type' => $type,
                            'id' => $record->id,
                            'error' => $e->getMessage()
                        ]);
                    }

                    $bar->advance();
                }
            });

            $bar->finish();
            $this->newLine();

            $this->info("同步完成 - {$type}");
            $this->info("成功: {$successCount}, 失败: {$failCount}");

        } catch (\Exception $e) {
            $this->error("同步 {$type} 数据时发生异常: {$e->getMessage()}");
            Log::error('数据同步命令异常', [
                'type' => $type,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 排除已同步的记录
     */
    private function excludeSyncedRecords($query, string $type)
    {
        // 这里可以实现逻辑来排除已同步的记录
        // 例如查询同步数据库中已存在的 original_id
        return $query;
    }

    /**
     * 为学校添加额外数据
     */
    private function addSchoolAdditionalData($school, array $data): array
    {
        // 添加校区数据
        $data['campuses'] = $school->campuses()->get()->toArray();
        
        // 添加默认管理员用户
        $data['admin_user'] = [
            'name' => $school->name . '管理员',
            'username' => 'admin_' . ($school->code ?: $school->id),
            'password' => bcrypt('123456'),
            'phone' => $school->phone ?: '',
            'email' => $school->email ?: ''
        ];

        return $data;
    }

    /**
     * 同步记录
     */
    private function syncRecord(string $type, array $data): array
    {
        return match($type) {
            'school' => $this->dataSyncService->syncSchool($data),
            'student' => $this->dataSyncService->syncStudent($data),
            'teacher' => $this->dataSyncService->syncTeacher($data),
            'admin' => $this->dataSyncService->syncAdmin($data),
            default => ['success' => false, 'message' => '不支持的数据类型']
        };
    }

    /**
     * 根据类型获取模型类
     */
    private function getModelByType(string $type): string
    {
        return match($type) {
            'school' => School::class,
            'student' => Student::class,
            'teacher' => Teacher::class,
            'admin' => Admin::class,
            default => throw new \InvalidArgumentException("不支持的数据类型: {$type}")
        };
    }
}
