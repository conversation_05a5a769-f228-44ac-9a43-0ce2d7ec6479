<?php

namespace App\Traits;

trait PasswordTrait
{

    public  function generatePassword($limit = 12)
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';

        $password = '';

        // 随机选择一个大写字母、一个小写字母和一个数字
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];

        // 生成剩余的随机字符
        $remainingLength = $limit - strlen($password);
        $characters = $uppercase . $lowercase . $numbers;
        for ($i = 0; $i < $remainingLength; $i++) {
            $password .= $characters[random_int(0, strlen($characters) - 1)];
        }

        // 打乱密码中字符的顺序
        $password = str_shuffle($password);

        return $password;
    }
}
