<?php

namespace App\Services\Admin;
use App\Services\BaseService;
use App\Models\Admin\AssessmentCareerHollandTemplate;
use App\Models\Admin\AssessmentCareerMbtiTemplate;
use App\Models\Admin\AssessmentCareerMiTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AssessmentTemplateService extends BaseService
{
    protected $hollandTemplate;
    protected $mbtiTemplate;
    protected $miTemplate;

    public function __construct(
        AssessmentCareerHollandTemplate $hollandTemplate,
        AssessmentCareerMbtiTemplate $mbtiTemplate,
        AssessmentCareerMiTemplate $miTemplate
    ) {
        $this->hollandTemplate = $hollandTemplate;
        $this->mbtiTemplate = $mbtiTemplate;
        $this->miTemplate = $miTemplate;
    }

    public function getTemplateTypes()
    {
        return [
            ['value' => 'holland', 'label' => '霍兰德职业兴趣测评'],
            ['value' => 'mbti', 'label' => 'MBTI职业性格测评'],
            ['value' => 'mi', 'label' => '多元智能']
        ];
    }

    public function getTemplateList(Request $request)
    {
        $template_type = $request->template_type;

        $search_keyword = $request->search_keyword;
        $gradeType = $request->input('grade_type');
        $query = null;

        if ($template_type === 'holland') {
            $query = $this->hollandTemplate->query();
            if ($search_keyword) {
                $query->where('code', $search_keyword);

            }
            if ($gradeType) {
                $query->where('grade_type', $gradeType);
            }

            // 对于 holland 类型，根据 code 分组，每种 code 只显示一条记录
            $page = $request->input('page', 1);
            $per_page = $request->input('page_size', 10);
            $skip = ($page - 1) * $per_page;

            // 先获取所有不重复的 code
            $distinctCodes = $query->distinct('code')->pluck('code');
            $cnt = $distinctCodes->count();

            // 分页获取 code
            $pagedCodes = $distinctCodes->slice($skip, $per_page);

            // 为每个 code 获取一条记录（按 id 降序取第一条）
            $list = collect();
            foreach ($pagedCodes as $code) {
                $template = $this->hollandTemplate->query()
                    ->where('code', $code);

                if ($gradeType) {
                    $template->where('grade_type', $gradeType);
                }

                $template = $template->orderBy('id', 'desc')->first();
                if ($template) {
                    $list->push($template);
                }
            }

            return compact('list', 'cnt');

        } else if ($template_type === 'mbti') {
            $query = $this->mbtiTemplate->query();
            $query->where('code', 'like', "%{$search_keyword}%");

        } 
        if ($gradeType) {
            $query->where('grade_type', $gradeType);
        }
        $page = $request->input('page', 1);
        $per_page = $request->input('page_size', 10);
        $skip = ($page - 1) * $per_page;

        $cnt = $query->count();
        $list = $query->skip($skip)
            ->take($per_page)
            ->orderBy('id', 'desc')
            ->get();

        return compact('list', 'cnt');
    }

    public function store(Request $request)
    {
        $template_type = $request->template_type;
        $requestBody = file_get_contents('php://input');
      //  $bodyData = json_decode($requestBody, true);
        $bodyData = json_decode($requestBody, true);

        switch($template_type) {
            case 'holland':
                $data = [
                    'code' => $bodyData['code'] ?? '',
                    'type' => $bodyData['type'] ?? '',
                    'grade_type' => $bodyData['grade_type'] ?? 2,
                    'content' => is_array($bodyData['content'] ?? '')
                        ? json_encode($bodyData['content'], JSON_UNESCAPED_UNICODE)
                        : ($bodyData['content'] ?? ''),
                    'appellation' => $bodyData['appellation'] ?? '',
                    'lowest_score' => $bodyData['lowest_score'] ?? 0,
                    'highest_score' => $bodyData['highest_score'] ?? 0,
                    'level' => $bodyData['level'] ?? ''
                ];

                return $this->hollandTemplate->create($data);

            case 'mbti':
                //exit;
                $data = [
                    'code' => $bodyData['code'] ?? '',
                    'appellation' => $bodyData['appellation'] ?? '',
                    'preface' => $bodyData['preface'] ?? '',
                    'interpret' => $bodyData['interpret'] ?? '',
                    'keywords' => json_encode($bodyData['keywords']) ?? '',
                    'superpowers' => json_encode($bodyData['superpowers']) ?? '',
                     'campus_roles' => json_encode($bodyData['campus_roles']) ?? '',
                    'subject_adaptation' => json_encode($bodyData['subject_adaptation']) ?? '',
                    'learning_strategy' => json_encode($bodyData['learning_strategy']) ?? '',
                    'major_analyse' => json_encode($bodyData['major_analyse']) ?? '',
                    'occupation_analyse' => json_encode($bodyData['occupation_analyse']) ?? '',
                    'social_comfort_level' => json_encode($bodyData['social_comfort_level']) ?? '',
                    'making_friends_analyse' => json_encode($bodyData['making_friends_analyse']) ?? '',
                    'communication_skill' => json_encode($bodyData['communication_skill']) ?? '',
                    'stress_analyse' => json_encode($bodyData['stress_analyse']) ?? '',
                    'growth_analyse' => json_encode($bodyData['growth_analyse']) ?? '',
                    'example_case' => json_encode($bodyData['example_case']) ?? '',
                    'summary' => $bodyData['summary'] ?? '',
                    'img_url' => $bodyData['img_url'] ?? ''
                ];

                return $this->mbtiTemplate->create($data);
        }
    }

    public function update(Request $request, $id)
    {
        $template_type = $request->template_type;
        $requestBody = file_get_contents('php://input');
        $bodyData = json_decode($requestBody, true);

        switch($template_type) {
            case 'holland':
                $data = [
                    'code' => $bodyData['code'] ?? '',
                    'type' => $bodyData['type'] ?? '',
                    'grade_type' => $bodyData['grade_type'] ?? 2,
                    'content' => is_array($bodyData['content'] ?? '')
                        ? json_encode($bodyData['content'], JSON_UNESCAPED_UNICODE)
                        : ($bodyData['content'] ?? ''),
                    'appellation' => $bodyData['appellation'] ?? '',
                    'lowest_score' => $bodyData['lowest_score'] ?? 0,
                    'highest_score' => $bodyData['highest_score'] ?? 0,
                    'level' => $bodyData['level'] ?? ''
                ];
                $template = $this->hollandTemplate->findOrFail($id);
                break;

            case 'mbti':
                $data = [
                    'code' => $bodyData['code'] ?? '',
                    'appellation' => $bodyData['appellation'] ?? '',
                    'preface' => $bodyData['preface'] ?? '',
                    'interpret' => $bodyData['interpret'] ?? '',
                    'keywords' => json_encode($bodyData['keywords']) ?? '',
                    'superpowers' => json_encode($bodyData['superpowers']) ?? '',
                    'campus_roles' => json_encode($bodyData['campus_roles']) ?? '',
                    'subject_adaptation' => json_encode($bodyData['subject_adaptation']) ?? '',
                    'learning_strategy' => json_encode($bodyData['learning_strategy']) ?? '',
                    'major_analyse' => json_encode($bodyData['major_analyse']) ?? '',
                    'occupation_analyse' => json_encode($bodyData['occupation_analyse']) ?? '',
                    'social_comfort_level' => json_encode($bodyData['social_comfort_level']) ?? '',
                    'making_friends_analyse' => json_encode($bodyData['making_friends_analyse']) ?? '',
                    'communication_skill' => json_encode($bodyData['communication_skill']) ?? '',
                    'stress_analyse' => json_encode($bodyData['stress_analyse']) ?? '',
                    'growth_analyse' => json_encode($bodyData['growth_analyse']) ?? '',
                    'example_case' => json_encode($bodyData['example_case']) ?? '',
                    'summary' => $bodyData['summary']?? '',
                    'img_url' => $bodyData['img_url'] ?? ''
                ];
                $template = $this->mbtiTemplate->findOrFail($id);
                break;

            case 'mi':
                // 处理优势智能和劣势智能参数
                $intelligenceType = '';
                if (isset($bodyData['strengths']) && isset($bodyData['needs_improvement'])) {
                    // 如果传递了优势和劣势智能，构建 JSON 格式
                    $intelligenceData = [
                        'strengths' => $bodyData['strengths'],
                        'intermediate' => $bodyData['intermediate'] ?? [],
                        'needs_improvement' => $bodyData['needs_improvement']
                    ];
                    $intelligenceType = json_encode($intelligenceData, JSON_UNESCAPED_UNICODE);
                } else {
                    // 否则使用原有的 intelligence_type 字段
                    $intelligenceType = is_array($bodyData['intelligence_type'] ?? '')
                        ? json_encode($bodyData['intelligence_type'], JSON_UNESCAPED_UNICODE)
                        : ($bodyData['intelligence_type'] ?? '');
                }

                $data = [
                    'intelligence_type' => $intelligenceType,
                    'type' => $bodyData['type'] ?? '',
                    'grade_type' => $bodyData['grade_type'] ?? 2,
                    'content' => is_array($bodyData['content'] ?? '')
                        ? json_encode($bodyData['content'], JSON_UNESCAPED_UNICODE)
                        : ($bodyData['content'] ?? ''),
                ];
                $template = $this->miTemplate->findOrFail($id);
                break;
        }

        return $template->update($data);
    }

    /**
     * 获取测评模板详情
     *
     * @param string $type 测评类型
     * @param int $id 模板ID
     * @return mixed 返回模板详情数据
     */
    public function getTemplateDetail($template_type, Request $request )
    {
        if ($template_type === 'holland') {
            $code=$request->code;
            $type=$request->type; 
            $gradeType=$request->grade_type; 
            if($request->type){
                    $query = $this->hollandTemplate->query();
                    if ($code) {
                        $query->where('code', $code);
                    }
                    if ($gradeType) {
                        $query->where('grade_type', $gradeType);
                    }
                    if ($type) {
                        $query->where('type', $type);
                    }
                $list = $query->firstOrFail();
                return  $list;     
            }else{
                $code=$request->code;
            $result = $this->hollandTemplate
            ->where(function ($query) use ($code) {
                $query->where('code', $code) // 精确匹配 code 字段
                    ->orWhere('code', substr($code, 0, 3)); // 或匹配三位字段
            })
            ->get();
            return  $result;
            } 
        } else if ($template_type === 'mbti') {
            $code=$request->code;
            return $this->mbtiTemplate->where('code', $code)->first();

        } else if ($template_type === 'mi') {
            // 处理优势智能和劣势智能参数
            $advantages = $request->input('advantages');
            $disadvantages = $request->input('disadvantages');

            if ($advantages && $disadvantages) {
                // 将字符串转换为数组
                $strengths = explode(',', $advantages);
                $weaknesses = explode(',', $disadvantages);

                // 去除空格
                $strengths = array_map('trim', $strengths);
                $weaknesses = array_map('trim', $weaknesses);

                $gradeType = $request->input('grade_type');

                $query = $this->miTemplate->query();

                $query->where(function($q) use ($strengths, $weaknesses) {
                    // 第一类：查询 type 为"智能维度分析"的所有记录
                    $q->orWhere('type', '智能维度分析');

                    // 第二类：intelligence_type 包含优势智能 且 type 等于"测评结果概览"
                    $q->orWhere(function($subQuery) use ($strengths) {
                        $subQuery->where('type', '测评结果概览');
                        // 必须同时包含所有优势智能，而不是任意一个
                        foreach ($strengths as $strength) {
                            $subQuery->where('intelligence_type', 'like', "%{$strength}%");
                        }
                    });

                    // 第三类：优势发展建议 - intelligence_type 精确匹配优势智能 且 type 包含"优势"字符
                    $q->orWhere(function($subQuery) use ($strengths) {
                        $subQuery->where('type', 'like', '%优势%');
                        // intelligence_type 精确匹配优势智能数组中的任意一个
                        $subQuery->whereIn('intelligence_type', $strengths);
                    });

                    // 第四类：intelligence_type 包含劣势智能 且 type 包含弱势相关字符
                    $q->orWhere(function($subQuery) use ($weaknesses) {
                        $subQuery->whereIn('intelligence_type', $weaknesses);
                        $subQuery->where('type', 'like', "%弱势%");
                    });

                });


                $firstFourResults = $query->orderBy('id', 'desc')->get();
                // 第五类：简单的两种排列（A,B 和 B,A）

                // 构建优势智能的两种排列
                $strengthArray1 = '["' . implode('","', $strengths) . '"]';
                $strengthArray2 = '["' . implode('","', array_reverse($strengths)) . '"]';

                // 构建劣势智能的两种排列
                $weaknessArray1 = '["' . implode('","', $weaknesses) . '"]';
                $weaknessArray2 = '["' . implode('","', array_reverse($weaknesses)) . '"]';

                // 构建完整的 SQL
                $fifthSql = "
                    SELECT * FROM (
                        SELECT * FROM assessment_career_mi_templates
                        WHERE intelligence_type LIKE '%\"strengths\":{$strengthArray1}%'
                           OR intelligence_type LIKE '%\"strengths\":{$strengthArray2}%'
                    ) AS first_filter
                    WHERE intelligence_type LIKE '%\"needs_improvement\":{$weaknessArray1}%'
                       OR intelligence_type LIKE '%\"needs_improvement\":{$weaknessArray2}%'
                ";



                $fifthResults = \DB::select($fifthSql);
                // 合并结果
                $allResults = $firstFourResults->toArray();
                foreach ($fifthResults as $result) {
                    $allResults[] = (array)$result; // 将 stdClass 转换为数组
                }

                // 去重并转换回集合
                $uniqueResults = collect($allResults)->unique('id')->values();

                return $uniqueResults;
            } else {
                // 原有的查询逻辑
                $code = $request->code;
                return $this->miTemplate->where('intelligence_type', $code)->first();
            }
        }
    }
    /**
     * 第五类：只使用嵌套子查询，不要其他条件
     */
    public function getTemplatesByIntelligenceOptimized($strengths, $weaknesses, $gradeType)
    {
        

        // 参数处理
      //  $advantagesList = $this->parseIntelligenceList($advantages);
      //  $disadvantagesList = $this->parseIntelligenceList($disadvantages);

        // 构建优势智能数组字符串
        $strengthsStr = '["' . implode('","', $strengths) . '"]';
        $weaknessesStr = '["' . implode('","', $weaknesses) . '"]';

        // 只使用这一个查询条件，其他都不要
        $sql = "
            SELECT * FROM (
                SELECT * FROM assessment_career_mi_templates
                WHERE intelligence_type LIKE ?
            ) AS first_filter
            WHERE intelligence_type LIKE ?
        ";

        $params = [
            "%\"strengths\":{$strengthsStr}%",
            "%\"needs_improvement\":{$weaknessesStr}%"
        ];

       
        $results = \DB::select($sql, $params);

        return collect($results);
    }


    /**
     * 生成数组的所有排列组合
     */
    private function generatePermutations($array)
    {
        if (count($array) <= 1) {
            return [$array];
        }

        $permutations = [];
        for ($i = 0; $i < count($array); $i++) {
            $current = $array[$i];
            $remaining = array_merge(array_slice($array, 0, $i), array_slice($array, $i + 1));
            $subPermutations = $this->generatePermutations($remaining);

            foreach ($subPermutations as $subPerm) {
                $permutations[] = array_merge([$current], $subPerm);
            }
        }

        return $permutations;
    }

}