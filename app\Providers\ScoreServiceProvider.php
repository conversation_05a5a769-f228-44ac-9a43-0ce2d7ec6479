<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Jobs\CalculateScoreJob;
use App\Services\School\Assessment\Score\ScoreServiceInterface;
use App\Services\School\Assessment\Score\ScoreServiceFactory;

class ScoreServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->when(CalculateScoreJob::class)
            ->needs(ScoreServiceInterface::class)
            ->give(function ($app, $parameters) {
                // 直接从队列任务参数获取
                $assessmentId = $parameters['assessment_id'] ?? null;
                $service = ScoreServiceFactory::create($assessmentId);
                
                if (!$service) {
                    throw new \RuntimeException("测评服务不存在 [assessment_id: {$assessmentId}]");
                }
                return $service;
            });
    }
}