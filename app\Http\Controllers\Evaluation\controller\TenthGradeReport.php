<?php
namespace app\evaluation\controller;

use app\login\controller\ApiAuth;

/**
 * Class Questions
 * @package app\evaluation\controller
 */
class TenthGradeReport extends ApiAuth
{
    private $Report;
    public function __construct()
    {
        parent::__construct();
        $this->Report = new \app\evaluation\service\TenthGradeReport();
    }
    /**
     * 模块：素养测评-个体报告
     * @SWG\Get(path="/evaluation/tenth_grade_report",
     *   tags={"素养测评-个体报告:report"},
     *   summary="十年级测评个体报告",
     *  @SWG\Parameter(
     *      in="query",
     *      name="distribution_id",
     *      type="integer",
     *      description="分发id",
     *      required=true,
     *    ),
     *  @SWG\Parameter(
     *      in="query",
     *      name="member_id",
     *      type="integer",
     *      description="学生member_id",
     *      required=false,
     *    ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    public function personal_report(){
        $data = $this->Report->personal_report();
        apiReturn($data);
    }

}