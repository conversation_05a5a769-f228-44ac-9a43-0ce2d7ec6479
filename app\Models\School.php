<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\DataSyncTrait;

/**
 * 学校模型 - 示例如何使用数据同步功能
 */
class School extends Model
{
    use DataSyncTrait;

    protected $table = 'schools';

    protected $fillable = [
        'name',
        'code',
        'type',
        'level',
        'address',
        'phone',
        'email',
        'website',
        'description',
        'status'
    ];

    protected $casts = [
        'type' => 'integer',
        'level' => 'integer',
        'status' => 'integer'
    ];

    // 数据同步配置
    protected $syncEnabled = true;
    protected $syncType = 'school';

    // 字段映射配置（如果同步数据库的字段名不同）
    protected $syncFieldMapping = [
        // 'original_field' => 'sync_field'
    ];

    /**
     * 关联校区
     */
    public function campuses(): HasMany
    {
        return $this->hasMany(Campus::class);
    }

    /**
     * 关联学生
     */
    public function students(): HasMany
    {
        return $this->hasMany(Student::class);
    }

    /**
     * 关联教师
     */
    public function teachers(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Teacher::class);
    }

    /**
     * 关联管理员
     */
    public function admins(): HasMany
    {
        return $this->hasMany(Admin::class);
    }

    /**
     * 获取额外的同步数据
     * 重写父类方法，添加学校特有的同步数据
     */
    protected function getAdditionalSyncData(): array
    {
        return [
            'campuses' => $this->campuses()->get()->toArray(),
            'admin_user' => $this->getDefaultAdminUser()
        ];
    }

    /**
     * 获取默认管理员用户信息
     */
    private function getDefaultAdminUser(): array
    {
        // 这里可以根据实际需求获取或创建默认管理员用户
        return [
            'name' => $this->name . '管理员',
            'username' => 'admin_' . $this->code,
            'password' => bcrypt('123456'), // 默认密码
            'phone' => $this->phone,
            'email' => $this->email
        ];
    }

    /**
     * 状态常量
     */
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    const STATUS_SUSPENDED = 2;

    /**
     * 类型常量
     */
    const TYPE_PRIMARY = 1;      // 小学
    const TYPE_MIDDLE = 2;       // 初中
    const TYPE_HIGH = 3;         // 高中
    const TYPE_VOCATIONAL = 4;   // 职业学校
    const TYPE_UNIVERSITY = 5;   // 大学

    /**
     * 等级常量
     */
    const LEVEL_MUNICIPAL = 1;   // 市级
    const LEVEL_PROVINCIAL = 2;  // 省级
    const LEVEL_NATIONAL = 3;    // 国家级

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            self::STATUS_INACTIVE => '未激活',
            self::STATUS_ACTIVE => '正常',
            self::STATUS_SUSPENDED => '暂停',
            default => '未知'
        };
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttribute(): string
    {
        return match($this->type) {
            self::TYPE_PRIMARY => '小学',
            self::TYPE_MIDDLE => '初中',
            self::TYPE_HIGH => '高中',
            self::TYPE_VOCATIONAL => '职业学校',
            self::TYPE_UNIVERSITY => '大学',
            default => '未知'
        };
    }

    /**
     * 获取等级文本
     */
    public function getLevelTextAttribute(): string
    {
        return match($this->level) {
            self::LEVEL_MUNICIPAL => '市级',
            self::LEVEL_PROVINCIAL => '省级',
            self::LEVEL_NATIONAL => '国家级',
            default => '未知'
        };
    }

    /**
     * 作用域：按状态
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按类型
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 作用域：按等级
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * 作用域：活跃的学校
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：搜索
     */
    public function scopeSearch($query, $keyword)
    {
        return $query->where(function($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
              ->orWhere('code', 'like', "%{$keyword}%")
              ->orWhere('address', 'like', "%{$keyword}%");
        });
    }
}
