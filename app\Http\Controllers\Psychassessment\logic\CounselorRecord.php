<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */
namespace app\psychassessment\logic;
use app\psychassessment\model\CounselorRecord as CounselorRecordModel;
use app\psychassessment\model\AppointmentRecord as AppointmentRecordModel;
use app\psychassessment\model\Focus as FocusModel;
use think\Loader;

class CounselorRecord
{
    protected $RecordModel;
    protected $AppModel;
    protected $user;

    public function __construct()
    {
        $this->RecordModel = new CounselorRecordModel();
        $this->AppModel = new AppointmentRecordModel();
        $this->FocusModel = new FocusModel();
        $this->user = get_user();
    }

    public function add()
    {
        //权限判断
        if($this->user['role_source_id'] != 2){
            $is_psych = db('teacher')->where(['member_id'=>$this->user['id'],'step'=>0])->value('is_psych');
            if(!is_numeric($is_psych) || $is_psych != 1) apiReturn([],'您不是心理老师，无权进行此操作',-5);
        }
        $data = input('post.');
        $data['school_id'] = $this->user['school_id'];
        //学生student_id根据学生member_id来查
        $student_id = db('student')->field('id')->where(['member_id'=>$data['member_id'],'step'=>0])->order('id desc')->find();
        $data['student_id'] = $student_id['id'];
        //老师的teacher_id根据当前登录人id来查
        $data['teacher_id'] = db('teacher')->where(['member_id'=>$this->user['id'],'step'=>0])->value('id');
        $times = $this->RecordModel->where(['member_id'=>$data['member_id'],'teacher_id'=>$data['teacher_id'],'status'=>0])->count();
        $data['times'] = $times + 1;
        $data['office_hours'] = strtotime($data['end_time']) - strtotime($data['start_time']);
        $this->RecordModel->allowField(true)->save($data);
        $id=$this->RecordModel->id;
        //修改预约状态，0未辅导1已辅导
        $this->AppModel->save(['appointment_status' =>'1'], ['id' => $data['student_appointment_id']]);
        //如果是加入重点关注就要录入focus表
        if(isset($data['care_level']) && $data['care_level'] == 1){
            $student_info = db('student')->field('id,grade_id,class_id,member_id')->where('id',$student_id['id'])->find();
            $ins = [
                'student_id'=>$student_id['id'],
                'school_id'=>$this->user['school_id'],
                'grade_id'=>$student_info['grade_id'],
                'class_id'=>$student_info['class_id'],
                'member_id'=>$student_info['member_id'],
                'reason'=>$data['reason'],
                'teacher_member_id'=>$this->user['id'],
            ];
            $this->FocusModel->allowField(true)->save($ins);
        }
        apiReturn($id);
    }

    public function edit(){
        //权限判断
        $data = input('put.');
        $data['office_hours'] = strtotime($data['end_time']) - strtotime($data['start_time']);
        $res = $this->RecordModel->allowField(true)->save($data, ['id' => input('id')]);
        apiReturn($res);
    }

    public function del()
    {
        $this->RecordModel->save([
            'status' => '-1',
        ], ['id' => input('id')]);
        //软删除
        apiReturn(input('id'));
    }

    public function get_list()
    {
        $grade_id   = input('grade_id');
        $class_id   = input('class_id');
        $name       = input('student_name');
        $startDate  = input('start_date'); // 开始日期
        $endDate    = input('end_date'); // 结束日期
        $pageNumber = input('page', 1); // 获取页码，默认为1
        $pageSize   = input('pagesize', 10); // 获取每页显示的记录数，默认为10
        $member_id  = input('member_id'); // 点击查看辅导档案，显示该学生所有的辅导记录

        if($member_id){
            $where['a.member_id']  = $member_id;
        }else{
            switch ($this->user['role_source_id']){
                case 1:
                    $where['a.member_id']  = $this->user['id'];
                    break;
                case 3:
                    $where['a.teacher_id'] = db('teacher')->where(['member_id'=>$this->user['id'],'step'=>0])->value('id');
                    break;
            }
            if($grade_id) $where['student.grade_id'] = ['in',$grade_id];
            if($class_id) $where['student.class_id'] = ['in',$class_id];
            if($name) $where['student.name|student.student_no'] = ['like','%'.$name.'%'];
            if($startDate && $endDate) $where['a.start_time'] = ['between',[$startDate.' 00:00:00',$endDate.' 23:59:59']];
        }
        $where['a.school_id'] = $this->user['school_id'];
        $where['a.status'] = 0;
        $list = $this->RecordModel->alias('a')
            ->join('student student','a.student_id = student.id')
            ->join('grade grade','student.grade_id = grade.id')
            ->join('class class','student.class_id = class.id')
            ->join('teacher teacher','a.teacher_id = teacher.id')
            ->field('a.id,a.member_id,a.title,a.care_level,a.reason,a.times,a.office_hours,a.start_time,a.end_time,a.advice,a.content,a.annex,grade.name as grade_year,grade.grade_name,class.name as class_name,student.student_no,student.name as student_name,teacher.name as teacher_name')
            ->where($where)
            ->order('a.start_time desc')
            ->select();
        $list = to_arr($list);

        if($member_id){
            $focus = $this->FocusModel->alias('focus')
                ->join('teacher teacher','focus.teacher_member_id = teacher.member_id')
                ->field('focus.id,focus.reason,focus.status,focus.remove_reason,focus.created_at,focus.updated_at,teacher.name as teacher_name')
                ->where('focus.member_id',$member_id)
                ->order('focus.id desc')
                ->select();
            $focus = to_arr($focus);
            foreach ($focus as $key => $value){
                $focus_record[] = [
                    'id' => $value['id'],
                    'label' => '移入',
                    'reason' => $value['reason'],
                    'start_time' => $value['created_at'],
                    'teacher_name' => $value['teacher_name'],
                ];
                if($value['status'] == -1){
                    $focus_record[] = [
                        'id' => $value['id'],
                        'label' => '移出',
                        'reason' => $value['remove_reason'],
                        'start_time' => $value['updated_at'],
                        'teacher_name' => $value['teacher_name'],
                    ];
                }
            }
            $data = array_merge($list,$focus_record);
            array_multisort(array_column($data,'start_time'),SORT_DESC,$data);
        }else{
            $data = pageing($list,$pageSize,$pageNumber);
        }
        return $data;

    }

    public function get_appointment_student_list()
    {
        $name       = input('name'); // 学生姓名
        $pageNumber = input('page', 1); // 获取页码，默认为1
        $pageSize   = input('pagesize', 10); // 获取每页显示的记录数，默认为10

        $where['teacher.member_id'] = $this->user['id'];
        $where['teacher.school_id'] = $this->user['school_id'];
        if($name) $where['student.name'] = ['like','%'.$name.'%'];
        $list = db('teacher teacher')
            ->join('student student','find_in_set(student.class_id,teacher.class_ids)')
            ->join('grade grade','student.grade_id = grade.id')
            ->join('class class','student.class_id = class.id')
            ->field('grade.name as grade_year,grade.grade_name,class.name as class_name,student.id as student_id,student.member_id,student.student_no,student.name as student_name,teacher.name as teacher_name')
            ->where($where)
            ->group('student.member_id')
            ->select();
        $list = to_arr($list);
        return pageing($list,$pageSize,$pageNumber);
    }

    public function down_counselor_record()
    {

        $ids   = input('ids'); // 获取每页显示的记录数，默认为10
        $where['a.status'] = 0;
        $where['a.school_id'] = $this->user['school_id'];
        $where['a.id'] = ['in',$ids];
        $result = $this->RecordModel->alias('a')
            ->join('student student','a.student_id = student.id')
            ->join('grade grade','student.grade_id = grade.id')
            ->join('class class','student.class_id = class.id')
            ->join('teacher teacher','a.teacher_id = teacher.id')
            ->field('a.id,a.title,case a.care_level when 0 then "无需关注" when 1 then "重点关注" end as care_level,a.office_hours,a.start_time,a.end_time,a.advice,a.content,a.annex,grade.name as grade_year,grade.grade_name,class.name as class_name,student.student_no,student.name as student_name,teacher.name as teacher_name')
            ->where($where)
            ->select();
        $result = to_arr($result);

        if(empty($result))apiReturn([],'查询数据不存在');
        Loader::import("phpexcel/Classes/PHPExcel",VENDOR_PATH);
        $objPHPExcel  = new \PHPExcel();

        $filename = '心理辅导记录';

        $objPHPExcel->getActiveSheet()->mergeCells('A1:M1');
        $objPHPExcel->setActiveSheetIndex(0)->setCellValue('A1',$filename);
        $objPHPExcel->setActiveSheetIndex(0)
            ->setCellValue('A2','序号')
            ->setCellValue('B2','姓名')
            ->setCellValue('C2','学号')
            ->setCellValue('D2','年级')
            ->setCellValue('E2','班级')
            ->setCellValue('F2','主题')
            ->setCellValue('G2','辅导老师')
            ->setCellValue('H2','开始时间')
            ->setCellValue('I2','结束时间')
            ->setCellValue('J2','辅导时长')
            ->setCellValue('K2','辅导内容')
            ->setCellValue('L2','辅导建议')
            ->setCellValue('M2','关注等级');

        foreach ($result as $key => $val)
        {
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('A'.($key+3),($key+1));
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('B'.($key+3),$val['student_name']);
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('C'.($key+3),$val['student_no']);
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('D'.($key+3),$val['grade_year'].$val['grade_name']);
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('E'.($key+3),$val['class_name']);
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('F'.($key+3),$val['title']);
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('G'.($key+3),$val['teacher_name']);
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('H'.($key+3),$val['start_time']);
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('I'.($key+3),$val['end_time']);
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('J'.($key+3),$val['office_hours']);
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('K'.($key+3),$val['content']);
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('L'.($key+3),$val['advice']);
            $objPHPExcel->setActiveSheetIndex(0)->setCellValue('M'.($key+3),$val['care_level']);
        }
        //发送标题强制用户下载文件
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, "Excel2007");
        ob_end_clean();//这一步非常关键，用来清除缓冲区防止导出的excel乱码
        // 下载这个表格，在浏览器输出
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-excel;charset=utf-8");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");;
        header('Content-Disposition:attachment;filename='.$filename.'.xlsx');
        header("Content-Transfer-Encoding:binary");
        $objWriter->save('php://output');
        exit();
    }

    public function counselor_head_info(){
        $where['a.status'] = 0;
        $where['a.school_id'] = $this->user['school_id'];
        $where['teacher.member_id'] = $this->user['id'];
        $list = $this->RecordModel->alias('a')
            ->join('teacher teacher','a.teacher_id = teacher.id')
            ->field('a.id,a.member_id,a.office_hours,a.start_time')
            ->where($where)
            ->select();
        $list = to_arr($list);

        $member_num = count(array_unique(array_column($list,'member_id')));
        $duration_all = round(array_sum(array_column($list,'office_hours'))/3600,1);
        $current_date = date('Y-m'); // 获取当前年份和月份
        $duration_month = 0;
        foreach ($list as $key => $value){
            if ($current_date == substr($value['start_time'],0,7)) {
                $duration_month += $value['office_hours'];
            }
        }
        $data = [
            'member_num' => $member_num,
            'duration_all' => $duration_all,
            'duration_month' => round($duration_month/3600,1),
        ];

        return $data;
    }

    public function counselor_wait()
    {
        $pageNumber          = input('page', 1); // 获取页码，默认为1
        $pageSize            = input('pagesize', 10); // 获取每页显示的记录数，默认为10
        $name                = input('name'); // 学生姓名

        $where['a.status'] = 0;
        $where['a.school_id'] = $this->user['school_id'];
        $where['teacher.member_id'] = $this->user['id'];
        if($name) $where['student.name'] = ['like', '%' . $name . '%'];

        $list = $this->AppModel->alias('a')
            ->join('teacher teacher','a.teacher_id = teacher.id','left')
            ->join('student student','a.student_id = student.id','left')
            ->join('grade grade','student.grade_id = grade.id','left')
            ->join('class class','student.class_id = class.id','left')
            ->field('a.member_id,grade.name as grade_year,grade.grade_name,class.name as class_name,student.student_no,student.name as student_name')
            ->where($where)
            ->group('a.member_id')
            ->select();
        $member_ids = array_column($list,'member_id');
        $counselor_records = $this->RecordModel->where(['member_id'=>['in',$member_ids]])->column('member_id');
        //如果已经辅导了就给干掉
        foreach ($list as $key => $value){
            if(in_array($value['member_id'],$counselor_records)) unset($list[$key]);
        }
        return pageing($list,$pageSize,$pageNumber);
    }

}