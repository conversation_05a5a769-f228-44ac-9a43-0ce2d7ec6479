<?php

namespace App\Repositories;

use App\Models\School\Assessment\Question\AssessmentCareerQuestion;
use App\Models\School\Assessment\Question\AssessmentCapabilityQuestion;
use App\Models\School\Assessment\Question\AssessmentCompetencyQuestion;
use App\Models\School\Assessment\Question\AssessmentPsychologyQuestion;
use App\Models\School\Assessment\Question\AssessmentSubjectQuestion;
use App\Models\School\Assessment\Assessment;

class AssessmentRepository
{
    public function getCareerDimensions($assessmentId)
    {
        $dimensions = AssessmentCareerQuestion::where('assessment_id', $assessmentId)
            ->whereNotNull('dimension_name')
            ->pluck('dimension_name')
            ->toArray();
        return $dimensions;
    }

    public function getCapabilityDimensions($assessmentId)
    {
        $dimensions = AssessmentCapabilityQuestion::where('assessment_id', $assessmentId)
            ->whereNotNull('dimension_name')
            ->pluck('dimension_name')
            ->toArray();
        return $dimensions;
    }

    public function getCompetencyDimensions($assessmentId)
    {
        $dimensions = AssessmentCompetencyQuestion::where('assessment_id', $assessmentId)
            ->whereNotNull('dimension_name')
            ->pluck('dimension_name')
            ->toArray();
        return $dimensions;
    }

    public function getPsychologyDimensions($assessmentId)
    {
        $dimensions = AssessmentPsychologyQuestion::where('assessment_id', $assessmentId)
            ->whereNotNull('dimension_name')
            ->pluck('dimension_name')
            ->toArray();
        return $dimensions;
    }

    public function getSubjectDimensions($assessmentId)
    {
        $dimensions = AssessmentSubjectQuestion::where('assessment_id', $assessmentId)
            ->whereNotNull('dimension_name')
            ->pluck('dimension_name')
            ->toArray();
        return $dimensions;
    }
    
    public function getAssessmentName($assessmentId)
    {
        $assessmentName = Assessment::where('id', $assessmentId)->value('name');
        return $assessmentName;
    }

    /**
     * 获取测评数据
     *
     * @param array $assessmentIds 测评ID列表
     * @param int|null $type 测评类型，可为空
     * @param string $platform 平台类型，teacher表示教务端，student表示学生端
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAssessments(array $assessmentIds, ?int $type, string $platform = '')
    {
        $query = Assessment::whereIn('id', $assessmentIds)
            ->select(['id', 'name', 'category_code', 'official_name', 'icon', 'introduction_pc']);
            
        if ($platform === 'teacher') {
            // 教务端：如果传递了type，则按type过滤
            if (!is_null($type)) {
                $query->where('type', $type);
            }
        } else {
            // 其他端（例如学生端）：如果type不为null，则按type过滤 (此处保持原有逻辑，学生端type一般不会为null)
            if (!is_null($type)) {
                 $query->where('type', $type);
            }
        }
        
        $assessments = $query->orderBy('assessment_sort')->get();

        if ($assessments->isEmpty()) return [];

        return $assessments;
    }
}