<?php

namespace App\Models\Psychassessment;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 心理评估测评计划参与学生模型
 */
class PsychassessmentSurveyMember extends Model
{
    protected $table = 'psychassessment_survey_member';

    protected $fillable = [
        'survey_id',
        'member_id',
        'status',
        'completed_at',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'survey_id' => 'integer',
        'member_id' => 'integer',
        'status' => 'integer',
        'completed_at' => 'datetime'
    ];

    // 状态常量
    const STATUS_PENDING = 0;    // 待完成
    const STATUS_COMPLETED = 1;  // 已完成
    const STATUS_EXPIRED = 2;    // 已过期

    /**
     * 关联测评计划
     */
    public function survey(): BelongsTo
    {
        return $this->belongsTo(PsychassessmentSurvey::class, 'survey_id');
    }

    /**
     * 关联学生
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Member::class, 'member_id');
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            self::STATUS_PENDING => '待完成',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_EXPIRED => '已过期',
            default => '未知'
        };
    }

    /**
     * 作用域：按测评计划
     */
    public function scopeBySurvey($query, $surveyId)
    {
        return $query->where('survey_id', $surveyId);
    }

    /**
     * 作用域：按学生
     */
    public function scopeByMember($query, $memberId)
    {
        return $query->where('member_id', $memberId);
    }

    /**
     * 作用域：按状态
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：已完成
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * 作用域：待完成
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }
}
