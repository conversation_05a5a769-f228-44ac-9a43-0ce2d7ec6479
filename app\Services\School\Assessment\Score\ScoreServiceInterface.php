<?php

namespace App\Services\School\Assessment\Score;

/**
 * 评分服务接口
 * 
 * 定义了评分服务的基本方法
 */
interface ScoreServiceInterface
{
    /**
     * 计算分数
     * @param array $answers
     * @param array $questions
     * @return array{answers: array, total_score: int, lie_score: int}
     */
    public function calculateScores(array $params): array;

    /**
     * 计算评估结果
     * @param array $data 包含 answers, questions, assessment_id
     * @param mixed ...$args 额外参数
     * @return array
     */
    public function calculate(array $params): array;
}