<?php

namespace App\Repositories;

use App\Models\School\Assessment\AssessmentSchedule;
use App\Models\School\Assessment\AssessmentTaskAssignment;
use App\Models\School\Assessment\AssessmentTask;
use App\Models\School\Assessment\Assessment;
use Illuminate\Database\Eloquent\Collection;
use App\Models\School\System\Teacher;

class ScheduleRepository
{
    /**
     * 获取计划列表
     * 根据角色类型判断可查看的测评计划
     * 如果是老师角色，则只返回包含其可查看班级学生的测评计划
     *
     * @param int $schoolId 学校ID
     * @param Teacher|null $teacher 当前登录的教师用户
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getSchedules($type, $schoolId, ?Teacher $teacher = null)
    {
        // 基础查询：获取指定学校的测评计划
        $query = AssessmentSchedule::where('school_id', $schoolId)
            ->where('type', $type);
            
        // 使用with并添加排序条件
        $query->with([
            'tasks' => function($query) {
                $query->select('assessment_tasks.id', 'assessment_schedule_id', 'assessment_id')
                      ->join('assessments', 'assessment_tasks.assessment_id', '=', 'assessments.id')
                      ->orderBy('assessments.assessment_sort')
                      ->orderBy('assessment_tasks.id');
            },
            'tasks.assessment:id,name'
        ]);
        
        // 如果是教师用户，需要进行权限过滤
        if ($teacher) {
            $query->whereHas('tasks.assignments.studentClass', function($query) use ($teacher) {
                $query->whereIn('student_classes.class_id', function($subQuery) use ($teacher) {
                    $subQuery->select('class_id')
                        ->from('teacher_view_classes')
                        ->where('teacher_id', $teacher->id)
                        ->whereRaw('teacher_view_classes.school_year = student_classes.school_year');
                });
            });
        }
        
        // 选择需要的字段
        $query->select([
            'assessment_schedules.id',
            'assessment_schedules.name',
            'assessment_schedules.open_time',
            'assessment_schedules.close_time',
            'assessment_schedules.school_id',
            'assessment_schedules.school_year',
        ]);
        
        $query->orderBy('assessment_schedules.id', 'desc');

        return $query;
    }


    /**
     * 获取计划下的所有任务及其关联的测评
     *
     * @param int $scheduleId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getTasksWithAssessment(int $scheduleId): Collection
    {
        return AssessmentTask::where('assessment_schedule_id', $scheduleId)
            ->with('assessment')
            ->orderBy('assessment_id')
            ->get();
    }

    /**
     * 获取任务下的所有学生分配及其关联信息
     *
     * @param int $taskId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getStudentAssignments(int $taskId): Collection
    {
        return AssessmentTaskAssignment::where('assessment_task_id', $taskId)
            ->with(['student:id,student_name', 'studentClass:id,class_name'])
            ->get();
    }
    
    /**
     * 获取计划未完成学生名单
     *
     * @param int $scheduleId 计划ID
     * @param array $filters 过滤条件
     * @return \Illuminate\Database\Eloquent\Builder
     */
    /**
     * 获取任务分配记录
     *
     * @param array $taskIds 任务ID数组
     * @param array $filters 过滤条件
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getTaskAssignments(array $taskIds, array $filters = [])
    {
        // 使用with预加载关联数据
        $query = AssessmentTaskAssignment::with([
            'student:id,student_no,student_name,gender,date_start',
            'studentClass:id,class_id,school_year',
            'studentClass.claass:id,class_name,grade_id',
            'studentClass.claass.grade:id,grade_name'
        ])
        ->whereHas('student')
        ->whereHas('studentClass.claass.grade')
        ->whereIn('assessment_task_id', $taskIds);
        
        // 应用过滤条件
        $this->applyFiltersToAssignments($query, $filters);
        
        // 获取所有分配记录
        return $query->get();
    }
    
    /**
     * 应用过滤条件到分配查询
     *
     * @param \Illuminate\Database\Eloquent\Builder $query 查询构建器
     * @param array $filters 过滤条件
     * @return void
     */
    private function applyFiltersToAssignments($query, array $filters): void
    {
        // 关键词搜索
        if (!empty($filters['keyword'])) {
            $keyword = $filters['keyword'];
            $query->whereHas('student', function($q) use ($keyword) {
                $q->where('student_name', 'like', "%{$keyword}%")
                  ->orWhere('student_no', 'like', "%{$keyword}%");
            });
        }
        
        // 班级筛选
        if (!empty($filters['class_id'])) {
            $query->whereHas('studentClass', function($q) use ($filters) {
                $q->where('class_id', $filters['class_id']);
            });
        }
    }
    
    /**
     * 获取计划下的所有任务
     *
     * @param int $scheduleId 计划ID
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getScheduleTasks(int $scheduleId): Collection
    {
        return AssessmentTask::where('assessment_schedule_id', $scheduleId)
            ->with(['assessment:id,name,category_code,official_name'])
            ->get();
    }
    
    /**
     * 构建安全的列名
     *
     * @param \App\Models\School\Assessment\AssessmentTask $task 任务对象
     * @return string
     */
    public function buildSafeColumnName($task): string
    {
        $officialName = $task->assessment->official_name;
        
        return !empty($officialName) ? 
            preg_replace('/[^a-zA-Z0-9_]/', '_', $officialName) : 
            'assessment_' . $task->id . '_status';
    }
    
    /**
     * 获取学生的测评计划列表
     * 
     * @param int $studentId 学生ID
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getStudentSchedules(int $studentId, int $schoolId, int $type)
    {
        // 查询分配给该学生的所有计划,索引建在school_id上
        $query = AssessmentSchedule::select(
                'assessment_schedules.id',
                'assessment_schedules.name',
                'assessment_schedules.type',
                'assessment_schedules.open_time',
                'assessment_schedules.close_time'
            )
            ->join('assessment_tasks', 'assessment_schedules.id', '=', 'assessment_tasks.assessment_schedule_id')
            ->join('assessment_task_assignments', 'assessment_tasks.id', '=', 'assessment_task_assignments.assessment_task_id')
            ->where('assessment_task_assignments.student_id', $studentId)
            ->where('assessment_task_assignments.school_id', $schoolId)
            ->where('assessment_schedules.type', $type)
            ->groupBy(
                'assessment_schedules.id',
                'assessment_schedules.name',
                'assessment_schedules.type',
                'assessment_schedules.open_time',
                'assessment_schedules.close_time'
            );
        
        $query->orderBy('assessment_schedules.id', 'desc');
        
        return $query;
    }

    /**
     * 获取学生的测评计划下的任务列表
     * 
     * @param int $studentId 学生ID
     * @param int $schoolId 学校ID
     * @param int $scheduleId 计划ID
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getStudentTasks(int $studentId, int $schoolId, int $scheduleId)
    {
        $data = AssessmentTask::select(
                'assessments.id as assessment_id',
                'assessments.name',
                'assessments.icon',
                'assessments.category_code as question_type',
                'assessments.introduction_pc',
                'assessment_task_assignments.id as assessment_task_assignment_id',
                'assessment_task_assignments.status',
                'assessment_task_assignments.pdf_url',
            )
            ->join('assessments', 'assessment_tasks.assessment_id', '=', 'assessments.id')
            ->join('assessment_task_assignments', 'assessment_tasks.id', '=', 'assessment_task_assignments.assessment_task_id')
            ->where('assessment_tasks.assessment_schedule_id', $scheduleId)
            ->where('assessment_task_assignments.student_id', $studentId)
            ->where('assessment_task_assignments.school_id', $schoolId)
            ->orderBy('assessment_tasks.assessment_id');
            
        return $data;
    }
    
    /**
     * 获取开始测评信息
     * 
     * @param int $assessmentId 学生ID
     * @return array
     */
    public function getAssessmentInfo(int $assessmentId): array
    {
        $data = Assessment::where('id', $assessmentId)
            ->select('id','name','icon','prompt','note')
            ->first()
            ->toArray();
        return $data;
    }

    /**
     * 获取计划关联的班级列表
     *
     * @param int $scheduleId 计划ID
     * @return \Illuminate\Support\Collection
     */
    public function getScheduleClasses(int $scheduleId)
    {
        // 获取计划下的所有任务ID
        $taskIds = AssessmentTask::where('assessment_schedule_id', $scheduleId)
            ->pluck('id')
            ->toArray();
        
        if (empty($taskIds)) return collect();
        
        // 使用with预加载关联数据
        $assignments = AssessmentTaskAssignment::with([
                'studentClass:id,class_id,school_year,student_id',
                'studentClass.claass:id,class_name,grade_id',
                'studentClass.claass.grade:id,grade_name'
            ])
            ->whereIn('assessment_task_id', $taskIds)
            ->whereHas('studentClass.claass.grade')
            ->get();
        
        // 按班级ID分组，提取唯一的班级信息
        $classes = collect();
        $processedClassIds = [];
        
        foreach ($assignments as $assignment) {
            $studentClass = $assignment->studentClass;
            if (!$studentClass) continue;
            
            $class = $studentClass->claass;
            if (!$class) continue;
            
            $grade = $class->grade;
            if (!$grade) continue;
            
            // 如果已经处理过这个班级，则跳过
            if (in_array($class->id, $processedClassIds)) {
                continue;
            }
            
            $processedClassIds[] = $class->id;
            
            // 添加到结果集
            $classes->push((object)[
                'id' => $class->id,
                'school_year' => $studentClass->school_year,
                'grade_name' => $grade->grade_name,
                'class_name' => $class->class_name
            ]);
        }
        
        return $classes;
    }

    /**
     * 获取计划信息
     *
     * @param int $schedule_id 计划ID
     * @return string 计划名称
     */
    public function getScheduleInfo(int $schedule_id, string $field): array
    {
        return AssessmentSchedule::where('id', $schedule_id)
            ->select($field)
            ->first()
            ->toArray();
    }
}