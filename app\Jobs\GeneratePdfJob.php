<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\School\Assessment\AssessmentTaskAssignment;
use Illuminate\Support\Facades\Log;
use Throwable;
use App\Services\CreatePdfFileService;

class GeneratePdfJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务尝试次数
     *
     * @var int
     */
    public $tries = 3;

    /**
     * 任务参数
     *
     * @var array
     */
    protected $params;

    /**
     * Create a new job instance.
     *
     * @param array $params
     * @return void
     */
    public function __construct(array $params)
    {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            Log::info('开始生成pdf', ['params' => $this->params]);

            $assessment_id = $this->params['assessment_id'];

            # 1. 生成pdf
            $pdf_url = $this->generatePdf($assessment_id, $this->params['assessment_task_assignment_id']);
            
            # 2. 更新pdf_url
            $this->updateAssignmentPdfUrl($this->params['assessment_task_assignment_id'],  $pdf_url);

            Log::info('pdf已生成', [
                'pdf_url' => $pdf_url
            ]);
        } catch (\Exception $e) {
            throw new \Exception("pdf生成失败", 500, $e);
        }
    }
    
    private function updateAssignmentPdfUrl(int $assignment_id, string $pdf_url): void
    {
        // 更新测评状态，0未测评,1已测评未有结果,2有结果没有pdf_url,3有pdf_url
        AssessmentTaskAssignment::where('id', $assignment_id)->update(['pdf_url'=>$pdf_url, 'status' => 3]);
    }

    private function generatePdf(int $assessment_id, int $assessment_task_assignment_id): string
    {
        ini_set('default_socket_timeout', 30);
        
        if (!$assessment_id || !$assessment_task_assignment_id) { throw new \Exception('参数不能为空');}

        $assessment_id_path_mapping = config('assessment.assessment_id_path');

        $path = $assessment_id_path_mapping[$assessment_id];

        $html_url = 'https://saas.yishengya.cn/survey/survey_report/'.$path.'?type=pdf&assessment_id=' . $assessment_id. '&assessment_task_assignment_id='. $assessment_task_assignment_id;

        $service = new CreatePdfFileService($html_url);
        $pdfFileName = '/assessment/pdf/' . date('Ymd') . '/' . $assessment_task_assignment_id . '-' . $assessment_id . '-' . time() . '.pdf';

        //$customDictConfig不同的测评会有不同的配置，所以写在此处
        $customDictConfig = [
            'path' => '/data/ysy/uploads_cdn' . $pdfFileName,
            'format' => 'A4',
            'printBackground' => 'true',
        ];
        $pdf_path = $service->createPdfFile($pdfFileName, $customDictConfig);

        return $pdf_path;
    }

    /**
     * 处理失败作业
     */
    public function failed(Throwable $exception): void
    {
        // 向用户发送失败通知等...
    }

}
