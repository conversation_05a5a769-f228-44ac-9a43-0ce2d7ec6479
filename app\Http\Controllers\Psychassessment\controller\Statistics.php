<?php
/**
 * Created by PhpStorm.
 * User: zhao
 * Date: 2024/01/19
 * Time: 17:52
 */
namespace app\psychassessment\controller;
use app\login\controller\ApiAuth;


/**
 * Class Questions
 * @package app\psychassessment\controller
 */
class Statistics extends ApiAuth{
    public function __construct()
    {
        parent::__construct();
        $this->statistics = new \app\psychassessment\service\Statistics();
    }

    /**
     * 模块：心理评估-统计分析
     * @SWG\Get(path="/psychassessment/get_member_score_list",
     *   tags={"心理评估-统计分析:Statistics"},
     *   summary="获取测评计划内学生列表",
     *   description="数据说明：",
     *   @SWG\Parameter(
     *     in="query",
     *     name="plan_id",
     *     type="integer",
     *     description="计划id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="survey_type",
     *     type="integer",
     *     description="心理测评类型自信26意识27焦虑28",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="is_complete",
     *     type="string",
     *     description="已完成未完成",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="warning_level",
     *     type="string",
     *     description="预警等级",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="abnormal",
     *     type="string",
     *     description="异常项目",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="page",
     *     type="integer",
     *     description="页码",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="pagesize",
     *     type="integer",
     *     description="页数量",
     *     required=false,
     *   ),
     *   produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function get_member_score_list()
    {
        $data = $this->statistics->get_member_score_list();
        apiReturn($data);
    }

    /**
     * 模块：心理评估-统计分析
     * @SWG\Get(path="/psychassessment/get_statistic_analysis",
     *   tags={"心理评估-统计分析:Statistics"},
     *   summary="获取完成数据和维度与code对应关系",
     *   description="数据说明：",
     *   @SWG\Parameter(
     *     in="query",
     *     name="plan_id",
     *     type="integer",
     *     description="计划id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="survey_type",
     *     type="integer",
     *     description="心理测评类型自信26意识27焦虑28",
     *     required=false,
     *   ),
     *   produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function get_statistic_analysis()
    {
        $data = $this->statistics->get_statistic_analysis();
        apiReturn($data);
    }

    /**
     * 模块：心理评估-统计分析
     * @SWG\Get(path="/psychassessment/batch_download_pdf",
     *   tags={"心理评估-统计分析:Statistics"},
     *   summary="批量下载心里测评报告，其实是把生成好的pdf打包的过程",
     *   description="数据说明：",
     *   @SWG\Parameter(
     *     in="query",
     *     name="session_ids",
     *     type="string",
     *     description="测评编号,多个用,拼接",
     *     required=false,
     *   ),
     *   produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function batch_download_pdf(){
        $data = $this->statistics->batch_download_pdf();
        return $data;
    }
}