<?php

namespace App\Traits;

use App\Models\LoginLog;
use App\Models\User;
use Illuminate\Http\Request;

trait LogsLoginAttempts
{
    /**
     * 记录登录失败日志
     */
    protected function logLoginFailure(Request $request, string $reason, string $username = null): void
    {
        LoginLog::createFailedLog(
            $username ?? $request->input('username'),
            $reason,
            $request->ip(),
            $request->userAgent()
        );
    }

    /**
     * 记录登录成功日志
     */
    protected function logLoginSuccess(Request $request, User $user): void
    {
        LoginLog::createSuccessLog(
            $user,
            $request->ip(),
            $request->userAgent()
        );
    }

    /**
     * 记录登录失败并返回错误响应的便捷方法
     */
    protected function failLoginWithLog(Request $request, string $reason, string $errorMessage = null): \Illuminate\Http\JsonResponse
    {
        $this->logLoginFailure($request, $reason);
        return $this->error($errorMessage ?? $reason);
    }

    /**
     * 检查并记录可疑登录活动
     */
    protected function checkSuspiciousActivity(string $username): bool
    {
        $recentFailures = LoginLog::getFailedAttempts(null, $username, 1);
        return $recentFailures->count() >= 5;
    }
}
