<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MajorSubjectZK extends BaseModel
{
    use HasFactory;

    protected $table = 'MajorSubject_ZK';

    // 指定连接
    protected $connection = 'sqlsrv_gk';

    //主键
    protected $primaryKey = 'Id';

    // 隐藏字段
    protected $hidden = [];

    // 与 MajorZK 的一对多关系
    public function majors()
    {
        return $this->hasMany(MajorZK::class, 'MajorSubjectID', 'ID');
    }

    // 与 MajorCategoryZK 的一对多关系
    public function categories()
    {
        return $this->hasMany(MajorCategoryZK::class, 'MajorSubjectID', 'ID');
    }
}
