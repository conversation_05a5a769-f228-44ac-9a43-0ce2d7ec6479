<?php

namespace App\Services\School\Assessment;

use App\Exceptions\BusinessException;
use Illuminate\Contracts\Foundation\Application;

class AssessmentServiceFactory
{
    protected string $namespace = "App\\Services\\School\\";

    public function __construct(protected Application $app) {}

    /**
     * 获取指定 type 和 module 的服务实例
     * @param string $type 示例： CareerAnswer
     * @param string $module 示例： Assessment\\Answer
     * @return mixed
     */
    public function getService(string $type, string $module)
    {
        if (empty($type) || empty($module)) {
            throw new BusinessException("Invalid type or module");
        }
        // 计算类名，例如："App\Services\School\Assessment\Answer\CareerAnswerService"
        $serviceClass = $this->namespace . ucfirst($module) . '\\' . ucfirst($type) . "Service";

        // 确保类存在
        if (!class_exists($serviceClass)) {
            throw new BusinessException("Service '{$serviceClass}' not found");
        }

        // 通过 Laravel 容器解析
        return $this->app->make($serviceClass);
    }
}