<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use App\Services\Evaluation\ConfigService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * 配置管理控制器
 */
class EvaluationConfigController extends Controller
{
    protected $configService;

    public function __construct(ConfigService $configService)
    {
        $this->configService = $configService;
    }

    /**
     * 获取配置信息
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function config(Request $request): JsonResponse
    {
        try {
            $data = $this->configService->getConfig();
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取配置信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取学科配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getCourses(Request $request): JsonResponse
    {
        try {
            $data = $this->configService->course();
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取学科配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取年级配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getGrades(Request $request): JsonResponse
    {
        try {
            $data = $this->configService->grade();
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取年级配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取学校配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSchools(Request $request): JsonResponse
    {
        try {
            // 学校配置需要从数据库读取，这里返回空数组或默认配置
            $data = [];
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取学校配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取班级配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getClasses(Request $request): JsonResponse
    {
        try {
            // 班级配置需要从数据库读取，这里返回空数组或默认配置
            $data = [];
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取班级配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取老师配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getTeachers(Request $request): JsonResponse
    {
        try {
            // 老师配置需要从数据库读取，这里返回空数组或默认配置
            $data = [];
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取老师配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取学生配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudents(Request $request): JsonResponse
    {
        try {
            // 学生配置需要从数据库读取，这里返回空数组或默认配置
            $data = [];
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取学生配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取题目类型配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getQuestionTypes(Request $request): JsonResponse
    {
        try {
            // 题目类型配置需要从数据库读取，这里返回空数组或默认配置
            $data = [];
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取题目类型配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取应用场景配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getScenarios(Request $request): JsonResponse
    {
        try {
            $data = $this->configService->scenario();
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取应用场景配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取素养水平配置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getLiteracyLevels(Request $request): JsonResponse
    {
        try {
            // 素养等级配置，返回默认配置
            $data = [
                ['code' => 'excellent', 'name' => '优', 'color' => '#52c41a', 'description' => '优秀水平'],
                ['code' => 'good', 'name' => '良', 'color' => '#1890ff', 'description' => '良好水平'],
                ['code' => 'need_improve', 'name' => '有待提高', 'color' => '#faad14', 'description' => '需要提高'],
            ];
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取素养水平配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取系统设置
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSystemSettings(Request $request): JsonResponse
    {
        try {
            $data = $this->configService->getSystemSettings();
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取系统设置失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新配置
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $key = $request->input('key');
            $value = $request->input('value');
            
            if (empty($key)) {
                return $this->error('配置键不能为空');
            }
            
            $this->configService->updateConfig($key, $value);
            return $this->success(null, '配置更新成功');
        } catch (\Exception $e) {
            return $this->error('配置更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量更新配置
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchUpdate(Request $request): JsonResponse
    {
        try {
            $configs = $request->input('configs', []);
            
            if (empty($configs)) {
                return $this->error('配置数据不能为空');
            }
            
            $this->configService->batchUpdateConfig($configs);
            return $this->success(null, '配置批量更新成功');
        } catch (\Exception $e) {
            return $this->error('配置批量更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 重置配置缓存
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function resetCache(Request $request): JsonResponse
    {
        try {
            $this->configService->resetConfigCache();
            return $this->success(null, '配置缓存重置成功');
        } catch (\Exception $e) {
            return $this->error('配置缓存重置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取完整配置（包含所有子配置）
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getFullConfig(Request $request): JsonResponse
    {
        try {
            $data = [
                'basic' => $this->configService->getConfig(),
                'schools' => $this->configService->getSchools(),
                'grades' => $this->configService->getGrades(),
                'courses' => $this->configService->getCourses(),
                'question_types' => $this->configService->getQuestionTypes(),
                'scenarios' => $this->configService->getScenarios(),
                'literacy_levels' => $this->configService->getLiteracyLevels(),
                'system_settings' => $this->configService->getSystemSettings(),
            ];
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取完整配置失败: ' . $e->getMessage());
        }
    }
}
