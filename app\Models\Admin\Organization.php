<?php

namespace App\Models\Admin;

use App\Models\BaseModel;
use App\Models\User;

class Organization extends BaseModel
{
    protected $hidden = [
        'created_at',
        'updated_at',
        'creator',
        'updater',
    ];

    public function model()
    {
        return $this->morphTo();
    }

    public function users()
    {
        return $this->hasMany(User::class,'organization_id','id');
    }
}
