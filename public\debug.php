<?php
/**
 * Laravel 路由调试脚本
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

try {
    // 检查 Laravel 是否可以启动
    require_once __DIR__ . '/../vendor/autoload.php';
    
    $app = require_once __DIR__ . '/../bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    
    // 获取路由信息
    $router = $app->make('router');
    $routes = $router->getRoutes();

    $evaluationRoutes = [];
    $questionTypeRoutes = [];
    foreach ($routes as $route) {
        $uri = $route->uri();
        if (strpos($uri, 'evaluation') !== false) {
            $evaluationRoutes[] = [
                'method' => implode('|', $route->methods()),
                'uri' => $uri,
                'name' => $route->getName(),
                'action' => $route->getActionName()
            ];

            // 特别关注 question_types 相关路由
            if (strpos($uri, 'question') !== false) {
                $questionTypeRoutes[] = [
                    'method' => implode('|', $route->methods()),
                    'uri' => $uri,
                    'name' => $route->getName(),
                    'action' => $route->getActionName()
                ];
            }
        }
    }
    
    $response = [
        'status' => 'success',
        'message' => 'Laravel is working!',
        'debug_info' => [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'unknown',
            'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'http_host' => $_SERVER['HTTP_HOST'] ?? 'unknown',
            'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
        ],
        'evaluation_routes_count' => count($evaluationRoutes),
        'evaluation_routes' => array_slice($evaluationRoutes, 0, 10), // 只显示前10个
        'question_type_routes_count' => count($questionTypeRoutes),
        'question_type_routes' => $questionTypeRoutes, // 显示所有question相关路由
        'route_files_exist' => [
            'web.php' => file_exists(__DIR__ . '/../routes/web.php'),
            'api.php' => file_exists(__DIR__ . '/../routes/api.php'),
            'evaluation.php' => file_exists(__DIR__ . '/../routes/evaluation.php'),
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $response = [
        'status' => 'error',
        'message' => 'Laravel failed to start',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'debug_info' => [
            'php_version' => PHP_VERSION,
            'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'unknown',
            'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
