<?php
namespace App\Services\School\Assessment\IndividualReport\Capability;

/**
 * 沟通与合作能力个人报告服务类
 * 
 * 该类用于生成沟通与合作能力的个人评估报告
 */
class CommunicationCollaborationService extends AbstractCapabilityReportService
{
    /**
     * 改进指数限制
     * 
     * @var int
     */
    protected const IMPROVEMENT_INDEX_LIMIT = 8; // 或者该类特定的值

    /**
     * 获取配置键名
     * 
     * @return string 配置键名
     */
    protected function getConfigKey(): string
    {
        return 'communication_collaboration';
    }

    /**
     * 获取改进指数限制
     * 
     * @return int
     */
    protected function getImprovementIndexLimit(): int
    {
        return self::IMPROVEMENT_INDEX_LIMIT;
    }

    /**
     * 获取维度的高分题和低分题
     * 
     * @param array $answer_scores 答案得分数组
     * @param string $dimension_name 维度名称
     * @return array 包含高分题和低分题的数组
     */
    protected function getDimensionQuestionsByScore(array $answer_scores, string $dimension_name): array
    {
        // 如果是沟通与合作基本能力维度，直接返回空数组
        if ($dimension_name === '沟通与合作基本能力') {
            return [[], []];
        }

        return parent::getDimensionQuestionsByScore($answer_scores, $dimension_name);
    }
}