# 学生同步功能说明

## 概述

学生同步功能与教师同步类似，支持单个和批量学生数据同步到ysy_member和ysy_student表。学生同步的特点是需要包含class_id和grade_id信息。

## 功能特点

### 1. 单个学生同步

**触发条件：**
- 学生创建后自动触发同步
- 同步失败不影响主流程，只记录日志

**同步数据格式：**
```php
$student_data = [
    'name' => $student_name,
    'username' => $username,
    'password' => '827ccb0eea8a706c4c34a16891f84e7b',
    'role_id' => $role->id,
    'school_id' => $syncSchoolId,
    'school_district' => $school_campus_id,
    'class_id' => $class_id,
    'grade_id' => $grade_id,
    'role_source_id' => '4', // 学生角色类型
    'step' => '0',
    'gender' => $gender
];
```

### 2. 批量学生同步

**请求数据格式：**
```json
{
    "school_campus_id": 1291,
    "students": [
        {
            "student_name": "张三",
            "gender": "男",
            "username": "zhangsan001",
            "class_id": 101,
            "grade_id": 201
        },
        {
            "student_name": "李四",
            "gender": "女", 
            "username": "lisi002",
            "class_id": 102,
            "grade_id": 201
        }
    ]
}
```

## 数据库表结构

### ysy_member表（学生用户信息）
```sql
CREATE TABLE ysy_member (
    id INT PRIMARY KEY COMMENT '用户ID',
    name VARCHAR(50) COMMENT '姓名',
    username VARCHAR(50) COMMENT '用户名',
    password VARCHAR(255) COMMENT '密码',
    gender TINYINT COMMENT '性别1男2女',
    school_id INT COMMENT '学校ID',
    role_id VARCHAR(50) COMMENT '角色ID，格式：0,角色ID,0',
    role_source_id VARCHAR(10) COMMENT '角色来源ID',
    step TINYINT DEFAULT 0 COMMENT '步骤状态',
    create_time TIMESTAMP COMMENT '创建时间'
);
```

### ysy_student表（学生详细信息）
```sql
CREATE TABLE ysy_student (
    id INT PRIMARY KEY AUTO_INCREMENT,
    member_id INT COMMENT '关联ysy_member表的ID',
    name VARCHAR(50) COMMENT '学生姓名',
    username VARCHAR(50) COMMENT '用户名',
    gender TINYINT COMMENT '性别1男2女',
    school_id INT COMMENT '学校ID',
    school_district INT COMMENT '校区ID',
    class_id INT COMMENT '班级ID',
    grade_id INT COMMENT '年级ID',
    step TINYINT DEFAULT 0 COMMENT '步骤状态',
    type VARCHAR(20) DEFAULT '学生' COMMENT '类型',
    role_id INT COMMENT '角色ID'
);
```

## API方法

### 1. 单个学生同步
```php
public function syncSingleStudent($request): array
```

**参数（通过Request对象传递）：**
- `student_name`: 学生姓名
- `username`: 用户名
- `gender`: 性别（1=男，2=女）
- `class_id`: 班级ID
- `grade_id`: 年级ID
- `school_campus_id`: 校区ID

### 2. 批量学生同步
```php
public function syncBatchStudents(array $request_data): array
```

**参数：**
- `school_campus_id`: 校区ID
- `students`: 学生数组，每个学生包含上述字段

### 3. 学生数据同步核心方法
```php
public function syncStudent(array $studentData): array
```

## 同步流程

### 单个学生同步流程
```
1. 接收请求参数（学生姓名、用户名、性别、班级ID、年级ID）
2. 查找刚创建的学生记录
3. 获取校区信息和学校ID
4. 查询学生角色ID
5. 准备同步数据
6. 调用syncStudent方法
7. 同步到ysy_member表（获取member_id）
8. 同步到ysy_student表（使用member_id）
9. 提交事务
```

### 批量学生同步流程
```
1. 接收批量请求数据
2. 循环处理每个学生：
   - 检查用户名是否已存在
   - 查询学生角色ID
   - 准备学生数据
   - 调用syncStudent方法
3. 统计同步结果
4. 返回详细的同步报告
```

## 与教师同步的区别

| 特性 | 教师同步 | 学生同步 |
|------|----------|----------|
| 角色查询 | 教务/老师 | 学生 |
| 特有字段 | is_psychology_teacher | class_id, grade_id |
| 角色类型 | 2(教务), 3(老师) | 4(学生) |
| 目标表 | ysy_teacher | ysy_student |
| 角色判断 | 根据roles数组判断 | 固定为学生角色 |

## 实现示例

### 单个学生同步
```php
// 在StudentService中直接传递request对象
$result = $dataSyncService->syncSingleStudent($request);

// 或者创建Request对象
$request_data = [
    'student_name' => '张三',
    'username' => 'zhangsan001',
    'gender' => 1,
    'class_id' => 101,
    'grade_id' => 201,
    'school_campus_id' => 1291
];

$mock_request = new \Illuminate\Http\Request();
$mock_request->merge($request_data);
$result = $dataSyncService->syncSingleStudent($mock_request);
```

### 批量学生同步
```php
$batch_data = [
    'school_campus_id' => 1291,
    'students' => [
        [
            'student_name' => '张三',
            'gender' => '男',
            'username' => 'zhangsan001',
            'class_id' => 101,
            'grade_id' => 201
        ],
        [
            'student_name' => '李四',
            'gender' => '女',
            'username' => 'lisi002',
            'class_id' => 102,
            'grade_id' => 201
        ]
    ]
];

$result = $dataSyncService->syncBatchStudents($batch_data);
```

## 返回结果

### 单个学生同步返回
```json
{
    "success": true,
    "member_id": 1001,
    "synced_to_member": true,
    "synced_to_student": true,
    "class_id": 101,
    "grade_id": 201,
    "message": "学生数据同步成功"
}
```

### 批量学生同步返回
```json
{
    "success": true,
    "sync_results": [
        {
            "student_name": "张三",
            "username": "zhangsan001",
            "success": true,
            "member_id": 1001,
            "class_id": 101,
            "grade_id": 201
        },
        {
            "student_name": "李四",
            "username": "lisi002",
            "success": false,
            "message": "用户名已存在，跳过插入",
            "skipped": true
        }
    ],
    "total_count": 2,
    "success_count": 1,
    "skipped_count": 1,
    "message": "批量学生数据同步完成"
}
```

## 注意事项

1. **角色查询**：学生角色名称固定为"学生"，需要在ysy_role表中存在
2. **班级和年级**：class_id和grade_id是学生同步的必需字段
3. **用户名唯一性**：系统会自动检查用户名是否已存在，避免重复插入
4. **事务处理**：每个学生的同步都是独立的事务，一个失败不影响其他学生
5. **日志记录**：所有同步结果都会记录到日志中，便于问题排查
6. **角色类型**：学生的role_source_id固定为'4'
