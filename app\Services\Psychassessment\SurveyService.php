<?php

namespace App\Services\Psychassessment;

use App\Models\Psychassessment\PsychassessmentSurvey;
use App\Models\Psychassessment\PsychassessmentSurveyMember;
use App\Models\Psychassessment\PsychassessmentAssessment;
use Illuminate\Support\Facades\DB;

/**
 * 心理评估-测评计划服务层
 */
class SurveyService
{
    protected $surveyModel;
    protected $surveyMemberModel;
    protected $assessmentModel;

    public function __construct()
    {
        $this->surveyModel = new PsychassessmentSurvey();
        $this->surveyMemberModel = new PsychassessmentSurveyMember();
        $this->assessmentModel = new PsychassessmentAssessment();
    }

    /**
     * 创建测评计划
     * 
     * @param array $data
     * @param object $user
     * @return array
     */
    public function create(array $data, $user): array
    {
        try {
            DB::beginTransaction();

            $surveyData = [
                'title' => $data['title'],
                'description' => $data['description'] ?? '',
                'survey_type' => $data['survey_type'],
                'start_time' => $data['start_time'],
                'end_time' => $data['end_time'],
                'status' => $data['status'] ?? 1,
                'creator_id' => $user->id,
                'school_id' => $user->school_id ?? null,
                'created_at' => now()
            ];

            $survey = $this->surveyModel->create($surveyData);

            // 添加参与学生
            if (!empty($data['member_ids'])) {
                $this->addSurveyMembers($survey->id, $data['member_ids']);
            }

            DB::commit();
            return $survey->toArray();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 更新测评计划
     * 
     * @param int $id
     * @param array $data
     * @param object $user
     * @return bool
     */
    public function update($id, array $data, $user): bool
    {
        try {
            DB::beginTransaction();

            $updateData = array_filter([
                'title' => $data['title'] ?? null,
                'description' => $data['description'] ?? null,
                'survey_type' => $data['survey_type'] ?? null,
                'start_time' => $data['start_time'] ?? null,
                'end_time' => $data['end_time'] ?? null,
                'status' => $data['status'] ?? null,
                'updated_at' => now()
            ], function($value) {
                return $value !== null;
            });

            $result = $this->surveyModel->where('id', $id)->update($updateData);

            // 更新参与学生
            if (isset($data['member_ids'])) {
                $this->updateSurveyMembers($id, $data['member_ids']);
            }

            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取测评计划列表
     * 
     * @param array $params
     * @return array
     */
    public function getList(array $params): array
    {
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 15;
        
        $query = $this->surveyModel->with(['creator', 'members']);

        // 筛选条件
        if (!empty($params['title'])) {
            $query->where('title', 'like', '%' . $params['title'] . '%');
        }

        if (!empty($params['survey_type'])) {
            $query->where('survey_type', $params['survey_type']);
        }

        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        if (!empty($params['creator_id'])) {
            $query->where('creator_id', $params['creator_id']);
        }

        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $query->whereBetween('created_at', [$params['start_time'], $params['end_time']]);
        }

        $total = $query->count();
        $data = $query->orderBy('created_at', 'desc')
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get()
            ->toArray();

        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }

    /**
     * 删除测评计划
     * 
     * @param int $id
     * @return bool
     */
    public function delete($id): bool
    {
        try {
            DB::beginTransaction();

            // 删除测评计划
            $this->surveyModel->where('id', $id)->delete();
            
            // 删除相关的学生记录
            $this->surveyMemberModel->where('survey_id', $id)->delete();

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取心理评估列表
     * 
     * @return array
     */
    public function getAssessList(): array
    {
        return $this->assessmentModel->where('status', 1)
            ->orderBy('sort', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * 获取老师辅导的学生列表
     * 
     * @param object $user
     * @return array
     */
    public function getPsychassessmentClassMember($user): array
    {
        // 根据用户角色获取对应的学生列表
        $query = DB::table('member as m')
            ->leftJoin('class as c', 'm.class_id', '=', 'c.id')
            ->leftJoin('grade as g', 'c.grade_id', '=', 'g.id')
            ->select('m.id', 'm.name', 'm.student_number', 'c.name as class_name', 'g.name as grade_name')
            ->where('m.role', 'student')
            ->where('m.status', 1);

        if ($user->role === 'teacher') {
            // 班主任只能看到自己班级的学生
            $query->where('c.teacher_id', $user->id);
        } elseif ($user->role === 'counselor') {
            // 心理老师可以看到学校所有学生
            $query->where('m.school_id', $user->school_id);
        }

        return $query->get()->toArray();
    }

    /**
     * 获取未完成的学生列表
     * 
     * @param array $params
     * @return array
     */
    public function getMemberCompleteStatus(array $params): array
    {
        $surveyId = $params['survey_id'];
        
        $query = DB::table('psychassessment_survey_member as sm')
            ->leftJoin('member as m', 'sm.member_id', '=', 'm.id')
            ->leftJoin('class as c', 'm.class_id', '=', 'c.id')
            ->select('m.id', 'm.name', 'm.student_number', 'c.name as class_name', 'sm.status', 'sm.completed_at')
            ->where('sm.survey_id', $surveyId);

        if (!empty($params['status'])) {
            $query->where('sm.status', $params['status']);
        }

        return $query->orderBy('sm.status', 'asc')
            ->orderBy('m.name', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * 获取学生端测评计划列表
     * 
     * @param object $user
     * @return array
     */
    public function getStudentPsychassess($user): array
    {
        return DB::table('psychassessment_survey_member as sm')
            ->leftJoin('psychassessment_survey as s', 'sm.survey_id', '=', 's.id')
            ->leftJoin('psychassessment_assessment as a', 's.survey_type', '=', 'a.id')
            ->select('s.id', 's.title', 's.description', 'a.name as assessment_name', 
                    's.start_time', 's.end_time', 'sm.status', 'sm.completed_at')
            ->where('sm.member_id', $user->id)
            ->where('s.status', 1)
            ->orderBy('s.created_at', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * 获取测评计划详情
     * 
     * @param int $id
     * @return array|null
     */
    public function getDetail($id): ?array
    {
        $survey = $this->surveyModel->with(['creator', 'assessment', 'members.member'])
            ->find($id);

        return $survey ? $survey->toArray() : null;
    }

    /**
     * 批量删除测评计划
     * 
     * @param array $ids
     * @return bool
     */
    public function batchDelete(array $ids): bool
    {
        try {
            DB::beginTransaction();

            $this->surveyModel->whereIn('id', $ids)->delete();
            $this->surveyMemberModel->whereIn('survey_id', $ids)->delete();

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 更新测评计划状态
     * 
     * @param int $id
     * @param int $status
     * @return bool
     */
    public function updateStatus($id, $status): bool
    {
        return $this->surveyModel->where('id', $id)->update(['status' => $status]);
    }

    /**
     * 复制测评计划
     * 
     * @param int $id
     * @param object $user
     * @return array
     */
    public function copy($id, $user): array
    {
        try {
            DB::beginTransaction();

            $originalSurvey = $this->surveyModel->find($id);
            if (!$originalSurvey) {
                throw new \Exception('原测评计划不存在');
            }

            $newSurveyData = $originalSurvey->toArray();
            unset($newSurveyData['id']);
            $newSurveyData['title'] = $newSurveyData['title'] . ' (复制)';
            $newSurveyData['creator_id'] = $user->id;
            $newSurveyData['created_at'] = now();
            $newSurveyData['status'] = 0; // 复制的计划默认为草稿状态

            $newSurvey = $this->surveyModel->create($newSurveyData);

            // 复制参与学生
            $originalMembers = $this->surveyMemberModel->where('survey_id', $id)->get();
            foreach ($originalMembers as $member) {
                $memberData = $member->toArray();
                unset($memberData['id']);
                $memberData['survey_id'] = $newSurvey->id;
                $memberData['status'] = 0; // 重置状态
                $memberData['completed_at'] = null;
                $this->surveyMemberModel->create($memberData);
            }

            DB::commit();
            return $newSurvey->toArray();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取测评统计数据
     * 
     * @param int $id
     * @return array
     */
    public function getStatistics($id): array
    {
        $total = $this->surveyMemberModel->where('survey_id', $id)->count();
        $completed = $this->surveyMemberModel->where('survey_id', $id)->where('status', 1)->count();
        $pending = $total - $completed;

        return [
            'total' => $total,
            'completed' => $completed,
            'pending' => $pending,
            'completion_rate' => $total > 0 ? round($completed / $total * 100, 2) : 0
        ];
    }

    /**
     * 导出测评结果
     * 
     * @param int $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export($id)
    {
        // 实现导出逻辑
        // 这里需要根据具体需求实现Excel导出
        throw new \Exception('导出功能待实现');
    }

    /**
     * 获取可用的心理测评类型
     * 
     * @return array
     */
    public function getSurveyTypes(): array
    {
        return $this->assessmentModel->where('status', 1)
            ->select('id', 'name', 'description', 'type')
            ->orderBy('sort', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * 获取班级学生列表
     * 
     * @param array $classIds
     * @return array
     */
    public function getClassMembers(array $classIds): array
    {
        return DB::table('member as m')
            ->leftJoin('class as c', 'm.class_id', '=', 'c.id')
            ->select('m.id', 'm.name', 'm.student_number', 'c.name as class_name')
            ->whereIn('m.class_id', $classIds)
            ->where('m.role', 'student')
            ->where('m.status', 1)
            ->orderBy('c.name', 'asc')
            ->orderBy('m.name', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * 添加测评参与学生
     * 
     * @param int $surveyId
     * @param array $memberIds
     * @return void
     */
    private function addSurveyMembers($surveyId, array $memberIds): void
    {
        $data = [];
        foreach ($memberIds as $memberId) {
            $data[] = [
                'survey_id' => $surveyId,
                'member_id' => $memberId,
                'status' => 0,
                'created_at' => now()
            ];
        }
        $this->surveyMemberModel->insert($data);
    }

    /**
     * 更新测评参与学生
     * 
     * @param int $surveyId
     * @param array $memberIds
     * @return void
     */
    private function updateSurveyMembers($surveyId, array $memberIds): void
    {
        // 删除原有记录
        $this->surveyMemberModel->where('survey_id', $surveyId)->delete();
        
        // 添加新记录
        $this->addSurveyMembers($surveyId, $memberIds);
    }
}
