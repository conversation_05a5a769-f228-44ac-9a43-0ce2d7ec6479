<?php

namespace App\Http\Resources\School\Xuezhi;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * 职业列表资源类
 * 
 * 该类用于格式化职业列表的响应数据
 */
class OccupationCollection extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // 基础职业信息
        $data = [
            'id' => $this->resource['Id'] ?? $this->Id ?? null,
            'code' => $this->resource['Code'] ?? $this->Code ?? null,
            'parent_code' => $this->resource['ParentCode'] ?? $this->ParentCode ?? null,
            'occupation_name' => $this->resource['OccupationName'] ?? $this->OccupationName ?? null,
            'level_type' => $this->resource['LevelType'] ?? $this->LevelType ?? null,
        ];
        
        // 如果有子职业，递归处理
        if (isset($this->resource['children']) && is_array($this->resource['children'])) {
            $data['children'] = collect($this->resource['children'])->map(function ($child) {
                return new OccupationCollection($child);
            });
        } elseif (isset($this->children) && is_array($this->children)) {
            $data['children'] = collect($this->children)->map(function ($child) {
                return new OccupationCollection($child);
            });
        }
        
        return $data;
    }
}