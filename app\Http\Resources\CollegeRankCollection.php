<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CollegeRankCollection extends JsonResource
{


    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'college_id' => $this->CollegeId,
            'college_name' => $this->CollegeName,
            'province' => $this->college->province ? [
                'id' => $this->college->province->ID,
                'province_name' => $this->college->province->ProvinceName,
                'code' => $this->college->province->Code,
                'province_spell' => $this->college->province->ProvinceSpell,
            ] : null,
            'city' => $this->college->city ? [
                'id' => $this->college->city->ID,
                'province_id' => $this->college->city->ProvinceID,
                'city_name' => $this->college->city->CityName,
                'lat' => $this->college->city->Lat,
                'lng' => $this->college->city->Lng,
                'salesperson_id' => $this->college->city->SalespersonID,
            ] : null,
        ];
    }


}
