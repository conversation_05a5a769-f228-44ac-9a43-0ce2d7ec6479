<?php

namespace App\Models\Psychassessment;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 心理评估结果模型
 */
class PsychassessmentResult extends Model
{
    protected $table = 'psychassessment_result';

    protected $fillable = [
        'survey_id',
        'member_id',
        'survey_type',
        'result_data',
        'score_data',
        'dimension_scores',
        'total_score',
        'level',
        'warning_level',
        'abnormal_items',
        'completed_at',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'survey_id' => 'integer',
        'member_id' => 'integer',
        'survey_type' => 'integer',
        'total_score' => 'float',
        'level' => 'integer',
        'warning_level' => 'integer',
        'completed_at' => 'datetime'
    ];

    /**
     * 关联测评计划
     */
    public function survey(): BelongsTo
    {
        return $this->belongsTo(PsychassessmentSurvey::class, 'survey_id');
    }

    /**
     * 关联学生
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Member::class, 'member_id');
    }

    /**
     * 关联心理评估类型
     */
    public function assessment(): BelongsTo
    {
        return $this->belongsTo(PsychassessmentAssessment::class, 'survey_type');
    }

    /**
     * 获取结果数据数组
     */
    public function getResultDataArrayAttribute()
    {
        return $this->result_data ? json_decode($this->result_data, true) : [];
    }

    /**
     * 设置结果数据
     */
    public function setResultDataAttribute($value)
    {
        $this->attributes['result_data'] = is_array($value) ? json_encode($value) : $value;
    }

    /**
     * 获取分数数据数组
     */
    public function getScoreDataArrayAttribute()
    {
        return $this->score_data ? json_decode($this->score_data, true) : [];
    }

    /**
     * 设置分数数据
     */
    public function setScoreDataAttribute($value)
    {
        $this->attributes['score_data'] = is_array($value) ? json_encode($value) : $value;
    }

    /**
     * 获取维度分数数组
     */
    public function getDimensionScoresArrayAttribute()
    {
        return $this->dimension_scores ? json_decode($this->dimension_scores, true) : [];
    }

    /**
     * 设置维度分数
     */
    public function setDimensionScoresAttribute($value)
    {
        $this->attributes['dimension_scores'] = is_array($value) ? json_encode($value) : $value;
    }

    /**
     * 获取异常项目数组
     */
    public function getAbnormalItemsArrayAttribute()
    {
        return $this->abnormal_items ? json_decode($this->abnormal_items, true) : [];
    }

    /**
     * 设置异常项目
     */
    public function setAbnormalItemsAttribute($value)
    {
        $this->attributes['abnormal_items'] = is_array($value) ? json_encode($value) : $value;
    }

    /**
     * 获取等级文本
     */
    public function getLevelTextAttribute()
    {
        return match($this->level) {
            1 => '正常',
            2 => '轻度',
            3 => '中度',
            4 => '重度',
            default => '未知'
        };
    }

    /**
     * 获取预警等级文本
     */
    public function getWarningLevelTextAttribute()
    {
        return match($this->warning_level) {
            0 => '无预警',
            1 => '一级预警',
            2 => '二级预警',
            3 => '三级预警',
            default => '未知'
        };
    }

    /**
     * 作用域：按测评计划
     */
    public function scopeBySurvey($query, $surveyId)
    {
        return $query->where('survey_id', $surveyId);
    }

    /**
     * 作用域：按学生
     */
    public function scopeByMember($query, $memberId)
    {
        return $query->where('member_id', $memberId);
    }

    /**
     * 作用域：按评估类型
     */
    public function scopeBySurveyType($query, $surveyType)
    {
        return $query->where('survey_type', $surveyType);
    }

    /**
     * 作用域：按等级
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * 作用域：按预警等级
     */
    public function scopeByWarningLevel($query, $warningLevel)
    {
        return $query->where('warning_level', $warningLevel);
    }

    /**
     * 作用域：有异常项目
     */
    public function scopeWithAbnormalItems($query)
    {
        return $query->whereNotNull('abnormal_items')
            ->where('abnormal_items', '!=', '[]')
            ->where('abnormal_items', '!=', '');
    }

    /**
     * 作用域：按完成时间范围
     */
    public function scopeByCompletedDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('completed_at', [$startDate, $endDate]);
    }
}
