<?php

namespace App\Services\School\Assessment\GroupReport;

use App\Models\School\Assessment\AssessmentSchedule;
use App\Services\BaseService;
use App\Services\School\Assessment\GroupReport\GroupReportServiceInterface;

/**
 * 评估团体报告抽象服务类
 * 
 * 该类提供了评估团体报告生成的基础功能，包括数据获取、过滤和统计
 */
abstract class AbstractGroupReportService extends BaseService implements GroupReportServiceInterface
{
    /**
     * 获取评估数据
     * 
     * @param array $params 请求参数
     * @param int $school_id 学校ID
     * @param array $additional_conditions 额外的查询条件
     * @return array 评估数据数组
     * @throws \Exception 当测评数据为空时抛出异常
     */
    protected function getAssessmentData($params, $school_id, $additional_conditions = []): array
    {
        $query = AssessmentSchedule::where([
            'assessment_schedules.id' => $params['assessment_schedule_id'],
            'assessment_tasks.assessment_id' => $params['assessment_id'],
            'assessment_task_assignments.school_id' => $school_id
        ]);

        if (!empty($additional_conditions)) {
            $query->where($additional_conditions);
        }

        $assessment_info = $query->join('assessment_tasks', 'assessment_tasks.assessment_schedule_id', '=', 'assessment_schedules.id')
            ->join('assessment_task_assignments', 'assessment_task_assignments.assessment_task_id', '=', 'assessment_tasks.id')
            ->join('assessments', 'assessment_task_assignments.assessment_id', '=', 'assessments.id')
            ->join('students', 'assessment_task_assignments.student_id', '=', 'students.id')
            ->join('student_classes', 'assessment_task_assignments.student_class_id', '=', 'student_classes.id')
            ->join('classes', 'student_classes.class_id', '=', 'classes.id')
            ->join('school_campuses', 'students.school_campus_id', '=', 'school_campuses.id')
            ->join('schools', 'assessment_task_assignments.school_id', '=', 'schools.id')
            ->select(
                'assessment_schedules.name as schedule_name',
                'assessment_task_assignments.assessment_id',
                'assessments.name as assessment_name',
                'results',
                'standard_results',
                'assessment_task_assignments.student_id',
                'assessment_task_assignments.updated_at as create_time',
                'student_name',
                'gender',
                'student_no',
                'student_classes.class_id',
                'students.grade_year',
                'classes.class_name',
                'schools.name as school_name',
                'school_campuses.type as school_type'
            )
            ->get();

        $assessment_data = $assessment_info->toArray();

        // 检查是否有数据
        if (empty($assessment_data)) {
            throw new \Exception('测评数据为空');
        }
        
        // 检查是否所有学生的standard_results都为空
        $has_valid_results = false;
        foreach ($assessment_data as $student) {
            if (!empty($student['standard_results'])) {
                $has_valid_results = true;
                break;
            }
        }
        
        if (!$has_valid_results) {
            throw new \Exception('暂无学生测评结果');
        }
        
        return $assessment_data;
    }

    /**
     * 获取过滤条件
     * 
     * @param array $params 请求参数
     * @return array 过滤条件数组
     */
    protected function getFilterConditions($params): array
    {
        $conditions = [];
        if (!empty($params['class_id'])) {
            $conditions['classes.id'] = $params['class_id'];
        }
        if (!empty($params['gender'])) {
            $conditions['students.gender'] = $params['gender'];
        }
        return $conditions;
    }

    /**
     * 计算班级统计数据
     * 
     * @param array $assessments 评估数据
     * @return array 班级统计数据
     */
    protected function calculateClassStatistics($assessments): array
    {
        $class_stats = [];
        $total_stats = [
            'total' => 0,
            'completed' => 0,
            'male' => 0,
            'female' => 0,
        ];
        $category_class_data = [];

        foreach ($assessments as $student) {
            $class_name = $student['class_name'];
            $class_id = $student['class_id'];

            if (!isset($class_stats[$class_name])) {
                $class_stats[$class_name] = [
                    'class_name' => $class_name,
                    'class_id' => $class_id,
                    'total' => 0,
                    'completed' => 0,
                    'male' => 0,
                    'female' => 0,
                ];
            }
            if (!isset($category_class_data[$class_name])) {
                $category_class_data[$class_name] = [];
            }

            if($student['standard_results']) {
                $category_class_data[$class_name][] = $student;
            }
            
            $class_stats[$class_name]['total']++;
            $total_stats['total']++;
            
            if (!empty($student['standard_results'])) {
                $class_stats[$class_name]['completed']++;
                $total_stats['completed']++;
            }
            
            if ($student['gender'] == 1) {
                $class_stats[$class_name]['male']++;
                $total_stats['male']++;
            } else {
                $class_stats[$class_name]['female']++;
                $total_stats['female']++;
            }
        }

        foreach ($class_stats as &$stats) {
            $stats['completion_rate'] = $stats['total'] > 0 ? round($stats['completed'] / $stats['total'] * 100, 2) : 0;
        }

        $total_stats['completion_rate'] = $total_stats['total'] > 0 ? round($total_stats['completed'] / $total_stats['total'] * 100, 2) : 0;
        $total_stats['male_rate'] = round($total_stats['male'] / $total_stats['total'] * 100, 2);
        $total_stats['female_rate'] = round($total_stats['female'] / $total_stats['total'] * 100, 2);

        return [
            'stats' => $class_stats,
            'total' => $total_stats,
            'category_data' => $category_class_data
        ];
    }

    /**
     * 获取成员数量统计
     * 
     * @param array $class_statistics 班级统计数据
     * @return array 成员数量统计结果
     */
    protected function getMemberCount($class_statistics): array
    {
        $sort = $class_statistics['total'];

        $sort['class_count'] = count($class_statistics['stats']);

        $axis = array_column($class_statistics['stats'], 'class_name');
        array_push($axis, "总体");

        $series = array_column($class_statistics['stats'], 'completion_rate');
        array_push($series, $sort['completion_rate']);

        return [
            'header'=>[
                'number' => 1,
                'name' => '参与活跃度',
            ],
            'default' => [
                'sort' => $sort,
                'legend' => ['参与人数','未参与人数'],
                'series' => [$sort['completed'], $sort['total'] - $sort['completed']]
            ],
            'class' => [
                'tabledata' => $class_statistics['stats'],
                'axis' => $axis,
                'series' => $series
            ]
        ];
    }

    /**
     * 生成评估报告
     * 
     * @param array $params 请求参数
     * @param int $school_id 学校ID
     * @return array 评估报告数据
     */
    abstract public function generateReport(array $params, int $school_id): array;
    
    /**
     * 生成报告数据
     * 
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @param array $params 请求参数
     * @return array 报告数据
     */
    abstract protected function generateReportData(
        array $filtered_assessment_with_results, 
        array $class_statistics,
        array $params
    ): array;
}