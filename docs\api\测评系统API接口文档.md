# 测评系统API接口文档

## 目录

- [1. 概述](#1-概述)
- [2. 接口基础信息](#2-接口基础信息)
- [3. 教务端接口](#3-教务端接口)
  - [3.1 测评基础接口](#31-测评基础接口)
  - [3.2 计划管理接口](#32-计划管理接口)
  - [3.3 报告管理接口](#33-报告管理接口)
- [4. 学生端接口](#4-学生端接口)
  - [4.1 测评列表接口](#41-测评列表接口)
  - [4.2 学生画像接口](#42-学生画像接口)
- [5. 测评过程接口](#5-测评过程接口)
- [6. 公共接口](#6-公共接口)

## 1. 概述

本文档详细描述了测评系统的API接口，包括教务端接口、学生端接口、测评过程接口和公共接口。所有接口均采用RESTful风格设计，返回JSON格式数据。

## 2. 接口基础信息

- **基础URL前缀**: `/v1/assessment`
- **认证方式**: 大部分接口需要通过`auth.refresh`中间件进行身份验证
- **响应格式**: 统一使用JSON格式，包含状态码、消息和数据

## 3. 教务端接口

### 3.1 测评基础接口

#### 3.1.1 获取测评所有类型

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/types`
- **功能描述**: 获取系统中所有可用的测评类型列表
- **请求参数**: 无
- **响应参数**:
  - 测评类型列表，包含类型ID、名称、描述等信息

#### 3.1.2 获取测评维度

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/dimensions/{assessment_id}`
- **功能描述**: 根据测评ID获取该测评的维度信息
- **请求参数**:
  - `assessment_id`: 测评ID，路径参数
- **响应参数**:
  - 测评维度列表，包含维度ID、名称、描述等信息

#### 3.1.3 获取测评库列表

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/list`
- **功能描述**: 获取学校拥有的测评库列表
- **请求参数**: 无
- **响应参数**:
  - 测评库列表，包含测评ID、名称、类型、描述等信息

#### 3.1.4 提交测评答案并生成报告

- **请求方法**: POST
- **接口路径**: `/v1/assessment/teacher_platform/submit_and_report`
- **功能描述**: 教师端提交测评答案并直接生成报告（不保存数据）
- **请求参数**:
  - 测评ID
  - 答案数据
- **响应参数**:
  - 生成的报告数据

### 3.2 计划管理接口

#### 3.2.1 获取计划列表

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/schedule`
- **功能描述**: 获取测评计划列表
- **请求参数**: 无
- **响应参数**:
  - 计划列表，包含计划ID、名称、状态、创建时间等信息

#### 3.2.2 获取计划详情

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/schedule/{id}`
- **功能描述**: 获取指定ID的测评计划详情
- **请求参数**:
  - `id`: 计划ID，路径参数
- **响应参数**:
  - 计划详情，包含计划基本信息、关联测评、参与班级等

#### 3.2.3 创建计划

- **请求方法**: POST
- **接口路径**: `/v1/assessment/teacher_platform/schedule`
- **功能描述**: 创建新的测评计划
- **请求参数**:
  - 计划名称
  - 计划描述
  - 开始时间
  - 结束时间
  - 测评ID列表
  - 班级ID列表
- **响应参数**:
  - 创建成功的计划信息

#### 3.2.4 更新计划

- **请求方法**: PUT
- **接口路径**: `/v1/assessment/teacher_platform/schedule/{schedule_id}`
- **功能描述**: 更新指定ID的测评计划
- **请求参数**:
  - `schedule_id`: 计划ID，路径参数
  - 计划名称
  - 计划描述
  - 开始时间
  - 结束时间
  - 测评ID列表
  - 班级ID列表
- **响应参数**:
  - 更新后的计划信息

#### 3.2.5 删除计划

- **请求方法**: DELETE
- **接口路径**: `/v1/assessment/teacher_platform/schedule/{schedule_id}`
- **功能描述**: 删除指定ID的测评计划
- **请求参数**:
  - `schedule_id`: 计划ID，路径参数
- **响应参数**:
  - 删除结果

#### 3.2.6 获取计划统计详情

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/schedule/stat_detail`
- **功能描述**: 获取计划及计划下任务的统计详情(计划信息、状态、完成人数、完成率、平均完成时间)
- **请求参数**:
  - 计划ID（查询参数）
- **响应参数**:
  - 计划统计详情

#### 3.2.7 获取计划完成名单情况

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/schedule/student_list`
- **功能描述**: 获取计划完成名单情况
- **请求参数**:
  - 计划ID（查询参数）
- **响应参数**:
  - 学生完成情况列表

#### 3.2.8 获取计划中测评的得分情况

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/schedule/students_score`
- **功能描述**: 获取计划中某个测评的学生得分情况
- **请求参数**:
  - 计划ID（查询参数）
  - 测评ID（查询参数）
- **响应参数**:
  - 学生得分情况列表

#### 3.2.9 批量下载个人报告

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/schedule/report_download`
- **功能描述**: 计划中某个测评的个人报告批量下载
- **请求参数**:
  - 计划ID（查询参数）
  - 测评ID（查询参数）
- **响应参数**:
  - 报告ZIP文件下载链接

#### 3.2.10 获取题目统计

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/schedule/question_stat`
- **功能描述**: 获取计划中某个测评的题目统计
- **请求参数**:
  - 计划ID（查询参数）
  - 测评ID（查询参数）
- **响应参数**:
  - 题目统计数据

#### 3.2.11 导出题目统计

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/schedule/question_export`
- **功能描述**: 导出计划中某个测评的题目统计
- **请求参数**:
  - 计划ID（查询参数）
  - 测评ID（查询参数）
- **响应参数**:
  - 统计数据导出文件

#### 3.2.12 获取团体报告

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/schedule/group_report`
- **功能描述**: 获取计划中某个测评的团体报告
- **请求参数**:
  - 计划ID（查询参数）
  - 测评ID（查询参数）
- **响应参数**:
  - 团体报告数据

#### 3.2.13 获取计划班级列表

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/{id}/schedule_classes`
- **功能描述**: 获取计划关联的班级列表
- **请求参数**:
  - `id`: 计划ID，路径参数
- **响应参数**:
  - 班级列表数据

#### 3.2.14 获取计划未完成学生名单

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/{id}/incomplete_students`
- **功能描述**: 获取计划未完成学生名单
- **请求参数**:
  - `id`: 计划ID，路径参数
- **响应参数**:
  - 未完成学生列表

### 3.3 报告管理接口

#### 3.3.1 获取综合报告列表

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/comprehensive_report/list`
- **功能描述**: 获取综合报告列表
- **请求参数**: 无
- **响应参数**:
  - 综合报告列表

#### 3.3.2 批量下载综合报告

- **请求方法**: GET
- **接口路径**: `/v1/assessment/teacher_platform/comprehensive_report/donwload`
- **功能描述**: 批量下载综合报告
- **请求参数**:
  - 报告ID列表（查询参数）
- **响应参数**:
  - 报告ZIP文件下载链接

## 4. 学生端接口

### 4.1 测评列表接口

#### 4.1.1 获取自主探索列表

- **请求方法**: GET
- **接口路径**: `/v1/assessment/student_platform/autonomous_exploration`
- **功能描述**: 获取学生可自主探索的测评列表
- **请求参数**: 无
- **响应参数**:
  - 测评列表，包含测评ID、名称、类型、描述等信息

#### 4.1.2 获取我的测评计划列表

- **请求方法**: GET
- **接口路径**: `/v1/assessment/student_platform/my_schedules`
- **功能描述**: 获取学生参与的测评计划列表
- **请求参数**: 无
- **响应参数**:
  - 计划列表，包含计划ID、名称、状态、创建时间等信息

#### 4.1.3 获取我的测评任务列表

- **请求方法**: GET
- **接口路径**: `/v1/assessment/student_platform/my_tasks/{id}`
- **功能描述**: 获取指定计划下学生的测评任务列表
- **请求参数**:
  - `id`: 计划ID，路径参数
- **响应参数**:
  - 任务列表，包含任务ID、测评信息、完成状态等

#### 4.1.4 获取计划测评列表

- **请求方法**: GET
- **接口路径**: `/v1/assessment/student_platform/schedule/assessments`
- **功能描述**: 获取计划中的测评列表
- **请求参数**:
  - 计划ID（查询参数）
- **响应参数**:
  - 测评列表，包含测评ID、名称、类型、描述等信息

#### 4.1.5 获取自主探索测评列表

- **请求方法**: GET
- **接口路径**: `/v1/assessment/student_platform/assessments`
- **功能描述**: 获取学生可自主探索的测评列表
- **请求参数**: 无
- **响应参数**:
  - 测评列表，包含测评ID、名称、类型、描述等信息

### 4.2 学生画像接口

#### 4.2.1 获取生涯画像结果

- **请求方法**: GET
- **接口路径**: `/v1/assessment/student_platform/portray/shengya_result`
- **功能描述**: 获取学生生涯画像结果
- **请求参数**: 无
- **响应参数**:
  - 生涯画像结果数据

#### 4.2.2 获取关键词

- **请求方法**: GET
- **接口路径**: `/v1/assessment/student_platform/portray/keyword`
- **功能描述**: 获取学生画像关键词
- **请求参数**: 无
- **响应参数**:
  - 关键词列表

#### 4.2.3 获取生涯测评数据

- **请求方法**: GET
- **接口路径**: `/v1/assessment/student_platform/portray/shengya_data`
- **功能描述**: 获取学生生涯测评数据
- **请求参数**: 无
- **响应参数**:
  - 生涯测评数据

## 5. 测评过程接口

#### 5.1 获取测评信息

- **请求方法**: GET
- **接口路径**: `/v1/assessment/process/assessment_info/{id}`
- **功能描述**: 获取测评基本信息，用于开始测评
- **请求参数**:
  - `id`: 测评ID，路径参数
- **响应参数**:
  - 测评基本信息，包含测评ID、名称、类型、描述、时长等

#### 5.2 获取问题列表

- **请求方法**: GET
- **接口路径**: `/v1/assessment/process/questions`
- **功能描述**: 根据测评ID和类型获取问题列表
- **请求参数**:
  - 测评ID（查询参数）
  - 测评类型（查询参数，可选）
- **响应参数**:
  - 问题列表，包含问题ID、题干、选项、类型等信息

#### 5.3 保存答案

- **请求方法**: POST
- **接口路径**: `/v1/assessment/process/answers`
- **功能描述**: 保存测评答案
- **请求参数**:
  - 测评ID
  - 答案数据（问题ID和选择的答案）
- **响应参数**:
  - 保存结果

#### 5.4 获取个人报告

- **请求方法**: GET
- **接口路径**: `/v1/assessment/process/report`
- **功能描述**: 获取测评个人报告
- **请求参数**:
  - 测评ID（查询参数）
  - 学生ID（查询参数，可选）
- **响应参数**:
  - 个人报告数据

#### 5.5 获取团体报告

- **请求方法**: GET
- **接口路径**: `/v1/assessment/process/group_report`
- **功能描述**: 获取测评团体报告
- **请求参数**:
  - 测评ID（查询参数）
  - 班级ID或学生ID列表（查询参数）
- **响应参数**:
  - 团体报告数据

## 6. 公共接口

#### 6.1 预览PDF

- **请求方法**: GET
- **接口路径**: `/v1/assessment/previewPdf`
- **功能描述**: 预览测评报告PDF
- **请求参数**:
  - 报告ID（查询参数）
- **响应参数**:
  - PDF文件或预览链接
- **备注**: 此接口不需要鉴权