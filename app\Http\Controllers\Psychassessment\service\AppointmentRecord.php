<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON>
 * Date: 2024/01/19
 * Time: 17:52
 */
namespace app\psychassessment\service;

class AppointmentRecord{
    protected $AppointmentRecordLogic;
    public function __construct()
    {
        $this->AppointmentRecordLogic = new \app\psychassessment\logic\AppointmentRecord();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://查询

                return $this->AppointmentRecordLogic->get_list();
                break;
            case 'post'://增加

                return $this->AppointmentRecordLogic->add();
                break;
            case 'put'://修改

                return $this->AppointmentRecordLogic->edit();
                break;
            case 'delete'://删除
                return $this->AppointmentRecordLogic->del();
                break;
            default:
                return false;
        }
    }

    public function teacher_info(){
        return $this->AppointmentRecordLogic->teacher_info();
    }

}