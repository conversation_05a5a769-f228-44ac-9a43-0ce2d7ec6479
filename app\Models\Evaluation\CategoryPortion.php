<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 素养占比模型
 */
class CategoryPortion extends Model
{
    protected $table = 'evaluation_category_portions';

    protected $fillable = [
        'question_id',
        'parent_id',
        'category_id',
        'percentage',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'question_id' => 'integer',
        'category_id' => 'integer',
        'percentage' => 'decimal:2',
    ];

    /**
     * 所属题目关联
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class, 'question_id');
    }

    /**
     * 素养类别关联
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Categories::class, 'category_id');
    }
}
