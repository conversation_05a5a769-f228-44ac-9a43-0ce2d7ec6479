<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use App\Services\Evaluation\PapersService;
use App\Http\Requests\Evaluation\PaperRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * 素养测评-试卷管理控制器
 */
class EvaluationPapersController extends Controller
{
    protected $papersService;

    public function __construct(PapersService $papersService)
    {
        $this->papersService = $papersService;
    }

    /**
     * 试卷查询
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function papers(Request $request): JsonResponse
    {
        try {
            $data = $this->papersService->getPapersList($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取试卷列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 试卷添加
     * 
     * @param PaperRequest $request
     * @return JsonResponse
     */
    public function store(PaperRequest $request): JsonResponse
    {
        try {
            $data = $this->papersService->createPaper($request->validated());
            return $this->success($data, '试卷添加成功');
        } catch (\Exception $e) {
            return $this->error('试卷添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 试卷修改
     * 
     * @param PaperRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(PaperRequest $request, int $id): JsonResponse
    {
        try {
            $data = $this->papersService->updatePaper($id, $request->validated());
            return $this->success($data, '试卷修改成功');
        } catch (\Exception $e) {
            return $this->error('试卷修改失败: ' . $e->getMessage());
        }
    }

    /**
     * 试卷删除
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->papersService->deletePaper($id);
            return $this->success(null, '试卷删除成功');
        } catch (\Exception $e) {
            return $this->error('试卷删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取试卷详情
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $data = $this->papersService->getPaperDetail($id);
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取试卷详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 复制试卷
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function copy(Request $request): JsonResponse
    {
        try {
            $data = $this->papersService->copyPaper($request->all());
            return $this->success($data, '试卷复制成功');
        } catch (\Exception $e) {
            return $this->error('试卷复制失败: ' . $e->getMessage());
        }
    }

    /**
     * 试卷预览
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function preview(int $id): JsonResponse
    {
        try {
            $data = $this->papersService->previewPaper($id);
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('试卷预览失败: ' . $e->getMessage());
        }
    }
}
