<?php

namespace App\Http\Controllers\Psychassessment;

use App\Http\Controllers\Controller;
use App\Services\Psychassessment\PageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * 心理评估-页面控制器 - 基于原 ThinkPHP Page 控制器重新实现
 */
class PsychassessmentPageController extends Controller
{
    protected $pageService;

    public function __construct(PageService $pageService)
    {
        $this->pageService = $pageService;
    }

    /**
     * 心理评估首页
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->pageService->getIndexData(auth()->user());
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 报告详情页
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function reportDetail(Request $request): JsonResponse
    {
        try {
            $reportId = $request->input('report_id');
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $memberId = $request->input('member_id');
            
            $data = $this->pageService->getReportDetailData($reportId, $planId, $surveyType, $memberId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取页面配置
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPageConfig(Request $request): JsonResponse
    {
        try {
            $pageType = $request->input('page_type', 'index');
            $data = $this->pageService->getPageConfig($pageType);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新页面配置
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updatePageConfig(Request $request): JsonResponse
    {
        try {
            $pageType = $request->input('page_type');
            $config = $request->input('config');
            
            $data = $this->pageService->updatePageConfig($pageType, $config, auth()->user());
            return $this->success($data, '更新成功');
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取导航菜单
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getNavigation(Request $request): JsonResponse
    {
        try {
            $data = $this->pageService->getNavigation(auth()->user());
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取统计概览
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStatisticsOverview(Request $request): JsonResponse
    {
        try {
            $data = $this->pageService->getStatisticsOverview(auth()->user());
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取最近活动
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getRecentActivities(Request $request): JsonResponse
    {
        try {
            $limit = $request->input('limit', 10);
            $data = $this->pageService->getRecentActivities(auth()->user(), $limit);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取快捷操作
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getQuickActions(Request $request): JsonResponse
    {
        try {
            $data = $this->pageService->getQuickActions(auth()->user());
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取通知消息
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getNotifications(Request $request): JsonResponse
    {
        try {
            $limit = $request->input('limit', 5);
            $data = $this->pageService->getNotifications(auth()->user(), $limit);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 标记通知为已读
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function markNotificationRead(Request $request): JsonResponse
    {
        try {
            $notificationId = $request->input('notification_id');
            $data = $this->pageService->markNotificationRead($notificationId, auth()->user());
            return $this->success($data, '标记成功');
        } catch (\Exception $e) {
            return $this->error('标记失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取用户偏好设置
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getUserPreferences(Request $request): JsonResponse
    {
        try {
            $data = $this->pageService->getUserPreferences(auth()->user());
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新用户偏好设置
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateUserPreferences(Request $request): JsonResponse
    {
        try {
            $preferences = $request->input('preferences');
            $data = $this->pageService->updateUserPreferences(auth()->user(), $preferences);
            return $this->success($data, '更新成功');
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取帮助文档
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getHelpDocuments(Request $request): JsonResponse
    {
        try {
            $category = $request->input('category');
            $data = $this->pageService->getHelpDocuments($category);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 搜索功能
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $keyword = $request->input('keyword');
            $type = $request->input('type', 'all'); // all, survey, report, student
            
            $data = $this->pageService->search($keyword, $type, auth()->user());
            return $this->success($data, '搜索成功');
        } catch (\Exception $e) {
            return $this->error('搜索失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取系统公告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getAnnouncements(Request $request): JsonResponse
    {
        try {
            $data = $this->pageService->getAnnouncements();
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取版本信息
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getVersionInfo(Request $request): JsonResponse
    {
        try {
            $data = $this->pageService->getVersionInfo();
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }
}
