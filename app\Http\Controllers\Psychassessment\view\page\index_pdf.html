<?php
//$url = config('https_url');//新模板不认识这个
$url = 'https://s.yishengya.cn';
?>
<html>
<link rel="stylesheet" href="{$url}/public/new_site/dist/common/css/menu-nav.css">
<link rel="stylesheet" href="{$url}/public/new_site/dist/common/css/comm.min.css">
<link rel="stylesheet" href="{$url}/public/new_site/dist/common/css/g-iconfont.css">
<link rel="stylesheet" href="{$url}/public/new_site/dist/common/css/g-navbar.css">
<link rel="stylesheet" href="{$url}/public/new_site/dist/common/css/common-style.css">

<script>
    var $data=<?php echo json_encode($data);?>;
    console.log($data);
    if (!Function.prototype.bind) {
        Function.prototype.bind = function(oThis) {
            if (typeof this !== 'function') {
                // closest thing possible to the ECMAScript 5
                // internal IsCallable function
                throw new TypeError('Function.prototype.bind - what is trying to be bound is not callable');
            }

            var aArgs   = Array.prototype.slice.call(arguments, 1),
                fToBind = this,
                fNOP    = function() {},
                fBound  = function() {
                    // this instanceof fBound === true时,说明返回的fBound被当做new的构造函数调用
                    return fToBind.apply(this instanceof fBound
                        ? this
                        : oThis,
                        // 获取调用时(fBound)的传参.bind 返回的函数入参往往是这么传递的
                        aArgs.concat(Array.prototype.slice.call(arguments)));
                };

            // 维护原型关系
            if (this.prototype) {
                // Function.prototype doesn't have a prototype property
                fNOP.prototype = this.prototype;
            }
            // 下行的代码使fBound.prototype是fNOP的实例,因此
            // 返回的fBound若作为new的构造函数,new生成的新对象作为this传入fBound,新对象的__proto__就是fNOP的实例
            fBound.prototype = new fNOP();

            return fBound;
        };
    }
</script>
<div id="app" data-env="/setting/user"></div>
<script type="module" crossorigin src="{$url}/public/dist/pc/v3/js/main-cb337e2e.js"></script>
<link rel="stylesheet" href="{$url}/public/dist/pc/v3/css/vendor-6d2e0fdf.css">
</html>