<?php

namespace App\Services\Tool;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;

// 默认列宽，无样式的Excel导出
class ExcelExportService implements FromCollection, WithTitle
{
    public $data; // 导出数据
    public $header; // 表头
    public $title; // sheet名称
    public $dataTransformCallback; // 数据转换回调函数

    // 示例
//    $header = [
//        'updater' => '最后更新人',
//        'updated_at' => '最后更新时间'
//    ];
//    $dataTransformCallback = function ($row) {
//        $row['is_need_dine'] = $row['is_need_dine'] == 1 ? '需要' : '不需要';
//        return $row;
//    };


    // 构造函数
    public function __construct($data, $header, $title, callable $dataTransformCallback = null)
    {
        $this->data = $data;
        $this->header = $header;
        $this->title = $title;
        $this->dataTransformCallback = $dataTransformCallback;
    }

    // 从集合中导出数据
    public function collection()
    {
        return new Collection($this->createData());
    }

    // 设置sheet名称
    public function title(): string
    {
        return $this->title;
    }

    public function createData(): array
    {
        $data = $this->data;
        $header = $this->header;
        $result = [];
        $result[] = $header;
        //获取表头字段名称
        foreach ($header as $key => $value) {
            $keyArr[] = $key;
        }

        //获取数据
        foreach ($data as $key => $value) {
            // 应用数据转换回调函数
            if ($this->dataTransformCallback) {
                $value = ($this->dataTransformCallback)($value);
            }
            $row = [];
            for ($i = 0; $i < count($keyArr); $i++) {
                $key = $keyArr[$i];
                $row = array_merge($row, [$keyArr[$i] => !is_array($value) ? $value->$key : $value[$key]]);
            }
            array_push($result, $row);
        }
        return $result;
    }

}
