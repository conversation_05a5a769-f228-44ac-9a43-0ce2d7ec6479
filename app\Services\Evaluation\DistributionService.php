<?php

namespace App\Services\Evaluation;

use App\Models\Evaluation\Distribution;
use App\Models\Evaluation\DistributionDetail;
use App\Models\Evaluation\DistributionTeachersStudents;
use App\Models\Evaluation\Papers;
use App\Models\Evaluation\EvaluationLog;
use App\Models\School\System\School;
use App\Models\School\System\Teacher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

/**
 * 分发管理服务类
 */
class DistributionService
{
    protected $distributionModel;
    protected $distributionDetailModel;
    protected $distributionTeachersStudents;
    protected $paperModel;
    protected $evaluationLogModel;
    protected $schoolModel;
    protected $teacherModel;
    protected $user;

    public function __construct(
        Distribution $distributionModel,
        DistributionDetail $distributionDetailModel,
        DistributionTeachersStudents $distributionTeachersStudents,
        Papers $paperModel,
        EvaluationLog $evaluationLogModel,
        School $schoolModel,
        Teacher $teacherModel
    ) {
        $this->distributionModel = $distributionModel;
        $this->distributionDetailModel = $distributionDetailModel;
        $this->distributionTeachersStudents = $distributionTeachersStudents;
        $this->paperModel = $paperModel;
        $this->evaluationLogModel = $evaluationLogModel;
        $this->schoolModel = $schoolModel;
        $this->teacherModel = $teacherModel;
        $this->user = Auth::user();
    }

    /**
     * 获取分发列表
     * 
     * @param array $params
     * @return array
     */
    public function getDistributionList(array $params): array
    {
        if (!empty($params['id'])) {
            $data = $this->distributionModel->with(['details', 'papers'])->find($params['id']);
            return $data ? $data->toArray() : [];
        }

        $query = $this->distributionModel->query();

        // 应用筛选条件
        $this->applyFilters($query, $params);

        $page = $params['page'] ?? 1;
        $pagesize = $params['pagesize'] ?? 15;

        $total = $query->count();
        $distributions = $query->with(['papers', 'creator'])
            ->orderBy('id', 'desc')
            ->offset(($page - 1) * $pagesize)
            ->limit($pagesize)
            ->get();

        return [
            'list' => $distributions,
            'total' => $total,
            'page' => $page,
            'pagesize' => $pagesize
        ];
    }

    /**
     * 创建分发
     * 
     * @param array $data
     * @return Distribution
     */
    public function createDistribution(array $data): Distribution
    {
        return DB::transaction(function () use ($data) {
            // 权限判断
            $schoolIds = $this->getSchoolIds($data);

            $distributionData = [
                'title' => $data['title'],
                'paper_ids' => $data['paper_ids'],
                'grade_id' => $data['grade_id'] ?? 0,
                'class_ids' => $data['class_ids'] ?? '',
                'member_ids' => $data['member_ids'] ?? '',
                'distribution_by' => $this->user->id,
                'role_id' => $this->user->role_id ?? 0,
                'from_id' => $data['from_id'] ?? 0,
                'school_ids' => $schoolIds,
                'status' => 0,
            ];

            $distribution = $this->distributionModel->create($distributionData);

            // 处理分发详情
            $this->createDistributionDetails($distribution->id, $data);

            return $distribution;
        });
    }

    /**
     * 更新分发
     * 
     * @param int $id
     * @param array $data
     * @return Distribution
     */
    public function updateDistribution(int $id, array $data): Distribution
    {
        return DB::transaction(function () use ($id, $data) {
            $distribution = $this->distributionModel->findOrFail($id);

            $updateData = [
                'title' => $data['title'] ?? $distribution->title,
                'paper_ids' => $data['paper_ids'] ?? $distribution->paper_ids,
            ];

            // 权限控制：只有老师和教务能指定测评人和批阅人
            if (isset($this->user->school_id) && $this->user->school_id && 
                in_array($this->user->role_source_id, [2, 3])) {
                $updateData['grade_id'] = $data['grade_id'] ?? $distribution->grade_id;
                $updateData['class_ids'] = $data['class_ids'] ?? $distribution->class_ids;
                $updateData['member_ids'] = $data['member_ids'] ?? $distribution->member_ids;
                $updateData['from_id'] = $data['from_id'] ?? $distribution->from_id;
            }

            // 超级管理员可以修改学校
            if ($this->isAdmin()) {
                $updateData['school_ids'] = $data['school_ids'] ?? $distribution->school_ids;
            }

            $distribution->update($updateData);

            // 更新分发详情
            $this->updateDistributionDetails($id, $data);

            return $distribution;
        });
    }

    /**
     * 删除分发
     * 
     * @param int $id
     * @return bool
     */
    public function deleteDistribution(int $id): bool
    {
        $distribution = $this->distributionModel->findOrFail($id);

        // 检查是否有答题记录
        $hasAnswers = $this->evaluationLogModel->where('distribution_id', $id)->exists();
        if ($hasAnswers) {
            throw new \Exception('该分发已有答题记录，无法删除');
        }

        return $distribution->update(['status' => -1]);
    }

    /**
     * 更新分发状态
     * 
     * @param int $id
     * @param int $status
     * @return bool
     */
    public function updateStatus(int $id, int $status): bool
    {
        $distribution = $this->distributionModel->findOrFail($id);
        return $distribution->update(['status' => $status]);
    }

    /**
     * 获取分发详情
     * 
     * @param int $id
     * @return array
     */
    public function getDistributionDetail(int $id): array
    {
        $distribution = $this->distributionModel->with([
            'details.paper',
            'teacherStudents.teacher',
            'papers',
            'creator'
        ])->findOrFail($id);

        return $distribution->toArray();
    }

    /**
     * 获取分发的学生列表
     * 
     * @param int $id
     * @return array
     */
    public function getDistributionStudents(int $id): array
    {
        $distribution = $this->distributionModel->findOrFail($id);
        
        if (empty($distribution->member_ids)) {
            return [];
        }

        $memberIds = explode(',', $distribution->member_ids);
        
        $students = DB::table('student as s')
            ->join('member as m', 's.member_id', '=', 'm.id')
            ->join('grade as g', 's.grade_id', '=', 'g.id')
            ->join('class as c', 's.class_id', '=', 'c.id')
            ->whereIn('s.member_id', $memberIds)
            ->where('s.status', 0)
            ->select([
                's.id as student_id',
                's.student_no',
                'm.name as student_name',
                'g.grade_name',
                'c.name as class_name'
            ])
            ->get()
            ->toArray();

        return $students;
    }

    /**
     * 获取分发统计
     * 
     * @param int $id
     * @return array
     */
    public function getDistributionStatistics(int $id): array
    {
        $distribution = $this->distributionModel->findOrFail($id);
        
        $totalStudents = 0;
        if (!empty($distribution->member_ids)) {
            $totalStudents = count(explode(',', $distribution->member_ids));
        }

        $completedCount = $this->evaluationLogModel
            ->where('distribution_id', $id)
            ->where('check_status', 1)
            ->count();

        $pendingCount = $this->evaluationLogModel
            ->where('distribution_id', $id)
            ->where('check_status', 0)
            ->count();

        $avgScore = $this->evaluationLogModel
            ->where('distribution_id', $id)
            ->where('check_status', 1)
            ->avg('score');

        return [
            'total_students' => $totalStudents,
            'completed_count' => $completedCount,
            'pending_count' => $pendingCount,
            'completion_rate' => $totalStudents > 0 ? round(($completedCount / $totalStudents) * 100, 2) : 0,
            'avg_score' => $avgScore ? round($avgScore, 2) : 0
        ];
    }

    /**
     * 批量分配老师
     * 
     * @param int $distributionId
     * @param array $assignments
     * @return bool
     */
    public function assignTeachers(int $distributionId, array $assignments): bool
    {
        return DB::transaction(function () use ($distributionId, $assignments) {
            // 删除原有分配
            $this->distributionTeachersStudents
                ->where('distribution_id', $distributionId)
                ->update(['status' => -1]);

            // 创建新分配
            $insertData = [];
            foreach ($assignments as $assignment) {
                $insertData[] = [
                    'distribution_id' => $distributionId,
                    'paper_id' => $assignment['paper_id'],
                    'teacher_member_id' => $assignment['teacher_member_id'],
                    'member_ids' => implode(',', $assignment['member_ids']),
                    'status' => 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            if (!empty($insertData)) {
                $this->distributionTeachersStudents->insert($insertData);
            }

            return true;
        });
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 应用筛选条件
     * 
     * @param $query
     * @param array $params
     */
    private function applyFilters($query, array $params): void
    {
        $query->where('status', 0);

        if (isset($this->user->school_id) && $this->user->school_id) {
            $query->where(function ($q) {
                $q->where('school_ids', 'like', '%' . $this->user->school_id . '%')
                  ->orWhere('distribution_by', $this->user->id);
            });
        }

        if (!empty($params['title'])) {
            $query->where('title', 'like', '%' . $params['title'] . '%');
        }

        if (!empty($params['grade_id'])) {
            $query->where('grade_id', $params['grade_id']);
        }

        if (!empty($params['school_ids'])) {
            $schoolIds = is_array($params['school_ids']) ? $params['school_ids'] : [$params['school_ids']];
            $query->where(function ($q) use ($schoolIds) {
                foreach ($schoolIds as $schoolId) {
                    $q->orWhere('school_ids', 'like', '%' . $schoolId . '%');
                }
            });
        }

        if (!empty($params['distribution_by'])) {
            $query->where('distribution_by', $params['distribution_by']);
        }
    }

    /**
     * 获取学校ID
     * 
     * @param array $data
     * @return string
     */
    private function getSchoolIds(array $data): string
    {
        if ($this->isAdmin()) {
            $schoolIds = $data['school_ids'] ?? '';
            if (empty($schoolIds)) {
                throw new \Exception('指定学校不能为空');
            }
            return $schoolIds;
        } else {
            return (string)$this->user->school_id;
        }
    }

    /**
     * 检查是否为管理员
     * 
     * @return bool
     */
    private function isAdmin(): bool
    {
        return $this->user->role_id == 999 || $this->user->role_source_id == 1;
    }

    /**
     * 创建分发详情
     * 
     * @param int $distributionId
     * @param array $data
     */
    private function createDistributionDetails(int $distributionId, array $data): void
    {
        if ($this->isAdmin()) {
            // 管理员端操作
            $this->createAdminDistributionDetails($distributionId, $data);
        } else {
            // 教务端操作
            $this->createSchoolDistributionDetails($distributionId, $data);
        }
    }

    /**
     * 创建管理员分发详情
     * 
     * @param int $distributionId
     * @param array $data
     */
    private function createAdminDistributionDetails(int $distributionId, array $data): void
    {
        $paperIds = explode(',', $data['paper_ids']);
        $insertData = [];

        foreach ($paperIds as $paperId) {
            $insertData[] = [
                'distribution_id' => $distributionId,
                'paper_id' => $paperId,
                'status' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        if (!empty($insertData)) {
            $this->distributionDetailModel->insert($insertData);
        }
    }

    /**
     * 创建学校分发详情
     * 
     * @param int $distributionId
     * @param array $data
     */
    private function createSchoolDistributionDetails(int $distributionId, array $data): void
    {
        $studentMemberIds = explode(',', $data['member_ids']);
        $studentNum = count($studentMemberIds);
        $distributionArr = json_decode($data['distribution_arr'], true);

        $detailInsertData = [];
        $teacherStudentData = [];
        $i = 0;

        foreach ($distributionArr as $item) {
            // 录入分发详情表
            $detailInsertData[] = [
                'distribution_id' => $distributionId,
                'paper_id' => $item['paper_id'],
                'teacher_member_ids' => $item['teacher_ids'],
                'start_time' => $item['start_time'],
                'exam_duration' => $item['exam_duration'],
                'status' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // 给每个老师平均分配负责批改的学生
            $teacherMemberIds = explode(',', $item['teacher_ids']);
            $teacherNum = count($teacherMemberIds);
            $avg = ceil($studentNum / $teacherNum);

            foreach ($teacherMemberIds as $key => $teacherId) {
                $teacherStudentData[] = [
                    'distribution_id' => $distributionId,
                    'paper_id' => $item['paper_id'],
                    'teacher_member_id' => $teacherId,
                    'member_ids' => $this->getTeacherStudentIds($studentMemberIds, $key, $avg, $teacherNum),
                    'status' => 0,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
                $i++;
            }
        }

        if (!empty($detailInsertData)) {
            $this->distributionDetailModel->insert($detailInsertData);
        }

        if (!empty($teacherStudentData)) {
            $this->distributionTeachersStudents->insert($teacherStudentData);
        }
    }

    /**
     * 获取老师负责的学生ID
     * 
     * @param array $studentMemberIds
     * @param int $teacherIndex
     * @param int $avg
     * @param int $teacherNum
     * @return string
     */
    private function getTeacherStudentIds(array $studentMemberIds, int $teacherIndex, int $avg, int $teacherNum): string
    {
        // 最后一个老师截取的不一样，从$teacherIndex*$avg到最后
        if (($teacherIndex + 1) == $teacherNum) {
            $studentIds = array_slice($studentMemberIds, $teacherIndex * $avg);
        } else {
            $studentIds = array_slice($studentMemberIds, $teacherIndex * $avg, $avg);
        }

        return implode(',', $studentIds);
    }

    /**
     * 更新分发详情
     * 
     * @param int $distributionId
     * @param array $data
     */
    private function updateDistributionDetails(int $distributionId, array $data): void
    {
        // 软删除原有详情
        $this->distributionDetailModel
            ->where('distribution_id', $distributionId)
            ->where('status', 0)
            ->update(['status' => -1]);

        if (!$this->isAdmin()) {
            $this->distributionTeachersStudents
                ->where('distribution_id', $distributionId)
                ->where('status', 0)
                ->update(['status' => -1]);
        }

        // 创建新详情
        $this->createDistributionDetails($distributionId, $data);
    }
}
