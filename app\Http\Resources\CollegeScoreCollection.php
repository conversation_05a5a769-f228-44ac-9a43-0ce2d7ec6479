<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CollegeScoreCollection extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->ID,
            'college_id' => $this->CollegeID,
            'college' => $this->College,
            'college_code' => $this->CollegeCode,
            'year' => $this->Year,
            'liberalScience' => $this->LiberalScience,
            'phase' => $this->Phase,
            'phase_name' => $this->PhaseName,
            'score' => $this->Score,
            'rank' => $this->Rank,
            'highest_score' => $this->HighestScore,
            'avg_score' => $this->AvgScore,
            'lowest_score' => $this->LowestScore,
            'real_count' => $this->RealCount,
            'college_remark' => $this->CollegeRemark,
            'province_id' => $this->ProvinceId,
            'course' => (isset($this->Course) ? ($this->Course) : '') . (isset($this->CourseSecond) ? ('+' . $this->CourseSecond) : ''),
        ];
    }
}
