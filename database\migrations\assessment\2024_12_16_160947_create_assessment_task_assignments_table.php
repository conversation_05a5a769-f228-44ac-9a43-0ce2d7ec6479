<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_task_assignments', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('old_session_id')->comment('老表的测评唯一标识');
            $table->unsignedInteger('school_id')->comment('学校ID便于统计学校学生总数');
            $table->unsignedInteger('assessment_task_id')->comment('测评任务ID');
            $table->unsignedInteger('student_class_id')->comment('学生和班级关系表ID');
            $table->unsignedInteger('student_id')->comment('学生ID');
            $table->unsignedInteger('duration')->comment('时长');
            $table->json('results')->comment('测评结果');
            $table->string('pdf_url')->nullable()->comment('报告地址');
            $table->string('creator', 20)->nullable()->comment('添加人');
            $table->softDeletes();
            $table->timestamps();
            $table->index(['school_id','student_id','assessment_id'], 'idx_school_student_assessment');
            $table->index('assessment_task_id');
        });
        DB::statement("ALTER TABLE `assessment_task_assignments` comment '测评任务指定参与人详情表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_task_assignments');
    }
};
