<?php

namespace App\Services\School\Assessment\Score;

use App\Services\School\Assessment\Score\ScoreServiceInterface;
use Illuminate\Container\Container;

class ScoreServiceFactory
{
    private const ASSESSMENT_MAPPING = [
        1 => ['class' => 'Career\AdaptationService', 'name' => '学习生活适应性'],
        2 => ['class' => 'Career\IntelligenceService', 'name' => '智能评估（高中）'],
        3 => ['class' => 'Career\PersonalityService', 'name' => '性格评估'],
        4 => ['class' => 'Career\InterestService', 'name' => '兴趣评估（高中）'],
        5 => ['class' => 'Career\DevelopmentService', 'name' => '生涯发展水平评估(高中)'],
        6 => ['class' => 'Career\IntelligenceService', 'name' => '智能评估（初中）'],
        7 => ['class' => 'Career\DevelopmentService', 'name' => '生涯发展水平评估（初中版）'],
        8 => ['class' => 'Career\InterestService', 'name' => '兴趣测评(初中版)'],
        9 => ['class' => 'Capability\LearningAbilityService', 'name' => '学习力测评'],
        10 => ['class' => 'Capability\CriticalThinkingService', 'name' => '批判性思维能力'],
        11 => ['class' => 'Capability\ProblemSolvingService', 'name' => '问题解决能力'],
        12 => ['class' => 'Capability\CreativeThinkingService', 'name' => '创造思维倾向'],
        13 => ['class' => 'Capability\CommunicationCollaborationService', 'name' => '沟通与合作能力'],
        14 => ['class' => 'Competency\CreativeThinkingService', 'name' => '创造性思维倾向'],
        15 => ['class' => 'Competency\CriticalThinkingService', 'name' => '批判性思维能力'],
        16 => ['class' => 'Competency\CommunicationCollaborationService', 'name' => '沟通与合作能力'],
        17 => ['class' => 'Competency\ProblemSolvingService', 'name' => '问题解决能力(高中)'],
        18 => ['class' => 'Competency\LearningAbilityService', 'name' => '学习力(高中)'],
        19 => ['class' => 'Competency\ProblemSolvingService', 'name' => '问题解决能力(初中)'],
        20 => ['class' => 'Competency\LearningAbilityService', 'name' => '学习力(初中)'],
        21 => ['class' => 'Psychology\ConfidenceService', 'name' => '自信心评估'],
        22 => ['class' => 'Psychology\SelfAwarenessService', 'name' => '自我意识评估'],
        23 => ['class' => 'Psychology\AnxietyService', 'name' => '焦虑评估'],
        24 => ['class' => 'Subject\SubjectInterestService', 'name' => '学科兴趣'],
    ];

    public static function create(int $assessmentId): ScoreServiceInterface
    {
        if (!isset(self::ASSESSMENT_MAPPING[$assessmentId])) {
            throw new \Exception('无效的测评类型');
        }

        $serviceClass = "App\\Services\\School\\Assessment\\Score\\" .
            self::ASSESSMENT_MAPPING[$assessmentId]['class'];

        if (!class_exists($serviceClass)) {
            throw new \Exception('测评服务不存在');
        }

        return Container::getInstance()->make($serviceClass);
    }
}