<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_career_questions', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('assessment_id')->comment('测评类型ID');
            $table->unsignedInteger('old_question_id')->comment('老题目ID');
            $table->tinyInteger('is_normal')->default(0)->comment('是否为测谎题目 1:正常题 0:测谎题');
            $table->string('content')->comment('题目内容');
            $table->unsignedInteger('number')->comment('题号');
            $table->string('dimension_name')->comment('维度名称');
            $table->string('dimension_code')->comment('维度代码');
            $table->json('options')->comment('题目选项');
            $table->timestamps();
        });
        DB::statement("ALTER TABLE `assessment_career_questions` comment '生涯测评问题量表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_career_questions');
    }
};
