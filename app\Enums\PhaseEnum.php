<?php

namespace App\Enums;

/**
 * 学历阶段枚举
 * 
 * 定义系统中使用的学历阶段类型
 */
enum PhaseEnum: int
{
    /**
     * 本科
     */
    case UNDERGRADUATE = 1;
    
    /**
     * 专科
     */
    case COLLEGE = 4;
    
    /**
     * 获取学历阶段的中文名称
     *
     * @return string 学历阶段的中文名称
     */
    public function getDisplayName(): string
    {
        return match($this) {
            self::UNDERGRADUATE => '本科',
            self::COLLEGE => '专科',
        };
    }
    
    /**
     * 获取对应的专业表名前缀
     *
     * @return string 专业表名前缀
     */
    public function getMajorTablePrefix(): string
    {
        return match($this) {
            self::UNDERGRADUATE => 'Major_BK',
            self::COLLEGE => 'Major_ZK',
        };
    }
    
    /**
     * 从整数值获取枚举实例
     *
     * @param int $value 整数值
     * @return self|null 对应的枚举实例，如果不存在则返回 null
     */
    public static function fromInt(int $value): ?self
    {
        return self::tryFrom($value);
    }
}
