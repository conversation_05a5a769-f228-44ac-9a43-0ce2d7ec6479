<?php

namespace App\Http\Controllers\Psychassessment;

use App\Http\Controllers\Controller;
use App\Services\Psychassessment\StatisticsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * 心理评估-统计分析控制器 - 基于原 ThinkPHP Statistics 控制器重新实现
 */
class PsychassessmentStatisticsController extends Controller
{
    protected $statisticsService;

    public function __construct(StatisticsService $statisticsService)
    {
        $this->statisticsService = $statisticsService;
    }

    /**
     * 获取测评计划内学生列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getMemberScoreList(Request $request): JsonResponse
    {
        try {
            $params = $request->all();
            $data = $this->statisticsService->getMemberScoreList($params);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取完成数据和维度与code对应关系
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStatisticAnalysis(Request $request): JsonResponse
    {
        try {
            $params = $request->all();
            $data = $this->statisticsService->getStatisticAnalysis($params);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取班级统计数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getClassStatistics(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            
            $data = $this->statisticsService->getClassStatistics($planId, $surveyType);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取年级统计数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getGradeStatistics(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            
            $data = $this->statisticsService->getGradeStatistics($planId, $surveyType);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取学校统计数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getSchoolStatistics(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            
            $data = $this->statisticsService->getSchoolStatistics($planId, $surveyType);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取维度分析数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getDimensionAnalysis(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $dimension = $request->input('dimension');
            
            $data = $this->statisticsService->getDimensionAnalysis($planId, $surveyType, $dimension);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取预警等级统计
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getWarningLevelStatistics(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            
            $data = $this->statisticsService->getWarningLevelStatistics($planId, $surveyType);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取异常项目统计
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getAbnormalStatistics(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            
            $data = $this->statisticsService->getAbnormalStatistics($planId, $surveyType);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出统计数据
     * 
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|JsonResponse
     */
    public function exportStatistics(Request $request)
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $exportType = $request->input('export_type', 'excel'); // excel, pdf
            
            return $this->statisticsService->exportStatistics($planId, $surveyType, $exportType);
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取趋势分析数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getTrendAnalysis(Request $request): JsonResponse
    {
        try {
            $params = $request->all();
            $data = $this->statisticsService->getTrendAnalysis($params);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取对比分析数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getComparisonAnalysis(Request $request): JsonResponse
    {
        try {
            $planIds = $request->input('plan_ids');
            $surveyType = $request->input('survey_type');
            
            $data = $this->statisticsService->getComparisonAnalysis($planIds, $surveyType);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取分布统计数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getDistributionStatistics(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $groupBy = $request->input('group_by', 'class'); // class, grade, gender
            
            $data = $this->statisticsService->getDistributionStatistics($planId, $surveyType, $groupBy);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取相关性分析数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getCorrelationAnalysis(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $surveyTypes = $request->input('survey_types');
            
            $data = $this->statisticsService->getCorrelationAnalysis($planId, $surveyTypes);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取综合分析报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getComprehensiveReport(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $data = $this->statisticsService->getComprehensiveReport($planId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }
}
