<?php
namespace App\Services;

class QwenService extends BaseService
{
    public function qwenMax($question) {
        // 请求地址
        $url = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';

        // 构造请求体
        $payload = json_encode([
            "model" => "qwen-max",
            "parameters" => [
                "enable_search" => true
            ],
            "input" => [
                "messages" => [
                    [
                        "role" => "user",
                        "content" => $question
                    ]
                ]
            ]
        ]);

        // 设置请求头
        $headers = [
            'Authorization: Bearer sk-snPk7e5lQy',
            'Content-Type: application/json',
            // 'X-DashScope-SSE: enable'
        ];

        // 初始化 cURL
        $ch = curl_init($url);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 300);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 如需验证证书请设为 true

        // 记录开始时间
        $start = microtime(true);

        // 执行请求
        $response = curl_exec($ch);

        // 检查是否出错
        if (curl_errno($ch)) {
            echo 'cURL 错误: ' . curl_error($ch);
        }

        // 关闭连接
        curl_close($ch);

        // 记录结束时间
        $end = microtime(true);
        $duration = $end - $start;
        // printf("函数执行耗时：%.4f 秒\n", $duration);

        // 打印返回内容
        // echo $response;

        return $response;
    }

    // 示例调用：
    // $messages = [
    //     ["role" => "user", "content" => "你好，给我讲个故事"]
    // ];
    // qwen_max($messages);
}
?>
