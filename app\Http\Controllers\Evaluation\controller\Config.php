<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */
namespace app\evaluation\controller;
use app\login\controller\ApiAuth;

/**
 * Class Questions
 * @package app\evaluation\controller
 */
class Config extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->Config = new \app\evaluation\service\Config();
    }
    /**
     * 模块：素养测评-配置信息
     * @SWG\get(path="/evaluation/Config",
     *   tags={"素养测评-素养类别设置:Config"},
     *   summary="素养测评相关配置信息",
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function index(){
        $data = $this->Config->get_config();
        apiReturn($data);
    }

}