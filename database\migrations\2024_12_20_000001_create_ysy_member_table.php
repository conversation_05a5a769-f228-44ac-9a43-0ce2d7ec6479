<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 使用sync_mysql连接创建ysy_member表
        Schema::connection('sync_mysql')->create('ysy_member', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50)->comment('姓名');
            $table->string('username', 50)->unique()->comment('用户名');
            $table->string('password')->comment('密码');
            $table->string('mobile', 20)->nullable()->comment('手机号');
            $table->tinyInteger('gender')->default(1)->comment('性别1男2女');
            $table->integer('school_id')->comment('学校ID');
            $table->string('role_id', 50)->comment('角色ID，格式：0,角色类型,0');
            $table->tinyInteger('step')->default(0)->comment('步骤状态');
            $table->timestamps();
            
            // 索引
            $table->index('school_id');
            $table->index('role_id');
            $table->index('step');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('sync_mysql')->dropIfExists('ysy_member');
    }
};
