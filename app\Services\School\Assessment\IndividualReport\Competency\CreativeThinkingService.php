<?php
namespace App\Services\School\Assessment\IndividualReport\Competency;

/**
 * 创造性思维能力个人报告服务类
 * 
 * 该类用于生成创造性思维能力的个人评估报告，包括维度分析、等级计算和改进建议
 */
class CreativeThinkingService extends AbstractCompetencyReportService
{
    /**
     * 改进指数限制
     * 
     * @var int
     */
    private const IMPROVEMENT_INDEX_LIMIT = 5;

    /**
     * 获取配置键名
     * 
     * @return string 配置键名
     */
    protected function getConfigKey(): string
    {
        return 'creative_thinking';
    }
    
    /**
     * 获取改进指数限制
     * 
     * @return int 改进指数限制
     */
    protected function getImprovementIndexLimit(): int
    {
        return self::IMPROVEMENT_INDEX_LIMIT;
    }
}