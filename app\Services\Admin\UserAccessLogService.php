<?php

namespace App\Services\Admin;

use App\Models\Admin\UserAccessLog;
use App\Models\Permission;
use App\Services\BaseService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class UserAccessLogService extends BaseService
{
    public function listBuilder(Request $request){
        $created_at = $request->input('created_at');
        $creator = $request->input('creator');
        $permission_name = $request->input('permission_name');
        $parameter = $request->input('parameter');
        $builder = UserAccessLog
            ::when($permission_name, fn($query) => $query->where('permission_name', $permission_name))
            ->when($creator, fn($query) => $query->where('creator', $creator))
            ->when($parameter, fn($query) => $query->where('parameters', 'like', "%$parameter%"))
            ->when($created_at, fn($query) => $query->whereBetween('created_at', searchBetweenDate($created_at)));
        return $builder;
    }

    public function store(Request $request, Permission $permission): void
    {
        $userAccessLog = new UserAccessLog();
        $userAccessLog->method = $request->method();
        $userAccessLog->url = $request->url();
//        $userAccessLog->permission_id = $permission->id;
        $userAccessLog->permission_name = $permission->name;
        $userAccessLog->parameters = $request->all();
        $userAccessLog->creator = $request->user() ? $request->user()->real_name : '';
        $userAccessLog->ip = $request->ip();
        $userAccessLog->api_token = $request->user() ? $request->user()->api_token : '';
        $userAccessLog->created_at = Carbon::now();
        $userAccessLog->save();
    }

}
