<?php

namespace App\Models\School\Assessment;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssessmentTask extends Model
{
    use HasFactory;
    use SoftDeletes;
    
    protected $fillable = [
        'assessment_schedule_id',
        'assessment_id',
    ];

    protected $hidden = [
        'old_survey_id',
        'old_times',
        'old_grade_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];
    
    /**
     * 获取任务所属的计划
     */
    public function schedule()
    {
        return $this->belongsTo(AssessmentSchedule::class, 'assessment_schedule_id');
    }
    
    /**
     * 获取任务的所有分配
     */
    public function assignments()
    {
        return $this->hasMany(AssessmentTaskAssignment::class, 'assessment_task_id');
    }
    
    /**
     * 获取任务关联的测评
     */
    public function assessment()
    {
        return $this->belongsTo(Assessment::class, 'assessment_id');
    }

}
