# 数据同步服务重构说明

## 概述

原来的DataSyncService类变得过于庞大和复杂，包含了学校、班级、教师、学生等多种数据的同步逻辑。为了提高代码的可维护性和可扩展性，我们将其拆分为多个专门的同步服务类。

## 重构架构

### 1. 服务拆分

| 服务类 | 职责 | 文件位置 |
|--------|------|----------|
| SchoolSyncService | 学校数据同步 | app/Services/DataSync/SchoolSyncService.php |
| ClassSyncService | 班级数据同步 | app/Services/DataSync/ClassSyncService.php |
| TeacherSyncService | 教师数据同步 | app/Services/DataSync/TeacherSyncService.php |
| StudentSyncService | 学生数据同步 | app/Services/DataSync/StudentSyncService.php |
| DataSyncServiceNew | 统一入口服务 | app/Services/DataSync/DataSyncServiceNew.php |

### 2. 依赖关系

```
DataSyncServiceNew (统一入口)
├── SchoolSyncService (基础服务)
├── ClassSyncService (依赖 SchoolSyncService)
├── TeacherSyncService (依赖 SchoolSyncService)
└── StudentSyncService (依赖 SchoolSyncService, ClassSyncService)
```

## 各服务详细说明

### 1. SchoolSyncService (学校同步服务)

**主要功能：**
- 学校数据同步
- 校区信息查询
- 同步数据库学校ID映射

**核心方法：**
```php
public function syncSchool(array $schoolData): array
public function syncBatchSchools(array $schoolsData): array
public function getSyncSchoolId(int $originalSchoolId): ?int
public function getCampusInfo(int $campusId): array
```

### 2. ClassSyncService (班级同步服务)

**主要功能：**
- 班级数据同步
- 年级ID查询和映射
- 班级ID查询和映射

**核心方法：**
```php
public function syncClass(array $classData): array
public function syncBatchClasses(array $classesData): array
public function getGradeIdByInfo(int $schoolId, string $gradeName, int $gradeSort): ?int
public function getClassIdByInfo(int $schoolId, int $gradeId, string $className): ?int
```

### 3. TeacherSyncService (教师同步服务)

**主要功能：**
- 教师数据同步到ysy_member和ysy_teacher表
- 教师角色处理
- 教师更新同步

**核心方法：**
```php
public function syncSingleTeacher($request): array
public function syncTeacher(array $teacherData): array
public function syncBatchTeachersUpdate(array $teachers): array
public function syncSingleTeacherUpdate(\App\Models\School\System\Teacher $teacher): array
```

### 4. StudentSyncService (学生同步服务)

**主要功能：**
- 学生数据同步到ysy_member和ysy_student表
- 学生班级和年级映射
- 批量学生同步

**核心方法：**
```php
public function syncSingleStudent($request): array
public function syncStudent(array $studentData): array
public function syncBatchStudents($request): array
```

### 5. DataSyncServiceNew (统一入口服务)

**主要功能：**
- 提供统一的同步接口
- 向后兼容原有调用方式
- 服务状态监控

**核心方法：**
```php
// 学校相关
public function syncSchool(array $schoolData): array
public function getCampusInfo(int $campusId): array

// 班级相关
public function syncClass(array $classData): array

// 教师相关
public function syncSingleTeacher($request): array
public function syncBatchTeachersUpdate(array $teachers): array

// 学生相关
public function syncSingleStudent($request): array
public function syncBatchStudents($request): array
```

## 使用方式

### 1. 直接使用专门服务

```php
// 使用学校同步服务
$schoolSyncService = app(SchoolSyncService::class);
$result = $schoolSyncService->syncSchool($schoolData);

// 使用学生同步服务
$studentSyncService = app(StudentSyncService::class);
$result = $studentSyncService->syncSingleStudent($request);
```

### 2. 使用统一入口服务

```php
// 使用统一入口（向后兼容）
$dataSyncService = app(DataSyncServiceNew::class);
$result = $dataSyncService->syncSingleStudent($request);
```

### 3. 在现有代码中的使用

```php
// 在StudentService中
$dataSyncService = app(\App\Services\DataSync\StudentSyncService::class);
$sync_result = $dataSyncService->syncSingleStudent($request);

// 或者使用统一入口
$dataSyncService = app(\App\Services\DataSync\DataSyncServiceNew::class);
$sync_result = $dataSyncService->syncSingleStudent($request);
```

## 迁移步骤

### 1. 注册服务提供者

在 `config/app.php` 中添加：
```php
'providers' => [
    // ...
    App\Providers\DataSyncServiceProvider::class,
],
```

### 2. 更新现有调用

将现有的DataSyncService调用更新为新的服务：

**修改前：**
```php
$dataSyncService = app(\App\Services\DataSync\DataSyncService::class);
```

**修改后：**
```php
// 选项1：使用专门服务
$studentSyncService = app(\App\Services\DataSync\StudentSyncService::class);

// 选项2：使用统一入口
$dataSyncService = app(\App\Services\DataSync\DataSyncServiceNew::class);
```

### 3. 测试验证

确保所有同步功能正常工作：
- 单个学生同步
- 批量学生同步
- 单个教师同步
- 批量教师同步

## 优势

### 1. 代码组织
- **单一职责**：每个服务只负责一种数据类型的同步
- **清晰结构**：代码结构更加清晰，易于理解
- **易于维护**：修改某种数据的同步逻辑不会影响其他数据

### 2. 可扩展性
- **独立扩展**：可以独立扩展某种数据的同步功能
- **新增数据类型**：容易添加新的数据类型同步服务
- **功能增强**：可以为每种数据类型添加专门的功能

### 3. 测试友好
- **单元测试**：可以为每个服务编写独立的单元测试
- **模拟依赖**：容易模拟依赖服务进行测试
- **隔离测试**：测试某个功能不会影响其他功能

### 4. 性能优化
- **按需加载**：只加载需要的同步服务
- **缓存优化**：可以为每种数据类型实现专门的缓存策略
- **并发处理**：可以并发处理不同类型的数据同步

## 注意事项

1. **向后兼容**：保持原有接口的兼容性，避免破坏现有功能
2. **依赖注入**：正确配置服务之间的依赖关系
3. **错误处理**：确保每个服务都有完善的错误处理机制
4. **日志记录**：保持统一的日志记录格式和级别
5. **事务处理**：确保数据同步的事务一致性

## 后续计划

1. **逐步迁移**：逐步将现有代码迁移到新的服务架构
2. **性能监控**：添加性能监控和指标收集
3. **缓存策略**：实现智能缓存策略提高同步效率
4. **异步处理**：考虑实现异步同步处理提高响应速度
5. **API文档**：完善各个服务的API文档
