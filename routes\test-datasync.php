<?php

use Illuminate\Support\Facades\Route;
use App\Services\DataSync\DataSyncService;

/**
 * 数据同步服务测试路由
 * 
 * 用于测试DataSyncService的各项功能
 */

// 测试服务状态
Route::get('/test/datasync/status', function () {
    try {
        $dataSyncService = app(DataSyncService::class);
        $status = $dataSyncService->getServicesStatus();
        
        return response()->json([
            'success' => true,
            'message' => '服务状态检查成功',
            'data' => $status
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => '服务状态检查失败',
            'error' => $e->getMessage()
        ], 500);
    }
});

// 测试方法存在性
Route::get('/test/datasync/methods', function () {
    try {
        $dataSyncService = app(DataSyncService::class);
        
        $methods = [
            // 学校同步方法
            'school_sync' => [
                'syncSchool' => '同步学校数据',
                'syncBatchSchools' => '批量同步学校数据',
                'getCampusInfo' => '获取校区信息'
            ],
            
            // 班级同步方法
            'class_sync' => [
                'syncClass' => '同步班级数据',
                'syncBatchClasses' => '批量同步班级数据'
            ],
            
            // 教师同步方法
            'teacher_sync' => [
                'syncSingleTeacher' => '同步单个教师数据',
                'syncTeacher' => '同步教师数据',
                'syncBatchTeachers' => '批量同步教师数据',
                'syncBatchTeachersUpdate' => '批量同步教师更新',
                'syncSingleTeacherUpdate' => '同步单个教师更新'
            ],
            
            // 学生同步方法
            'student_sync' => [
                'syncSingleStudent' => '同步单个学生数据',
                'syncStudent' => '同步学生数据',
                'syncBatchStudents' => '批量同步学生数据'
            ],
            
            // 通用方法
            'common' => [
                'getServicesStatus' => '获取服务状态',
                'logSyncOperation' => '记录同步操作日志'
            ]
        ];

        $results = [];
        $totalMethods = 0;
        $existingMethods = 0;

        foreach ($methods as $category => $categoryMethods) {
            $results[$category] = [];
            
            foreach ($categoryMethods as $method => $description) {
                $totalMethods++;
                $exists = method_exists($dataSyncService, $method);
                
                if ($exists) {
                    $existingMethods++;
                }
                
                $results[$category][$method] = [
                    'description' => $description,
                    'exists' => $exists,
                    'status' => $exists ? '✅ 可用' : '❌ 不可用'
                ];
            }
        }

        return response()->json([
            'success' => true,
            'message' => '方法检查完成',
            'summary' => [
                'total_methods' => $totalMethods,
                'existing_methods' => $existingMethods,
                'coverage' => round(($existingMethods / $totalMethods) * 100, 2) . '%'
            ],
            'data' => $results
        ]);
        
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => '方法检查失败',
            'error' => $e->getMessage()
        ], 500);
    }
});

// 测试日志功能
Route::get('/test/datasync/log', function () {
    try {
        $dataSyncService = app(DataSyncService::class);
        
        // 测试成功日志
        $dataSyncService->logSyncOperation('测试操作', [
            'test_type' => 'API测试',
            'timestamp' => now(),
            'data' => ['key' => 'value']
        ], true);
        
        // 测试失败日志
        $dataSyncService->logSyncOperation('测试失败操作', [
            'test_type' => 'API测试',
            'timestamp' => now(),
            'error' => '模拟错误'
        ], false);
        
        return response()->json([
            'success' => true,
            'message' => '日志功能测试成功',
            'data' => [
                'success_log' => '成功日志已记录',
                'error_log' => '错误日志已记录'
            ]
        ]);
        
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => '日志功能测试失败',
            'error' => $e->getMessage()
        ], 500);
    }
});

// 测试依赖注入
Route::get('/test/datasync/dependencies', function () {
    try {
        $dataSyncService = app(DataSyncService::class);
        $reflection = new \ReflectionClass($dataSyncService);
        
        $expectedServices = [
            'schoolSyncService' => 'SchoolSyncService',
            'classSyncService' => 'ClassSyncService',
            'teacherSyncService' => 'TeacherSyncService',
            'studentSyncService' => 'StudentSyncService'
        ];

        $results = [];
        $successCount = 0;

        foreach ($expectedServices as $property => $serviceName) {
            $exists = $reflection->hasProperty($property);
            
            if ($exists) {
                $successCount++;
            }
            
            $results[$serviceName] = [
                'property' => $property,
                'injected' => $exists,
                'status' => $exists ? '✅ 注入成功' : '❌ 注入失败'
            ];
        }

        return response()->json([
            'success' => true,
            'message' => '依赖注入检查完成',
            'summary' => [
                'total_services' => count($expectedServices),
                'injected_services' => $successCount,
                'injection_rate' => round(($successCount / count($expectedServices)) * 100, 2) . '%'
            ],
            'data' => $results
        ]);
        
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => '依赖注入检查失败',
            'error' => $e->getMessage()
        ], 500);
    }
});

// 综合测试
Route::get('/test/datasync/all', function () {
    try {
        $dataSyncService = app(DataSyncService::class);
        
        // 1. 服务状态
        $status = $dataSyncService->getServicesStatus();
        
        // 2. 方法检查
        $methods = [
            'syncSchool', 'syncBatchSchools', 'getCampusInfo',
            'syncClass', 'syncBatchClasses',
            'syncSingleTeacher', 'syncTeacher', 'syncBatchTeachers', 'syncBatchTeachersUpdate', 'syncSingleTeacherUpdate',
            'syncSingleStudent', 'syncStudent', 'syncBatchStudents',
            'getServicesStatus', 'logSyncOperation'
        ];
        
        $methodResults = [];
        $existingMethods = 0;
        
        foreach ($methods as $method) {
            $exists = method_exists($dataSyncService, $method);
            if ($exists) $existingMethods++;
            $methodResults[$method] = $exists;
        }
        
        // 3. 依赖注入检查
        $reflection = new \ReflectionClass($dataSyncService);
        $dependencies = ['schoolSyncService', 'classSyncService', 'teacherSyncService', 'studentSyncService'];
        $injectedDependencies = 0;
        
        foreach ($dependencies as $dependency) {
            if ($reflection->hasProperty($dependency)) {
                $injectedDependencies++;
            }
        }
        
        // 4. 日志测试
        $dataSyncService->logSyncOperation('综合测试', ['test' => 'all'], true);
        
        return response()->json([
            'success' => true,
            'message' => '数据同步服务综合测试完成',
            'summary' => [
                'service_status' => '✅ 正常',
                'method_coverage' => round(($existingMethods / count($methods)) * 100, 2) . '%',
                'dependency_injection' => round(($injectedDependencies / count($dependencies)) * 100, 2) . '%',
                'log_function' => '✅ 正常'
            ],
            'details' => [
                'services' => $status,
                'methods' => [
                    'total' => count($methods),
                    'existing' => $existingMethods,
                    'results' => $methodResults
                ],
                'dependencies' => [
                    'total' => count($dependencies),
                    'injected' => $injectedDependencies
                ]
            ],
            'conclusion' => '🎉 数据同步服务拆分成功，所有功能正常运行！'
        ]);
        
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => '综合测试失败',
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});
