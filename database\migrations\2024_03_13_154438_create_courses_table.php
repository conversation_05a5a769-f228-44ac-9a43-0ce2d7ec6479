<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->id();
            $table->string('course_name',20)->comment('科目名称');
            $table->string('code',20)->nullable()->comment('科目编码001 002便于排序');
            $table->string('type',20)->nullable()->comment('科目类型required必修elective选修based校本');
            $table->integer('school_id')->comment('学校ID');
            $table->integer('school_campus_id')->comment('学校校区ID');
            $table->integer('grade_id')->comment('年级ID');
            $table->softDeletes();
            $table->timestamps();
            $table->string('creator',20)->nullable()->comment('创建人');
            $table->string('updater',20)->nullable()->comment('最后更新人');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subjects');
    }
};
