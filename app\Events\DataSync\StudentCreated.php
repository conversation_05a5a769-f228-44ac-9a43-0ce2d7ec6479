<?php

namespace App\Events\DataSync;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 学生创建事件
 */
class StudentCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $studentData;

    /**
     * Create a new event instance.
     */
    public function __construct(array $studentData)
    {
        $this->studentData = $studentData;
    }
}
