<?php
namespace App\Services\School\Assessment\IndividualReport\Competency;

class CriticalThinkingService extends AbstractCompetencyReportService
{
    /**
     * 改进指数限制
     * 
     * @var int
     */
    private const IMPROVEMENT_INDEX_LIMIT = 3;

    /**
     * 获取配置键名
     * 
     * @return string 配置键名
     */
    protected function getConfigKey(): string
    {
        return 'critical_thinking';
    }
    
    /**
     * 获取改进指数限制
     * 
     * @return int 改进指数限制
     */
    protected function getImprovementIndexLimit(): int
    {
        return self::IMPROVEMENT_INDEX_LIMIT;
    }
}
