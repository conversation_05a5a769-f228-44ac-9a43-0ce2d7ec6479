<?php

namespace Database\Seeders\assessment;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UpdateAssessmentIdSeeder extends Seeder
{
    private const SURVEYIDTOASSESSMENTID = [
        1  => 1,
        18 => 2,
        21 => 3,
        28 => 4,
        45 => 5,
        32 => 6,
        53 => 7,
        72 => 8,
        234 => 9,
        258 => 10,
        257 => 11,
        256 => 12,
        255 => 13,
        426 => 14,
        427 => 15,
        428 => 16,
        429 => 17,
        430 => 18,
        431 => 19,
        432 => 20,
        471 => 21,
        472 => 22,
        473 => 23,
        433 => 24,
    ];

    protected int $school_id;

    public function __construct($schoolId = 508)
    {
        $this->school_id = $schoolId;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取需要更新的记录
        $results = DB::table('assessment_task_assignments as assignments')
            ->join('ysy_survey_user_session as session', 'session.session_id', '=', 'assignments.old_session_id')
            ->where('assignments.school_id', $this->school_id)
            ->select('assignments.id', 'assignments.results', 'session.survey_id')
            ->get();
            
        // 遍历结果并更新
        foreach ($results as $item) {
            if (isset(self::SURVEYIDTOASSESSMENTID[$item->survey_id])) {
                DB::table('assessment_task_assignments')
                    ->where('id', $item->id)
                    ->update(['assessment_id' => self::SURVEYIDTOASSESSMENTID[$item->survey_id]]);
            }
        }
    }
}
