<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ModifyPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'old_pass' => 'required',
            'password' => 'required|string|min:8|regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/',
        ];
    }
    
    public function messages(): array
    {
        return [
            'password.regex' => '密码至少8位,必须包含大写字母、小写字母、数字',
            'password.required' => '密码至少8位,必须包含大写字母、小写字母、数字',
            'password.min' => '密码至少8位,必须包含大写字母、小写字母、数字',
        ];
    }
}
