<?php

namespace Database\Seeders;

use App\Models\Admin\Organization;
use App\Models\Partner\Partner;
use App\Models\School\System\School;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class InitAdminUserTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * php artisan make:seed InitAdminUserTableSeeder
     * php artisan db:seed --class=InitAdminUserTableSeeder
     */
    public function run(): void
    {
        // 查看当前数据库
        $dbName = DB::connection()->getDatabaseName();
        if ($dbName === 'shengya_yishengya'){
           // 截断partner相关表数据
//           DB::table('partners')->truncate();
//           DB::table('partner_schools')->truncate();
//
//           // 截断school相关表数据
//           DB::table('schools')->truncate();
//           DB::table('organizations')->truncate();
//           DB::table('organization_has_menus')->truncate();
//
//           DB::table('school_campuses')->truncate();
//           DB::table('school_assessments')->truncate();
//           DB::table('classes')->truncate();
//           DB::table('classes_old')->truncate();
//           DB::table('courses')->truncate();
//
//           DB::table('role_has_menus')->truncate();
//           DB::table('role_has_permissions')->truncate();
//           DB::table('roles')->truncate();
//
//
//
//
////            // 截断学生相关表数据
//           DB::table('students')->truncate();
//           DB::table('student_classes')->truncate();
//           DB::table('student_classes_error')->truncate();
//
//           // 截断老师相关表数据
//           DB::table('teachers')->truncate();
//           DB::table('teacher_view_classes')->truncate();
//
//           // 截断用户相关表数据
//           DB::table('users')->truncate();
//           DB::table('model_has_roles')->truncate();
//           DB::table('user_access_logs')->truncate();
//           DB::table('user_login_logs')->truncate();
//
//           // 截断测评相关表数据
//           DB::table('assessment_schedules')->truncate();
//           DB::table('assessment_tasks')->truncate();
//           DB::table('assessment_task_assignments')->truncate();
//           DB::table('assessment_career_answers')->truncate();
//           DB::table('assessment_capability_answers')->truncate();
//           DB::table('assessment_subject_answers')->truncate();
//           DB::table('assessment_competency_answers')->truncate();
//           DB::table('assessment_psychology_answers')->truncate();







//             // 创建 管理员初始化数据
//             $this->createAdminInitData();
//
//             // 创建 测试教育局 初始化数据
//             $this->createPartnerInitData();
//
//             // 创建 测试学校 初始化数据
//             $this->createSchoolInitData();


        }
    }

    public function createAdminInitData()
    {
        // 创建 管理员 Partner 表数据
        $partner = Partner::create([
            'id' => 1,
            'name' => '后台管理端',
            'province' => '上海',
            'city' => '上海市',
            'district' => '徐汇区',
            'type' => 1,
            'status' => 1,
            'date_start' => '2018-03-07',
            'date_due' => '2099-03-31',
            'created_at' => now(),
            'updated_at' => now(),
            'creator' => '管理员',
            'updater' => '管理员',
        ]);

        // 创建 Organization 表数据
        $organization = Organization::create([
            'model_type' => 'partner',
            'model_id' => $partner->id,
            'org_name' => '后台管理端',
            'created_at' => now(),
            'updated_at' => now(),
            'creator' => '管理员',
            'updater' => '管理员',
        ]);

        // 创建 User 表数据
        $user = User::create([
            'organization_id' => $organization->id,
            'username' => 'admin',
            'real_name' => '后端管理员',
            'gender' => 1,
            'password' => bcrypt('114Study'),
            'status' => 1,
            'phone' => '13800000000',
            'creator' => '管理员',
            'updater' => '管理员',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 创建 Role 表数据
        $role = Role::create([
            'organization_id' => $organization->id,
            'name' => '后端管理员',
            'type' => 999,
            'guard_name' => 'admin',
            'status' => 1,
            'creator' => '管理员',
            'updater' => '管理员',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // 关联用户和角色
        $user->roles()->sync([$role->id]);

        //创建organization_has_menus数据
        $organization_has_menus = [
            ['organization_id' => $organization->id, 'menu_id' => 266, 'parent_id' => 0, 'sort' => 2, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            ['organization_id' => $organization->id, 'menu_id' => 267, 'parent_id' => 266, 'sort' => 1, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            ['organization_id' => $organization->id, 'menu_id' => 268, 'parent_id' => 266, 'sort' => 3, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            ['organization_id' => $organization->id, 'menu_id' => 269, 'parent_id' => 266, 'sort' => 2, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            ['organization_id' => $organization->id, 'menu_id' => 271, 'parent_id' => 0, 'sort' => 1, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            ['organization_id' => $organization->id, 'menu_id' => 272, 'parent_id' => 271, 'sort' => 1, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            ['organization_id' => $organization->id, 'menu_id' => 273, 'parent_id' => 271, 'sort' => 2, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()]
        ];
        DB::table('organization_has_menus')->insert($organization_has_menus);

        //创建role_has_menus数据
        $role_has_menus = [
            [ 'role_id' => $role->id, 'organization_menu_id' => 1, 'creator'=> '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            [ 'role_id' => $role->id, 'organization_menu_id' => 2, 'creator'=> '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            [ 'role_id' => $role->id, 'organization_menu_id' => 3, 'creator'=> '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            [ 'role_id' => $role->id, 'organization_menu_id' => 4, 'creator'=> '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            [ 'role_id' => $role->id, 'organization_menu_id' => 5, 'creator'=> '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            [ 'role_id' => $role->id, 'organization_menu_id' => 6, 'creator'=> '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            [ 'role_id' => $role->id, 'organization_menu_id' => 7, 'creator'=> '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()]
        ];
        DB::table('role_has_menus')->insert($role_has_menus);
    }

    public function createPartnerInitData()
    {
        $partner = Partner::create([
            'id' => 2,
            'name' => '测试教育局',
            'province' => '上海',
            'city' => '上海市',
            'district' => '徐汇区',
            'type' => 1,
            'status' => 1,
            'date_start' => '2018-03-07',
            'date_due' => '2099-03-31',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 创建 Organization 表数据
        $organization = Organization::create([
            'model_type' => 'partner',
            'model_id' => $partner->id,
            'org_name' => '测试教育局',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 创建 User 表数据
        $user = User::create([
            'organization_id' => $organization->id,
            'username' => 'partnerManager',
            'real_name' => '教育局管理员',
            'gender' => 1,
            'password' => bcrypt('114Study'),
            'status' => 1,
            'phone' => '13800000000',
            'creator' => '管理员',
            'updater' => '管理员',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 创建 Role 表数据
        $roleData = [
            'organization_id' => $organization->id,
            'name' => '教育局管理员',
            'type' => 4,
            'guard_name' => 'user',
            'status' => 1,
            'creator' => '管理员',
            'updater' => '管理员',
            'created_at' => now(),
            'updated_at' => now(),
        ];
        $role_id = DB::table('roles')->insertGetId($roleData);
        // 关联用户和角色
        $user->roles()->sync([$role_id]);

        //创建organization_has_menus数据
        $organization_has_menus = [
            ['organization_id' => $organization->id, 'menu_id' => 283, 'parent_id' => 0, 'sort' => 2, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            ['organization_id' => $organization->id, 'menu_id' => 284, 'parent_id' => 283, 'sort' => 1, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            ['organization_id' => $organization->id, 'menu_id' => 285, 'parent_id' => 283, 'sort' => 2, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            ['organization_id' => $organization->id, 'menu_id' => 286, 'parent_id' => 283, 'sort' => 3, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()]
        ];
        DB::table('organization_has_menus')->insert($organization_has_menus);

        $has_menus_id = DB::table('organization_has_menus')->where('organization_id', $organization->id)->pluck('id')->toArray();
        $role_has_menus = [];
        for ($i = 0; $i < count($has_menus_id); $i++) {
            $role_has_menus[] = [
                'role_id' => $role_id,
                'organization_menu_id' => $has_menus_id[$i],
                'creator'=> '管理员',
                'updater' => '管理员',
                'created_at' => now(),
                'updated_at' => now()
            ];
        }
        DB::table('role_has_menus')->insert($role_has_menus);
    }

    public function createSchoolInitData()
    {
        $school = School::find(1049);

        $organization = Organization::query()->where('model_type', 'school')->where('model_id', $school->id)->first();

        // 创建 User 表数据
        $user = User::create([
            'organization_id' => $organization->id,
            'username' => 'schoolManager',
            'real_name' => '学校管理员',
            'gender' => 1,
            'password' => bcrypt('114Study'),
            'status' => 1,
            'phone' => '13800000000',
            'creator' => '管理员',
            'updater' => '管理员',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 创建 Role 表数据
        $roleData = [
            'organization_id' => $organization->id,
            'name' => '学校管理员',
            'type' => 2,
            'guard_name' => 'user',
            'status' => 1,
            'creator' => '管理员',
            'updater' => '管理员',
            'created_at' => now(),
            'updated_at' => now(),
        ];
        $role_id = DB::table('roles')->insertGetId($roleData);
        // 关联用户和角色
        $user->roles()->sync([$role_id]);

        //创建organization_has_menus数据
        $organization_has_menus = [
            ['organization_id' => $organization->id, 'menu_id' => 274, 'parent_id' => 0, 'sort' => 1, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            ['organization_id' => $organization->id, 'menu_id' => 275, 'parent_id' => 274, 'sort' => 1, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            ['organization_id' => $organization->id, 'menu_id' => 276, 'parent_id' => 275, 'sort' => 1, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()],
            ['organization_id' => $organization->id, 'menu_id' => 277, 'parent_id' => 275, 'sort' => 2, 'status' => 1, 'date_start' => '2018-03-07', 'date_due' => '2099-03-31', 'creator' => '管理员', 'updater' => '管理员', 'created_at' => now(), 'updated_at' => now()]
        ];
        DB::table('organization_has_menus')->insert($organization_has_menus);

        $has_menus_id = DB::table('organization_has_menus')->where('organization_id', $organization->id)->pluck('id')->toArray();
        $role_has_menus = [];
        for ($i = 0; $i < count($has_menus_id); $i++) {
            $role_has_menus[] = [
                'role_id' => $role_id,
                'organization_menu_id' => $has_menus_id[$i],
                'creator'=> '管理员',
                'updater' => '管理员',
                'created_at' => now(),
                'updated_at' => now()
            ];
        }
        DB::table('role_has_menus')->insert($role_has_menus);
    }

}
