<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MajorPlanCollection extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'college_id' => $this->CollegeID,
            'college' => $this->College,
            'college_code' => $this->CollegeCode,
            'major' => $this->Major,
            'major_code' => $this->MajorCode,
            'plan_type' => $this->PlanType,
            'phase' => $this->Phase,
            'phase_name' => $this->PhaseName,
            'liberalScience' => $this->LiberalScience,
            'xue_zhi' => $this->Xuezhi,
            'xue_fei' => $this->Xuefei,
            'plan_count' => $this->PlanCount,
            'year' => $this->Year,
            'major_remark' => $this->MajorRemark,
            'course' => (isset($this->Course) ? ($this->Course) : '') . (isset($this->CourseSecond) ? ('+' . $this->CourseSecond) : ''),
        ];
    }
}
