<?php

namespace App\Services\School\Assessment\Score\Career;

use App\Services\School\Assessment\Score\Career\AbstractScoreService;

/**
 * 兴趣评估服务
 * 
 * 用于计算学生的兴趣评估结果
 */
class InterestService extends AbstractScoreService
{
    /**
     * 计算兴趣评估结果
     * 
     * @param array $params 包含评估所需参数的数组
     * @return array 兴趣评估结果数组
     */
    public function calculate($params): array
    {
        $dimension_scores = $this->calculateScores($params);
        $scores = array_column($dimension_scores, 'score');
        array_multisort($scores, SORT_DESC, $dimension_scores);

        return [
            'dimensions' => $dimension_scores, 
            'code' => implode('', array_column($dimension_scores, 'code'))
        ];
    }
}