<?php

namespace Database\Seeders\assessment\psychology;

use Database\Seeders\BaseIncrementalSeeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TaskAssignmentsSeeder extends BaseIncrementalSeeder
{
    protected string $assessment_type = 'psychology';
    private const SURVEYIDTOASSESSMENTID = [
        471 => 21,
        472 => 22,
        473 => 23
    ];
    
    public function __construct($schoolId = 508)
    {
        parent::__construct($schoolId);
    }
    
    protected function getSurveyIds(): array
    {
        return [471, 472, 473];
    }

    protected function getAssessmentIds(): array
    {
        return [21, 22, 23];
    }
    
    protected function executeSeeder(): void
    {
        $lastProcessedId = $this->getLastProcessedId();
        
        // 查询学生测评记录
        $studentSurveyList = DB::connection($this->connect)
        ->table('assessment_schedules as schedules')
        ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
        ->join('survey_user_session as session', function($join) {
            $join->on('session.survey_id', '=', 'tasks.old_survey_id')
                ->on('session.grade_id', '=', 'tasks.old_grade_id')
                ->on('session.times', '=', 'tasks.old_times');
        })
        ->whereIn('tasks.assessment_id', $this->getAssessmentIds())
        ->where('schedules.school_id', $this->school_id)
        ->where('session.school_id', $this->school_id)
        ->whereIn('session.survey_id', $this->getSurveyIds())
        ->where('session.is_delete', 0)
        ->where('session.is_abnormal', 0)
        ->where('session.time_error', 0)
        ->where('session.result', '!=', '')
        ->whereNotNull('session.result')
        ->where('session.id', '>', $lastProcessedId) // 只处理新记录
        ->select([
            'tasks.id',
            'tasks.assessment_id',
            'session.session_id',
            'session.member_id',
            'session.student_id',
            'session.create_time',
            'session.used_time',
            'session.result',
            'session.survey_id',
            'session.pdf_url',
            'session.school_id',
            'session.id as survey_session_id' // 添加survey_session_id用于更新执行日志
        ])
        ->get();

        // 优化查询效率
        $oldStudentIds = $studentSurveyList->pluck('student_id')->unique()->values()->toArray();
        $studentClassIdArr = DB::table('student_classes')
        ->whereIn('old_student_id', $oldStudentIds)
        ->select(['id', 'old_student_id'])
        ->pluck('id', 'old_student_id')
        ->toArray();

        // 使用集合方法优化数据处理
        $surveyToAssessmentMap = self::SURVEYIDTOASSESSMENTID; // 将常量赋值给变量
        $assignmentsData = $studentSurveyList->map(function($item) use ($studentClassIdArr, $surveyToAssessmentMap) {
            return [
                'old_session_id' => $item->session_id,
                'school_id' => $item->school_id,
                'assessment_task_id' => $item->id,
                'student_class_id' => $studentClassIdArr[$item->student_id] ?? 0,
                'student_id' => $item->member_id,
                'user_id' => $item->member_id,
                'duration' => $item->used_time,
                'results' => $item->result,
                'status' => 2,
                'assessment_id' => $surveyToAssessmentMap[$item->survey_id] ?? 0,
                'pdf_url' => $item->pdf_url,
                'created_at' => $item->create_time,
            ];
        })->toArray();

        // 使用事务和 chunk 插入大量数据
        if (isset($assignmentsData) && is_array($assignmentsData) && !empty($assignmentsData)) {
            DB::transaction(function () use ($assignmentsData) {
                collect($assignmentsData)->chunk(1000)->each(function($chunk) {
                    DB::table('assessment_task_assignments')->insert($chunk->toArray());
                });
            });

            // 获取最大的 survey_session_id 用于更新执行日志
            $maxSurveySessionId = $studentSurveyList->max('survey_session_id');
            $this->updateExecutionLog($maxSurveySessionId, count($assignmentsData));

            Log::info('Psychology assignments seeding completed', [
                'school_id' => $this->school_id,
                'total_records' => count($assignmentsData),
                'last_task_id' => end($assignmentsData)['assessment_task_id'] ?? null,
                'last_processed_survey_session_id' => $maxSurveySessionId,
            ]);
        } else {
            Log::info('没有新的psychology assignments数据需要处理', [
                'school_id' => $this->school_id,
                'last_processed_id' => $lastProcessedId
            ]);
        }
    }
}
