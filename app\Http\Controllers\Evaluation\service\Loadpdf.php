<?php
namespace app\evaluation\service;

use app\evaluation\logic\Report as ReportLogic;
use app\evaluation\logic\Loadpdf as LoadpdfLogic;
use app\backend\logic\Backend as BackendLogic;
use think\Db;

class Loadpdf{
    protected $ReportLogic;
    protected $LoadpdfLogic;

    public function __construct()
    {
        $this->report_logic = new ReportLogic();
        $this->loadpdf_ogic = new LoadpdfLogic();
        $this->backend_logic = new BackendLogic();
    }

    public function personal_report()
    {
        $member_id = input('member_id');
        $distribution_id = input('distribution_id');
        $data = $this->report_logic->personal_report($member_id,$distribution_id);
        return $data;
    }

    public function individual()
    {
        $member_id = input('member_id');
        $distribution_id = input('distribution_id');
        $school_id = Db::name('member')->where('id', $member_id)->value('school_id');
        $logo = $this->backend_logic->logo_school($school_id);
        $grade_id = Db::name('evaluation_distribution_detail detail')
            ->join('evaluation_papers papers', 'detail.paper_id = papers.id')
            ->where('distribution_id', $distribution_id)
            ->value('grade_id');
        if($grade_id == 6){
            $url= 'https://'.$_SERVER['HTTP_HOST'].'/evaluation/loadpdf/sixth_grade_personal_pdf?member_id='.$member_id.'&distribution_id='.$distribution_id.'#/exam_statistic_individual/list/detail';
        }
        if($grade_id == 10){
            $url= 'https://'.$_SERVER['HTTP_HOST'].'/evaluation/loadpdf/tenth_grade_personal_pdf?member_id='.$member_id.'&distribution_id='.$distribution_id.'#/exam_statistic_individual/list/detail2';
        }
        $data = $this->loadpdf_ogic->evaluation_create($url,$member_id,$distribution_id,$logo['logo_base64']);
        //生成pdf后，将url录入survey_user_session表
        $session_id = Db::name('evaluation_distribution_shengya_relation')->where(['member_id'=>$member_id,'distribution_id'=>$distribution_id,'status'=>0])->value('session_id');
        Db::name('survey_user_session')->where(['session_id'=>$session_id])->update(['pdf_url'=>$data[0]]);
        return $data;
    }
}