<?php
/**
 * Created by PhpStorm.
 * User: kane
 * Date: 2024/02/01
 * Time: 13:52
 */
namespace app\psychassessment\controller;
use app\login\controller\ApiAuth;

/**
 * Class Questions
 * @package app\psychassessment\controller
 */
class Configurations extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->Configurations = new \app\psychassessment\service\Configurations();
    }
    /**
     * 模块：心理评估-心理辅导可预约时间配置
     * @SWG\Post(path="/psychassessment/Configurations",
     *   tags={"心理评估-可预约时间配置"},
     *   summary="时间配置",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="start_date",
     *     type="string",
     *     description="生效日期",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="start_time",
     *     type="string",
     *     description="开始时间",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="end_time",
     *     type="string",
     *     description="结束时间",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="end_date",
     *     type="string",
     *     description="结束日期",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="repeat_type",
     *     type="integer",
     *     description="重复类型（0不重复，1每天，2每周，3每二周，4自定义）",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="repeat_daysofweek",
     *     type="string",
     *     description="重复日期（星期一1，星期二，2，3，4，5，6，7）",
     *     required=true,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：心理评估-心理辅导可预约时间配置
     * @SWG\Delete(path="/psychassessment/Configurations",
     *   tags={"心理评估-可预约时间配置"},
     *   summary="可预约时间删除",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="start_date",
     *     type="string",
     *     description="生效日期",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="end_date",
     *     type="string",
     *     description="结束日期",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="update",
     *     type="integer",
     *     description="0删除当条记录，1删除所有同时间段记录",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="config_id",
     *     type="integer",
     *     description="config_id",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    /**
     * 模块：心理评估-心理辅导可预约时间配置
     * @SWG\Put(path="/psychassessment/Configurations/{id}",
     *   tags={"心理评估-可预约时间配置"},
     *   summary="预约时间修改",
     * @SWG\Parameter(
     * in="path",
     * name="id",
     * type="integer",
     * description="时间记录id",
     * required=true,
     * ),
     * @SWG\Parameter(
     * in="body",
     * name="data",
     * description="辅导记录修改",
     * required=true,
     * @SWG\Schema(
     *          type="object",
     *          @SWG\Property(property="start_date", type="string", description="开始日期"),
     *          @SWG\Property(property="end_date", type="string", description="结束日期"),
     *          @SWG\Property(property="start_time", type="string", description="开始时间"),
     *          @SWG\Property(property="end_time", type="string", description="结束时间"),
     *          @SWG\Property(property="config_id", type="integer", description="config_id"),
     *          @SWG\Property(property="update", type="integer", description="0: 只修改当条，1：修改当前日期以后所有"),
     *  )
     * ),
     *   description="数据说明",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：心理评估-心理辅导可预约时间配置
     * @SWG\Get(path="/psychassessment/Configurations",
     *   tags={"心理评估-可预约时间配置"},
     *   summary="时间查询",
     *   @SWG\Parameter(
     *     in="query",
     *     name="start_date",
     *     type="string",
     *     description="生效日期",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="end_date",
     *     type="string",
     *     description="结束日期",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="teacher_id",
     *     type="integer",
     *     description="老师ID,查看自己，不用传",
     *     required=false,
     *   ),
     *   description="数据说明",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function Configurations(){

        $data = $this->Configurations->hand_out();
        apiReturn($data);
    }
    /**
     * 模块：心理评估-心理辅导可预约时间配置
     * @SWG\Post(path="/psychassessment/Configurations/dates",
     *   tags={"心理评估-可预约时间配置"},
     *   summary="时间查询",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="dates",
     *     type="string",
     *     description="查询多个日期",
     *     required=true,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function dates(){

        $data = $this->Configurations->dates();
        apiReturn($data);
    }

}