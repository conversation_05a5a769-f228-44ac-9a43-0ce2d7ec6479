<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */
namespace app\evaluation\controller;
use app\login\controller\ApiAuth;

/**
 * Class Questions
 * @package app\evaluation\controller
 */
class Papers extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->Papers = new \app\evaluation\service\Papers();
    }
    /**
     * 模块：素养测评-组卷管理
     * @SWG\Post(path="/evaluation/papers",
     *   tags={"素养测评-试卷管理:papers"},
     *   summary="试卷添加",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="paper_name",
     *     type="string",
     *     description="试卷名称",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="course_id",
     *     type="integer",
     *     description="科目ID
     *     '语文' : 1,
     *     '数学' : 2,
     *     '英语' : 3,
     *     '物理' : 4,
     *     '化学' : 5,
     *     '生物' : 6,
     *     '政治' : 7,
     *     '历史' : 8,
     *     '生命科学' : 9,
     *     '地理' : 10,
     *     '技术' : 11,
     *     '信息' : 12,",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="scenario_id",
     *     type="integer",
     *     description="应用场景
     *     '素养测评' : 1,
     *     '阶段测试' : 2,
     *     '课时练习' : 3,",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="grade_id",
     *     type="integer",
     *     description="适用年级,通用为0，一年级到高三1-12",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="question_ids",
     *     type="string",
     *     description="question_id，多个用,拼接",
     *     required=true,
     *  ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="school_id",
     *     type="integer",
     *     description="组卷关连学校ID，默认为登录人的school_id,可不传此项",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-组卷管理
     * @SWG\Put(path="/evaluation/papers/{id}",
     * tags={"素养测评-试卷管理:papers"},
     * summary="试卷修改",
     * @SWG\Parameter(
     * in="path",
     * name="id",
     * type="string",
     * description="试卷ID",
     * required=true,
     * ),
     * @SWG\Parameter(
     * in="body",
     * name="data",
     * description="试卷包含题目",
     * required=true,
     * @SWG\Schema(
     * type="object",
     * @SWG\Property(property="paper_name", type="string", description="试卷名称"),
     * @SWG\Property(property="course_id", type="integer", description="试题ID"),
     * @SWG\Property(property="grade_id", type="integer", description="适用年级"),
     * @SWG\Property(property="scenario_id", type="integer", description="应用场景"),
     * @SWG\Property(property="school_id", type="integer", description="学校ID"),
     * @SWG\Property(property="question_ids", type="string", description="question_id，多个用,拼接")
     * )
     * ),
     * description="数据说明：",
     * produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-组卷管理
     * @SWG\Delete(path="/evaluation/papers/{id}",
     *   tags={"素养测评-试卷管理:papers"},
     *   summary="试卷删除",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-组卷管理
     * @SWG\Get(path="/evaluation/papers",
     *   tags={"素养测评-试卷管理:papers"},
     *   summary="试卷查询",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="course_id",
     *     type="integer",
     *     description="科目ID",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="grade_id",
     *     type="integer",
     *     description="一年级到高三对应1-12",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="start_time",
     *     type="datetime",
     *     description="组卷时间左区间，格式为 'YYYY-MM-DD HH:MM:SS'",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="end_time",
     *     type="datetime",
     *     description="组卷时间右区间",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="paper_name",
     *     type="string",
     *     description="试卷名称",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="pagesize",
     *     type="integer",
     *     description="每页显示记录数",
     *     required=false,
     *   ),
     *  @SWG\Parameter(
     *     in="query",
     *     name="page",
     *     type="integer",
     *     description="页码",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    
    public function papers(){

        $data = $this->Papers->hand_out();
        apiReturn($data);
    }

}