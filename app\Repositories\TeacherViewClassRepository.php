<?php

namespace App\Repositories;


use App\Models\School\System\TeacherViewClass;

class TeacherViewClassRepository
{
    /**
     * 获取教师在某学年可查的班级id列表
     *
     * @param int $teacher_id 教师ID
     * @param int $school_year 学年
     * @return array
     */
    public function getViewClassIds(int $teacher_id, int $school_year)
    {
        return TeacherViewClass::where('teacher_id', $teacher_id)
        ->where('school_year', $school_year)
        ->pluck('class_id')
        ->toArray();
    }

    // 删除教师在某学年的班级
    public function deleteTeacherViewClass(int $teacher_id, int $school_year): bool|null
    {
        return TeacherViewClass::where('teacher_id', $teacher_id)
        ->where('school_year', $school_year)
        ->delete();
    }

}