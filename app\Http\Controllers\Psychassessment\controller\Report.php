<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */
namespace app\psychassessment\controller;
use app\login\controller\ApiAuth;

/**
 * Class Questions
 * @package app\psychassessment\controller
 */
class Report extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->ReportService = new \app\psychassessment\service\Report();
    }

    /**
     * 模块：心理评估-个体报告
     * @SWG\Get(path="/psychassessment/report",
     *   tags={"心理评估-个体报告:Report"},
     *   summary="个体报告",
     *   description="数据说明：",
     *   @SWG\Parameter(
     *     in="query",
     *     name="plan_id",
     *     type="integer",
     *     description="计划id",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="survey_type",
     *     type="integer",
     *     description="心理测评类型26自信27自我意识28焦虑",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="member_id",
     *     type="integer",
     *     description="学生的member_id,学生登录可不传此项",
     *     required=false,
     *   ),
     *   produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */

    public function report(){

        $data = $this->ReportService->report();
        apiReturn($data);
    }

}