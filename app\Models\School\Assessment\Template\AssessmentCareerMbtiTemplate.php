<?php

namespace App\Models\School\Assessment\Template;

use Illuminate\Database\Eloquent\Model;

class AssessmentCareerMbtiTemplate extends Model
{
    protected $table = 'assessment_career_mbti_templates';

    protected $casts = [
        'keywords' => 'json',
        'superpowers' => 'json',
        'campus_roles' => 'json',
        'subject_adaptation' => 'json',
        'learning_strategy' => 'json',
        'major_analyse' => 'json',
        'occupation_analyse' => 'json',
        'social_comfort_level' => 'json',
        'making_friends_analyse' => 'json',
        'communication_skill' => 'json',
        'stress_analyse' => 'json',
        'growth_analyse' => 'json',
        'example_case' => 'json'
    ];

    protected $fillable = [
        'code',
        'appellation',
        'preface',
        'interpret',
        'keywords',
        'superpowers',
        'campus_roles',
        'subject_adaptation',
        'learning_strategy',
        'major_analyse',
        'occupation_analyse',
        'social_comfort_level',
        'making_friends_analyse',
        'communication_skill',
        'stress_analyse',
        'growth_analyse',
        'example_case',
        'summary'
    ];
}