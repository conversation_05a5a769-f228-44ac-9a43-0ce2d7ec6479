<?php

namespace App\Http\Controllers\Tool;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Mews\Captcha\Facades\Captcha;


class ImgCodeController extends Controller
{

    // 生成验证码
    public function getCaptcha()
    {
//        $captcha = Captcha::create('default');

        //这里可以直接app('captcha')的原因就是因为在config\app.php中的providers中添加了这一句\Mews\Captcha\CaptchaServiceProvider::class,然后在CaptchaServiceProvider中的register绑定了bind的名字是captcha。
        return $this->success(app('captcha')->create('math', true));
    }

    // 校验验证码
    public function checkCaptcha(Request $request)
    {
        // 调用captcha_api_check方法。
        $captcha = $request->input('captcha'); //验证码
        $key = $request->input('key'); //key

        if (!captcha_api_check($captcha , $key, 'math')){
            return $this->error('验证码错误或已过期');
        }

        return $this->message('验证码正确');
    }

}
