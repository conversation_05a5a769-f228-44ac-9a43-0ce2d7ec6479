<?php

namespace App\Services\School\System;

use App\Http\Requests\School\System\ClassRequest;
use App\Models\Admin\Organization;
use App\Models\School\System\Claass;
use App\Services\BaseService;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Builder;

class ClassService extends BaseService
{
    /**
     * 构建班级列表查询
     */
    public function listBuilder(Request $request)
    {
        $school_id = $this->getSchoolId($request);
        $school_campus_id = $request['school_campus_id'];
        $grade_id = $request['grade_id'];

        return $this->buildClassQuery($school_id, $school_campus_id, $grade_id);
    }

    /**
     * 根据校区、年级获取班级下拉列表
     */
    public function getClassList(ClassRequest $request)
    {
        $school_id = $this->getSchoolId($request);
        $school_campus_id = $request['school_campus_id'];
        $grade_id = $request['grade_id'];

        $list = $this->buildClassQuery($school_id, $school_campus_id, $grade_id)
            ->select('id', 'class_name')->orderBy('class_name')
            ->get();
            
        return $list;
    }

    /**
     * 新增班级
     */
    public function store(ClassRequest $request)
    {
        $school_id = $this->getSchoolId($request);
        $school_campus_id = $request['school_campus_id'];
        $grade_id = $request['grade_id'];
        $class_name = $request['class_name'];
        
        // 检查班级是否已存在
        if ($this->isClassExists($school_id, $school_campus_id, $grade_id, $class_name)) {
            $this->throwBusinessException('班级已存在');
        }

        $data = filterRequestData('classes');
        $data['school_id'] = $school_id;
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;

        return Claass::forceCreate($data);
    }

    /**
     * 批量新增班级
     */
    public function batchStore(ClassRequest $request)
    {
        $school_id = $this->getSchoolId($request);
        $school_campus_id = $request['school_campus_id'];
        $grade_id = $request['grade_id'];
        $class_names = $request['class_names'];

        $class_name_list = array_filter(array_unique($class_names));
        
        // 检查是否有班级已存在
        if ($this->isAnyClassExists($school_id, $school_campus_id, $grade_id, $class_name_list)) {
            $this->throwBusinessException('导入数据中有班级名称在数据库中已存在');
        }

        $class_result_list = $this->createMultipleClasses(
            $school_id, 
            $school_campus_id, 
            $grade_id, 
            $class_name_list, 
            $request->user()->real_name
        );

        return $class_result_list;
    }

    /**
     * 更新班级信息
     */
    public function update(ClassRequest $request, $id)
    {
        $record = Claass::find($id);
        if (!$record) {
            $this->throwBusinessException('更新对象不存在');
        }
        
        $school_id = $this->getSchoolId($request);
        $school_campus_id = $request['school_campus_id'];
        $grade_id = $request['grade_id'];
        $class_name = $request['class_name'];
        
        // 检查是否有同名班级(排除当前记录)
        if ($this->isClassExists($school_id, $school_campus_id, $grade_id, $class_name, $id)) {
            $this->throwBusinessException('班级已存在');
        }

        $data = filterRequestData('classes');
        $data['updater'] = $request->user()->real_name;
        $record->fill($data)->save();
        
        return $record;
    }

    /**
     * 获取学校id（根据是否有参数 organization_id 动态判断）
     */
    public function getSchoolId(Request $request)
    {
        // 获取organization_id
        $organization_id = $request->input('organization_id');
        
        // 如果请求参数中没有organization_id，则取当前登录用户对应机构的的school_id
        if (!$organization_id) {
            $organization_id = $request->user()->organization_id;
        }

        $organization = Organization::find($organization_id);
        if (!$organization) {
            $this->throwBusinessException('学校对应机构数据不存在');
        }
        
        return $organization->model_id;
    }
    
    /**
     * 构建班级查询
     */
    private function buildClassQuery($school_id, $school_campus_id = null, $grade_id = null): Builder
    {
        return Claass::where('school_id', $school_id)
            ->when($school_campus_id, fn($query) => $query->where('school_campus_id', $school_campus_id))
            ->when($grade_id, fn($query) => $query->where('grade_id', $grade_id));
    }
    
    /**
     * 检查班级是否已存在
     */
    private function isClassExists($school_id, $school_campus_id, $grade_id, $class_name, $exclude_id = null): bool
    {
        $query = $this->buildClassQuery($school_id, $school_campus_id, $grade_id)
            ->where('class_name', $class_name);
            
        if ($exclude_id) {
            $query->where('id', '!=', $exclude_id);
        }
        
        return $query->exists();
    }
    
    /**
     * 检查是否有任何班级已存在
     */
    private function isAnyClassExists($school_id, $school_campus_id, $grade_id, array $class_names): bool
    {
        return $this->buildClassQuery($school_id, $school_campus_id, $grade_id)
            ->whereIn('class_name', $class_names)
            ->exists();
    }
    
    /**
     * 批量创建班级
     */
    private function createMultipleClasses($school_id, $school_campus_id, $grade_id, array $class_names, $user_name): array
    {
        $class_result_list = [];
        
        foreach ($class_names as $class_name) {
            $class = Claass::create([
                'school_id' => $school_id,
                'school_campus_id' => $school_campus_id,
                'grade_id' => $grade_id,
                'class_name' => $class_name,
                'creator' => $user_name,
                'updater' => $user_name,
            ]);
            
            $class_result_list[] = $class;
        }
        
        return $class_result_list;
    }
}
