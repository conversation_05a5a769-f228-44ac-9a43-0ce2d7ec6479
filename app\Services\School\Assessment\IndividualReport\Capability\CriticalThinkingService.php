<?php
namespace App\Services\School\Assessment\IndividualReport\Capability;

/**
 * 批判性思维能力个人报告服务类
 * 
 * 该类用于生成批判性思维能力的个人评估报告
 */
class CriticalThinkingService extends AbstractCapabilityReportService
{
    /**
     * 改进指数限制
     * 
     * @var int
     */
    protected const IMPROVEMENT_INDEX_LIMIT = 3;

    /**
     * 获取改进指数限制
     * 
     * @return int
     */
    protected function getImprovementIndexLimit(): int
    {
        return self::IMPROVEMENT_INDEX_LIMIT;
    }
    
    /**
     * 获取配置键名
     * 
     * @return string 配置键名
     */
    protected function getConfigKey(): string
    {
        return 'critical_thinking';
    }
}