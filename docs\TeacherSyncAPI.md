# 教师同步功能API文档

## 概述

教师同步功能用于将教师数据同步到ysy_member和ysy_teacher表中。根据用户的角色类型，系统会智能判断同步策略：

- **教务角色（type=2）**：只同步到ysy_member表
- **教师角色（type=3）**：同步到ysy_member和ysy_teacher表
- **混合角色**：根据角色类型组合进行相应同步

## API接口

### 同步教师数据

**接口地址：** `POST /api/school/system/teacher/sync`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
```json
{
    "teacher_ids": [1, 2, 3, 4, 5]
}
```

**参数说明：**
- `teacher_ids` (array, required): 需要同步的教师ID数组

**响应示例：**

成功响应：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "sync_results": [
            {
                "teacher_id": 1,
                "success": true,
                "message": "同步成功",
                "synced_to_member": true,
                "synced_to_teacher": true,
                "role_types": [3]
            },
            {
                "teacher_id": 2,
                "success": true,
                "message": "同步成功",
                "synced_to_member": true,
                "synced_to_teacher": false,
                "role_types": [2]
            }
        ],
        "total_count": 2,
        "success_count": 2,
        "message": "教师数据同步完成"
    }
}
```

失败响应：
```json
{
    "code": 400,
    "message": "teacher_ids字段是必需的"
}
```

## 同步逻辑说明

### 角色类型判断

系统会获取用户的角色信息，并过滤出状态为启用（status=1）的角色：

```php
$rolesTypes = $user->roles->filter(function ($item) {
    return $item->status == 1;
})->pluck('type')->unique()->toArray();
```

### 同步到ysy_member表

所有教师都会同步到ysy_member表，包含以下字段：
- `id`: 用户ID
- `name`: 教师姓名
- `username`: 用户名
- `password`: 加密密码
- `mobile`: 手机号
- `gender`: 性别
- `school_id`: 学校ID
- `role_id`: 角色ID（格式：0,角色类型,0）
- `step`: 步骤状态（默认0）

### 同步到ysy_teacher表

只有角色类型包含教师（type=3）的用户才会同步到ysy_teacher表，包含以下字段：
- `id`: 教师ID
- `member_id`: 关联ysy_member表的ID
- `name`: 教师姓名
- `school_id`: 学校ID
- `school_district`: 校区ID
- `is_psych`: 是否心理教师
- `step`: 步骤状态（默认0）

## 数据库配置

确保在`.env`文件中配置了同步数据库连接：

```env
SYNC_DB_HOST=127.0.0.1
SYNC_DB_PORT=3306
SYNC_DB_DATABASE=sync_database
SYNC_DB_USERNAME=username
SYNC_DB_PASSWORD=password
```

## 使用示例

### 前端调用示例

```javascript
// 同步指定教师
const syncTeachers = async (teacherIds) => {
    try {
        const response = await fetch('/api/school/system/teacher/sync', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                teacher_ids: teacherIds
            })
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            console.log('同步成功:', result.data);
        } else {
            console.error('同步失败:', result.message);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
};

// 使用示例
syncTeachers([1, 2, 3]);
```

## 注意事项

1. **权限验证**：接口需要用户登录并具有相应权限
2. **事务处理**：同步过程使用数据库事务，确保数据一致性
3. **错误处理**：如果某个教师同步失败，不会影响其他教师的同步
4. **日志记录**：同步过程会记录详细日志，便于问题排查
5. **角色判断**：系统会根据用户的实际角色类型智能判断同步策略

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误
- `404`: 教师不存在
- `500`: 服务器内部错误
