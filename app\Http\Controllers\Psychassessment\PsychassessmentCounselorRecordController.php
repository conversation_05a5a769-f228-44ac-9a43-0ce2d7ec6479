<?php

namespace App\Http\Controllers\Psychassessment;

use App\Http\Controllers\Controller;
use App\Services\Psychassessment\CounselorRecordService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * 心理评估-辅导记录控制器 - 基于原 ThinkPHP CounselorRecord 控制器重新实现
 */
class PsychassessmentCounselorRecordController extends Controller
{
    protected $counselorRecordService;

    public function __construct(CounselorRecordService $counselorRecordService)
    {
        $this->counselorRecordService = $counselorRecordService;
    }

    /**
     * 辅导记录操作（添加/查询/删除/修改）
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function counselorRecord(Request $request): JsonResponse
    {
        try {
            $method = $request->method();
            
            switch ($method) {
                case 'POST':
                    // 辅导记录录入
                    $data = $this->counselorRecordService->createRecord($request->all(), auth()->user());
                    return $this->success($data, '录入成功');
                    
                case 'GET':
                    // 查询辅导记录
                    $params = $request->all();
                    $data = $this->counselorRecordService->getRecordList($params);
                    return $this->success($data, '获取成功');
                    
                case 'PUT':
                    // 修改辅导记录
                    $id = $request->route('id') ?: $request->input('id');
                    $data = $this->counselorRecordService->updateRecord($id, $request->all(), auth()->user());
                    return $this->success($data, '修改成功');
                    
                case 'DELETE':
                    // 删除辅导记录
                    $id = $request->route('id') ?: $request->input('id');
                    $data = $this->counselorRecordService->deleteRecord($id, auth()->user());
                    return $this->success($data, '删除成功');
                    
                default:
                    return $this->error('不支持的请求方法');
            }
        } catch (\Exception $e) {
            return $this->error('操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取辅导记录详情
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getRecordDetail(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $data = $this->counselorRecordService->getRecordDetail($id);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取学生辅导历史
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudentHistory(Request $request): JsonResponse
    {
        try {
            $studentId = $request->input('student_id');
            $memberId = $request->input('member_id');
            
            $data = $this->counselorRecordService->getStudentHistory($studentId, $memberId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取教师辅导记录
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getTeacherRecords(Request $request): JsonResponse
    {
        try {
            $teacherId = $request->input('teacher_id');
            $params = $request->all();
            
            $data = $this->counselorRecordService->getTeacherRecords($teacherId, $params);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量删除辅导记录
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchDelete(Request $request): JsonResponse
    {
        try {
            $ids = $request->input('ids');
            $data = $this->counselorRecordService->batchDelete($ids, auth()->user());
            return $this->success($data, '批量删除成功');
        } catch (\Exception $e) {
            return $this->error('批量删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出辅导记录
     * 
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|JsonResponse
     */
    public function exportRecords(Request $request)
    {
        try {
            $params = $request->all();
            return $this->counselorRecordService->exportRecords($params);
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取辅导统计数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStatistics(Request $request): JsonResponse
    {
        try {
            $params = $request->all();
            $data = $this->counselorRecordService->getStatistics($params);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取辅导类型列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getCounselingTypes(Request $request): JsonResponse
    {
        try {
            $data = $this->counselorRecordService->getCounselingTypes();
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取辅导效果评估
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getEffectEvaluation(Request $request): JsonResponse
    {
        try {
            $recordId = $request->input('record_id');
            $data = $this->counselorRecordService->getEffectEvaluation($recordId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 添加辅导效果评估
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function addEffectEvaluation(Request $request): JsonResponse
    {
        try {
            $recordId = $request->input('record_id');
            $evaluation = $request->input('evaluation');
            
            $data = $this->counselorRecordService->addEffectEvaluation($recordId, $evaluation, auth()->user());
            return $this->success($data, '添加成功');
        } catch (\Exception $e) {
            return $this->error('添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取辅导计划
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getCounselingPlan(Request $request): JsonResponse
    {
        try {
            $studentId = $request->input('student_id');
            $data = $this->counselorRecordService->getCounselingPlan($studentId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建辅导计划
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function createCounselingPlan(Request $request): JsonResponse
    {
        try {
            $data = $this->counselorRecordService->createCounselingPlan($request->all(), auth()->user());
            return $this->success($data, '创建成功');
        } catch (\Exception $e) {
            return $this->error('创建失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新辅导计划
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateCounselingPlan(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $data = $this->counselorRecordService->updateCounselingPlan($planId, $request->all(), auth()->user());
            return $this->success($data, '更新成功');
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取辅导模板
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getTemplates(Request $request): JsonResponse
    {
        try {
            $type = $request->input('type');
            $data = $this->counselorRecordService->getTemplates($type);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 保存辅导模板
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function saveTemplate(Request $request): JsonResponse
    {
        try {
            $data = $this->counselorRecordService->saveTemplate($request->all(), auth()->user());
            return $this->success($data, '保存成功');
        } catch (\Exception $e) {
            return $this->error('保存失败: ' . $e->getMessage());
        }
    }
}
