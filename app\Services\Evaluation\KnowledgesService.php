<?php

namespace App\Services\Evaluation;

use App\Models\Evaluation\Knowledges;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

/**
 * 知识点管理服务类
 */
class KnowledgesService
{
    protected $knowledgesModel;
    protected $user;

    public function __construct(Knowledges $knowledgesModel)
    {
        $this->knowledgesModel = $knowledgesModel;
        $this->user = Auth::user();
    }

    /**
     * 获取知识点列表
     *
     * @param array $params
     * @return array
     */
    public function getKnowledgesList(array $params): array
    {
        if (!empty($params['id'])) {
            $data = $this->knowledgesModel->where('id', $params['id'])->first();
            return $data ? $data->toArray() : [];
        }

        // 简化版本：直接获取所有知识点，然后构建树形结构
        $query = $this->knowledgesModel->where('status', 0);

        // 应用筛选条件
        if (!empty($params['is_high'])) {
            $query->where('is_high', $params['is_high']);
        }

        if (!empty($params['course_id'])) {
            $query->where('course_id', $params['course_id']);
        }

        if (!empty($params['name'])) {
            $query->where('name', 'like', '%' . $params['name'] . '%');
        }

        $knowledges = $query->orderBy('id')->get()->toArray();

        // 构建树形结构
        return $this->buildTree($knowledges);
    }

    /**
     * 创建知识点
     * 
     * @param array $data
     * @return Knowledges
     */
    public function createKnowledge(array $data): Knowledges
    {
        $knowledgeData = [
            'name' => $data['name'],
            'parent_id' => $data['parent_id'] ?? 0,
            'is_high' => $data['is_high'] ?? 0,
            'course_id' => $data['course_id'],
            'status' => 0,
        ];

        return $this->knowledgesModel->create($knowledgeData);
    }

    /**
     * 更新知识点
     * 
     * @param int $id
     * @param array $data
     * @return Knowledges
     */
    public function updateKnowledge(int $id, array $data): Knowledges
    {
        $knowledge = $this->knowledgesModel->findOrFail($id);

        $updateData = [
            'name' => $data['name'] ?? $knowledge->name,
            'parent_id' => $data['parent_id'] ?? $knowledge->parent_id,
            'is_high' => $data['is_high'] ?? $knowledge->is_high,
            'course_id' => $data['course_id'] ?? $knowledge->course_id,
        ];

        $knowledge->update($updateData);

        return $knowledge;
    }

    /**
     * 删除知识点（软删除）
     * 
     * @param int $id
     * @return bool
     */
    public function deleteKnowledge(int $id): bool
    {
        $knowledge = $this->knowledgesModel->findOrFail($id);

        // 检查是否有子知识点
        $hasChildren = $this->knowledgesModel->where('parent_id', $id)->where('status', 0)->exists();
        if ($hasChildren) {
            throw new \Exception('该知识点下还有子知识点，无法删除');
        }

        // 检查是否被题目使用
        $isUsed = DB::table('evaluation_questions')
            ->where('knowlege_ids', 'like', '%' . $id . '%')
            ->where('status', 0)
            ->exists();
        if ($isUsed) {
            throw new \Exception('该知识点已被题目使用，无法删除');
        }

        return $knowledge->update(['status' => -1]);
    }

    /**
     * 获取知识点树形结构
     * 
     * @param array $params
     * @return array
     */
    public function getKnowledgeTree(array $params): array
    {
        $knowledges = $this->knowledgesModel
            ->where('status', 0)
            ->when(!empty($params['course_id']), function ($query) use ($params) {
                $query->where('course_id', $params['course_id']);
            })
            ->when(!empty($params['is_high']), function ($query) use ($params) {
                $query->where('is_high', $params['is_high']);
            })
            ->orderBy('id')
            ->get()
            ->toArray();

        return $this->buildTree($knowledges);
    }

    /**
     * 获取指定父级下的子知识点
     * 
     * @param int $parentId
     * @param int|null $courseId
     * @param int|null $isHigh
     * @return array
     */
    public function getChildrenKnowledges(int $parentId, ?int $courseId = null, ?int $isHigh = null): array
    {
        $query = $this->knowledgesModel
            ->where('parent_id', $parentId)
            ->where('status', 0);

        if ($courseId !== null) {
            $query->where('course_id', $courseId);
        }

        if ($isHigh !== null) {
            $query->where('is_high', $isHigh);
        }

        return $query->orderBy('id')
            ->get()
            ->toArray();
    }

    /**
     * 批量更新排序
     * 注意：由于数据库表中没有 sort 字段，此方法暂时返回 true
     *
     * @param array $data
     * @return bool
     */
    public function updateSort(array $data): bool
    {
        if (empty($data['knowledges'])) {
            throw new \InvalidArgumentException('排序数据不能为空');
        }

        // 由于数据库表中没有 sort 字段，暂时返回 true
        // 如果需要排序功能，需要先在数据库中添加 sort 字段
        return true;
    }

    /**
     * 移动知识点到指定父级
     * 
     * @param int $id
     * @param int $parentId
     * @return bool
     */
    public function moveKnowledge(int $id, int $parentId): bool
    {
        $knowledge = $this->knowledgesModel->findOrFail($id);

        // 检查是否会形成循环引用
        if ($this->wouldCreateCircularReference($id, $parentId)) {
            throw new \Exception('不能移动到自己的子知识点下');
        }

        return $knowledge->update(['parent_id' => $parentId]);
    }

    /**
     * 搜索知识点
     * 
     * @param array $params
     * @return array
     */
    public function searchKnowledges(array $params): array
    {
        $keyword = $params['keyword'] ?? '';
        $courseId = $params['course_id'] ?? null;
        $isHigh = $params['is_high'] ?? null;

        if (empty($keyword)) {
            return [];
        }

        $query = $this->knowledgesModel
            ->where('status', 0)
            ->where('name', 'like', '%' . $keyword . '%');

        if ($courseId !== null) {
            $query->where('course_id', $courseId);
        }

        if ($isHigh !== null) {
            $query->where('is_high', $isHigh);
        }

        $knowledges = $query->orderBy('sort')
            ->orderBy('id')
            ->get();

        // 为每个知识点添加路径信息
        foreach ($knowledges as &$knowledge) {
            $knowledge->path = $this->getKnowledgePath($knowledge->id);
        }

        return $knowledges->toArray();
    }

    /**
     * 获取知识点统计
     * 
     * @param array $params
     * @return array
     */
    public function getKnowledgeStatistics(array $params): array
    {
        $courseId = $params['course_id'] ?? null;
        $isHigh = $params['is_high'] ?? null;

        $query = $this->knowledgesModel->where('status', 0);

        if ($courseId !== null) {
            $query->where('course_id', $courseId);
        }

        if ($isHigh !== null) {
            $query->where('is_high', $isHigh);
        }

        $total = $query->count();
        $topLevel = $query->where('parent_id', 0)->count();
        $byCourse = $this->knowledgesModel->where('status', 0)
            ->selectRaw('course_id, COUNT(*) as count')
            ->groupBy('course_id')
            ->pluck('count', 'course_id')
            ->toArray();

        return [
            'total' => $total,
            'top_level' => $topLevel,
            'by_course' => $byCourse,
            'usage_stats' => $this->getKnowledgeUsageStats($courseId, $isHigh)
        ];
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 应用筛选条件
     * 
     * @param $query
     * @param array $params
     */
    private function applyFilters($query, array $params): void
    {
        if (!empty($params['is_high'])) {
            $query->where('a.is_high', $params['is_high']);
        }

        if (!empty($params['course_id'])) {
            $query->where('a.course_id', $params['course_id']);
        }

        if (!empty($params['name'])) {
            $query->where(function ($q) use ($params) {
                $q->where('a.name', 'like', '%' . $params['name'] . '%')
                  ->orWhere('b.name', 'like', '%' . $params['name'] . '%')
                  ->orWhere('c.name', 'like', '%' . $params['name'] . '%');
            });
        }
    }

    /**
     * 构建知识点树形结构
     * 
     * @param $data
     * @return array
     */
    private function buildKnowledgeTree($data): array
    {
        $result = [];
        
        foreach ($data as $item) {
            $resultItem = [
                "id" => $item['id'],
                "name" => $item['name'],
                "parent_id" => $item['parent_id'],
                "course_id" => $item['course_id'],
                "is_high" => $item['is_high'],
                "children" => []
            ];

            // 添加二级知识点
            if ($item['child_id']) {
                $childExists = false;
                foreach ($resultItem['children'] as &$child) {
                    if ($child['id'] == $item['child_id']) {
                        $childExists = true;
                        // 添加三级知识点
                        if ($item['grandchild_id']) {
                            $grandchildExists = false;
                            foreach ($child['children'] as $grandchild) {
                                if ($grandchild['id'] == $item['grandchild_id']) {
                                    $grandchildExists = true;
                                    break;
                                }
                            }
                            if (!$grandchildExists) {
                                $child['children'][] = [
                                    "id" => $item['grandchild_id'],
                                    "name" => $item['grandchild_name'],
                                    "parent_id" => $item['grandchild_parent_id'],
                                    "course_id" => $item['grandchild_course_id'],
                                    "is_high" => $item['grandchild_is_high'],
                                    "children" => []
                                ];
                            }
                        }
                        break;
                    }
                }

                if (!$childExists) {
                    $childItem = [
                        "id" => $item['child_id'],
                        "name" => $item['child_name'],
                        "parent_id" => $item['child_parent_id'],
                        "course_id" => $item['child_course_id'],
                        "is_high" => $item['child_is_high'],
                        "children" => []
                    ];

                    if ($item['grandchild_id']) {
                        $childItem['children'][] = [
                            "id" => $item['grandchild_id'],
                            "name" => $item['grandchild_name'],
                            "parent_id" => $item['grandchild_parent_id'],
                            "course_id" => $item['grandchild_course_id'],
                            "is_high" => $item['grandchild_is_high'],
                            "children" => []
                        ];
                    }

                    $resultItem['children'][] = $childItem;
                }
            }

            // 检查是否已存在相同的一级知识点
            $exists = false;
            foreach ($result as &$existingItem) {
                if ($existingItem['id'] == $resultItem['id']) {
                    // 合并子知识点
                    foreach ($resultItem['children'] as $child) {
                        $childExists = false;
                        foreach ($existingItem['children'] as &$existingChild) {
                            if ($existingChild['id'] == $child['id']) {
                                // 合并孙知识点
                                foreach ($child['children'] as $grandchild) {
                                    $grandchildExists = false;
                                    foreach ($existingChild['children'] as $existingGrandchild) {
                                        if ($existingGrandchild['id'] == $grandchild['id']) {
                                            $grandchildExists = true;
                                            break;
                                        }
                                    }
                                    if (!$grandchildExists) {
                                        $existingChild['children'][] = $grandchild;
                                    }
                                }
                                $childExists = true;
                                break;
                            }
                        }
                        if (!$childExists) {
                            $existingItem['children'][] = $child;
                        }
                    }
                    $exists = true;
                    break;
                }
            }

            if (!$exists) {
                $result[] = $resultItem;
            }
        }

        return $result;
    }

    /**
     * 构建树形结构
     * 
     * @param array $knowledges
     * @param int $parentId
     * @return array
     */
    private function buildTree(array $knowledges, int $parentId = 0): array
    {
        $tree = [];

        foreach ($knowledges as $knowledge) {
            if ($knowledge['parent_id'] == $parentId) {
                $children = $this->buildTree($knowledges, $knowledge['id']);
                if (!empty($children)) {
                    $knowledge['children'] = $children;
                }
                $tree[] = $knowledge;
            }
        }

        return $tree;
    }

    /**
     * 检查是否会形成循环引用
     * 
     * @param int $knowledgeId
     * @param int $parentId
     * @return bool
     */
    private function wouldCreateCircularReference(int $knowledgeId, int $parentId): bool
    {
        if ($parentId == 0) {
            return false;
        }

        if ($parentId == $knowledgeId) {
            return true;
        }

        $parent = $this->knowledgesModel->find($parentId);
        if (!$parent) {
            return false;
        }

        return $this->wouldCreateCircularReference($knowledgeId, $parent->parent_id);
    }

    /**
     * 获取知识点路径
     * 
     * @param int $knowledgeId
     * @return array
     */
    private function getKnowledgePath(int $knowledgeId): array
    {
        $path = [];
        $current = $this->knowledgesModel->find($knowledgeId);
        
        while ($current) {
            array_unshift($path, [
                'id' => $current->id,
                'name' => $current->name
            ]);
            $current = $current->parent_id > 0 ? $this->knowledgesModel->find($current->parent_id) : null;
        }
        
        return $path;
    }

    /**
     * 获取知识点使用统计
     * 
     * @param int|null $courseId
     * @param int|null $isHigh
     * @return array
     */
    private function getKnowledgeUsageStats(?int $courseId, ?int $isHigh): array
    {
        // 这里需要统计知识点在题目中的使用情况
        // 由于题目表的 knowlege_ids 字段存储的是逗号分隔的ID，需要特殊处理
        return [
            'used_count' => 0,
            'unused_count' => 0,
            'usage_rate' => 0
        ];
    }
}
