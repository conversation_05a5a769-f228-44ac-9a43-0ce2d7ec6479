<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MajorPostDistribution extends BaseModel
{
    use HasFactory;

    protected $table = 'Major_PostDistribution';

    // 指定连接
    protected $connection = 'sqlsrv_gk';

    //主键
    protected $primaryKey = 'Id';

    // 隐藏字段
    protected $hidden = [];
    // 是否使用时间戳
    public $timestamps = false;

    // 可批量赋值的字段
    protected $fillable = [
        'MajorID',
        'MajorName',
        'MajorSubject',
        'SpecificPosition',
        'Industry',
        'Position',
        'Proportin',
        'Year',
    ];
}
