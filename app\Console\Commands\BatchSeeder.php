<?php
/**
 * 基础数据填充批处理
 */
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\DatabaseSeeder;
use Illuminate\Support\Facades\Artisan;

class BatchSeeder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seed:batch {--seeders=*} {--school_id=} {--connect=} ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '生产环境-基础数据批量转化';

    /**
     * 源数据连接
     *
     * @var string
     */
    protected string $connect = 'mysql_prod';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 查询所有学校ID
//        $schoolIds = \DB::connection($this->connect)->table('school')
//            ->where('date_due', '>', '2024-07-31')
//            ->where('step', '>=', 0)
//            ->pluck('id')->toArray();
        $schoolIds = [122];
        // todo: 按学校为每所学校单独处理

        $seeders = $this->getSeeders();
        foreach ($schoolIds as $schoolId){
            foreach ($seeders as $seeder) {

                //if (class_exists($seeder)) {
                $this->info("开始执行数据填充类：{$seeder}");
                //$seeder::run($schoolId);
                $this->call('db:seed', ['class'=>$seeder]);
                $this->info("数据填充类：{$seeder}执行完毕");
                //}
            }
        }
    }

    /**
     * 获取所有数据填充类
     *
     * @return array
     */
    protected function getSeeders(): array
    {
        return [
            'SchoolSeeder',
//            'RoleSeeder',
//            'ClassSeeder',
//            'CourseSeeder',
//            'TeacherSeeder',
//            'TeacherCourseSeeder',
//            'TeacherClassSeeder',
//            'TeacherViewClassSeeder',
//            'TeacherViewCourseSeeder',
//            'StudentClassRelationSeeder',
//            'StudentSeeder'
        ];
    }
}
