<?php

namespace App\Services\School\Assessment\CompositeReport;

use App\Services\BaseService;
use App\Repositories\AssignmentRepository;
use App\Repositories\StudentRepository;
use App\Services\School\Assessment\CompositeReport\CompositeServiceFactory;

class CompetencyService extends BaseService
{
    public function __construct(
        protected StudentRepository $studentRepository,
        protected AssignmentRepository $assignmentRepository,
        protected CompositeServiceFactory $compositeServiceFactory
    )
    {
        
    }

    /**
     * 创新人才核心素养能力综合报告
     *
     * @param int $user_id 用户ID
     * @param int $school_id 学校ID
     * @return array 创新人才核心素养能力综合报告数据
     */
    public function index($user_id, $school_id): array
    {
        $assessment_ids = [14,15,16,17,18,19,20];

        $latestAssignments = $this->assignmentRepository->getLatestAssignments($user_id, $school_id, $assessment_ids);

        $score = 0;
        $data_all = [];
        foreach ($latestAssignments as $assessment_id_info){
            $params['assessment_id'] = $assessment_id_info['assessment_id'];
            $params['assessment_task_assignment_id'] = $assessment_id_info['id'];
            
            $scoreService = $this->compositeServiceFactory->create($assessment_id_info['assessment_id']);
            
            $data = $scoreService->generateReport($params);

            switch ($assessment_id_info['assessment_id']){
                case 14:
                    $data_all['content_create'] = $data['content'];
                    $score += round($data_all['content_create'][5]['score'] * 0.1,2);
                    break;
                case 15:
                    $data_all['content_critical'] = $data['content'];
                    $score += round($data_all['content_critical'][3]['score'] * 0.2,2);
                    break;
                case 16:
                    $data_all['content_communication'] = $data['content'];
                    $score += round($data_all['content_communication'][3]['score'] * 0.1,2);
                    break;
                case 17:
                    $data_all['content_solve'] = $data['content'];
                    $data_all['content_solve']['score'] = round($data_all['content_solve'][6]['score'] * 0.3 + $data_all['content_solve'][7]['score'] * 0.7,2);//实际问题解决能力*70%+问题解决倾向*30%
                    $score += round($data_all['content_solve']['score'] * 0.25,2);
                    break;
                case 18:
                    $data_all['content_learn'] = $data['content'];
                    $score += round($data_all['content_learn'][10]['score'] * 0.35,2);
                    break;
                case 19:
                    $data_all['content_solve_junior'] = $data['content'];
                    $data_all['content_solve_junior']['score'] = round($data_all['content_solve_junior'][6]['score'] * 0.3 + $data_all['content_solve_junior'][7]['score'] * 0.7,2);//实际问题解决能力*70%+问题解决倾向*30%
                    $score += round($data_all['content_solve_junior']['score'] * 0.25,2);
                    break;
                case 20:
                    $data_all['content_learn_junior'] = $data['content'];
                    $score += round($data_all['content_learn_junior'][10]['score'] * 0.35,2);
                    break;
            }
            if (empty($data_all['info'])) { // Ensure student info is fetched only once
                $data_all['info'] = $this->studentRepository->getStudentInfo($user_id);
            }
        }
        $data_all['total_score'] = $score;
        return $data_all;
    }
}