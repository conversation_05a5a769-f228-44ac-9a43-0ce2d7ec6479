<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */
namespace app\evaluation\logic;
use app\evaluation\model\Distribution as DistributionModel;
use app\evaluation\model\DistributionDetail as DistributionDetailModel;
use app\evaluation\model\Papers as PapersModel;
use app\evaluation\model\DistributionTeachersStudents as DistributionTeachersStudentsModel;
use app\evaluation\model\EvaluationLog as EvaluationLogModel;
use app\common\model\School as School;
use app\common\model\Teacher as Teacher;
use think\Db;
use think\Loader;
use think\Session;

class Distribution{
    protected $paperModel;
    protected $user;
    public function __construct()
    {
        $this->schoolModel = new School();
        $this->teacherModel = new Teacher();
        $this->paperModel = new PapersModel();
        $this->distributionDetailModel = new DistributionDetailModel();
        $this->distributionTeachersStudents = new DistributionTeachersStudentsModel();
        $this->evaluationLog = new EvaluationLogModel();
        $this->user = json_decode(Session::get('user'),true);
    }

    public function add()
    {
        //权限判断
        if(selfRbac() == 999){
            $school_ids = input("school_ids");
            if(empty($school_ids)) apiReturn([],'指定学校不能为空',-7);
        }else{
            $school_ids= $this->user['school_id'];
        }
        $model = new DistributionModel();
        $model->data([
            'title'           => input('title'),
            'paper_ids'       => input('paper_ids'),
            'grade_id'        => input('grade_id'),
            'class_ids'       => input('class_ids'),
            'member_ids'      => input('member_ids'),
            'distribution_by' => $this->user['id'],
            'role_id'         => $this->user['role_id'],
            'from_id'         => input('from_id'),
            'create_at'       => date('Y-m-d H:i:s'),
            'school_ids'      => $school_ids,
        ]);
        $model->save();
        $id=$model->id;
        $ins = [];
        //admin端操作
        if(selfRbac() == 999) {
            $paper_ids_arr = explode(',', input('paper_ids'));
            foreach ($paper_ids_arr as $ka => $va) {
                //录入分发详情表
                $ins[] = [
                    'distribution_id' => $id,
                    'paper_id'        => $va,
                    'create_at'       => date('Y-m-d H:i:s'),
                ];
            }
            $this->distributionDetailModel->saveAll($ins);
        }else{//教务端操作
            $student_memberid_arr = explode(',', input('member_ids'));
            $student_num          = count($student_memberid_arr);
            $i = 0;
            foreach (json_decode(input('distribution_arr'),true) as $kp => $vp) {
                //录入分发详情表
                $ins[] = [
                    'distribution_id'    => $id,
                    'paper_id'           => $vp['paper_id'],
                    'teacher_member_ids' => $vp['teacher_ids'],
                    'start_time'         => $vp['start_time'],
                    'exam_duration'      => $vp['exam_duration'],
                    'create_at'          => date('Y-m-d H:i:s'),
                ];
                //给每个老师平均分配负责批改的学生,并存入ysy_evaluation_distribution_teachers_students表中
                $teacher_member_id_arr = explode(',', $vp['teacher_ids']);
                $teacher_num           = count($teacher_member_id_arr);
                $avg                   = ceil($student_num / $teacher_num);
                foreach ($teacher_member_id_arr as $key => $value) {
                    $res[$i] = [
                        'distribution_id'   => $id,
                        'paper_id'          => $vp['paper_id'],
                        'teacher_member_id' => $value,
                        'create_at'         => date('Y-m-d H:i:s'),
                    ];
                    //最后一个老师截取的不一样，从$key*$avg到最后
                    if (($key + 1) == $teacher_num) {
                        $res[$i]['member_ids'] = implode(',', array_slice($student_memberid_arr, $key * $avg));
                    } else {
                        $res[$i]['member_ids'] = implode(',', array_slice($student_memberid_arr, $key * $avg, $avg));
                    }
                    $i++;
                }
            }
            $this->distributionDetailModel->saveAll($ins);
            $this->distributionTeachersStudents->saveAll($res);
        }
        apiReturn($id);
    }

    public function edit()
    {
        $data = [
            'title' =>  input('title'),
            'paper_ids'  =>  input('paper_ids'),
        ];
        //暂定只有老师和教务能指定测评人和批阅人
        if (isset($this->user['school_id']) && $this->user['school_id'] && in_array($this->user['role_source_id'],[2,3])) {
            $data['grade_id']   = input('grade_id');
            $data['class_ids']  = input('class_ids');
            $data['member_ids'] = input('member_ids');
            $data['from_id']    = input('from_id');
        }
        if(selfRbac() == 999){
            $data['school_ids'] = input('school_ids');
        }
        $model = new DistributionModel();
        $model->save($data,['id' => input('id')]);
        //admin端操作
        if(selfRbac() == 999) {
            $this->distributionDetailModel->where(['distribution_id' => input('id'), 'status' => 0])->update(['status' => -1]);
            $paper_ids_arr = explode(',', input('paper_ids'));
            foreach ($paper_ids_arr as $ka => $va) {
                //录入分发详情表
                $ins[] = [
                    'distribution_id' => input('id'),
                    'paper_id'        => $va,
                    'create_at'       => date('Y-m-d H:i:s'),
                ];
            }
            $this->distributionDetailModel->saveAll($ins);
        }else {//教务端操作
            //编辑。分发详情，一个分发id对应多个paper的关系表全删掉，给老师分配学生时把之前分配的全删掉，重新录
            $this->distributionDetailModel->where(['distribution_id' => input('id'), 'status' => 0])->update(['status' => -1]);
            $this->distributionTeachersStudents->where(['distribution_id' => input('id'), 'status' => 0])->update(['status' => -1]);
            $student_memberid_arr = explode(',', input('member_ids'));
            $student_num          = count($student_memberid_arr);
            $i = 0;
            foreach (json_decode(input('distribution_arr'), true) as $kp => $vp) {
                //录入分发详情表
                $ins[] = [
                    'distribution_id'    => input('id'),
                    'paper_id'           => $vp['paper_id'],
                    'teacher_member_ids' => $vp['teacher_ids'],
                    'start_time'         => $vp['start_time'],
                    'exam_duration'      => $vp['exam_duration'],
                    'create_at'          => date('Y-m-d H:i:s'),
                ];
                //给每个老师平均分配负责批改的学生,并存入ysy_evaluation_distribution_teachers_students表中
                $teacher_member_id_arr = explode(',', $vp['teacher_ids']);
                $teacher_num    = count($teacher_member_id_arr);
                $avg            = ceil($student_num / $teacher_num);
                foreach ($teacher_member_id_arr as $key => $value) {
                    $res[$i] = [
                        'distribution_id'   => input('id'),
                        'paper_id'          => $vp['paper_id'],
                        'teacher_member_id' => $value,
                        'create_at'         => date('Y-m-d H:i:s'),
                    ];
                    //最后一个老师截取的不一样，从$key*$avg到最后
                    if (($key + 1) == $teacher_num) {
                        $res[$i]['member_ids'] = implode(',', array_slice($student_memberid_arr, $key * $avg));
                    } else {
                        $res[$i]['member_ids'] = implode(',', array_slice($student_memberid_arr, $key * $avg, $avg));
                    }
                    $i++;
                }
            }
            $this->distributionDetailModel->saveAll($ins);
            $this->distributionTeachersStudents->saveAll($res);
        }
        apiReturn(input('id'));
    }

    public function del()
    {
        $model = new DistributionModel();
        $model->save([
            'status'  => '-1',
        ],['id' => input('id')]);
        //软删除
        apiReturn(input('id'));
    }

    //更新测评状态
    public function set_status()

    {

        $model = new DistributionModel();
        //软删除
        $model->save([
            'status'  => input('status'),
        ],['id' => input('id')]);
        //如果是admin禁用，那么教务二次分发的也要禁用
        if(selfRbac() == 999){
            $model->where(['from_id' => input('id')])->update(['status'  => input('status')]);
        }
        apiReturn(input('id'));
    }
    //查询
    public function get_list()
    {
        $result=[];
        $pageNumber = input('page', 1); // 获取页码，默认为1
        $pageSize = input('pagesize', 10); // 获取每页显示的记录数，默认为10
        $model = new DistributionModel();
        $field = 'd.*,member.name,group_concat(DISTINCT school.name order by school.id asc) as school_name';
        $row = $model->alias('d')
            ->field($field)
            ->join('ysy_member member', 'd.distribution_by = member.id', 'LEFT')
            ->join('ysy_school school', 'FIND_IN_SET(school.id,d.school_ids)','LEFT');
        //传id就看当前id的,教务只能看到自己学校的人分发的
        if(!empty(input('id'))){
            $row->where('d.id',input('id'));
        }elseif (isset($this->user['school_id']) && $this->user['school_id']) {
            //学校里的教务老师学生都需要过滤管理员分发的
            $row->where(['d.school_ids'=>$this->user['school_id'],'d.role_id'=>['<>','0,999,0'],'d.grade_id'=>['<>','']]);
            //教师只能看到自己分发的
            if($this->user['role_source_id'] == 3) $row->where('d.distribution_by',$this->user['id']);
        }
        if($this->user['role_source_id'] == 1){
            $row->where('FIND_IN_SET('.$this->user['id'].',d.member_ids)');
            //查询个人答卷记录，根据套卷中每一张卷子的状态，判断测评（即分发）状态
            $personal_answer_log = $this->evaluationLog
                ->field('id as log_id,distribution_id,paper_id,member_id,check_status')
                ->where(['member_id'=>$this->user['id'],'status'=>0])
                ->select();
            $personal_answer_log = to_arr($personal_answer_log);
            foreach ($personal_answer_log as $kl => $vl){
                $personal_arr[$vl['distribution_id'].'-'.$vl['paper_id']] = $vl;
            }
            //查询有没有做生涯测评
            $has_shengya = db('evaluation_distribution_shengya_relation')
                ->where(['member_id'=>$this->user['id'],'status'=>0])
                ->column('distribution_id,id');
        }
        $row->where(function ($query) {
            $query->where('d.status', 0);
            if (is_numeric(input('status'))) {
                $query->where('d.status', input('status'));
            } else {
                if($this->user['role_source_id'] == 1){
                    $query->where('d.status', '=', 0);
                }else{
                    $query->where('d.status', '>=', 0);
                }
            }
            if(!empty(input('distribution_name'))) {
                $query->where('d.title',  'like', '%' .input('distribution_name'). '%');
            }
        })
        ->with(['papers' => function($query) {
            if(!empty(input('start_time')) && !empty(input('end_time'))) {
                $query->where(['start_time'=>['between', [input('start_time'), input('end_time').' 23:59:59']]]);
            }
            $query->with(['detail' => function($query) {
                $query->field('*');
                if(!empty(input('papers_name'))) {
                    $query->where('paper_name',  'like', '%' .input('papers_name'). '%');
                }
                if(!empty(input('grade_id'))) {
                    $query->where('grade_id', input('grade_id'));
                }
            }]);
        }]);
        $row->group('d.id');
        $row->order('create_at desc');
        $result = $row->select();
        $grade = array_flip(grade());
        $data = to_arr($result);
        foreach ($data as $key => $value) {
            $grade_id_arr = explode(',',$value['grade_id']);
            $grade_name_arr = [];
            foreach ($grade_id_arr as $vg){
                $grade_name_arr[] = $grade[$vg] ?? '';
            }
            $data[$key]['grade'] = implode(array_unique($grade_name_arr),',');
            $i = $j = 0;
            if($value['status'] == 1){
                $data[$key]['evaluate_state'] = '已禁用';
            }else{
                $data[$key]['evaluate_state'] = '正常';
            }
            if(isset($has_shengya[$value['id']]) && !empty($has_shengya[$value['id']])){
                $data[$key]['has_shengya'] = '已完成';
                foreach ($value['papers'] as $k => $v){
                    if($v['detail'] === null){
                        unset($data[$key]['papers'][$k]);
                    }else{
                        $data[$key]['papers'][$k]['detail']['grade_name'] = $grade[$v['detail']['grade_id']] ?? '';
                        if(date('Y-m-d H:i:s') < $v['start_time'] ){
                            $data[$key]['papers'][$k]['evaluate_state'] = '暂未开始';
                            $i++;//本次测评所有试卷只要有一个未完成，则本次测评未完成，暂未开始和开始测评都属于未完成
                        }elseif(!isset($personal_arr[$v['distribution_id'].'-'.$v['paper_id']])) {
                            $data[$key]['papers'][$k]['evaluate_state'] = '开始测评';
                            $i++;//本次测评所有试卷只要有一个未完成，则本次测评未完成，暂未开始和开始测评都属于未完成
                        }else{
                            if($personal_arr[$v['distribution_id'].'-'.$v['paper_id']]['check_status'] == 0){
                                $data[$key]['papers'][$k]['evaluate_state'] = '待批阅';
                                $j++;//本次测评所有试卷都完成了，只要有一个未批改，则本次测评要等待报告
                            }elseif ($personal_arr[$v['distribution_id'].'-'.$v['paper_id']]['check_status'] == 1){
                                $data[$key]['papers'][$k]['evaluate_state'] = '已批阅';
                            }
                        }
                    }
                }
            }else{
                $data[$key]['has_shengya'] = '开始测评';
                foreach ($value['papers'] as $k => $v){
                    if($v['detail'] === null){
                        unset($data[$key]['papers'][$k]);
                    }else{
                        $data[$key]['papers'][$k]['detail']['grade_name'] = $grade[$v['detail']['grade_id']] ?? '';
                        $data[$key]['papers'][$k]['evaluate_state'] = '暂未开始';
                        $i++;//本次测评所有试卷只要有一个未完成，则本次测评未完成，暂未开始和开始测评都属于未完成
                    }
                }
            }
            if(empty($data[$key]['papers'])) {
                //没查询到试卷名称就直接过滤掉
                unset($data[$key]);
            }else{
                $data[$key]['papers'] = array_values($data[$key]['papers']);
                if($i > 0){
                    $data[$key]['evaluate_state'] = '未完成';
                }elseif($j > 0 || $value['is_completed'] == 0){//整个测评没有结束，也无法查看，因为需要所有人都完成算平均分
                    $data[$key]['evaluate_state'] = '等待报告';
                }elseif($value['is_completed'] == 1){
                    $data[$key]['evaluate_state'] = '查看报告';
                }
            }
        }
        return pageing($data,$pageSize,$pageNumber);

    }

    public function take_district_grade_class_student_linkage(){
        $level = input('level');
        $sql = "SELECT g.id grade_id,g.name as grade_year,g.grade_name,g.create_time,g.grade_sort,sch.name as school_name,dis.id as school_district_id,dis.campus_name,cl.id as class_id,cl.name as class_name,stu.member_id,stu.name
                FROM
                (
                    select t1.id,t1.name,t1.grade_name,t1.school_id,t1.school_district,t1.create_time,t1.grade_sort
                    from  ysy_grade  t1
                    JOIN (  
                        SELECT name,school_district, MAX(id) AS max_id  
                        FROM ysy_grade  
                        where school_id = ".$this->user['school_id']." and step >= 0
                        GROUP BY name,school_district  
                    ) t2 ON t1.name = t2.name and t1.school_district = t2.school_district AND t1.id = t2.max_id
                    where t1.school_id = ".$this->user['school_id']." and step >= 0
                ) g
                LEFT JOIN ysy_school sch ON g.school_id = sch.id
                LEFT JOIN ysy_class cl ON g.id = cl.grade_id
                LEFT JOIN ysy_school_district dis ON g.school_district = dis.id
                LEFT JOIN ysy_student stu ON stu.class_id = cl.id
                ";
        if($this->user['role_source_id'] == 2){
            $sql .="WHERE sch.step = 0 and cl.step = 0 and dis.step=0 and stu.step=0";
        }elseif($this->user['role_source_id'] == 3){
            $sql .= "LEFT JOIN ysy_teacher tea ON find_in_set(cl.id,tea.class_ids)
                WHERE sch.step = 0 and cl.step = 0 and dis.step=0 and stu.step=0 and tea.member_id = ".$this->user['id'];
        }
        $lists = Db::query($sql);
        //过滤学年制年份外的数据
        $school_year = db('grade')->where(['school_id' => $this->user['school_id'],'step'=>['>=',0],'school_year'=>['>=',3]])->value('school_year');
        $school_year = $school_year ?? 3;
        $year  = date('Y');
        $month = date('m');
        if($month > 8){
            $time = $year - $school_year + 1;
        }else{
            $time = $year - $school_year;
        }
        $temp  = [];
        foreach($lists as $k => $v){
            if($v['grade_year'] >= $time){
                $temp[]= $v;
            }
        }
        $new_data = [];
        //这么写是因为前端希望根据level来展示到几级
        switch ($level){
            case 1:
                foreach ($temp as $k => $v) {
                    $new_data[$v['school_name'] . $v['campus_name']]['label']                             = $v['school_name'] . '(' . $v['campus_name'] . ')';
                    $new_data[$v['school_name'] . $v['campus_name']]['value']                             = $v['school_district_id'];
                    $new_data[$v['school_name'] . $v['campus_name']]['level']                             = 0;
                    $new_data[$v['school_name'] . $v['campus_name']]['children'][$v['grade_id']]['label'] = $v['grade_year'] . '-' . $v['grade_name'];
                    $new_data[$v['school_name'] . $v['campus_name']]['children'][$v['grade_id']]['value'] = intval($v['grade_sort']);
                    $new_data[$v['school_name'] . $v['campus_name']]['children'][$v['grade_id']]['level'] = 1;
                }
                break;
            case 2:
                foreach ($temp as $k => $v){
                    $new_data[$v['school_name'].$v['campus_name']]['label'] = $v['school_name'].'('.$v['campus_name'].')';
                    $new_data[$v['school_name'].$v['campus_name']]['value'] = $v['school_district_id'];
                    $new_data[$v['school_name'].$v['campus_name']]['level'] = 0;
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['label'] = $v['grade_year'].'-'.$v['grade_name'];
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['value'] = intval($v['grade_sort']);
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['level'] = 1;
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['id']    = $v['grade_id'];
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['children'][$v['class_id']]['label'] = $v['class_name'];
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['children'][$v['class_id']]['value'] = $v['class_id'];
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['children'][$v['class_id']]['level'] = 2;
                }
                break;
            default:
                foreach ($temp as $k => $v){
                    $new_data[$v['school_name'].$v['campus_name']]['label'] = $v['school_name'].'('.$v['campus_name'].')';
                    $new_data[$v['school_name'].$v['campus_name']]['value'] = $v['school_district_id'];
                    $new_data[$v['school_name'].$v['campus_name']]['level'] = 0;
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['label'] = $v['grade_year'].'-'.$v['grade_name'];
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['value'] = intval($v['grade_sort']);
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['level'] = 1;
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['id']    = $v['grade_id'];
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['children'][$v['class_id']]['label'] = $v['class_name'];
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['children'][$v['class_id']]['value'] = $v['class_id'];
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['children'][$v['class_id']]['level'] = 2;
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['children'][$v['class_id']]['children'][$v['member_id']]['label'] = $v['name'];
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['children'][$v['class_id']]['children'][$v['member_id']]['value'] = $v['member_id'];
                    $new_data[$v['school_name'].$v['campus_name']]['children'][$v['grade_id']]['children'][$v['class_id']]['children'][$v['member_id']]['level'] = 3;
                }
                break;
        }
        return array_values($this->tree1($new_data));
    }

    public function take_course_grade_paper_linkage(){
        $course = array_flip(course());
        $grade = array_flip(grade());
        $data = $this->paperModel->field('id,course_id,paper_name,grade_id,has_subjective')->where('status',0)->select();
        $data = to_arr($data);
        foreach ($data as $k => $v){
            if(!isset($course[$v['course_id']]) || !isset($grade[$v['grade_id']])) continue;
            $new_data[$course[$v['course_id']]]['label'] = $course[$v['course_id']];
            $new_data[$course[$v['course_id']]]['value'] = $v['course_id'];
            $new_data[$course[$v['course_id']]]['level'] = 0;
            $new_data[$course[$v['course_id']]]['children'][$grade[$v['grade_id']]]['label'] = $grade[$v['grade_id']];
            $new_data[$course[$v['course_id']]]['children'][$grade[$v['grade_id']]]['value'] = $v['grade_id'];
            $new_data[$course[$v['course_id']]]['children'][$grade[$v['grade_id']]]['level'] = 1;
            $new_data[$course[$v['course_id']]]['children'][$grade[$v['grade_id']]]['children'][$v['paper_name']]['label'] = $v['paper_name'];
            $new_data[$course[$v['course_id']]]['children'][$grade[$v['grade_id']]]['children'][$v['paper_name']]['value'] = $v['id'];
            $new_data[$course[$v['course_id']]]['children'][$grade[$v['grade_id']]]['children'][$v['paper_name']]['has_subjective'] = $v['has_subjective'];
            $new_data[$course[$v['course_id']]]['children'][$grade[$v['grade_id']]]['children'][$v['paper_name']]['level'] = 2;
        }
        return array_values($this->tree1($new_data));
    }

    /**
     * 格式化数组键名
     * @param $data
     * @return array
     */
    public function tree1($data){
        foreach ($data as $k =>$v){
            if(isset($v['children'])){
                $data[$k]['children'] = array_values($this->tree1($v['children']));
            }
        }
        return array_values($data);
    }

    public function take_schools(){
        return $this->schoolModel->where(['step'=>0])->field('id,name')->select();
    }

    public function take_teachers(){
        return $this->teacherModel->get_tea_info(['school_id'=>$this->user['school_id'],'step'=>0],'id,member_id,name');
    }

    public function distribution_name_list(){
        $model = new DistributionModel();
        $where = "`school_ids` = '229' AND `status` = 0 AND `role_id` != '0,999,0'";
        $class_id = input('class_id');
        if(!empty($class_id)){
            $class_id_arr = explode(',',$class_id);
            foreach ($class_id_arr as $v){
                $where .= ' and find_in_set('.$v.', class_ids) > 0';
            }
        }
        return $model->field('id,title')->where($where)->order('id desc')->select();
    }

}