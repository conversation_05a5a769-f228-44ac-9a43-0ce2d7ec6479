<?php

namespace App\Constants;

use App\Enums\PhaseEnum;

/**
 * 专业(Major)相关常量
 *
 * 定义与专业相关的常量和辅助方法
 */
class MajorConstants
{
    /**
     * 默认工作年限
     *
     * 用于计算专业薪资统计时的默认工作年限
     */
    public const DEFAULT_WORKING_AGE = 5;

    /**
     * 默认薪资年份
     *
     * 用于获取专业薪资数据的默认年份
     */
    public const DEFAULT_SALARY_YEAR = 2016;

    /**
     * 专业排名年份
     *
     * 当前使用的专业排名年份
     */
    public const MAJOR_RANK_YEAR = 2024;

    /**
     * 根据专业代码判断学历阶段
     *
     * 根据专业代码的第一个字符判断是本科还是专科
     *
     * @param string $majorCode 专业代码
     * @return int 学历阶段值（PhaseEnum::UNDERGRADUATE 或 PhaseEnum::COLLEGE）
     */
    public static function getMajorPhase(string $majorCode): int
    {
        $firstChar = substr($majorCode, 0, 1);
        return ($firstChar === '4' || $firstChar === '5') ? PhaseEnum::COLLEGE->value : PhaseEnum::UNDERGRADUATE->value;
    }

    /**
     * 获取专业对应的视图表名
     *
     * 根据专业的学历阶段返回相应的视图表名
     *
     * @param int $phase 学历阶段（PhaseEnum::UNDERGRADUATE 或 PhaseEnum::COLLEGE）
     * @return string 视图表名
     */
    public static function getMajorViewTable(int $phase): string
    {
        return $phase === PhaseEnum::UNDERGRADUATE->value ? 'View_Major_BK_Summarizing' : 'View_Major_ZK_Summarizing';
    }
}
