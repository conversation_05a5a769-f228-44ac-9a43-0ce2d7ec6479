<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * 教师带科目数据填充
 */
class TeacherCourseSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 查询新的教师基础表
        $teacherIds = DB::table('teachers')->where('school_id', $this->school_id)
            ->pluck('teacher_id','id')->toArray();
        // 查询老表老师对应的带科目数据
        $teacherCourseList = DB::connection($this->connect)->table('teacher_course_relation')
            ->whereIn('teacher_id', array_values($teacherIds))
            ->select('teacher_id','course_id')
            ->get()->toArray();
        //老表course_id数组
        $courseIdArr = array_column($teacherCourseList,'course_id');
        // 转化科目数据，找到新的科目id，数组结构为老表course_id为键,新表course_id和year为值
        $courseInfo = $this->transformCourse($courseIdArr);
        if(!$courseInfo) dump('查询新表course_id为空');//没查到新表course_id打印空
        $teacherCourseData = [];
        foreach ($teacherCourseList as $item){
            $teacherId = $item->teacher_id;
            // 查找教师ID对应的新ID
            //$new_teacher_id = array_search($item->teacher_id, $teacherIds);
            //查询到新表course_id的才加进数组
            if(isset($courseInfo[$item->course_id])){
                $teacherCourseData[] = [
                    'teacher_id' => $teacherId,
                    'course_id' => $courseInfo[$item->course_id]['course_id'],
                    'school_year' => $courseInfo[$item->course_id]['school_year'],
                    'created_at' => date('Y-m-d H:i:s',time()),
                    'updated_at' => date('Y-m-d H:i:s',time()),
                ];
            }
        }
        // 存储老师和科目关系数据
        DB::table('teacher_courses')->insert($teacherCourseData);
        dump('教师科目转化...end...');
    }

    public function transformCourse($courseIdArr): array
    {
        /// 查询老表科目对应的数据
        $courseList = DB::connection($this->connect)->table('course')
            ->join('grade', 'grade.id', '=', 'course.grade_id')
            ->whereIn('course.id', $courseIdArr)
            ->selectRaw('ysy_course.id as ysy_course_id,ysy_course.name,ysy_course.school_id,ysy_course.school_district,ysy_grade.grade_sort,ysy_grade.name as grade_name')
            ->get();
        // 根据老表科目对应的数据查询新表的科目id
        if(!$courseList) return [];//没查到老表数据返回空
        $courseInfo = [];
        foreach ($courseList as $val){
            $new_course_id = DB::table('courses')
                ->where('course_name', $val->name)
                ->where('school_id', $val->school_id)
                ->where('school_campus_id', $val->school_district)
                ->where('grade_id', $val->grade_sort)
                ->value('id');
            if($new_course_id) {
                $courseInfo[$val->ysy_course_id]['course_id'] = $new_course_id;
                $courseInfo[$val->ysy_course_id]['school_year'] = $this->matchSchoolYear(intval($val->grade_sort), $val->grade_name);
            }
        }
        return $courseInfo;
    }

    public function matchSchoolYear($grade_sort,$grade_year){
        // 判断学生所在教育阶段并计算学年
        if ($grade_sort<=5){ // 小学阶段
            $school_year = $grade_year+$grade_sort-1;
        }elseif ($grade_sort<=9){ // 初中阶段
            $school_year = $grade_year+$grade_sort-6;
        }else{// 高中阶段
            $school_year = $grade_year+$grade_sort-10;
        }
        return $school_year;
    }
}
