<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 分发老师学生分配模型
 */
class DistributionTeachersStudents extends Model
{
    protected $table = 'evaluation_distribution_teachers_students';

    protected $fillable = [
        'distribution_id',
        'paper_id',
        'teacher_member_id',
        'member_ids',
        'status',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'distribution_id' => 'integer',
        'paper_id' => 'integer',
        'teacher_member_id' => 'integer',
        'status' => 'integer',
    ];

    /**
     * 所属分发关联
     */
    public function distribution(): BelongsTo
    {
        return $this->belongsTo(Distribution::class, 'distribution_id');
    }

    /**
     * 所属试卷关联
     */
    public function paper(): BelongsTo
    {
        return $this->belongsTo(Papers::class, 'paper_id');
    }

    /**
     * 老师关联
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'teacher_member_id');
    }

    /**
     * 作用域：按分发筛选
     */
    public function scopeByDistribution($query, $distributionId)
    {
        return $query->where('distribution_id', $distributionId);
    }

    /**
     * 作用域：按试卷筛选
     */
    public function scopeByPaper($query, $paperId)
    {
        return $query->where('paper_id', $paperId);
    }

    /**
     * 作用域：按老师筛选
     */
    public function scopeByTeacher($query, $teacherId)
    {
        return $query->where('teacher_member_id', $teacherId);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 获取学生ID数组
     */
    public function getMemberIdsArray(): array
    {
        if (empty($this->member_ids)) {
            return [];
        }
        return explode(',', $this->member_ids);
    }

    /**
     * 设置学生ID数组
     */
    public function setMemberIdsArray(array $memberIds): void
    {
        $this->member_ids = implode(',', $memberIds);
    }

    /**
     * 获取负责学生数量
     */
    public function getStudentCount(): int
    {
        return count($this->getMemberIdsArray());
    }

    /**
     * 检查是否包含指定学生
     */
    public function hasStudent(int $memberId): bool
    {
        return in_array($memberId, $this->getMemberIdsArray());
    }

    /**
     * 添加学生
     */
    public function addStudent(int $memberId): bool
    {
        $memberIds = $this->getMemberIdsArray();
        if (!in_array($memberId, $memberIds)) {
            $memberIds[] = $memberId;
            $this->setMemberIdsArray($memberIds);
            return $this->save();
        }
        return true;
    }

    /**
     * 移除学生
     */
    public function removeStudent(int $memberId): bool
    {
        $memberIds = $this->getMemberIdsArray();
        $key = array_search($memberId, $memberIds);
        if ($key !== false) {
            unset($memberIds[$key]);
            $this->setMemberIdsArray(array_values($memberIds));
            return $this->save();
        }
        return true;
    }

    /**
     * 获取老师负责的答题记录
     */
    public function getAnswerRecords()
    {
        $memberIds = $this->getMemberIdsArray();
        if (empty($memberIds)) {
            return collect();
        }

        return EvaluationAnswer::where('distribution_id', $this->distribution_id)
            ->where('paper_id', $this->paper_id)
            ->whereIn('member_id', $memberIds)
            ->get();
    }

    /**
     * 获取老师负责的答题日志
     */
    public function getAnswerLogs()
    {
        $memberIds = $this->getMemberIdsArray();
        if (empty($memberIds)) {
            return collect();
        }

        return EvaluationLog::where('distribution_id', $this->distribution_id)
            ->where('paper_id', $this->paper_id)
            ->whereIn('member_id', $memberIds)
            ->get();
    }

    /**
     * 获取批改进度
     */
    public function getGradingProgress(): array
    {
        $logs = $this->getAnswerLogs();
        $total = $logs->count();
        $completed = $logs->where('check_status', 1)->count();
        $pending = $logs->where('check_status', 0)->count();

        return [
            'total' => $total,
            'completed' => $completed,
            'pending' => $pending,
            'completion_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0
        ];
    }

    /**
     * 软删除
     */
    public function softDelete(): bool
    {
        return $this->update(['status' => -1]);
    }

    /**
     * 恢复软删除
     */
    public function restore(): bool
    {
        return $this->update(['status' => 0]);
    }
}
