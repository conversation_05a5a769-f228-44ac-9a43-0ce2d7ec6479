<?php

namespace App\Http\Controllers\School\Xuezhi;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\Xuezhi\DataQueryRequest;
use App\Http\Resources\CollegeScoreCollection;
use App\Http\Resources\MajorPlanCollection;
use App\Http\Resources\MajorScoreCollection;
use App\Services\School\Xuezhi\CollegeService;
use App\Services\School\Xuezhi\DataQueryService;
use App\Traits\PaginationTrait;
use Illuminate\Http\JsonResponse;

class DataQueryController extends Controller
{
    use PaginationTrait;

    protected DataQueryService $service;

    public function __construct(DataQueryService $service)
    {
        $this->service = $service;
    }


    /**
     * 获取年份列表
     */
    public function yearList(DataQueryRequest $request): JsonResponse
    {
        $provinceId = $request['province_id'];
        $queryType = $request['query_type'];
        $data = $this->service->yearList($provinceId, $queryType);
        return $this->success($data);
    }

    public function liberalSciences(DataQueryRequest $request)
    {
        $provinceId = $request['province_id'];
        $query_type = $request['query_type'];
        $year = $request['year'];
        $data = $this->service->getLiberalSciencesNew($provinceId, $query_type, $year);
        return $this->success($data);
    }


    /**
     * collegeScore
     * 院校录取数据
     * @param DataQueryRequest $request
     * @param CollegeService $service
     */
    public function collegeScore(DataQueryRequest $request): JsonResponse
    {
        $query = $this->service->collegeScoreListBuilder($request);
        $list = CollegeScoreCollection::collection($query->get());
        return $this->success($list);
    }

    /**
     * majorScore
     * 按院校 查询专业录取数据
     * @param DataQueryRequest $request
     * @param CollegeService $service
     */
    public function majorScore(DataQueryRequest $request)
    {
        $query = $this->service->majorScoreListBuilder($request);
        $cnt = $query->count();
        // 使用自定义的分页方法
        $list = $this->scopePagination($query)->get();
        $list = MajorScoreCollection::collection($list);
        // return $this->success(compact('list', 'cnt'));
        return $this->paginateSuccess($list, $cnt);
    }

    /**
     * majorPlan
     * 招生计划-按院校查询
     * @param DataQueryRequest $request
     * @param CollegeService $service
     */
    public function majorPlan(DataQueryRequest $request)
    {
        $query = $this->service->majorPlanListBuilder($request);
        $cnt = $query->count();
        $list = $this->scopePagination($query)->get();
        $list = MajorPlanCollection::collection($list);
        // return $this->success(compact('list', 'cnt'));
        return $this->paginateSuccess($list, $cnt);
    }



}
