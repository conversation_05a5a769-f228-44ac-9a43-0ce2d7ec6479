<?php
/**
 * 家长信息表
 */
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('parents', function (Blueprint $table) {
            $table->id();
            $table->string('name',20)->comment('家长姓名');
            $table->string('phone',20)->comment('家长手机号');
            $table->integer('user_id')->comment('用户ID');
            $table->enum('relations',['父亲','母亲','其他'])->comment('关系');
            $table->integer('student_id')->comment('绑定学生ID');
            $table->tinyInteger('status')->default(1)->comment('状态1启用2禁用');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('parents');
    }
};
