<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */

namespace app\evaluation\controller;

use app\login\controller\ApiAuth;

/**
 * Class Questions
 * @package app\evaluation\controller
 */
class Report extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->Report = new \app\evaluation\service\Report();
    }
    /**
     * 模块：素养测评-个体报告
     * @SWG\Get(path="/evaluation/personal_report",
     *   tags={"素养测评-个体报告:report"},
     *   summary="六年级测评个体报告",
     *  @SWG\Parameter(
     *     in="query",
     *     name="distribution_id",
     *     type="integer",
     *     description="分发id",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="query",
     *     name="member_id",
     *     type="integer",
     *     description="学生member_id",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    public function personal_report(){
        $data = $this->Report->personal_report();
        apiReturn($data);
    }

    public function settlement(){
        $data = $this->Report->settlement();
        apiReturn($data);
    }

    public function all_settlement(){
        $data = $this->Report->all_settlement();
        apiReturn($data);
    }


}