<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */
namespace app\psychassessment\controller;
use app\login\controller\ApiAuth;

/**
 * Class Questions
 * @package app\psychassessment\controller
 */
class CounselorRecord extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->Record = new \app\psychassessment\service\CounselorRecord();
    }
    /**
     * 模块：心理评估-辅导记录
     * @SWG\Post(path="/psychassessment/counselor_record",
     *   tags={"心理评估-辅导:counselor_record"},
     *   summary="辅导记录录入",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="student_appointment_id",
     *     type="integer",
     *     description="关联预约表的id",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="title",
     *     type="string",
     *     description="辅导标题",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="teacher_id",
     *     type="integer",
     *     description="辅导老师的ID",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="member_id",
     *     type="integer",
     *     description="学生的member_id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="care_level",
     *     type="integer",
     *     description="关注等级0: 无需关注，1：重点关注",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="reason",
     *     type="string",
     *     description="注意重点关注才需要写原因，此字段录入focus表中",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="start_time",
     *     type="string",
     *     description="辅导开始时间",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="end_time",
     *     type="string",
     *     description="辅导结束时间",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="advice",
     *     type="string",
     *     description="辅导处理意见",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="annex",
     *     type="string",
     *     description="附件地址,多个用,拼接",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="content",
     *     type="string",
     *     description="辅导内容",
     *     required=true,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：心理评估-辅导记录
     * @SWG\Put(path="/psychassessment/counselor_record/{id}",
     *   tags={"心理评估-辅导:counselor_record"},
     *   summary="辅导记录修改",
     * @SWG\Parameter(
     * in="path",
     * name="id",
     * type="integer",
     * description="辅导记录id",
     * required=true,
     * ),
     * @SWG\Parameter(
     * in="body",
     * name="data",
     * description="辅导记录修改",
     * required=true,
     * @SWG\Schema(
     *          type="object",
     *          @SWG\Property(property="title", type="string", description="辅导主题"),
     *          @SWG\Property(property="student_appointment_id", type="integer", description="关联预约表的id"),
     *          @SWG\Property(property="start_time", type="string", description="辅导开始时间"),
     *          @SWG\Property(property="end_time", type="string", description="辅导结束时间"),
     *          @SWG\Property(property="member_id", type="integer", description="辅导学生的member_id"),
     *          @SWG\Property(property="care_level", type="integer", description="关注等级0: 无需关注，1：重点关注"),
     *          @SWG\Property(property="advice", type="string", description="辅导意见"),
     *          @SWG\Property(property="content", type="string", description="辅导内容"),
     *          @SWG\Property(property="annex", type="string", description="附件地址"),
     *  )
     * ),
     * description="数据说明：",
     * produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：心理评估-学生辅导记录
     * @SWG\Delete(path="/psychassessment/counselor_record/{id}",
     *   tags={"心理评估-辅导:counselor_record"},
     *   summary="删除记录",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：心理评估-学生辅导记录
     * @SWG\Get(path="/psychassessment/counselor_record",
     *   tags={"心理评估-辅导:counselor_record"},
     *   summary="记录查询",
     *   @SWG\Parameter(
     *     in="query",
     *     name="member_id",
     *     type="integer",
     *     description="学生member_id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="grade_id",
     *     type="string",
     *     description="年级id,多个用,拼接",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="class_id",
     *     type="string",
     *     description="班级id,多个用,拼接",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="name",
     *     type="string",
     *     description="学生姓名或学号",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="start_date",
     *     type="string",
     *     description="开始时间，格式为 'YYYY-MM-DD '",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="end_date",
     *     type="string",
     *     description="结束时间",
     *     required=false,
     *   ),
     *   description="数据说明",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    public function CounselorRecord(){

        $data = $this->Record->hand_out();
        apiReturn($data);
    }

    /**
     * 模块：心理评估-学生辅导记录
     * @SWG\Post(path="/psychassessment/uploads",
     *   tags={"心理评估-辅导:counselor_record"},
     *   summary="上传图片",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="file",
     *     type="file",
     *     description="图片",
     *     required=true,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function uploads(){
        apiReturn(sftpUpload('/psychassessment/'));
    }

    /**
     * 模块：心理评估-学生辅导记录
     * @SWG\Get(path="/psychassessment/get_appointment_student_list",
     *   tags={"心理评估-辅导:counselor_record"},
     *   summary="记录查询",
     *   @SWG\Parameter(
     *     in="query",
     *     name="name",
     *     type="string",
     *     description="学生姓名",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="page",
     *     type="integer",
     *     description="页码",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="pagesize",
     *     type="integer",
     *     description="页数量",
     *     required=false,
     *   ),
     *   description="数据说明",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function get_appointment_student_list(){

        $data = $this->Record->get_appointment_student_list();
        apiReturn($data);
    }

    /**
     * 模块：心理评估-学生辅导记录
     * @SWG\Get(path="/psychassessment/down_counselor_record",
     *   tags={"心理评估-辅导:counselor_record"},
     *   summary="下载辅导记录",
     *   @SWG\Parameter(
     *     in="query",
     *     name="ids",
     *     type="string",
     *     description="多个辅导记录id用都,拼接",
     *     required=false,
     *   ),
     *   description="数据说明",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function down_counselor_record(){
        $data = $this->Record->down_counselor_record();
        apiReturn($data);
    }

    /**
     * 模块：心理评估-学生辅导记录
     * @SWG\Get(path="/psychassessment/counselor_head_info",
     *   tags={"心理评估-辅导:counselor_record"},
     *   summary="辅导记录页头部信息",
     *   description="数据说明",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function counselor_head_info(){
        $data = $this->Record->counselor_head_info();
        apiReturn($data);
    }

    /**
     * 模块：心理评估-学生辅导记录
     * @SWG\Get(path="/psychassessment/counselor_wait",
     *   tags={"心理评估-辅导:counselor_record"},
     *   summary="待约谈学生",
     *   @SWG\Parameter(
     *     in="query",
     *     name="name",
     *     type="string",
     *     description="学生姓名",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="page",
     *     type="integer",
     *     description="页码",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="pagesize",
     *     type="integer",
     *     description="页数量",
     *     required=false,
     *   ),
     *   description="数据说明",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function counselor_wait(){
        $data = $this->Record->counselor_wait();
        apiReturn($data);
    }
}