<?php

namespace App\Services\School\System;

use App\Models\School\System\School;
use App\Models\School\System\SchoolCampus;
use App\Services\BaseService;
use Illuminate\Http\Request;
use App\Constants\SchoolConstants;
use App\Models\School\System\Claass;
use App\Models\School\System\Grade;
use App\Http\Requests\School\System\SchoolRequest;

class SchoolService extends BaseService
{

    public function updateSchool(Request $request, $id)
    {
        $record = School::find($id);
        if (!$record) {
            $this->throwBusinessException('更新对象不存在');
        }
        $data = filterRequestData('schools');
        $data['updater'] = $request->user()->real_name;
        $record->fill($data)->save();
        return $record;
    }

    //创建校区模型：SchoolCampus
    public function createSchoolCampus($data, $real_name)
    {
        $data['creator'] = $real_name;
        $data['updater'] = $real_name;
        return SchoolCampus::forceCreate($data);
    }

    public function getSchoolCampus($school_id)
    {
        return SchoolCampus::where('school_id', $school_id)->with(['partner'])->get();
    }

    public function getAllSchoolCampus(Request $request)
    {
        // 获取请求中的省、市、区参数
        $province = $request->input('province');
        $city = $request->input('city');
        $district = $request->input('district');
        // 查询 SchoolCampus 表，关联 School 表，并根据省、市、区进行过滤
        $campuses = SchoolCampus::where('status', 1)
            ->with(['school' => function ($query) use ($province, $city, $district) {
                $query->filterByLocation($province, $city, $district);
            }])
            ->hasByNonDependentSubquery('school', function ($query) use ($province, $city, $district) {
                $query->filterByLocation($province, $city, $district);
            })
            ->orderBy('id')
            ->get();
        return $campuses;
    }


    

/**
 * 获取班级数据，用于班级列表穿梭框使用
 * 返回层级结构：校区->年级->班级（不包含学生）
 *
 * @param SchoolRequest $request
 * @return array
 */
public function classTransferData(SchoolRequest $request)
{
    // 获取学校ID
    $school_id = $request->input('school_id');
    $school = School::find($school_id);
    $isShanghai = SchoolConstants::isShanghai($school->province);

    // 1. 一次性获取所有校区
    $campuses = SchoolCampus::where('school_id', $school_id)
        ->where('status', 1)
        ->get(['id', 'campus_name', 'type']);

    // 2. 获取所有校区类型对应的年级ID
    $allGradeIds = [];
    foreach ($campuses as $campus) {
        $gradeIds = SchoolConstants::getGradeIdsByType($campus->type, $isShanghai);
        $allGradeIds = array_merge($allGradeIds, $gradeIds);
    }
    $allGradeIds = array_unique($allGradeIds);

    // 3. 一次性获取所有年级
    $gradesQuery = Grade::whereIn('id', $allGradeIds);
    if ($isShanghai) {
        // 如果是上海地区，使用别名作为年级名称
        $grades = $gradesQuery->get(['id', 'alisa_name as grade_name'])->keyBy('id');
    } else {
        $grades = $gradesQuery->get(['id', 'grade_name'])->keyBy('id');
    }

    // 4. 一次性获取所有班级
    $classes = Claass::where('school_id', $school_id)
        ->whereIn('school_campus_id', $campuses->pluck('id'))
        ->whereIn('grade_id', $allGradeIds)
        ->get(['id', 'class_name', 'school_campus_id', 'grade_id'])
        ->groupBy(['school_campus_id', 'grade_id']);

    // 5. 构建结果树（只到班级层级）
    $result = [];

    foreach ($campuses as $campus) {
        $campusData = [
            'label' => $campus->campus_name,
            'value' => $campus->id,
            'level' => 0,
            'children' => []
        ];

        $gradeIds = SchoolConstants::getGradeIdsByType($campus->type, $isShanghai);
        $hasGrades = false;

        foreach ($gradeIds as $gradeId) {
            if (!isset($grades[$gradeId])) continue;

            $grade = $grades[$gradeId];
            $gradeData = [
                'label' => $grade->grade_name,
                'value' => $grade->id,
                'level' => 1,
                'children' => []
            ];

            // 获取该校区和年级下的所有班级
            $campusGradeClasses = $classes[$campus->id][$grade->id] ?? collect();
            $hasClasses = false;

            foreach ($campusGradeClasses as $class) {
                $classData = [
                    'label' => $class->class_name,
                    'value' => $class->id,
                    'level' => 2,
                    'isLeaf' => true  // 班级是叶子节点，不再有子级
                ];

                $gradeData['children'][] = $classData;
                $hasClasses = true;
            }

            // 只添加有班级的年级
            if ($hasClasses) {
                $campusData['children'][] = $gradeData;
                $hasGrades = true;
            }
        }

        // 只添加有年级的校区
        if ($hasGrades) {
            $result[] = $campusData;
        }
    }

    return $result;
}


}
