<?php
namespace App\Services\School\Assessment\IndividualReport\Career;

use App\Services\School\Assessment\IndividualReport\AbstractIndividualReportService;
use App\Models\School\Assessment\Template\AssessmentCareerMiTemplate;

class IntelligenceService extends AbstractIndividualReportService
{
    // 智能维度分组常量
    private const EXPECTED_DIMENSIONS_COUNT = 8;
    private const MIN_DIMENSIONS_COUNT = 6;
    private const STRENGTHS_COUNT = 2;
    private const INTERMEDIATE_COUNT = 4;
    private const NEEDS_IMPROVEMENT_COUNT = 2;

    public function generateReport(array $params): array
    {
        $assignmentId = $params['assessment_task_assignment_id'];
        $assessmentId = $params['assessment_id'];
        // 获取分发信息
        $assignmentInfo = $this->getAssignmentInfo($assignmentId);
        
        // 生成报告
        // $results = json_decode($assignmentInfo['standard_results'], true);
        
        // 只使用standard_results字段
        $standardResults = $assignmentInfo['standard_results'];
        if (is_string($standardResults)) {
            $results = json_decode($standardResults, true);
        } else {
            $results = $standardResults;
        }

        $assessmentInfo = $this->intelligenceInfo($results, $assessmentId);

        // 合并结果
        return array_merge($assignmentInfo, $assessmentInfo);
    }

    /**
     * 解析智能评估结果
     */
    private function intelligenceInfo($result, $assessmentId): array
    {
        // 获取智能模板数据
        return $this->getIntelligenceTemplateData($result, $assessmentId);
    }

    /**
     * 获取智能模板数据
     *
     * @param mixed $result 测评结果
     * @param int $assessmentId 测评ID
     * @return array 模板数据
     */
    private function getIntelligenceTemplateData($result, int $assessmentId): array
    {
        // 1. 数据预处理和验证
        $parsedResult = $this->parseAndValidateResult($result);
        $gradeType = getGradeType($assessmentId);

        // 2. 智能分析（一次性完成排序和分组）
        $intelligenceAnalysis = $this->analyzeIntelligenceLevels($parsedResult['dimensions']);

        // 3. 批量获取所有需要的模板（减少数据库查询）
        $templates = $this->batchLoadTemplates($intelligenceAnalysis, $gradeType);

        // 4. 构建模板数据
        $templateData = $this->buildTemplateData($intelligenceAnalysis, $templates, $parsedResult, $gradeType);

        return $templateData;
    }

    /**
     * 解析和验证结果数据
     *
     * @param mixed $result 原始结果数据
     * @return array 解析后的结果
     * @throws \InvalidArgumentException 当数据格式无效时
     */
    private function parseAndValidateResult($result): array
    {
        // 检查结果是否为新格式（包含dimensions数组）
        if (is_array($result) && isset($result['dimensions'])) {
            $parsedResult = $result;
        } else {
            // 兼容旧格式，转换为新格式
            $parsedResult = $this->convertLegacyFormat($result);
        }

        // 验证维度数据
        if (!isset($parsedResult['dimensions']) || !is_array($parsedResult['dimensions'])) {
            throw new \InvalidArgumentException('Invalid dimensions data format');
        }

        $dimensionCount = count($parsedResult['dimensions']);
        if ($dimensionCount < self::MIN_DIMENSIONS_COUNT) {
            \Log::warning("智能维度数量不足", [
                'expected' => self::EXPECTED_DIMENSIONS_COUNT,
                'actual' => $dimensionCount
            ]);
        }

        return $parsedResult;
    }

    /**
     * 批量加载所有需要的模板
     *
     * @param array $intelligenceAnalysis 智能分析结果
     * @param int|null $gradeType 年级类型
     * @return array 模板数据
     */
    private function batchLoadTemplates(array $intelligenceAnalysis, ?int $gradeType): array
    {
        // 收集所有需要查询的智能类型
        $allIntelligenceTypes = array_merge(
            $intelligenceAnalysis['strengths'],
            $intelligenceAnalysis['intermediate'],
            $intelligenceAnalysis['needs_improvement']
        );

        // 批量查询智能维度分析模板
        $dimensionTemplates = AssessmentCareerMiTemplate::where('type', '智能维度分析')
            ->whereIn('intelligence_type', $allIntelligenceTypes)
            ->get()
            ->keyBy('intelligence_type');

        // 批量查询优势智能发展建议模板
        $advantageTemplates = AssessmentCareerMiTemplate::where('type', '优势智能发展建议')
            ->whereIn('intelligence_type', $intelligenceAnalysis['strengths'])
            ->when($gradeType !== null, function($query) use ($gradeType) {
                return $query->where('grade_type', $gradeType);
            })
            ->get()
            ->keyBy('intelligence_type');

        // 批量查询弱势智能发展建议模板
        $weaknessTemplates = AssessmentCareerMiTemplate::where('type', '弱势智能发展建议')
            ->whereIn('intelligence_type', $intelligenceAnalysis['needs_improvement'])
            ->when($gradeType !== null, function($query) use ($gradeType) {
                return $query->where('grade_type', $gradeType);
            })
            ->get()
            ->keyBy('intelligence_type');

        return [
            'dimension' => $dimensionTemplates,
            'advantages' => $advantageTemplates,
            'weaknesses' => $weaknessTemplates
        ];
    }

    /**
     * 构建模板数据
     *
     * @param array $intelligenceAnalysis 智能分析结果
     * @param array $templates 模板数据
     * @param array $parsedResult 解析后的结果
     * @param int|null $gradeType 年级类型
     * @return array 构建的模板数据
     */
    private function buildTemplateData(array $intelligenceAnalysis, array $templates, array $parsedResult, ?int $gradeType): array
    {
        $templateData = [];

        // 1. 基础智能分析数据
        // $templateData['intelligence_result'] = $intelligenceAnalysis['intelligence_result'];
        $templateData['intelligence_type'] = json_decode($intelligenceAnalysis['intelligence_type'], true);

        // 2. 构建分组的维度数据
        $templateData['dimensions'] = $this->buildGroupedDimensions($parsedResult['dimensions'], $templates['dimension'], $gradeType);

        // 3. 获取优势和弱势智能发展建议
        $templateData['advantages_development'] = $this->buildAdvantagesFromTemplates($intelligenceAnalysis['strengths'], $templates['advantages']);
        $templateData['weaknesses_development'] = $this->buildWeaknessesFromTemplates($intelligenceAnalysis['needs_improvement'], $templates['weaknesses']);

        // 4. 获取测评结果概览
        $templateData['result_overview'] = $this->getResultOverviewTemplate($intelligenceAnalysis['strengths']);

        // 5. 获取综合类型模板
        $templateData = array_merge($templateData, $this->buildComprehensiveTemplates($intelligenceAnalysis, $gradeType));

        // 6. 添加统计数据
        $this->addStatisticsData($templateData, $parsedResult);

        return $templateData;
    }

    /**
     * 构建分组的维度数据
     *
     * @param array $dimensions 原始维度数据
     * @param \Illuminate\Database\Eloquent\Collection $templates 模板集合
     * @param int|null $gradeType 年级类型
     * @return array 分组的维度数据
     */
    private function buildGroupedDimensions(array $dimensions, $templates, ?int $gradeType): array
    {
        // 按分数排序所有维度（从高到低）
        $sortedDimensions = $dimensions;
        usort($sortedDimensions, fn($a, $b) => ($b['score'] ?? 0) <=> ($a['score'] ?? 0));

        // 使用常量进行分组
        $strengthsDimensions = array_slice($sortedDimensions, 0, self::STRENGTHS_COUNT);
        $intermediateDimensions = array_slice($sortedDimensions, self::STRENGTHS_COUNT, self::INTERMEDIATE_COUNT);
        $needsImprovementDimensions = array_slice($sortedDimensions, self::STRENGTHS_COUNT + self::INTERMEDIATE_COUNT, self::NEEDS_IMPROVEMENT_COUNT);

        return [
            'strengths' => $this->buildDimensionGroup($strengthsDimensions, 'advantage', $templates),
            'intermediate' => $this->buildDimensionGroup($intermediateDimensions, 'medium', $templates),
            'needs_improvement' => $this->buildDimensionGroup($needsImprovementDimensions, 'disadvantage', $templates)
        ];
    }

    /**
     * 构建维度组数据
     *
     * @param array $dimensions 维度数组
     * @param string $level 等级类型
     * @param \Illuminate\Database\Eloquent\Collection $templates 模板集合
     * @return array 构建的维度组数据
     */
    private function buildDimensionGroup(array $dimensions, string $level, $templates): array
    {
        $result = [];
        foreach ($dimensions as $dimension) {
            $name = $dimension['name'] ?? '';
            $template = $templates->get($name);

            $content = null;
            if ($template) {
                $templateContent = is_string($template->content)
                    ? json_decode($template->content, true)
                    : $template->content;

                if (is_array($templateContent) && isset($templateContent[$level])) {
                    $content = $templateContent[$level];
                }
            } else {
                \Log::warning("未找到智能维度分析模板", [
                    'intelligence_type' => $name,
                    'level' => $level
                ]);
            }

            $result[] = [
                'code' => $dimension['code'] ?? '',
                'content' => $content,
                'name' => $name,
                'score' => $dimension['score'] ?? 0
            ];
        }
        return $result;
    }

    /**
     * 从模板构建优势智能发展建议
     *
     * @param array $strengths 优势智能列表
     * @param \Illuminate\Database\Eloquent\Collection $templates 模板集合
     * @return array 优势智能发展建议
     */
    private function buildAdvantagesFromTemplates(array $strengths, $templates): array
    {
        $result = [];
        foreach ($strengths as $strength) {
            $template = $templates->get($strength);
            if ($template) {
                $content = is_string($template->content)
                    ? json_decode($template->content, true)
                    : $template->content;
                $result[] = $content;
            }
        }
        return $result;
    }

    /**
     * 从模板构建弱势智能发展建议
     *
     * @param array $weaknesses 弱势智能列表
     * @param \Illuminate\Database\Eloquent\Collection $templates 模板集合
     * @return array 弱势智能发展建议
     */
    private function buildWeaknessesFromTemplates(array $weaknesses, $templates): array
    {
        $result = [];
        foreach ($weaknesses as $weakness) {
            $template = $templates->get($weakness);
            if ($template) {
                $content = is_string($template->content)
                    ? json_decode($template->content, true)
                    : $template->content;
                $result[] = $content;
            }
        }
        return $result;
    }

    /**
     * 构建综合类型模板
     *
     * @param array $intelligenceAnalysis 智能分析结果
     * @param int|null $gradeType 年级类型
     * @return array 综合类型模板数据
     */
    private function buildComprehensiveTemplates(array $intelligenceAnalysis, ?int $gradeType): array
    {
        $comprehensiveTypes = [
            'learning_strategy' => '综合学习策略',
            'subject_guidance' => '学科选择与发展指导',
            'career_direction' => '生涯发展方向',
            'summary_message' => '总结与寄语'
        ];

        // 如果是高中，添加选科建议
        if ($gradeType == 2) {
            $comprehensiveTypes['subject_selection'] = '高中选科建议';
        }

        $result = [];
        foreach ($comprehensiveTypes as $key => $type) {
            $template = $this->getComprehensiveTemplate($type, $intelligenceAnalysis['intelligence_type'], $gradeType);
            if ($template) {
                // 除了"总结与寄语"，其他类型都是JSON格式
                if ($type === '总结与寄语') {
                    $result[$key] = $template->content;
                } else {
                    $result[$key] = is_string($template->content)
                        ? json_decode($template->content, true)
                        : $template->content;
                }
            }
        }
        return $result;
    }

    /**
     * 添加统计数据
     *
     * @param array &$templateData 模板数据（引用传递）
     * @param array $parsedResult 解析后的结果
     */
    private function addStatisticsData(array &$templateData, array $parsedResult): void
    {
        if (isset($parsedResult['each_level_count'])) {
            $templateData['each_level_count'] = $parsedResult['each_level_count'];
        }

        if (isset($parsedResult['number_below_threshold'])) {
            $templateData['number_below_threshold'] = $parsedResult['number_below_threshold'];
        }
    }

    /**
     * 转换旧格式数据为新格式
     *
     * @param mixed $result 旧格式结果
     * @return array 新格式结果
     */
    private function convertLegacyFormat($result): array
    {
        $intelligenceTypes = config('assessment.career.intelligence_types');
        $dimensions = [];

        if (is_array($result) && isset($result[0])) {
            foreach ($intelligenceTypes as $key => $name) {
                if (isset($result[0][$key])) {
                    $dimensions[] = [
                        'code' => (string)($key + 1), // 转换为字符串代码
                        'name' => $name,
                        'score' => $result[0][$key]
                    ];
                }
            }
        }

        $convertedResult = [
            'dimensions' => $dimensions
        ];

        // 添加统计数据
        if (isset($result[1])) {
            $convertedResult['each_level_count'] = [
                'advantages' => $result[1][0] ?? 0,
                'neutral' => $result[1][1] ?? 0,
                'disadvantages' => $result[1][2] ?? 0
            ];
        }

        if (isset($result[2])) {
            $convertedResult['number_below_threshold'] = $result[2];
        }

        return $convertedResult;
    }

    /**
     * 分析智能等级
     *
     * @param array $dimensions 维度数据
     * @return array 分析结果
     */
    private function analyzeIntelligenceLevels(array $dimensions): array
    {
        // 确保所有维度名称都是正确的
        $correctedDimensions = [];
        foreach ($dimensions as $dimension) {
            $correctedDimensions[] = [
                'code' => $dimension['code'] ?? '',
                'name' => $dimension['name'] ?? '',
                'score' => $dimension['score'] ?? 0
            ];
        }

        // 按分数排序
        $sortedDimensions = $correctedDimensions;
        usort($sortedDimensions, function($a, $b) {
            return ($b['score'] ?? 0) <=> ($a['score'] ?? 0);
        });

        // 分为三个等级：优势(前2名)、一般(中间4名)、弱势(后2名)
        $strengths = array_slice($sortedDimensions, 0, 2);
        $intermediate = array_slice($sortedDimensions, 2, 4);
        $needsImprovement = array_slice($sortedDimensions, 6, 2);

        // 提取名称
        $strengthNames = array_column($strengths, 'name');
        $intermediateNames = array_column($intermediate, 'name');
        $needsImprovementNames = array_column($needsImprovement, 'name');

        $intelligenceResult = [
            'strengths' => $strengthNames,
            'intermediate' => $intermediateNames,
            'needs_improvement' => $needsImprovementNames
        ];

        $intelligenceType = json_encode($intelligenceResult, JSON_UNESCAPED_UNICODE);

        return [
            'intelligence_result' => $intelligenceResult,
            'intelligence_type' => $intelligenceType,
            'strengths' => $strengthNames,
            'intermediate' => $intermediateNames,
            'needs_improvement' => $needsImprovementNames
        ];
    }





    /**
     * 获取测评结果概览模板
     *
     * @param array $strengths 优势智能列表
     * @param int|null $gradeType 年级类型
     * @return string|null 模板内容
     */
    private function getResultOverviewTemplate(array $strengths): ?string
    {
        // 将优势智能数组转为JSON字符串用于匹配
        $strengthsJson = json_encode($strengths, JSON_UNESCAPED_UNICODE);

        // 首先尝试查找指定年级的模板
        $template = $this->findOverviewTemplateByGrade($strengthsJson);

        return $template ? $template->content : null;
    }

    /**
     * 根据年级查找测评结果概览模板
     */
    private function findOverviewTemplateByGrade(string $strengthsJson)
    {
        $query = AssessmentCareerMiTemplate::where('intelligence_type', $strengthsJson)
            ->where('type', '测评结果概览');

        $template = $query->first();



        return $template;
    }

    /**
     * 获取综合类型模板
     *
     * @param string $type 模板类型
     * @param string $intelligenceType 智能类型JSON字符串
     * @param int|null $gradeType 年级类型
     * @return AssessmentCareerMiTemplate|null 模板对象
     */
    private function getComprehensiveTemplate(string $type, string $intelligenceType, ?int $gradeType)
    {
        // 解析intelligence_type JSON
        $intelligenceData = json_decode($intelligenceType, true);

        // 对于综合类型模板，只需要比较优势和弱势
        $searchPattern = [
            'strengths' => $intelligenceData['strengths'] ?? [],
            'needs_improvement' => $intelligenceData['needs_improvement'] ?? []
        ];

        // 首先尝试查找指定年级的模板
        $template = $this->findTemplateByGradeAndPattern($type, $searchPattern, $gradeType);

        return $template;
    }

    /**
     * 根据年级和模式查找模板
     */
    private function findTemplateByGradeAndPattern(string $type, array $searchPattern, ?int $gradeType)
    {
        // 首先尝试查找指定年级的模板
        if ($gradeType !== null) {
            $template = $this->searchTemplatesByPattern($type, $searchPattern, $gradeType);
            if ($template) {
                return $template;
            }
        }

        // 如果没有找到指定年级的模板，则查找通用模板（grade_type 为 null）
        return $this->searchTemplatesByPattern($type, $searchPattern, null);
    }

    /**
     * 根据模式搜索模板
     *
     * @param string $type 模板类型
     * @param array $searchPattern 搜索模式
     * @param int|null $gradeType 年级类型（null 表示通用模板）
     * @return AssessmentCareerMiTemplate|null
     */
    private function searchTemplatesByPattern(string $type, array $searchPattern, ?int $gradeType)
    {
        $query = AssessmentCareerMiTemplate::where('type', $type);

        if ($gradeType !== null) {
            $query->where('grade_type', $gradeType);
        } else {
            $query->whereNull('grade_type');
        }

        // 获取所有该类型的模板
        $templates = $query->get();

        // 遍历模板找到匹配的
        foreach ($templates as $template) {
            $templateIntelligenceType = json_decode($template->intelligence_type, true);

            if (is_array($templateIntelligenceType) &&
                isset($templateIntelligenceType['strengths']) &&
                isset($templateIntelligenceType['needs_improvement'])) {

                // 比较优势和弱势是否匹配
                $strengthsMatch = $this->arraysEqual($searchPattern['strengths'], $templateIntelligenceType['strengths']);
                $weaknessesMatch = $this->arraysEqual($searchPattern['needs_improvement'], $templateIntelligenceType['needs_improvement']);

                if ($strengthsMatch && $weaknessesMatch) {
                    return $template;
                }
            }
        }

        return null;
    }

    /**
     * 比较两个数组是否相等（忽略顺序）
     *
     * @param array $array1
     * @param array $array2
     * @return bool
     */
    private function arraysEqual(array $array1, array $array2): bool
    {
        sort($array1);
        sort($array2);
        return $array1 === $array2;
    }

}
