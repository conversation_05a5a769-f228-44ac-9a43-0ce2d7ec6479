<?php

namespace App\Services\School\Assessment\Answer;

use App\Models\School\Assessment\Answer\AssessmentCareerAnswer;

class CareerAnswerService extends BaseAnswerService
{
    /**
     * 获取职业测评的问题ID在答案表中的字段名。
     *
     * @return string 职业测评问题ID的字段名 (e.g., 'assessment_career_question_id')
     */
    protected function getSpecificQuestionIdKey(): string
    {
        return 'assessment_career_question_id';
    }

    /**
     * 获取职业测评的答案模型类名。
     *
     * @return string 职业测评答案模型的完整类名 (e.g., AssessmentCareerAnswer::class)
     */
    protected function getAnswerModelClass(): string
    {
        return AssessmentCareerAnswer::class;
    }

    //Todo 待实现 是否需要验证任务安排 根据具体测评来判断是否需要
    // 此方法可以保留，因为它提供了特定于 CareerAnswerService 的逻辑
    protected function needsScheduleValidation(array $context): bool
    {
        //自主探索
        if(!$context['needs_check']) return false;
        return true;
    }
}