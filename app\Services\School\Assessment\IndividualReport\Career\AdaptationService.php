<?php
namespace App\Services\School\Assessment\IndividualReport\Career;

use App\Services\School\Assessment\IndividualReport\AbstractIndividualReportService;

class AdaptationService extends AbstractIndividualReportService
{
    public function generateReport(array $params): array
    {
        // 获取分发信息
        $assignmentInfo = $this->getAssignmentInfo($params['assessment_task_assignment_id']);

        // 生成报告
        $assessmentInfo = $this->adaptationInfo($assignmentInfo['standard_results']);

        // 合并结果
        return array_merge($assignmentInfo, $assessmentInfo);
    }

    private function adaptationInfo($result): array
    {
        $adaptationDescriptions = config('assessment.career.adaptation_descriptions');

        $survey = [];

        foreach ($result['dimensions'] as $key => $value) {
            $survey[$key] = [
                'name' => $value['name'],
                'score' => $value['score'],
                'content' => $this->getAdaptationContent($key, $value, $adaptationDescriptions[$value['code']]),
            ];
        }

        return [
            'total' => $result['total_score'],
            'content' => $survey,
        ];
    }

    private function getAdaptationContent($key, $score, $descriptions): string
    {
        if ($key < 6) {
            return $score <= 8 ? $descriptions[0] : $descriptions[1];
        } else {
            if ($score > 8) {
                return $descriptions[2];
            } elseif ($score >= 3 && $score <= 8) {
                return $descriptions[1];
            } else {
                return $descriptions[0];
            }
        }
    }
}
