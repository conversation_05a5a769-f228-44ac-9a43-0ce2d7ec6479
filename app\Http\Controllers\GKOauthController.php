<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;


class GKOauthController extends Controller
{

    /**
     * 模拟 GK 平台的 OAuth2.0 接口
     * 请求apiv4接口的时候，需要携带 profiles、ticket、sysCode参数。
     */
    public function index(Request $request)
    {
        // 获取请求参数
        $profiles = 'ysy2024';
        //用户id
        $user = $request->user();
        $sysCode = $user->id;
        // 生成随机的 ticket
        $ticket = bin2hex(random_bytes(16)); // 生成一个随机字符串作为 ticket
        //user对象添加name字段 值为username的值，添加gaokaoyear值为2024
        $user->name = $user->username;
        $user->gaokaoyear = 2024;
        Cache::put($ticket, $user,3600);
        // 请求apiv4接口：http://localhost:8011/yishengyaNew/oauth2?profiles=$profiles&sysCode=$sysCode&ticket=$ticket get请求
//        $response = $this->httpGet("http://localhost:8086/yishengyaNew/oauth2?profiles=$profiles&sysCode=$sysCode&ticket=$ticket");
        // 直接使用Http facade发起GET请求
        $response = Http::get("http://localhost:8086/yishengyaNew/oauth", [
            'profiles' => $profiles,
            'sysCode' => $sysCode,
            'ticket' => $ticket
        ]);
        // 返回结果
        return $this->success(json_decode($response));
    }

    /**
     * 获取 AccessToken 的接口
     */
    public function validateData(Request $request)
    {
        // 获取请求参数
        $appId = $request->input('appId');
        $timeStamp = $request->input('timeStamp');
        $keyInfo = $request->input('keyInfo');
        $sysCode = $request->input('sysCode');

        // 假设 appId 和 appKey 是预定义的（应该存储在配置文件中）
        $validAppId = env('GK_APPID');
        $validAppKey = env('GK_APPKEY');

        // 验证参数完整性
        if (empty($appId) || empty($timeStamp) || empty($sysCode)) {
            return $this->oauthSuccess(['retCode' => '100001', 'message' => 'Invalid parameters'],400);
        }

        // 验证 appId 是否正确
        if ($appId !== $validAppId) {
            return $this->oauthSuccess(['retCode' => '100001', 'message' => 'Invalid appId'],401);
        }

        // 生成 keyInfo
        $paramValues = $appId . $validAppKey . $timeStamp;
        $generatedKeyInfo = strtoupper(hash_hmac('sha1', $paramValues, $validAppKey));

        // 验证 keyInfo 是否正确
        if ($keyInfo !== $generatedKeyInfo) {
            return $this->oauthSuccess(['retCode' => '100001', 'message' => 'Invalid keyInfo'],403);
        }

        // 生成 accessToken
        $accessToken = base64_encode(hash_hmac('sha1', $sysCode . $timeStamp, $validAppKey));

        // 将 accessToken 存储在缓存中（1小时有效）
        Cache::put($accessToken, true, 3600);

        // 返回成功响应
        return $this->oauthSuccess([
            'data' => [
                'accessToken' => $accessToken,
                'expiresIn' => 3600 // token 过期时间
            ],
            'retCode' => '000000',
            'message' => 'Success'
        ]);
    }


    /**
     * 验证 ticket 的接口
     */
    public function validateTicket(Request $request)
    {
        // 获取请求参数
        $ticket = $request->input('ticket');
        $accessToken = $request->input('accessToken');

        // 验证参数是否完整
        if (empty($ticket) || empty($accessToken)) {
            return $this->oauthSuccess(['retCode' => '100001', 'message' => 'Missing parameters'],400);
        }

        // 验证 accessToken 是否有效
        if (!Cache::has($accessToken)) {
            return $this->oauthSuccess(['retCode' => '100002', 'message' => 'Invalid accessToken'],401);
        }

        // 假设我们有一个 ticket 验证逻辑
        // 这里简单模拟 ticket 验证成功的情况
        if (Cache::has($ticket)) {
            // 从缓存中查找 ticket
            $userData = Cache::get($ticket);
            // 返回成功验证结果
            return $this->oauthSuccess([
                'retCode' => '000000',
                'message' => 'Ticket validated successfully',
                'data' => $userData,
//                'user' => $userData
            ]);
        } else {
            // 返回 ticket 验证失败的结果
            return $this->oauthSuccess(['retCode' => '100003', 'message' => 'Invalid ticket'],403);
        }
    }


}
