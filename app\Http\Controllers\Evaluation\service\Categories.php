<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 16:01
 */
namespace app\evaluation\service;
use think\Loader;
class Categories{
    protected $CategoriesLogic;
    public function __construct()
    {
          $this->CategoriesLogic = new \app\evaluation\logic\Categories();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://查询

                return $this->CategoriesLogic->get_list();
                break;
            case 'post'://增加

                return $this->CategoriesLogic->add();
                break;
            case 'put'://修改

                return $this->CategoriesLogic->edit();
                break;
            case 'delete'://删除
                return $this->CategoriesLogic->del();
                break;
            default:
                return false;
        }
    }

}