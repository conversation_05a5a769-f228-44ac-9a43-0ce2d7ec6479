<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CollegeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $ranks = collect($this->ranks);

        $groupedMajors = [];
        foreach ($this->majorAssess as $assessment) {
            $level = $assessment->Rank;
            $major = $assessment->Major;

            if (!isset($groupedMajors[$level])) {
                $groupedMajors[$level] = [];
            }

            $groupedMajors[$level][] = $major;
        }

        $majorAssessResult = [];
        foreach ($groupedMajors as $level => $majors) {
            $majorAssessResult[] = [
                'majors' => $majors,
                'level' => $level,
            ];
        }

        return [
            'id' => $this->ID,
            'college_name' => $this->CollegeName,
            'province_id' => $this->ProvinceID,
            'province_name' => $this->province ? $this->province->ProvinceName : null,
            'city_id' => $this->CityID,
            'city_name' => $this->city ? $this->city->CityName : null,
            'yxls' => $this->Yxls,
            'levels' => $this->Levels,
            'bxtype' => $this->Bxtype,
            'yxtype' => $this->Yxtype,
            'yxjbz' => $this->Yxjbz,
            'big_logo' => $this->BigLogo,
            'small_logo' => $this->SmallLogo,
            'address' => $this->Address,
            // 'introduction' => $this->Introduction,
            'introduction_ai' => $this->IntroductionAi,
            'tags' => $this->tags->pluck('Type')->toArray(),
            'detail' => $this->detail ? [
                'student_total' => $this->detail->StudentTotal,
                'yuanshi_total' => $this->detail->YuanshiTotal,
                'boshi_total' => $this->detail->BoshiTotal,
                'shuoshi_total' => $this->detail->ShuoshiTotal,
                'official_website' => $this->detail->OfficialWebsite,
                'admissions_website' => $this->detail->AdmissionsWebsite,
                'create_college_time' => $this->detail->CreateCollegeTime,
            ] : null,
            'teacher' => $this->teacher ? [
                // 'yuanxi' => $this->teacher->Yuanxi,
                // 'major' => $this->teacher->Major,
                // 'teacher' => $this->teacher->Teacher,
                'yuanxi_ai' => $this->teacher->YuanxiAi ? array_map('trim', explode(',', $this->teacher->YuanxiAi)) : [],
                'major_ai' =>  $this->teacher->MajorAi ?? [],
            ] : null,
            'major_assess' => $majorAssessResult,
            'ranks' => $ranks->map(function ($rank) {
                if (is_array($rank)) {
                    return [
                        'type' => $rank['Type'] ?? null,
                        'year' => $rank['Year'] ?? null,
                        'rank' => $rank['Rank'] ?? null,
                    ];
                } elseif (is_object($rank)) {
                    return [
                        'type' => $rank->Type ?? null,
                        'year' => $rank->Year ?? null,
                        'rank' => $rank->Rank ?? null,
                    ];
                }
                return null;
            })->filter(),
        ];
    }
}
