<?php

namespace App\Http\Controllers\Partner;

use App\Http\Controllers\Controller;
use App\Models\Admin\Organization;
use App\Models\Partner\PartnerSchool;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;

class PartnerSchoolController extends Controller
{
    use CrudOperations;

    protected string $model = PartnerSchool::class;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // 获取当前登录用户所属机构id
        $partner_id = $request->user()->organization->model_id;

        $school_list = $this->model::where('partner_id', $partner_id)->with(['school:name,id,address', 'schoolCampus'])->get();
        return $this->success($school_list);
    }

    public function setHasSchools(Request $request)
    {
        // 获取当前登录用户所属机构id
        $partner_id = $request->user()->organization->model_id;

        // TODO 待完善
        return $this->success("");
    }

}
