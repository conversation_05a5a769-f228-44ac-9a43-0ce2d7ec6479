<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建admin账户
        $data = [
            'username' => 'admin',
            'real_name' => '系统管理员',
            'phone' => '13800000000',
            'gender' => 1,
            'password' => Hash::make(123456),
            'role_id' => 1,
            'created_at' => date('Y-m-d H:i:s',time()),
            'updated_at' => date('Y-m-d H:i:s',time()),
        ];
        //DB::table("users")->insert($data);
    }
}
