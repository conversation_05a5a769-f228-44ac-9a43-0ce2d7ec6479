<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * 试卷模型
 */
class Papers extends Model
{
    protected $table = 'evaluation_papers';

    protected $fillable = [
        'paper_name',
        'course_id',
        'scenario_id',
        'grade_id',
        'question_ids',
        'total_score',
        'question_count',
        'has_subjective',
        'description',
        'status',
        'creator_id',
        'school_id',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'course_id' => 'integer',
        'scenario_id' => 'integer',
        'grade_id' => 'integer',
        'total_score' => 'integer',
        'question_count' => 'integer',
        'has_subjective' => 'integer',
        'status' => 'integer',
        'creator_id' => 'integer',
        'school_id' => 'integer',
    ];

    /**
     * 试卷题目关联（多对多）
     */
    public function questions(): BelongsToMany
    {
        return $this->belongsToMany(Question::class, 'evaluation_paper_questions', 'paper_id', 'question_id')
            ->withPivot(['sort', 'score'])
            ->withTimestamps()
            ->orderBy('pivot_sort');
    }

    /**
     * 试卷题目关联表
     */
    public function paperQuestions(): HasMany
    {
        return $this->hasMany(PaperQuestion::class, 'paper_id');
    }

    /**
     * 答题记录关联
     */
    public function answers(): HasMany
    {
        return $this->hasMany(EvaluationAnswer::class, 'paper_id');
    }

    /**
     * 答题日志关联
     */
    public function logs(): HasMany
    {
        return $this->hasMany(EvaluationLog::class, 'paper_id');
    }

    /**
     * 分发记录关联
     */
    public function distributions(): HasMany
    {
        return $this->hasMany(Distribution::class, 'paper_id');
    }

    /**
     * 创建者关联
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'creator_id');
    }

    /**
     * 学科关联
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School\System\Course::class, 'course_id');
    }

    /**
     * 年级关联
     */
    public function grade(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School\System\Grade::class, 'grade_id');
    }

    /**
     * 作用域：按学科筛选
     */
    public function scopeByCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    /**
     * 作用域：按年级筛选
     */
    public function scopeByGrade($query, $gradeId)
    {
        return $query->where('grade_id', $gradeId);
    }

    /**
     * 作用域：按应用场景筛选
     */
    public function scopeByScenario($query, $scenarioId)
    {
        return $query->where('scenario_id', $scenarioId);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 作用域：按学校筛选
     */
    public function scopeBySchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * 获取题目ID数组
     */
    public function getQuestionIdsArray()
    {
        if (empty($this->question_ids)) {
            return [];
        }
        return explode(',', $this->question_ids);
    }

    /**
     * 设置题目ID数组
     */
    public function setQuestionIdsArray(array $questionIds)
    {
        $this->question_ids = implode(',', $questionIds);
    }

    /**
     * 获取应用场景文本
     */
    public function getScenarioTextAttribute()
    {
        $scenarioMap = [
            1 => '素养测评',
            2 => '阶段测试',
            3 => '课时练习'
        ];

        return $scenarioMap[$this->scenario_id] ?? '未知';
    }

    /**
     * 检查是否有主观题
     */
    public function hasSubjectiveQuestions(): bool
    {
        return $this->has_subjective == 1;
    }

    /**
     * 检查是否可以删除
     */
    public function canDelete(): bool
    {
        // 检查是否有答题记录
        if ($this->answers()->exists()) {
            return false;
        }

        // 检查是否有答题日志
        if ($this->logs()->exists()) {
            return false;
        }

        // 检查是否有分发记录
        if ($this->distributions()->exists()) {
            return false;
        }

        return true;
    }

    /**
     * 获取试卷统计信息
     */
    public function getStatistics(): array
    {
        $totalAnswers = $this->answers()->count();
        $totalLogs = $this->logs()->count();
        $checkedLogs = $this->logs()->where('check_status', 1)->count();
        $pendingLogs = $this->logs()->where('check_status', 0)->count();

        $avgScore = $this->logs()->where('check_status', 1)->avg('score');

        return [
            'total_answers' => $totalAnswers,
            'total_logs' => $totalLogs,
            'checked_logs' => $checkedLogs,
            'pending_logs' => $pendingLogs,
            'avg_score' => $avgScore ? round($avgScore, 2) : 0,
            'completion_rate' => $totalLogs > 0 ? round(($checkedLogs / $totalLogs) * 100, 2) : 0
        ];
    }

    /**
     * 复制试卷
     */
    public function duplicate(array $overrides = []): Papers
    {
        $attributes = $this->getAttributes();
        unset($attributes['id'], $attributes['created_at'], $attributes['updated_at']);
        
        $newPaper = static::create(array_merge($attributes, $overrides));
        
        // 复制试卷题目关联
        foreach ($this->paperQuestions as $paperQuestion) {
            $questionData = $paperQuestion->getAttributes();
            unset($questionData['id'], $questionData['created_at'], $questionData['updated_at']);
            $questionData['paper_id'] = $newPaper->id;
            PaperQuestion::create($questionData);
        }
        
        return $newPaper;
    }
}
