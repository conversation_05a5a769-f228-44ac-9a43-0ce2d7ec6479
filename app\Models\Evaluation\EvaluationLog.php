<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 答题日志模型
 */
class EvaluationLog extends Model
{
    protected $table = 'evaluation_logs';

    // 如果数据库使用 create_at 和 update_at，取消下面两行的注释
    // const CREATED_AT = 'create_at';
    // const UPDATED_AT = 'update_at';

    protected $fillable = [
        'paper_id',
        'grade_id',
        'class_id',
        'student_id',
        'member_id',
        'distribution_id',
        'used_time',
        'check_member_id',
        'score',
        'check_status',
        'literacy_level',
        'school_id',
        'school_district_id',
        'status',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'paper_id' => 'integer',
        'grade_id' => 'integer',
        'class_id' => 'integer',
        'student_id' => 'integer',
        'member_id' => 'integer',
        'distribution_id' => 'integer',
        'used_time' => 'integer',
        'check_member_id' => 'integer',
        'score' => 'decimal:2',
        'check_status' => 'integer',
        'school_id' => 'integer',
        'school_district_id' => 'integer',
        'status' => 'integer',
    ];

    /**
     * 所属试卷关联
     */
    public function paper(): BelongsTo
    {
        return $this->belongsTo(Papers::class, 'paper_id');
    }

    /**
     * 所属分发关联
     */
    public function distribution(): BelongsTo
    {
        return $this->belongsTo(Distribution::class, 'distribution_id');
    }

    /**
     * 所属学生关联
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School\System\Student::class, 'student_id');
    }

    /**
     * 所属年级关联
     */
    public function grade(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School\System\Grade::class, 'grade_id');
    }

    /**
     * 所属班级关联
     */
    public function class(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School\System\Classes::class, 'class_id');
    }

    /**
     * 批阅老师关联
     */
    public function checkTeacher(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'check_member_id');
    }

    /**
     * 作用域：按试卷筛选
     */
    public function scopeByPaper($query, $paperId)
    {
        return $query->where('paper_id', $paperId);
    }

    /**
     * 作用域：按学生筛选
     */
    public function scopeByStudent($query, $studentId)
    {
        return $query->where('student_id', $studentId);
    }

    /**
     * 作用域：按分发筛选
     */
    public function scopeByDistribution($query, $distributionId)
    {
        return $query->where('distribution_id', $distributionId);
    }

    /**
     * 作用域：按批阅状态筛选
     */
    public function scopeByCheckStatus($query, $status)
    {
        return $query->where('check_status', $status);
    }

    /**
     * 作用域：按素养水平筛选
     */
    public function scopeByLiteracyLevel($query, $level)
    {
        return $query->where('literacy_level', $level);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 获取批阅状态文本
     */
    public function getCheckStatusTextAttribute()
    {
        $statusMap = [
            0 => '待批阅',
            1 => '已批阅'
        ];

        return $statusMap[$this->check_status] ?? '未知';
    }

    /**
     * 获取素养水平颜色
     */
    public function getLiteracyLevelColorAttribute()
    {
        $colorMap = [
            '优' => '#52c41a',
            '良' => '#1890ff',
            '有待提高' => '#faad14'
        ];

        return $colorMap[$this->literacy_level] ?? '#d9d9d9';
    }

    /**
     * 检查是否已批阅
     */
    public function isChecked(): bool
    {
        return $this->check_status == 1;
    }

    /**
     * 检查是否待批阅
     */
    public function isPending(): bool
    {
        return $this->check_status == 0;
    }
}
