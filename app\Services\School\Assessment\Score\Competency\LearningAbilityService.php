<?php

namespace App\Services\School\Assessment\Score\Competency;

use App\Services\School\Assessment\Score\Competency\AbstractScoreService;

/**
 * 学习力评估服务类
 * 
 * 该类用于计算学生的学习力评估结果，包括学习特质、学习基底、学习倾向和学习坚毅力等维度
 */
class LearningAbilityService extends AbstractScoreService
{
    /**
     * 维度代码常量
     */
    private const SUBJECTIVE_COGNITION = '1'; // 主观认知
    private const OBJECTIVE_PERFORMANCE = '2'; // 客观表现
    private const LEARNING_FOUNDATION = '3'; // 学习基底
    private const MATH_TENDENCY = '4'; // 数学倾向
    private const SCIENCE_TENDENCY = '5'; // 科学倾向
    private const LEARNING_ENTHUSIASM = '6'; // 学习坚毅力（热情）
    private const LEARNING_PERSEVERANCE = '7'; // 学习坚毅力（毅力）

    /**
     * 学习特质权重常量
     */
    private const SUBJECTIVE_COGNITION_WEIGHT = 0.2; // 主观认知权重
    private const OBJECTIVE_PERFORMANCE_WEIGHT = 0.8; // 客观表现权重

    /**
     * 学习力综合得分权重常量
     */
    private const LEARNING_TRAIT_WEIGHT = 0.5; // 学习特质权重
    private const LEARNING_FOUNDATION_WEIGHT = 0.3; // 学习基底权重
    private const LEARNING_TENDENCY_WEIGHT = 0.1; // 学习倾向权重
    private const LEARNING_PERSEVERANCE_WEIGHT = 0.1; // 学习坚毅力权重

    /**
     * 不同评估ID对应的客观表现题数
     * 
     * @var array
     */
    private const QUESTION_COUNT = [
        18 => 6,  // 高一升高二的客观表现的题数
        20 => 12, // 初三升高一的客观表现的题数
    ];

    /**
     * 分数计算倍数
     * 
     * @var int
     */
    private const MULTIPLIER = 10;

    /**
     * 计算学习力评估结果
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 评估结果数组，包含维度分数和总分
     */
    public function calculate(array $params): array
    {
        // 计算维度分数
        $dimension_scores = $this->calculateDimensionScores($params);
        return $this->formatLearningAbilityResults($dimension_scores, $params['assessment_id']);
    }

    /**
     * 格式化学习力评估结果
     * 
     * @param array $dimension_scores 维度分数数组
     * @param int $assessment_id 评估ID
     * @return array 格式化后的评估结果
     */
    private function formatLearningAbilityResults(array $dimension_scores, int $assessment_id): array
    {
        // 将数组转换为以维度代码为键的关联数组，方便后续处理
        $dimension_map = [];
        foreach ($dimension_scores as $dimension) {
            $dimension_map[$dimension['code']] = $dimension;
        }
        
        // 学习倾向（数学和科学）得分
        $total_learning_tendency_score = $this->calculateScore(
            $dimension_map[self::MATH_TENDENCY]['score'] + $dimension_map[self::SCIENCE_TENDENCY]['score'], 
            $dimension_map[self::MATH_TENDENCY]['question_count'] + $dimension_map[self::SCIENCE_TENDENCY]['question_count'], 
            2
        );
        
        // 学习坚毅力（热情和毅力）得分
        $total_learning_perseverance_score = $this->calculateScore(
            $dimension_map[self::LEARNING_ENTHUSIASM]['score'] + $dimension_map[self::LEARNING_PERSEVERANCE]['score'], 
            $dimension_map[self::LEARNING_ENTHUSIASM]['question_count'] + $dimension_map[self::LEARNING_PERSEVERANCE]['question_count'], 
            2
        );

        // 下层维度得分转换，得分*倍数/题数，十分制，题分值1分就*10，分值5分就*2
        foreach ($dimension_map as $code => &$dimension) {
            // 客观表现题数处理
            $question_count = $code == self::OBJECTIVE_PERFORMANCE ? 
                self::QUESTION_COUNT[$assessment_id] : 
                $dimension['question_count'];
                
            $multiplier = in_array($code, [self::OBJECTIVE_PERFORMANCE, self::LEARNING_FOUNDATION]) ? 
                self::MULTIPLIER : 
                2;
                
            $dimension['score'] = $this->calculateScore($dimension['score'], $question_count, $multiplier);
            unset($dimension['question_count']); // 移除 question_count
        }

        // 学习特质得分特殊规则 = 主观认知 * 0.2 + 客观表现 * 0.8
        $learning_trait_score = round(
            $dimension_map[self::SUBJECTIVE_COGNITION]['score'] * self::SUBJECTIVE_COGNITION_WEIGHT + 
            $dimension_map[self::OBJECTIVE_PERFORMANCE]['score'] * self::OBJECTIVE_PERFORMANCE_WEIGHT, 
            1
        );

        // 学习力综合得分 = 学习特质 * 50% + 学习基底 * 30% + 学习倾向 * 10% + 学习坚毅力 * 10%
        $comprehensive_learning_ability_score = round(
            $learning_trait_score * self::LEARNING_TRAIT_WEIGHT + 
            $dimension_map[self::LEARNING_FOUNDATION]['score'] * self::LEARNING_FOUNDATION_WEIGHT + 
            $total_learning_tendency_score * self::LEARNING_TENDENCY_WEIGHT + 
            $total_learning_perseverance_score * self::LEARNING_PERSEVERANCE_WEIGHT, 
            1
        );

        // 客观表现名称添加年级信息
        $dimension_map[self::OBJECTIVE_PERFORMANCE]['name'] = $assessment_id == 18 ? 
            '客观表现（高一升高二）' : 
            '客观表现（初三升高一）';

        // 构建新的返回结构
        return [
            'dimensions' => [
                [
                    'name' => '学习特质',
                    'score' => $learning_trait_score,
                    'children' => [
                        $dimension_map[self::SUBJECTIVE_COGNITION],
                        $dimension_map[self::OBJECTIVE_PERFORMANCE]
                    ]
                ],
                [
                    'name' => '学习基底',
                    'score' => $dimension_map[self::LEARNING_FOUNDATION]['score'],
                    'children' => []
                ],
                [
                    'name' => '学习倾向',
                    'score' => $total_learning_tendency_score,
                    'children' => [
                        $dimension_map[self::MATH_TENDENCY],
                        $dimension_map[self::SCIENCE_TENDENCY]
                    ]
                ],
                [
                    'name' => '学习坚毅力',
                    'score' => $total_learning_perseverance_score,
                    'children' => [
                        [
                            'code' => $dimension_map[self::LEARNING_ENTHUSIASM]['code'],
                            'name' => '学习坚毅力（热情）',
                            'score' => $dimension_map[self::LEARNING_ENTHUSIASM]['score']
                        ],
                        [
                            'code' => $dimension_map[self::LEARNING_PERSEVERANCE]['code'],
                            'name' => '学习坚毅力（毅力）',
                            'score' => $dimension_map[self::LEARNING_PERSEVERANCE]['score']
                        ]
                    ]
                ]
            ],
            'total_score' => $comprehensive_learning_ability_score
        ];
    }

    /**
     * 得分计算逻辑
     * 
     * @param float $score 原始分数
     * @param int $total_items 题目总数
     * @param int $multiplier 倍数
     * @return float 计算后的分数
     */
    private function calculateScore($score, $total_items, $multiplier = 1) {
        return round($score * $multiplier / $total_items, 1);
    }
}