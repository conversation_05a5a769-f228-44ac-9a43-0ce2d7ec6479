<?php

namespace App\Models\School\Assessment;

use App\Models\School\System\Student;
use App\Models\School\System\StudentClass;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 测评任务分配模型
 * 
 * 用于管理测评任务分配给学生的记录
 */
class AssessmentTaskAssignment extends Model
{
    use HasFactory;
    use SoftDeletes;
    
    protected $fillable = [
        'assessment_task_id',
        'assessment_id',
        'school_id',
        'student_class_id',
        'student_id',
        'duration',
        'user_id',
    ];

    /**
     * 字段类型转换
     */
    protected $casts = [
        'assessment_task_id' => 'integer',
        'assessment_id' => 'integer',
        'school_id' => 'integer',
        'student_class_id' => 'integer',
        'student_id' => 'integer',
        'duration' => 'integer',
        'standard_results' => 'json',
    ];

    /**
     * 获取关联的学生
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    /**
     * 获取关联的测评
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function assessment()
    {
        return $this->belongsTo(Assessment::class, 'assessment_id');
    }

    /**
     * 获取关联的学生班级
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function studentClass()
    {
        return $this->belongsTo(StudentClass::class, 'student_class_id');
    }

    /**
     * 获取关联的测评任务
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function assessmentTask()
    {
        return $this->belongsTo(AssessmentTask::class, 'assessment_task_id');
    }
}
