<?php

namespace App\Exceptions;

use App\Traits\ApiResponse;
use Exception;
use Illuminate\Support\Facades\Log;

class BusinessException extends Exception
{
    use ApiResponse;

    protected $code;
    protected $message;
    protected $log_data;
    protected $log_type;

    public function __construct($message = '业务异常', $code = 500, $log_data = null, $log_type = 'error')
    {
        $this->message = $message;
        $this->code = $code;
        $this->log_data = $log_data;
        $this->log_type = $log_type;
        
        parent::__construct($this->message, $this->code);
    }

    public function render($request)
    {
        // 记录日志
        $log_data = $this->log_data;
        $log_type = $this->log_type;
        // 如果传入了log_data，则记录日志
        if (!is_null($log_data)) {
            switch ($log_type) {
                case 'info':
                    Log::info($this->message, ['data' => $log_data]);
                    break;
                case 'warning':
                    Log::warning($this->message, ['data' => $log_data]);
                    break;
                case 'debug':
                    Log::debug($this->message, ['data' => $log_data]);
                    break;
                default:
                    Log::error($this->message, ['data' => $log_data]);
                    break;
            }
        }

        return $this->error($this->message, $this->code ?? 500);
    }
}