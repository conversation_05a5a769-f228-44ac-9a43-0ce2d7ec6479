<?php

namespace App\Models\Admin;

use App\Models\BaseModel;
use App\Traits\PaginationTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property string $method 方法:get,post...
 * @property string $url 接口url
 * @property int|null $permission_id 权限编号
 * @property int|null $permission_name 权限名称
 * @property array $parameters 参数
 * @property string $creator 添加人
 * @property string|null $api_token token
 * @property string|null $ip ip
 * @property \Illuminate\Support\Carbon $created_at
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog whereApiToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog whereIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog whereMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog whereParameters($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog wherePermissionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog wherePermissionName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserAccessLog whereUrl($value)
 * @mixin \Eloquent
 */
class UserAccessLog extends BaseModel
{

    public $timestamps = false;

    protected $casts = [
        'parameters' => 'array'
    ];
}
