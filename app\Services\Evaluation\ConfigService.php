<?php

namespace App\Services\Evaluation;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;

/**
 * 配置管理服务类 - 读取配置文件而非数据库
 * 与原 ThinkPHP 版本的 common.php 保持一致
 */
class ConfigService
{
    protected $user;

    public function __construct()
    {
        $this->user = Auth::user();
    }

    /**
     * 获取配置信息 - 与原 ThinkPHP 版本保持一致
     * 对应原 Config.php 中的 config() 方法
     * 
     * @return array
     */
    public function getConfig(): array
    {
        return [
            'is_high' => $this->isHigh(),
            'course' => $this->course(),
            'grade' => $this->grade(),
            'scenario' => $this->scenario(),
            'cenario_Type' => $this->cenarioType(), // 保持原来的拼写
        ];
    }

    /**
     * 学段配置 - 对应原 common.php 中的 is_high() 函数
     * 
     * @return array
     */
    public function isHigh(): array
    {
        return [
            '初中' => '0',
            '高中' => '1',
        ];
    }

    /**
     * 学科配置 - 对应原 common.php 中的 course() 函数
     * 
     * @return array
     */
    public function course(): array
    {
        return [
            '阅读' => '1',
            '数学' => '2',
            '科学' => '3',
        ];
    }

    /**
     * 年级配置 - 对应原 common.php 中的 grade() 函数
     * 
     * @return array
     */
    public function grade(): array
    {
        return [
            '高三' => '12',
            '高二' => '11',
            '十年级' => '10',
            '九年级' => '9',
            '八年级' => '8',
            '七年级' => '7',
            '六年级' => '6',
            '五年级' => '5',
            '四年级' => '4',
            '三年级' => '3',
            '二年级' => '2',
            '一年级' => '1',
            '通用' => '0',
        ];
    }

    /**
     * 场景配置 - 对应原 common.php 中的 scenario() 函数
     * 
     * @return array
     */
    public function scenario(): array
    {
        return [
            '素养测评' => '1',
            '阶段测试' => '2',
            '课时练习' => '3',
        ];
    }

    /**
     * 场景类型配置 - 对应原 common.php 中的 cenario_Type() 函数
     * 注意：保持原来的拼写 cenario_Type（不是 scenario_Type）
     * 
     * @return array
     */
    public function cenarioType(): array
    {
        return [
            '生活' => '1',
            '科学' => '2',
            '社会' => '3',
            '职业' => '4',
            '无' => '0',
        ];
    }

    /**
     * 获取素养能力配置 - 对应原 common.php 中的 competence_shine() 函数
     * 
     * @return array
     */
    public function competenceShine(): array
    {
        return [
            '语言建构与运用' => 'language',
            '思维发展与提升' => 'thinking',
            '审美鉴赏与创造' => 'appreciation',
            '文化传承与理解' => 'culture',
            '数学推理' => 'reasoning',
            '数学建模' => 'modeling',
            '数学运算' => 'operation',
            '直观想象' => 'imagine',
            '数据分析' => 'analysis',
            '数学抽象' => 'abstract',
            '科学观念' => 'concept',
            '科学思维' => 'thought',
            '探究实践' => 'explore',
            '态度责任' => 'attitude',
        ];
    }

    /**
     * 获取性格代码配置 - 对应原 common.php 中的 character_code() 函数
     * 
     * @return array
     */
    public function characterCode(): array
    {
        return [
            'E' => '外倾',
            'I' => '内倾',
            'S' => '感觉',
            'N' => '直觉',
            'T' => '思维',
            'F' => '情感',
            'J' => '判断',
            'P' => '知觉'
        ];
    }

    /**
     * 获取Redis键前缀 - 对应原 common.php 中的 get_redis_key() 函数
     * 
     * @return string
     */
    public function getRedisKey(): string
    {
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $configRedisKey = [
            'www.yishengya.cn' => 'www',
            'demo.yishengya.cn' => 'demo',
            'test.yishengya.cn' => 'test'
        ];
        
        if (in_array($host, array_keys($configRedisKey))) {
            $prefix = $configRedisKey[$host];
        } else {
            // 本地测试域名
            $prefix = 'localtp';
        }
        
        return $prefix;
    }

    /**
     * 获取完整配置信息（包含扩展配置）
     * 
     * @return array
     */
    public function getFullConfig(): array
    {
        return [
            'is_high' => $this->isHigh(),
            'course' => $this->course(),
            'grade' => $this->grade(),
            'scenario' => $this->scenario(),
            'cenario_Type' => $this->cenarioType(),
            'competence_shine' => $this->competenceShine(),
            'character_code' => $this->characterCode(),
            'redis_key' => $this->getRedisKey(),
        ];
    }

    /**
     * 重置配置缓存
     * 
     * @return bool
     */
    public function resetConfigCache(): bool
    {
        Cache::forget('evaluation_config');
        return true;
    }

    /**
     * 获取系统设置配置
     * 
     * @return array
     */
    public function getSystemSettings(): array
    {
        return [
            'max_upload_size' => '10MB',
            'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
            'default_exam_duration' => 120, // 分钟
            'auto_save_interval' => 30, // 秒
            'max_question_count' => 100,
            'enable_auto_grading' => true,
            'enable_manual_grading' => true,
            'enable_report_export' => true,
        ];
    }

    /**
     * 获取MI智能类型配置
     * 
     * @return array
     */
    public function getMiIntelligenceTypes(): array
    {
        return [
            '语言智能' => 'linguistic',
            '逻辑数学智能' => 'logical_mathematical',
            '空间智能' => 'spatial',
            '身体运动智能' => 'bodily_kinesthetic',
            '音乐智能' => 'musical',
            '人际智能' => 'interpersonal',
            '内省智能' => 'intrapersonal',
            '自然观察智能' => 'naturalistic',
        ];
    }

    /**
     * 获取Holland职业兴趣类型配置
     * 
     * @return array
     */
    public function getHollandTypes(): array
    {
        return [
            'R' => '实用型',
            'I' => '研究型',
            'A' => '艺术型',
            'S' => '社会型',
            'E' => '企业型',
            'C' => '常规型',
        ];
    }
}
