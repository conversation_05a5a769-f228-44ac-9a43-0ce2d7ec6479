<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */
namespace app\evaluation\logic;
use app\evaluation\model\Papers as PapersModel;
use app\evaluation\model\Question as QuestionModel;
use app\evaluation\model\QuestionType as QuestionTypesModel;
use app\evaluation\model\Distribution as DistributionModel;
use think\Db;

class Papers{
    protected $user;
    protected $paperModel;
    public function __construct()
    {
        $this->paperModel = new PapersModel();
        $this->questionsModel = new QuestionModel();
        $this->questionsTypesModel = new QuestionTypesModel();
        $this->distributionModel = new DistributionModel();
        $this->user = get_user();
    }

    public function add()
    {
        //录入试卷表
        $data = input('post.');
        $data['examiner_id'] = $this->user['id'];
        $data['school_id'] = input('school_id',$this->user['school_id']);
        //计算试卷总分
        $question_arr = $this->questionsModel->where(['id'=>['in',$data['question_ids']],'status'=>0])->field('id,is_common,score')->select();
        $question_arr = to_arr($question_arr);
        $score1 = 0;//无题冒
        foreach ($question_arr as $v){
            if($v['is_common'] == 1){
                $score1 += $v['score'];
            }elseif($v['is_common'] == 2){
                $common_arr[] = $v['id'];
            }
        }
        $score2 = 0;
        if(isset($common_arr) && !empty($common_arr)){
            $score2 = $this->questionsModel->where(['parent_id'=>['in',$common_arr],'status'=>0])->value('sum(score)');//有题冒
        }
        $data['total_score'] = $score1 + $score2;
        //将data录入paper表
        $this->paperModel->allowField(true)->data($data)->save();
        $id=$this->paperModel->id;
        //判断是否有主观题,0是主观题，有0则包含主观题
        $question_id_arr = explode(',',$data['question_ids']);
        $type_id_arr = $this->questionsModel->where(['id'=>['in',$question_id_arr]])->whereOr(['parent_id'=>['in',$question_id_arr]])->group('type_id')->column('type_id');
        $has_subjective = $this->questionsTypesModel->where(['id'=>['in',$type_id_arr],'is_subjective'=>1])->find();
        if($has_subjective){
            $this->paperModel->where(['id'=>$id])->update(['has_subjective'=>1]);
        }
        apiReturn($id);
    }

    public function edit()
    {
        $id = input('id');
        $data = input('put.');
        //计算试卷总分
        $question_arr = $this->questionsModel->where(['id'=>['in',$data['question_ids']],'status'=>0])->field('id,is_common,score')->select();
        $question_arr = to_arr($question_arr);
        $score1 = 0;//无题冒
        foreach ($question_arr as $v){
            if($v['is_common'] == 1){
                $score1 += $v['score'];
            }elseif($v['is_common'] == 2){
                $common_arr[] = $v['id'];
            }
        }
        $score2 = $this->questionsModel->where(['parent_id'=>['in',$common_arr],'status'=>0])->value('sum(score)');//有题冒
        $data['total_score'] = $score1 + $score2;
        $this->paperModel->allowField(true)->save($data,['id' => $id]);
        //判断是否有主观题,0是主观题，有0则包含主观题
        $question_id_arr = explode(',',$data['question_ids']);
        $type_id_arr = $this->questionsModel->where(['id'=>['in',$question_id_arr]])->whereOr(['parent_id'=>['in',$question_id_arr]])->group('type_id')->column('type_id');
        $has_subjective = $this->questionsTypesModel->where(['id'=>['in',$type_id_arr],'is_subjective'=>0])->find();
        if($has_subjective){
            $this->paperModel->where(['id'=>$id])->update(['has_subjective'=>1]);
        }
        apiReturn($id);
    }

    public function del()
    {
        $this->paperModel->save([
            'status'  => '-1',
        ],['id' => input('id')]);
        //软删除
        apiReturn(input('id'));
    }

    public function get_list()
    {
        $id = input('id');
        if (!empty($id)) {
            $data = $this->paperModel->where('id', $id)->find();
            //注意，修改题目分数后，paper表中的分数并不会随之改变，所以不用了，要再算一遍
            $question_arr = $this->questionsModel->where(['id'=>['in',$data['question_ids']],'status'=>0])->field('id,is_common,score')->select();
            $question_arr = to_arr($question_arr);
            $score1 = 0;//无题冒
            foreach ($question_arr as $v){
                if($v['is_common'] == 1){
                    $score1 += $v['score'];
                }elseif($v['is_common'] == 2){
                    $common_arr[] = $v['id'];
                }
            }
            $score2 = 0;
            if(isset($common_arr) && !empty($common_arr)){
                $score2 = $this->questionsModel->where(['parent_id'=>['in',$common_arr],'status'=>0])->value('sum(score)');//有题冒
            }
            $data['total_score'] = $score1 + $score2;
            apiReturn($data);
        } else {
            $pageNumber = input('page', 1); // 获取页码，默认为1
            $pageSize = input('pagesize', 10); // 获取每页显示的记录数，默认为10
            $course_id  = input('course_id');
            $paper_name = input('paper_name');
            $grade_id = input('grade_id');
            $start_time = input('start_time');
            $end_time = input('end_time');

            $paper_where = 'papers.status = 0 ';
            if (!empty($course_id)) $paper_where .= ' and papers.course_id = '.$course_id;
            if (!empty($paper_name)) $paper_where .= ' and papers.paper_name like "%' . $paper_name . '%"';
            if (!empty($grade_id)) $paper_where .= ' and papers.grade_id = '.$grade_id;
            if (!empty($start_time) && !empty($end_time)) $paper_where .= ' and papers.create_at between "'.$start_time.'" and "'.$end_time.' 23:59:59"';

            $where = '';
            //非admin加过滤条件
            if ($this->user['role_id'] != '0,999,0'){
                $where .= ' and papers.school_id = '.$this->user['school_id'];
                //教务自己组卷或者admin组卷分发给教务的都展示
                if($this->user['role_source_id'] == 2) {
                    //字段没啥实际意义，只是union字段需要一致
                    $where .= " UNION
                            SELECT null as paper_id,dis.id as distribution_id,dis.title as distribution_name,null as paper_name,null as question_ids,null as scenario_id,null as grade_id,null as course_id,create_at,null as has_subjective,ysy_member.name as teacher_name 
                            FROM ysy_evaluation_distribution dis
                            JOIN ysy_member ON dis.distribution_by = ysy_member.id
                            WHERE dis.role_id = '0,999,0' AND FIND_IN_SET( " . $this->user['school_id'] . ", school_ids ) 
                            GROUP BY dis.id";
                }
            }
            $sql = "SELECT papers.id as paper_id,null as distribution_id,null as distribution_name,papers.paper_name,papers.question_ids,papers.scenario_id,papers.grade_id,papers.course_id,papers.create_at,papers.has_subjective,ysy_member.name as teacher_name 
                    FROM ysy_evaluation_papers papers
                    JOIN ysy_member ON papers.examiner_id = ysy_member.id
                    WHERE $paper_where $where
                    ORDER BY create_at desc;";
            $data = Db::query($sql);
            //分发与试卷的关联关系
            $distribution_detail = $this->distributionModel->alias('dis')
                ->field('dis.id as distribution_id,papers.id as paper_id,papers.paper_name,papers.question_ids,papers.scenario_id,papers.grade_id,papers.course_id,papers.create_at,papers.has_subjective,m.name as teacher_name')
                ->join('evaluation_distribution_detail detail','dis.id = detail.distribution_id')
                ->join('evaluation_papers papers','detail.paper_id = papers.id')
                ->join('member m','papers.examiner_id = m.id')
                ->where('detail.status = 0 and dis.role_id = "0,999,0" AND FIND_IN_SET( '.$this->user['school_id'].', dis.school_ids )')
                ->where($paper_where)
                ->select();
            $grade = array_flip(grade());
            $course = array_flip(course());
            $scenario = array_flip(scenario());
            $res = [];
            foreach ($data as $key => $value) {
                //年级
                $res[$key]['create_at'] = $value['create_at'];
                $res[$key]['distribution_id'] = $value['distribution_id'];
                $res[$key]['distribution_name'] = $value['distribution_name'];
                $res[$key]['is_single'] = $value['distribution_id'] ? 2 : 1;//1是单卷2是套卷，有分发id说明是admin分发的套卷
                //如果分发id为空说明是组卷，不为空说明是admin分发
                $detail = [];
                if(!$value['distribution_id']){
                    $detail[] = [
                        'paper_id'       => $value['paper_id'],
                        'paper_name'     => $value['paper_name'],
                        'question_ids'   => $value['question_ids'],
                        'scenario_id'    => $value['scenario_id'],
                        'scenario_name'  => $scenario[$value['scenario_id']] ?? '',
                        'grade_id'       => $value['grade_id'],
                        'grade_name'     => $grade[$value['grade_id']] ?? '',
                        'course_id'      => $value['course_id'],
                        'course_name'    => $course[$value['course_id']] ?? '',
                        'create_at'      => $value['create_at'],
                        'has_subjective' => $value['has_subjective'],
                        'teacher_name'   => $value['teacher_name'],
                    ];
                }else{
                    foreach ($distribution_detail as $kd => $vd){
                        if($value['distribution_id'] == $vd['distribution_id']){
                            $detail[] = [
                                'paper_id'       => $vd['paper_id'],
                                'paper_name'     => $vd['paper_name'],
                                'question_ids'   => $vd['question_ids'],
                                'scenario_id'    => $vd['scenario_id'],
                                'scenario_name'  => $scenario[$vd['scenario_id']] ?? '',
                                'grade_id'       => $vd['grade_id'],
                                'grade_name'     => $grade[$vd['grade_id']] ?? '',
                                'course_id'      => $vd['course_id'],
                                'course_name'    => $course[$vd['course_id']] ?? '',
                                'create_at'      => $vd['create_at'],
                                'has_subjective' => $vd['has_subjective'],
                                'teacher_name'   => $vd['teacher_name'],
                            ];
                        }
                    }
                }
                $res[$key]['papers'] = $detail;
                if(empty($detail)) unset($res[$key]);
            }
            return pageing($res,$pageSize,$pageNumber);
        }
    }

}