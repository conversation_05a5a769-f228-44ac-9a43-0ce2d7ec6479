<?php

namespace App\Services\School\Assessment\Score\Psychology;

/**
 * 自信心评估服务
 * 
 * 用于计算学生的自信心评估结果
 */
class ConfidenceService extends AbstractScoreService
{
    /**
     * 计算自信心评估结果
     * 
     * @param array $params 包含评估所需参数的数组
     * @return array 自信心评估结果数组
     */
    public function calculate(array $params): array
    {
        $dimension_scores = $this->calculateScores($params);
        
        // 计算各维度得分
        $total_scores = [];
        foreach ($dimension_scores as $key => $dimension) {
            // 维度得分 = (维度内题目得分总和 / 维度题目数量) × 25
            $score = round($dimension['score'] / $dimension['question_count'] * 20, 1);
            $dimension_scores[$key]['score'] = $score;
            $total_scores[] = $score;
            unset($dimension_scores[$key]['question_count']);
        }

        // 计算总分
        $average_score = ceil(array_sum($total_scores) / count($total_scores));

        return ['dimensions' => $dimension_scores, 'total_score' => $average_score];
    }
}