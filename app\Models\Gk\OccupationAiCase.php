<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OccupationAiCase extends BaseModel
{
    use HasFactory;

    protected $table = 'OccupationAiCase';

    // 指定连接
    protected $connection = 'sqlsrv_gk';

    //主键
    protected $primaryKey = 'Id';

    // 隐藏字段
    protected $hidden = [];
    
    // 是否使用时间戳
    public $timestamps = false;

    // 可批量赋值的字段
    protected $fillable = [
        'Code',
        'CaseName',
        'RelatedCase',
    ];

    /**
     * 获取关联的职业信息
     */
    public function occupation(): BelongsTo
    {
        return $this->belongsTo(OccupationAi::class, 'Code', 'Code');
    }
}
