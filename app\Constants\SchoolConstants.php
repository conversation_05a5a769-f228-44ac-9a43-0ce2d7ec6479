<?php

namespace App\Constants;

/**
 * School-related constants used across the application
 */
class SchoolConstants
{
    // Shanghai province identifiers
    public const SHANGHAI_PROVINCES = ['上海', '上海市'];
    
    // Campus type constants
    public const CAMPUS_TYPE_PRIMARY = 1; // 小学
    public const CAMPUS_TYPE_MIDDLE = 2;  // 初中
    public const CAMPUS_TYPE_HIGH = 3;    // 高中

    // Grade mapping relationships
    public const GRADE_MAPPING = [
        'default' => [
            self::CAMPUS_TYPE_PRIMARY => [1, 2, 3, 4, 5, 6],
            self::CAMPUS_TYPE_MIDDLE => [7, 8, 9],
            self::CAMPUS_TYPE_HIGH => [10, 11, 12],
        ],
        'shanghai' => [
            self::CAMPUS_TYPE_PRIMARY => [1, 2, 3, 4, 5],
            self::CAMPUS_TYPE_MIDDLE => [6, 7, 8, 9],
            self::CAMPUS_TYPE_HIGH => [10, 11, 12],
        ]
    ];

    /**
     * Check if a province is in Shanghai
     *
     * @param string|null $province
     * @return bool
     */
    public static function isShanghai(?string $province): bool
    {
        return $province && in_array($province, self::SHANGHAI_PROVINCES);
    }

    /**
     * Get grade IDs by campus type and province
     *
     * @param int $campusType
     * @param bool $isShanghai
     * @return array
     */
    public static function getGradeIdsByType(int $campusType, bool $isShanghai): array
    {
        $gradeMap = $isShanghai ? self::GRADE_MAPPING['shanghai'] : self::GRADE_MAPPING['default'];
        
        return $gradeMap[$campusType] ?? [];
    }
}
