<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin\Menu;
use App\Services\Admin\MenuService;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;

class MenuController extends Controller
{
    use CrudOperations;

    protected string $model = Menu::class;

    protected $menuService;

    // 构造函数：注入RedisUtils
    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $list = Menu::where('parent_id', 0)
            ->where('status', 1)
            ->where('type', $request->type)
            ->with('children')
            ->orderBy('sort', 'asc')
            ->get();
        return $this->success($list);
    }

    public function getChildrenMenusByParentId(Request $request)
    {
        return $this->success($this->menuService->getChildrenMenusByParentId($request));
    }

    public function store(Request $request)
    {
        $validator = $this->validateRequest($request, [
            'type' => 'in:1,2,3',
        ]);
        if ($validator->fails()) {
            return $this->error('type参数值错误');
        }

        $this->menuService->store($request);

        return $this->message('创建成功');
    }

    public function update(Request $request, $id)
    {
        $this->menuService->update($request, $id);

        return $this->message('更新成功');
    }

    public function updateStatus($id)
    {
        $record = $this->model::find($id);
        if (!$record) {
            return $this->notFound('对象不存在');
        }

        if($record->status == 0) $record->status = 1;
        else $record->status = 0;

        $record->updater = request()->user()->real_name;
        $record->save();

        // 删除所有角色菜单缓存
        $this->menuService->clearMenuCache($record->type);

        return $this->message('更新成功');
    }

    public function setMenuSort(Request $request)
    {
        $this->menuService->setMenuSort($request);
        return $this->message('调整顺序成功');
    }

}
