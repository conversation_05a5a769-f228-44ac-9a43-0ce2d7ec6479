<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_competency_questions', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('old_question_id')->nullable()->comment('老题目表ID（题冒题为0）');
            $table->unsignedInteger('assessment_id')->comment('测评类型ID');
            $table->string('content',1000)->comment('题目内容');
            $table->json('options')->nullable()->comment('题目选项');
            $table->string('correct',10)->nullable()->comment('客观题正确选项，主观题为空');
            $table->unsignedTinyInteger('number')->nullable()->comment('题目展示顺序');
            $table->string('dimension_name')->comment('维度名称');
            $table->string('dimension_code')->comment('维度代码');
            $table->string('title')->comment('题干、题冒（题冒题有这个值）');
        });
        DB::statement("ALTER TABLE `assessment_competency_questions` comment '创新人才核心素养测评问题表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_competency_questions');
    }
};
