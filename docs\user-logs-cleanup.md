# 用户日志清理功能

## 概述

系统已配置自动清理用户日志表的定时任务，每月1号凌晨2点自动执行，清空以下两个表：
- `user_login_logs` - 用户登录日志表
- `user_access_logs` - 用户访问日志表

## 定时任务配置

定时任务在 `app/Console/Kernel.php` 中配置：

```php
// 每月1号凌晨2点清空用户日志表
$schedule->command('logs:clear-user-logs')
         ->monthlyOn(1, '02:00')
         ->withoutOverlapping()
         ->runInBackground();
```

## 手动执行命令

### 查看将要清理的数据统计（干运行）

```bash
php artisan logs:clear-user-logs --dry-run
```

这个命令会显示当前两个日志表的记录数量，但不会实际删除数据。

### 手动清理日志

```bash
php artisan logs:clear-user-logs
```

执行此命令会：
1. 显示当前记录数量
2. 要求确认操作
3. 清空两个日志表
4. 记录操作日志

## 安全特性

1. **确认机制**: 手动执行时需要用户确认
2. **干运行模式**: 可以预览将要删除的数据量
3. **操作日志**: 每次清理都会记录到系统日志中
4. **错误处理**: 包含完整的异常处理和错误日志记录
5. **防重叠**: 定时任务配置了 `withoutOverlapping()` 防止重复执行

## 查看定时任务状态

```bash
# 查看所有定时任务
php artisan schedule:list

# 运行定时任务（用于测试）
php artisan schedule:run
```

## 日志记录

清理操作会在 Laravel 日志中记录以下信息：
- 清理的记录数量
- 执行时间
- 成功/失败状态
- 错误详情（如果有）

## 注意事项

1. **数据不可恢复**: 清理操作使用 `TRUNCATE` 语句，删除的数据无法恢复
2. **生产环境**: 建议在生产环境部署前先在测试环境验证
3. **备份策略**: 如需保留历史数据，请在清理前进行数据备份
4. **监控**: 建议监控定时任务的执行状态和日志

## 相关文件

- 命令文件: `app/Console/Commands/ClearUserLogs.php`
- 调度配置: `app/Console/Kernel.php`
- 日志模型: `app/Models/LoginLog.php`
- 访问日志模型: `app/Models/Admin/UserAccessLog.php`
