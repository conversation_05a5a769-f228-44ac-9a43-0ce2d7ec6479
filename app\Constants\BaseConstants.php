<?php

namespace App\Constants;

/**
 * 基础常量类
 * 
 * 存放多个模块共享的常量
 */
class BaseConstants
{
    
    // 学校default角色
    const SCHOOL_DEFAULT_ROLE = [
        1 => '学生',
        2 => '教务',
        3 => '教师',
    ];

    // 模块组配置
    const MODULE_GROUP_DEFAULT = [
        [
            'name' => '生涯教育',
            'modules' => ['01','02','03','04']
        ],
        [
            'name' => '走班排课',
            'modules' => []
        ],
        [
            'name' => '学情分析',
            'modules' => []
        ],
        [
            'name' => '数据展板',
            'modules' => []
        ],
        [
            'name' => '学科素养',
            'modules' => ['10']
        ],
        [
            'name' => '心理健康',
            'modules' => []
        ],
        [
            'name' => '生涯翼站',
            'modules' => []
        ],
    ];


    /**
     * 没有分数数据表的省份
     * 
     * 用于标识哪些省份没有专门的分数数据表
     */
    public const NOT_HAS_SCORE_DATA_TABLE_PROVINCES = '澳门|台湾|香港|西藏';
    
    /**
     * 检查省份是否没有分数数据表
     *
     * @param string $provinceName 省份名称
     * @return bool 如果省份没有分数数据表则返回 true，否则返回 false
     */
    public static function hasNoScoreDataTable(string $provinceName): bool
    {
        return preg_match('/' . self::NOT_HAS_SCORE_DATA_TABLE_PROVINCES . '/', $provinceName);
    }


    const FOREIGN_COLLEGE_OTHER = ['亚洲', '欧洲'];
}
