<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAssessmentCareerHollandTemplatesTable extends Migration
{
    /**
     * 运行迁移
     * 创建职业性格测评模板表
     *
     * @return void
     */
    public function up()
    {
        Schema::create('assessment_career_holland_templates', function (Blueprint $table) {
            $table->id();
            $table->string('code', 50)->comment('模板代码');
            $table->string('type', 30)->comment('模板类型');
            $table->unsignedInteger('grade_type')->default(1)->comment('适用年级类别1初中 2高中');
            $table->text('content')->comment('模板内容');
            $table->timestamps();
            $table->softDeletes();
            
            // 索引
            $table->index('code');
            $table->index('type');
            $table->index('grade_type');
        });
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE `assessment_career_holland_templates` comment '职业性格测评模板表'");
    }

    /**
     * 回滚迁移
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('assessment_career_holland_templates');
    }
}