<?php

namespace Database\Seeders\assessment\pretreatment;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DataConversionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    protected string $connect = 'mysql_prod';
    public function run(): void
    {
// 从数据库中查询多条记录
        $records = DB::table('assessment_capability_questions')->where('assessment_id', 13)->get();

        foreach ($records as $record) {
            // 检查 options 字段是否不为空
            if (!empty($record->options)) {
                // 解码 options 字段的 JSON 字符串为 PHP 数组
                $optionsArray = json_decode($record->options, true);

                // 初始化结果关联数组
                $newOptionsArray = [];

                // 遍历解码后的数组并构建新的关联数组
                foreach ($optionsArray as $item) {
                    $newOptionsArray[$item['option']] = ['name' => $item['name']];
                }

                // 将新的关联数组编码回 JSON 字符串
                $newOptionsJson = json_encode($newOptionsArray, JSON_UNESCAPED_UNICODE); // 可以根据需要添加或省略 JSON_UNESCAPED_UNICODE

                // 更新数据库中的 options 字段
                DB::table('assessment_capability_questions')
                    ->where('id', $record->id) // 假设你的表有一个名为 id 的主键字段
                    ->update(['options' => $newOptionsJson]);
            } else {
                // 处理 options 字段为空的情况（如果需要）
                // ...
            }
        }
    }
}
