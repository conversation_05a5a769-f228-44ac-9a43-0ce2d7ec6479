# 批量教师同步功能说明

## 概述

批量教师同步功能支持根据role_name（汉字）进行角色判断，并自动检查用户名重复，避免重复插入。

## 请求数据格式

```json
{
    "school_campus_id": 1291,
    "teachers": [
        {
            "teacher_name": "测试1221",
            "gender": "男",
            "username": "ceshiteacher1221",
            "role_name": "教务"
        },
        {
            "teacher_name": "测试3311",
            "gender": "男",
            "username": "ceshiteacher1331",
            "role_name": "老师"
        }
    ]
}
```

## 功能特点

### 1. 角色名称转换

**支持的角色名称：**
- `"教务"` → 转换为 `roles = [2]`
- `"老师"` → 转换为 `roles = [3]`

### 2. 重复检查

在插入前检查用户名是否已存在于同步数据库的ysy_member表中：
```php
$existingMember = $this->syncConnection->table('ysy_member')
    ->where('username', $username)
    ->first();
```

如果用户名已存在，则跳过插入并记录为跳过状态。

### 3. 性别转换

- `"男"` → 转换为 `1`
- `"女"` → 转换为 `2`

## 处理流程

### 1. 数据预处理
```php
foreach ($teachers as $teacher_info) {
    $teacher_name = $teacher_info['teacher_name'];
    $username = $teacher_info['username'];
    $gender = $teacher_info['gender'] == '男' ? 1 : 2;
    $role_name = $teacher_info['role_name'];
    
    // 转换role_name为roles数组
    $roles = [];
    if ($role_name == '教务') {
        $roles = [2];
    } elseif ($role_name == '老师') {
        $roles = [3];
    }
}
```

### 2. 重复检查
```php
$existingMember = $this->syncConnection->table('ysy_member')
    ->where('username', $username)
    ->first();
    
if ($existingMember) {
    // 跳过插入，记录为已存在
    continue;
}
```

### 3. 角色查询
```php
if (in_array(2, $roles)) {
    // 教务角色
    $role = $this->syncConnection->table('ysy_role')
        ->where('name', '教务')
        ->where('school_id', $syncSchoolId)
        ->first();
} elseif (in_array(3, $roles)) {
    // 教师角色
    $role = $this->syncConnection->table('ysy_role')
        ->where('name', '老师')
        ->where('school_id', $syncSchoolId)
        ->first();
}
```

### 4. 数据同步
调用现有的`syncTeacher`方法进行数据同步到ysy_member和ysy_teacher表。

## 返回结果

```json
{
    "success": true,
    "sync_results": [
        {
            "teacher_name": "测试1221",
            "username": "ceshiteacher1221",
            "success": true,
            "message": "教师数据同步成功"
        },
        {
            "teacher_name": "测试3311",
            "username": "ceshiteacher1331",
            "success": false,
            "message": "用户名已存在，跳过插入",
            "skipped": true
        }
    ],
    "total_count": 2,
    "success_count": 1,
    "skipped_count": 1,
    "message": "批量教师数据同步完成"
}
```

## 返回字段说明

- **success**: 整体操作是否成功
- **sync_results**: 每个教师的同步结果详情
- **total_count**: 总教师数量
- **success_count**: 成功同步的教师数量
- **skipped_count**: 跳过的教师数量（因为用户名已存在）
- **message**: 操作结果消息

## 错误处理

### 1. 用户名重复
```json
{
    "teacher_name": "测试教师",
    "username": "existing_username",
    "success": false,
    "message": "用户名已存在，跳过插入",
    "skipped": true
}
```

### 2. 角色不存在
```json
{
    "teacher_name": "测试教师",
    "username": "test_username",
    "success": false,
    "message": "未找到对应的角色信息"
}
```

### 3. 同步失败
```json
{
    "teacher_name": "测试教师",
    "username": "test_username",
    "success": false,
    "message": "同步失败: 具体错误信息"
}
```

## 使用示例

### Controller调用
```php
// 批量创建教师（包含自动同步）
$created_teachers = $this->teacherService->batchStore($request);
```

### Service层集成
TeacherService的batchStore方法现在自动包含同步功能：

```php
public function batchStore(Request $request): array
{
    // ... 创建教师逻辑 ...

    DB::commit();

    // 调用批量同步功能
    try {
        $dataSyncService = app(\App\Services\DataSync\DataSyncService::class);
        $sync_result = $dataSyncService->syncBatchTeachers($request->all());
        \Log::info('批量教师同步结果', $sync_result);
    } catch (\Exception $e) {
        // 同步失败不影响主流程，只记录日志
        \Log::warning('批量教师同步失败', [
            'error' => $e->getMessage(),
            'teacher_count' => count($teacher_result_list)
        ]);
    }

    return $teacher_result_list;
}
```

## 注意事项

1. **用户名唯一性**: 系统会自动检查用户名是否已存在，避免重复插入
2. **角色名称**: 只支持"教务"和"老师"两种角色名称
3. **性别格式**: 只支持"男"和"女"两种性别表示
4. **事务处理**: 每个教师的同步都是独立的事务，一个失败不影响其他教师
5. **日志记录**: 所有同步结果都会记录到日志中，便于问题排查
