<?php

namespace App\Http\Controllers\School\Assessment;

use App\Http\Controllers\Controller;
use App\Http\Requests\School\Assessment\PdfZipRequest;
use App\Services\School\Assessment\BatchDownloadPdfService;
use App\Exceptions\BusinessException;
use App\Services\Tool\MessageService;

class PdfZipController extends Controller
{
    protected BatchDownloadPdfService $batchDownloadPdfService;

    public function __construct(BatchDownloadPdfService $batchDownloadPdfService)
    {
        $this->batchDownloadPdfService = $batchDownloadPdfService;
    }

    /**
     * 批量下载测评报告PDF
     *
     * @param PdfZipRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchDownloadReportPdf(PdfZipRequest $request)
    {
        try {
            $params = $request->validated();
            $params['creator'] = $request->user()->real_name;
            $params['school_id'] = $request->user()->organization->model_id;
            
            list($recordName, $zip_url) = $this->batchDownloadPdfService->handle($params);
            // 发送消息通知
            $message = [
                'title' => 'pdf批量下载成功',
                'content' => '测评任务：'.$recordName.'pdf批量下载成功',
                'type' => 'info',
                'url' => $zip_url,
            ];
            $messageService = new MessageService();
            $messageService->sendMessage($request->user()->id, $message);

            return $this->success($zip_url);
        } catch (\Exception $e) {
            throw new BusinessException($e->getMessage(), 500);
        }
    }
}