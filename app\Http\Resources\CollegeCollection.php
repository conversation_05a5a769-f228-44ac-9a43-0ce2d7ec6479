<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CollegeCollection extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $ranks = collect($this->ranks);
        return [
            'id' => $this->ID,
            'college_name' => $this->CollegeName,
            'province_id' => $this->ProvinceID,
            'province_name' => $this->province ? $this->province->ProvinceName : null,
            'city_id' => $this->CityID,
            'city_name' => $this->city ? $this->city->CityName : null,
            'yxls' => $this->Yxls,
            'levels' => $this->Levels,
            'bxtype' => $this->Bxtype,
            'yxtype' => $this->Yxtype,
            'yxjbz' => $this->Yxjbz,
            'big_logo' => $this->BigLogo,
            'small_logo' => $this->SmallLogo,
            'rank' => $this->Rank,
            'ranks' => $ranks->map(function ($rank) {
                if (is_array($rank)) {
                    return [
                        'type' => $rank['Type'] ?? null,
                        'year' => $rank['Year'] ?? null,
                        'rank' => $rank['Rank'] ?? null,
                    ];
                } elseif (is_object($rank)) {
                    return [
                        'type' => $rank->Type ?? null,
                        'year' => $rank->Year ?? null,
                        'rank' => $rank->Rank ?? null,
                    ];
                }
                return null;
            })->filter(),
            'tags' => $this->tagsFiltered->pluck('Type')->toArray(),
/*            'tags' => $this->tags->map(function ($tag) {
                return [
//                    'id' => $tag->Id,
//                    'college_id' => $tag->CollegeId,
//                    'college_name' => $tag->CollegeName,
                    'type' => $tag->Type,
                    'state' => $tag->State,
                    'sort' => $tag->Sort,
                    'influence' => $tag->Influence,
                ];
                }),*/
        ];
    }
}
