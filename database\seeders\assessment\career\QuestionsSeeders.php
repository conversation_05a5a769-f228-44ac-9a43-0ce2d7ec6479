<?php

namespace Database\Seeders\assessment\career;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class QuestionsSeeders extends Seeder
{
    protected string $connect = 'mysql_prod';

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 使用数据库事务
        DB::transaction(function () {
            // 学习生活适应性评估
            $this->processSurveyQuestions('1', 1);
            // 智能评估
            $this->processSurveyQuestions('18', 2);
            // 性格评估
            $this->processSurveyQuestions('21', 3);
            // 兴趣评估
            $this->processSurveyQuestions('28', 4);
            // 生涯发展水平评估
            $this->processSurveyQuestions('45', 5);
            // 多元智能评估（初中版）
            $this->processSurveyQuestions('32', 6);
            // 生涯发展水平评估（初中版)
            $this->processSurveyQuestions('53', 7);
            // 兴趣评估（初中版）
            $this->processSurveyQuestions('72', 8);
        });
    }

    function processSurveyQuestions($surveyId, $assessmentsId) {
        // 获取问题数据
        $surveyQuestionData = DB::connection($this->connect)
            ->table('survey_question')
            ->where('survey_id', $surveyId)
            ->orderBy('show_id', 'asc')
            ->get()
            ->toArray();

        $assessmentQuestionData = [];
        foreach ($surveyQuestionData as $item) {
            // 获取每个问题的选项
            $optionsData = DB::connection($this->connect)
                ->table('survey_question_answer')
                ->where('question_id', $item->id)
                ->orderBy('question_id', 'asc') // 使用适当的列来排序选项
                ->get()
                ->toArray();

            // 为选项分配字母
            $options = array_map(function ($option, $index) {
                $letter = chr(65 + $index); // ASCII 码 A=65, B=66, C=67, ...
                return [
                    "option" => $letter,
                    "name" => $option->answer,
                    "score" => $option->sort
                ];
            }, $optionsData, array_keys($optionsData));

            $Question_item_Data = [
                'question_content' => $item->name,
                'old_question_id' => $item->id,
                'is_normal' => $item->is_normal_type,
                'assessment_id' => $assessmentsId,
                'options' => json_encode($options),
                'created_at' => now()->toDateTimeString(),
            ];
            $assessmentQuestionData[] = $Question_item_Data;
        }

        DB::table('assessment_career_questions')->insert($assessmentQuestionData);
    }

}
