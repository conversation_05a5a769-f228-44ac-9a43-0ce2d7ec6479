<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MajorAiIntroduction extends JsonResource
{


    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->Id,
            'code' => $this->Code,
            'name' => $this->Name,
            'introduction' => $this->Introduction,
            'course' => $this->Course,
            'job_direction' => $this->JobDirection,
        ];
    }


}
