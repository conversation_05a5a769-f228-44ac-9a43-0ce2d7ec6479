<?php

namespace App\Models\School\System;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ViewLastStudentClass extends Model
{
    use HasFactory;
    protected $table = 'view_last_student_classes';

    // 归属于学生
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id', 'id');
    }

    // 归属于班级
    public function claass()
    {
        return $this->belongsTo(Claass::class, 'class_id', 'id');
    }

}
