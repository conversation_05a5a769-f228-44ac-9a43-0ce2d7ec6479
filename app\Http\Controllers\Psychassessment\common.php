<?php
    // 计算开始段号和结束段号
    function calculateSegment($time, $segmentsPerDay) {
        // 分割时间字符串为小时和分钟
        list($hour, $minute) = explode(':', $time);
        // 计算当前时间距离0点的分钟数
        $minutesFromMidnight = ($hour * 60) + $minute;
        // 计算段号，每30分钟为一段
        return (int) ($minutesFromMidnight / 30);
    }

    // 反推时间
    function reverseCalculateTime($segment, $segmentsPerDay) {
        // 每段30分钟，所以乘以30得到分钟数
        $minutesFromMidnight = $segment * 30;
        // 计算小时数和剩余的分钟数
        $hours = intdiv($minutesFromMidnight, 60);
        $minutes = $minutesFromMidnight % 60;
        // 格式化时间输出
        return sprintf('%02d:%02d', $hours, $minutes);
    }

    // 心里类型代号与心理对应关系
    function type_to_psych() {
        return [
            '1'=>'情绪困扰',
            '2'=>'行为问题',
            '3'=>'学业问题',
            '4'=>'人际交往',
            '5'=>'个人发展',
        ];
    }
    
    // 心里维度代号与心理维度对应关系
    function code_psych_competence($survey_type=26) {
        switch ($survey_type){
            case 26:
                return [
                    'jiazhizixin'  => '价值自信',
                    'pindezixin'   => '品德自信',
                    'renjizixin'   => '人际自信',
                    'nenglizixin'  => '能力自信',
                    'xueyezixin'   => '学业自信',
                    'waibiaozixin' => '外表自信',
                ];
                break;
            case 27:
                return [
                    'xingweiyishi' => '行为意识',
                    'xueyeyishi'   => '学业意识',
                    'shentiyishi'  => '身体意识',
                    'qingxuyishi'  => '情绪意识',
                    'renjiyishi'   => '人际意识',
                    'xingfuyishi'  => '幸福意识',
                ];
                break;
            case 28:
                return [
                    'xuexijiaolv'     => '学习焦虑',
                    'kaoshijiaolv'    => '考试焦虑',
                    'jiatingjiaolv'   => '家庭焦虑',
                    'renjijiaolv'     => '人际焦虑',
                    'xingxiangjiaolv' => '形象焦虑',
                ];
                break;
        }
    }

    /*总体自信结果划分
    ＜45信心不足 45≤Z＜70一般  70≤Z＜85比较自信 ≥85非常自信

    维度自信区间划分
    ＜45较低 45≤Z＜70一般  70≤Z良好

    总体自信及各维度自信得分全都≥85分  出特殊报告1
    总体自信及各维度自信得分全都在[75，85)之间  出特殊报告2
    其他 出正常报告

    $arr=[['价值自信','品德自信','人际自信','能力自信','学业自信','外表自信'],'总分'];*/
    function confidence_situation($arr){
        switch($arr[1]){
            case $arr[1] < 45:
                $confidence_situation = '信心不足';
                break;
            case 45 <= $arr[1] && $arr[1] < 70:
                $confidence_situation = '一般自信';
                break;
            case 70 <= $arr[1] && $arr[1] < 85:
                $confidence_situation = '比较自信';
                break;
            case $arr[1] >= 85:
                $confidence_situation = '非常自信';
                break;
        }
        return $confidence_situation;
    }

    function confidence_competence_level($confidence_competence){
        switch($confidence_competence){
            case $confidence_competence < 45:
                $competence_level = '较低';
                $competence_code = 'xinxibuzu';
                break;
            case 45 <= $confidence_competence && $confidence_competence < 70:
                $competence_level = '一般';
                $competence_code = 'yibanzixin';
                break;
            case $confidence_competence >= 70:
                $competence_level = '良好';
                $competence_code = 'bijiaozixin';
                break;
        }

        return $competence_code;
    }

    function confidence_abnormal($score){
        switch($score){
            case $score < 45:
                $situation = '异常';
                break;
            case $score >= 45:
                $situation = '正常';
                break;
        }

        return $situation;
    }

    function confidence_abnormal_kind($arr){
        $i = 0;
        $j = 0;
        $is_abnormal = '一般';
        foreach ($arr[0] as $v){
            switch($v){
                case $v < 45:
                    $competence_level = '较低';
                    break;
                case 45 <= $v && $v < 70:
                    $competence_level = '一般';
                    break;
                case $v >= 70:
                    $competence_level = '良好';
                    if($v >= 75 && $v < 85) $j++;
                    if($v >= 85) $i++;
                    break;
            }
        }
        if($arr[1] >= 85) $i++;
        if($arr[1] >= 75 && $arr[1] < 85) $j++;
        if($i == 7) $is_abnormal = '异常1,对应特殊报告1';//对应特殊报告1
        if($j == 7) $is_abnormal = '异常2,对应特殊报告2';//对应特殊报告2
        return $is_abnormal;
    }

    /*各维度分=维度内题目得分的总和/维度内题目数量×20
    3）总体自我意识水平
    总体自我意识得分=各维度分的加和平均
    即==（行为意识分+学业意识分+身体意识分+情绪意识分+人际意识分+幸福意识分）/6

    总体自我意识结果评估
    ＜45较低水平 ≥45正常水平

    维度水平区间划分
    ＜45较低水平 ≥45正常水平

    $arr=[['行为意识','学业意识','身体意识','情绪意识','人际意识','幸福意识'],'总分'];*/
    function awareness_situation($arr){
        switch($arr[1]){
            case $arr[1] < 45:
                $awareness_situation = '较低水平';
                break;
            case $arr[1] >= 45:
                $awareness_situation = '正常水平';
                break;
        }
        return $awareness_situation;
    }

    function awareness_competence_level($awareness_competence){
        switch($awareness_competence){
            case $awareness_competence < 45:
                $competence_level = '较低水平';
                $competence_code = 'low';
                break;
            case 45 <= $awareness_competence:
                $competence_level = '正常水平';
                $competence_code = 'normal';
                break;
        }

        return $competence_code;
    }

    function awareness_abnormal($score){
        switch($score){
            case $score < 45:
                $situation = '异常';
                break;
            case 45 <= $score:
                $situation = '正常';
                break;
        }

        return $situation;
    }

    /*注意焦虑程度和焦虑方面是区分开的，各20题
    判断规则：
    第一部分焦虑程度：焦虑总分=题目总分/题目数量*25。
    ＜50分：无焦虑，
    50≤得分＜60分：轻度焦虑，
    60≤得分＜70分：中度焦虑，
    ≥70分：重度焦虑

    第二部分焦虑方面：各维度分=维度内题目得分的总和/维度内题目数量*25
    判断规则：（各维度适用）
    ＜50分：正常状态
    50≤得分＜70分：一些焦虑
    ≥70分；非常焦虑
    $arr=['焦虑程度',['学习焦虑','考试焦虑','家庭焦虑','人际焦虑','形象焦虑']];*/
    function anxiety_situation($arr){
        switch($arr[0]){
            case $arr[0] < 50:
                $anxiety_situation = '无焦虑';
                break;
            case 50 <= $arr[0] && $arr[0] < 60:
                $anxiety_situation = '轻度焦虑';
                break;
            case 60 <= $arr[0] && $arr[0] < 70:
                $anxiety_situation = '中度焦虑';
                break;
            case $arr[0] >= 70:
                $anxiety_situation = '重度焦虑';
                break;
        }
        return $anxiety_situation;
    }

    function anxiety_code_and_situation($arr){
        switch($arr[0]){
            case $arr[0] < 50:
                $anxiety_situation = '无焦虑';
                $anxiety_code = 'free';
                break;
            case 50 <= $arr[0] && $arr[0] < 60:
                $anxiety_situation = '轻度焦虑';
                $anxiety_code = 'mild';
                break;
            case 60 <= $arr[0] && $arr[0] < 70:
                $anxiety_situation = '中度焦虑';
                $anxiety_code = 'moderate';
                break;
            case $arr[0] >= 70:
                $anxiety_situation = '重度焦虑';
                $anxiety_code = 'severe';
                break;
        }
        return [$anxiety_situation,$anxiety_code];
    }

    function anxiety_competence_level($anxiety_competence){
        switch($anxiety_competence){
            case $anxiety_competence < 50:
                $competence_code = 'normal';
                break;
            case 50 <= $anxiety_competence && $anxiety_competence < 70:
                $competence_code = 'some';
                break;
            case 70 <= $anxiety_competence:
                $competence_code = 'much';
                break;
        }

        return $competence_code;
    }

    function anxiety_abnormal($score){
        switch($score){
            case $score < 70:
                $situation = '正常';
                break;
            case 70 <= $score:
                $situation = '异常';
                break;
        }

        return $situation;
    }

    function getRankDescending($number, $array) {
        // 对数组进行降序排序
        rsort($array);
        // 使用array_search查找数字在排序后数组中的键名（索引）
        $index = array_search($number, $array);
        if ($index !== false) {
            // 当前索引（从0开始）再加1，即为排名
            $rank = $index + 1;
            return $rank;
        } else {
            return false; // 如果数字不在数组中，返回false
        }
    }