<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DataSyncController;

/*
|--------------------------------------------------------------------------
| Data Sync Routes
|--------------------------------------------------------------------------
|
| 数据同步模块路由
|
*/

// 需要认证的数据同步路由
Route::middleware(['auth:api'])->prefix('admin/datasync')->group(function () {
    
    // 手动同步接口
    Route::post('sync-school', [DataSyncController::class, 'syncSchool'])->name('datasync.sync_school');
    Route::post('sync-student', [DataSyncController::class, 'syncStudent'])->name('datasync.sync_student');
    Route::post('sync-teacher', [DataSyncController::class, 'syncTeacher'])->name('datasync.sync_teacher');
    Route::post('sync-admin', [DataSyncController::class, 'syncAdmin'])->name('datasync.sync_admin');
    
    // 批量同步接口
    Route::post('batch-sync', [DataSyncController::class, 'batchSync'])->name('datasync.batch_sync');
    
    // 同步数据管理接口
    Route::delete('delete-sync-data', [DataSyncController::class, 'deleteSyncData'])->name('datasync.delete_sync_data');
    Route::put('update-sync-data', [DataSyncController::class, 'updateSyncData'])->name('datasync.update_sync_data');
    
    // 同步状态查询接口
    Route::get('sync-status', [DataSyncController::class, 'getSyncStatus'])->name('datasync.sync_status');

    // 字段映射管理接口
    Route::get('field-mapping', [DataSyncController::class, 'getFieldMapping'])->name('datasync.field_mapping');
    Route::post('test-field-mapping', [DataSyncController::class, 'testFieldMapping'])->name('datasync.test_field_mapping');

    // 配置管理接口
    Route::get('config', [DataSyncController::class, 'getConfig'])->name('datasync.config');
});

// 测试接口（无需认证）
Route::get('datasync/test', function () {
    return response()->json([
        'status' => 'success',
        'code' => 200,
        'message' => '数据同步模块接口测试成功',
        'data' => [
            'timestamp' => now()->toDateTimeString(),
            'test_mode' => true,
            'available_endpoints' => [
                'POST /admin/datasync/sync-school' => '同步学校数据',
                'POST /admin/datasync/sync-student' => '同步学生数据',
                'POST /admin/datasync/sync-teacher' => '同步教师数据',
                'POST /admin/datasync/sync-admin' => '同步教务人员数据',
                'POST /admin/datasync/batch-sync' => '批量同步数据',
                'DELETE /admin/datasync/delete-sync-data' => '删除同步数据',
                'PUT /admin/datasync/update-sync-data' => '更新同步数据',
                'GET /admin/datasync/sync-status' => '获取同步状态',
                'GET /admin/datasync/field-mapping' => '获取字段映射配置',
                'POST /admin/datasync/test-field-mapping' => '测试字段映射',
                'GET /admin/datasync/config' => '获取数据同步配置'
            ]
        ]
    ]);
});

// 学校数据同步测试接口（无需认证）
Route::post('datasync/test-school-sync', function (Request $request) {
    try {
        $syncHelper = new \App\Helpers\DataSyncHelper();

        // 测试学校数据
        $testSchoolData = [
            'id' => $request->input('id', 999),
            'name' => $request->input('name', '测试学校'),
            'code' => $request->input('code', 'TEST001'),
            'type' => $request->input('type', 1),
            'level' => $request->input('level', 1),
            'phone' => $request->input('phone', '010-12345678'),
            'email' => $request->input('email', '<EMAIL>'),
            'address' => $request->input('address', '测试地址'),
            'status' => $request->input('status', 1),
        ];

        // 调用同步，传入请求数据
        $result = $syncHelper->syncSchool($testSchoolData, $request->all());

        return response()->json([
            'status' => 'success',
            'code' => 200,
            'message' => '学校数据同步测试完成',
            'data' => [
                'test_school_data' => $testSchoolData,
                'request_data' => $request->all(),
                'sync_result' => $result
            ]
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'code' => 500,
            'message' => '学校数据同步测试失败',
            'error' => $e->getMessage()
        ]);
    }
});
