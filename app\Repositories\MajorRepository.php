<?php

namespace App\Repositories;

use App\Models\Gk\MajorRank;

class MajorRepository
{

    /**
     * employment
     * 获取学校就业相关数据
     * @param int $id 学校ID
     * @return array 就业相关数据
     */
    public function getTop5MajorCollegeRank($major_code)
    {
        $majorCollegeRanks = MajorRank::where('MajorCode', $major_code)
        ->where('Year', MajorRank::max('Year'))
        ->with(['college' => function($query) {
            $query->select('ID', 'CollegeName', 'Is211', 'Is985', 'Yxls', 'Yxtype', 'SmallLogo');
        }])
        ->orderByDesc('StartRank')
        ->orderBy('Rank')
        ->orderBy('CollegeLevel')
        ->limit(5)
        ->get([
            'Id', 'MajorCode', 'MajorName', 'Rank', 'CollegeId',
            'CollegeName', 'StartRank', 'CollegeLevel', 'Year', 'Level', 'Score'
        ]);
        return $majorCollegeRanks;
    }

}