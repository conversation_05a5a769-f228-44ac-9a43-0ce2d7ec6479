<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 知识点模型
 */
class Knowledges extends Model
{
    protected $table = 'evaluation_knowledges';

    // 自定义时间戳字段名
    const CREATED_AT = 'create_at';
    const UPDATED_AT = 'update_at';

    protected $fillable = [
        'name',
        'description',
        'course_id',
        'parent_id',
        'is_high',
        'status',
        'create_at',
        'update_at'
    ];

    protected $casts = [
        'course_id' => 'integer',
        'parent_id' => 'integer',
        'is_high' => 'integer',
        'status' => 'integer',
    ];

    /**
     * 子知识点关联
     */
    public function children(): HasMany
    {
        return $this->hasMany(Knowledges::class, 'parent_id')->where('status', 0)->orderBy('id');
    }

    /**
     * 父知识点关联
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Knowledges::class, 'parent_id');
    }

    /**
     * 学科关联
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School\System\Course::class, 'course_id');
    }

    /**
     * 作用域：按学科筛选
     */
    public function scopeByCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    /**
     * 作用域：按父级筛选
     */
    public function scopeByParent($query, $parentId)
    {
        return $query->where('parent_id', $parentId);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 作用域：顶级知识点
     */
    public function scopeTopLevel($query)
    {
        return $query->where('parent_id', 0);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('id');
    }

    /**
     * 作用域：按学段筛选
     */
    public function scopeByIsHigh($query, $isHigh)
    {
        return $query->where('is_high', $isHigh);
    }

    /**
     * 获取所有子知识点ID（递归）
     */
    public function getAllChildrenIds(): array
    {
        $childrenIds = [];
        $this->collectChildrenIds($this->id, $childrenIds);
        return $childrenIds;
    }

    /**
     * 递归收集子知识点ID
     */
    private function collectChildrenIds(int $parentId, array &$childrenIds): void
    {
        $children = static::where('parent_id', $parentId)->where('status', 0)->get();
        
        foreach ($children as $child) {
            $childrenIds[] = $child->id;
            $this->collectChildrenIds($child->id, $childrenIds);
        }
    }

    /**
     * 获取知识点路径
     */
    public function getPath(): array
    {
        $path = [];
        $current = $this;
        
        while ($current) {
            array_unshift($path, [
                'id' => $current->id,
                'name' => $current->name
            ]);
            $current = $current->parent;
        }
        
        return $path;
    }

    /**
     * 获取知识点层级
     */
    public function getLevel(): int
    {
        $level = 0;
        $current = $this;
        
        while ($current->parent_id > 0) {
            $level++;
            $current = $current->parent;
            if (!$current) break;
        }
        
        return $level;
    }

    /**
     * 检查是否为叶子节点
     */
    public function isLeaf(): bool
    {
        return $this->children()->count() === 0;
    }

    /**
     * 检查是否为根节点
     */
    public function isRoot(): bool
    {
        return $this->parent_id === 0;
    }

    /**
     * 检查是否可以删除
     */
    public function canDelete(): bool
    {
        // 检查是否有子知识点
        if ($this->children()->exists()) {
            return false;
        }

        // 检查是否被题目使用
        $isUsed = \DB::table('evaluation_questions')
            ->where('knowlege_ids', 'like', '%' . $this->id . '%')
            ->where('status', 0)
            ->exists();

        if ($isUsed) {
            return false;
        }

        return true;
    }

    /**
     * 获取使用该知识点的题目数量
     */
    public function getQuestionCount(): int
    {
        return \DB::table('evaluation_questions')
            ->where('knowlege_ids', 'like', '%' . $this->id . '%')
            ->where('status', 0)
            ->count();
    }

    /**
     * 获取学段文本
     */
    public function getIsHighTextAttribute(): string
    {
        return $this->is_high == 1 ? '高中' : '初中';
    }

    /**
     * 获取完整名称（包含路径）
     */
    public function getFullNameAttribute(): string
    {
        $path = $this->getPath();
        return implode(' > ', array_column($path, 'name'));
    }

    /**
     * 软删除
     */
    public function softDelete(): bool
    {
        return $this->update(['status' => -1]);
    }

    /**
     * 恢复软删除
     */
    public function restore(): bool
    {
        return $this->update(['status' => 0]);
    }

    /**
     * 批量软删除
     */
    public static function batchSoftDelete(array $ids): bool
    {
        return static::whereIn('id', $ids)->update(['status' => -1]) > 0;
    }

    /**
     * 获取同级知识点
     */
    public function getSiblings()
    {
        return static::where('parent_id', $this->parent_id)
            ->where('id', '!=', $this->id)
            ->where('status', 0)
            ->orderBy('id')
            ->get();
    }

    /**
     * 获取下一个排序值
     * 注意：由于数据库中没有 sort 字段，此方法返回 0
     */
    public static function getNextSort(int $parentId = 0): int
    {
        // 由于数据库中没有 sort 字段，返回 0
        return 0;
    }

    /**
     * 重新排序
     * 注意：由于数据库中没有 sort 字段，此方法暂时返回 true
     */
    public static function reorder(int $parentId = 0): bool
    {
        // 由于数据库中没有 sort 字段，暂时返回 true
        return true;
    }

    /**
     * 移动到指定位置
     */
    public function moveTo(int $newParentId, int $newSort = null): bool
    {
        // 由于数据库中没有 sort 字段，只更新 parent_id
        return $this->update([
            'parent_id' => $newParentId
        ]);
    }

    /**
     * 复制知识点（包含子知识点）
     */
    public function duplicate(int $newParentId = null, string $nameSuffix = '_副本'): Knowledges
    {
        $newParentId = $newParentId ?? $this->parent_id;
        
        $newKnowledge = static::create([
            'name' => $this->name . $nameSuffix,
            'description' => $this->description,
            'course_id' => $this->course_id,
            'parent_id' => $newParentId,
            'is_high' => $this->is_high,
            'sort' => static::getNextSort($newParentId),
            'status' => 0
        ]);

        // 复制子知识点
        foreach ($this->children as $child) {
            $child->duplicate($newKnowledge->id, '');
        }

        return $newKnowledge;
    }
}
