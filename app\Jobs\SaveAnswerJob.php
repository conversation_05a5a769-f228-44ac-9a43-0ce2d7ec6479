<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

class SaveAnswerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务尝试次数
     *
     * @var int
     */
    public $tries = 3;

    /**
     * 任务参数
     *
     * @var array
     */
    protected $params;

    /**
     * Create a new job instance.
     *
     * @param array $params
     * @return void
     */
    public function __construct(array $params)
    {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            Log::info('开始录入评估答案', ['params' => $this->params]);
            $assessment_id_type_module = config('assessment.assessment_id_type_module');
            //录入
            $model = 'App\Models\School\Assessment\Answer\Assessment'.$assessment_id_type_module[$this->params['assessment_id']]['module'].'Answer';
            $model::insert($this->params['answers']);
            
            Log::info('职业评估答案保存完成');
        } catch (\Exception $e) {
            throw new \Exception("职业评估答案保存失败", 500, $e);
        }
    }

    /**
     * 处理失败作业
     */
    public function failed(Throwable $exception): void
    {
        // 向用户发送失败通知等...
    }

}
