<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_tasks', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('assessment_schedule_id')->comment('测评计划ID');
            $table->unsignedInteger('assessment_id')->comment('测评类型ID');
            $table->softDeletes();
            $table->timestamps();
            $table->index('assessment_schedule_id');
        });
        DB::statement("ALTER TABLE `assessment_tasks` comment '测评任务表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_tasks');
    }
};
