<?php

namespace App\Services\School\Assessment\Score\Psychology;

/**
 * 自我意识评估服务类
 * 
 * 该类用于计算学生的自我意识评估结果，包括各维度分数计算和总分计算
 */
class SelfAwarenessService extends AbstractScoreService
{
    /**
     * 计算自我意识评估结果
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 评估结果数组，包含维度分数和总分
     */
    public function calculate(array $params): array
    {
        $dimension_scores = $this->calculateScores($params);
        
        // 计算各维度得分
        $total_scores = [];
        foreach ($dimension_scores as $key => $dimension) {
            // 维度得分 = (维度内题目得分总和 / 维度题目数量) × 20
            $score = round($dimension['score'] / $dimension['question_count'] * 20, 1);
            $dimension_scores[$key]['score'] = $score;
            $total_scores[] = $score;
            unset($dimension_scores[$key]['question_count']);
        }

        // 计算总分
        $average_score = ceil(array_sum($total_scores) / count($total_scores));

        return ['dimensions'=>$dimension_scores,'total_score'=>$average_score];
    }
}