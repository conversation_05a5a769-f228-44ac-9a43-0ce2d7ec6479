<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON>
 * Date: 2024/01/19
 * Time: 17:52
 */
namespace app\psychassessment\service;

class Focus{
    protected $FocusLogic;
    public function __construct()
    {
        $this->FocusLogic = new \app\psychassessment\logic\Focus();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://查询

                return $this->FocusLogic->get_list();
                break;
            case 'post'://增加

                return $this->FocusLogic->add();
                break;
            case 'put'://修改

                return $this->FocusLogic->edit();
                break;
            case 'delete'://删除
                return $this->FocusLogic->del();
                break;
            default:
                return false;
        }
    }

}