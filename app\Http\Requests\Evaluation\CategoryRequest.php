<?php

namespace App\Http\Requests\Evaluation;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 类别请求验证类
 */
class CategoryRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        $rules = [
            'category_name' => 'required|string|max:100',
            'course_id' => 'required|integer|min:1',
        ];

        // 根据请求方法添加不同的规则
        if ($this->isMethod('post')) {
            // 创建时的额外规则
            $rules = array_merge($rules, [
                'parent_id' => 'sometimes|integer|min:0',
                'content' => 'sometimes|string|max:2000',
                'sort' => 'sometimes|integer|min:0',
            ]);
        } elseif ($this->isMethod('put') || $this->isMethod('patch')) {
            // 更新时的规则（大部分字段变为可选）
            $rules = [
                'category_name' => 'sometimes|string|max:100',
                'course_id' => 'sometimes|integer|min:1',
                'parent_id' => 'sometimes|integer|min:0',
                'content' => 'sometimes|string|max:2000',
                'sort' => 'sometimes|integer|min:0',
            ];
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性名称
     */
    public function attributes(): array
    {
        return [
            'category_name' => '类别名称',
            'course_id' => '学科',
            'parent_id' => '父类别',
            'content' => '类别描述',
            'sort' => '排序',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            'category_name.required' => '类别名称不能为空',
            'category_name.string' => '类别名称必须是字符串',
            'category_name.max' => '类别名称不能超过100个字符',
            'course_id.required' => '学科不能为空',
            'course_id.integer' => '学科必须是整数',
            'course_id.min' => '学科ID不能小于1',
            'parent_id.integer' => '父类别必须是整数',
            'parent_id.min' => '父类别ID不能小于0',
            'content.string' => '类别描述必须是字符串',
            'content.max' => '类别描述不能超过2000个字符',
            'sort.integer' => '排序必须是整数',
            'sort.min' => '排序不能小于0',
        ];
    }

    /**
     * 配置验证实例
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 自定义验证逻辑
            $this->validateParentCategory($validator);
            $this->validateCategoryName($validator);
        });
    }

    /**
     * 验证父类别
     */
    private function validateParentCategory($validator): void
    {
        $parentId = $this->input('parent_id', 0);
        
        if ($parentId > 0) {
            // 检查父类别是否存在
            $parentExists = \App\Models\Evaluation\Categories::where('id', $parentId)
                ->where('status', 0)
                ->exists();
                
            if (!$parentExists) {
                $validator->errors()->add('parent_id', '指定的父类别不存在');
            }
            
            // 更新时检查是否会形成循环引用
            if ($this->isMethod('put') || $this->isMethod('patch')) {
                $currentId = $this->route('id');
                if ($currentId && $this->wouldCreateCircularReference($currentId, $parentId)) {
                    $validator->errors()->add('parent_id', '不能将类别移动到自己的子类别下');
                }
            }
        }
    }

    /**
     * 验证类别名称唯一性
     */
    private function validateCategoryName($validator): void
    {
        $categoryName = $this->input('category_name');
        $courseId = $this->input('course_id');
        $parentId = $this->input('parent_id', 0);
        
        if ($categoryName && $courseId) {
            $query = \App\Models\Evaluation\Categories::where('category_name', $categoryName)
                ->where('course_id', $courseId)
                ->where('parent_id', $parentId)
                ->where('status', 0);
                
            // 更新时排除当前记录
            if ($this->isMethod('put') || $this->isMethod('patch')) {
                $currentId = $this->route('id');
                if ($currentId) {
                    $query->where('id', '!=', $currentId);
                }
            }
            
            if ($query->exists()) {
                $validator->errors()->add('category_name', '在同一学科和父类别下，类别名称不能重复');
            }
        }
    }

    /**
     * 检查是否会形成循环引用
     */
    private function wouldCreateCircularReference(int $categoryId, int $parentId): bool
    {
        if ($parentId == 0) {
            return false;
        }

        if ($parentId == $categoryId) {
            return true;
        }

        $parent = \App\Models\Evaluation\Categories::find($parentId);
        if (!$parent) {
            return false;
        }

        return $this->wouldCreateCircularReference($categoryId, $parent->parent_id);
    }
}
