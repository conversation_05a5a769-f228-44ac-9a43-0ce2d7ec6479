<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */
namespace app\evaluation\controller;
use app\login\controller\ApiAuth;


/**
 * Class Questions
 * @package app\evaluation\controller
 */
class QuestionTypes extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->questiontypes = new \app\evaluation\service\QuestionTypes();
    }
    /**
     * 模块：素养测评-题目类型管理
     * @SWG\Post(path="/evaluation/question_types",
     *   tags={"素养测评-题目类型设置:question_types"},
     *   summary="题目类型添加",
     *  @SWG\Parameter(
     *     in="formData",
     *     name="type_name",
     *     type="string",
     *     description="题目类型名称 (例如选择题、填空题、简答题)",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="is_subjective",
     *     type="integer",
     *     description="0为主观题，1为客观题",
     *     required=true,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-题目类型管理
     * @SWG\Put(path="/evaluation/question_types/{id}",
     *   tags={"素养测评-题目类型设置:question_types"},
     *   summary="题目类型修改",
     *  @SWG\Parameter(
     *     in="path",
     *     name="id",
     *     type="integer",
     *     description="id",
     *     required=true,
     *  ),
     *   @SWG\Parameter(
     *     in="body",
     *     name="data",
     *     description="更新的数据",
     *     required=true,
     *     @SWG\Schema(
     *       type="object",
     *       @SWG\Property(property="type_name", type="string", description="题目类型名称"),
     *       @SWG\Property(property="is_subjective", type="integer", description="0为主观题，1为客观题")
     *     )
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-题目类型管理
     * @SWG\Delete(path="/evaluation/question_types/{id}",
     *   tags={"素养测评-题目类型设置:question_types"},
     *   summary="题目类型删除",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="题目类型ID",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-题目类型管理
     * @SWG\Get(path="/evaluation/question_types",
     *   tags={"素养测评-题目类型设置:question_types"},
     *   summary="题目类型查询",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    public function question_types(){
        $data = $this->questiontypes->hand_out();
        apiReturn($data);
    }

}