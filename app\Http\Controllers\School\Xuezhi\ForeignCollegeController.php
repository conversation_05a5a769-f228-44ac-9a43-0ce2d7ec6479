<?php

namespace App\Http\Controllers\School\Xuezhi;

use App\Constants\BaseConstants;
use App\Http\Controllers\Controller;
use App\Models\School\Xuezhi\ForeignCollege;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ForeignCollegeController extends Controller
{
    private const SELECT_FIELDS = [
        'id', 
        'college_name', 
        'college_name_cn', 
        'location_code', 
        'continent', 
        'usnews_rank', 
        'college_nature', 
        'college_type', 
        'founding_time', 
        'image'
    ];

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = ForeignCollege::select(self::SELECT_FIELDS)
            ->where('location_code', $this->getLocationCode($request))
            ->tap(function ($query) use ($request) {
                $this->applySearchFilters($query, $request);
            });

        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id')->get();
        
        // return $this->success(compact('list', 'cnt'));
        return $this->paginateSuccess($list, $cnt);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $data = ForeignCollege::findOrFail($id);
        return $this->success($data);
    }

    private function getLocationCode(Request $request): string
    {
        return $request['location_code'] ?: 'OTHER';
    }

    private function applySearchFilters($query, Request $request): void
    {
        $collegeName = $request['college_name'] ?? '';
        $continent = $request['continent'] ?? '';
        $isOther = $this->getLocationCode($request) === 'OTHER';

        $query->where(function ($query) use ($collegeName, $isOther, $continent) {
            if ($collegeName) {
                $query->where('college_name', 'like', "%{$collegeName}%")
                    ->orWhere('college_name_cn', 'like', "%{$collegeName}%");
            }

            if ($isOther) {
                if ($continent) {
                    $query->where('continent', $continent);
                } else {
                    $query->whereNotIn('continent', BaseConstants::FOREIGN_COLLEGE_OTHER);
                }
            }
        });
    }
}
