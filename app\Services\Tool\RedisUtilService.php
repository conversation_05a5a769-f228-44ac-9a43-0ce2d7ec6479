<?php

namespace App\Services\Tool;

use App\Services\BaseService;
use Illuminate\Support\Facades\Redis;


// Redis 公共方法
class RedisUtilService extends BaseService
{

    // 批量删除带前缀的缓存
    public function deleteCacheByPrefix($prefix)
    {
        // 获取缓存前缀（如果有的话）
        $cachePrefix = config('cache.prefix') . ':';

        // 完整前缀：缓存前缀 + 自定义前缀
        $fullPrefix = $cachePrefix . $prefix;

        $cursor = '0';  // 初始化游标
        $count = 0;     // 用于统计删除的键数量

        do {
            // 使用 SCAN 命令获取带有指定前缀的键
//            $result = Redis::scan($cursor, ['match' => $fullPrefix . '*', 'count' => 100]);
            $result = Redis::connection('cache')->scan($cursor, ['match' => $fullPrefix . '*', 'count' => 100]);

            // 检查 Redis::scan() 的返回值是否为 false，避免访问错误
            if ($result === false) {
                break; // 如果 SCAN 失败，退出循环
            }

            // SCAN 的返回格式为 [cursor, keys]，检查是否为有效数组
            if (is_array($result) && count($result) === 2) {
                $cursor = $result[0]; // 更新游标
                $keys = $result[1];   // 获取匹配到的键

                // 确保获取到的键不是空数组
                if (!empty($keys)) {
                    // 删除匹配到的所有键
                    Redis::connection('cache')->del($keys);
                    $count += count($keys);
                }
            } else {
                break; // 如果返回结果不符合预期，退出循环
            }

        } while ($cursor != 0); // 当游标为 0 时，表示遍历完成

        return '成功删除了 ' . $count . ' 个缓存项';
    }

}
