<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_career_answers', function (Blueprint $table) {
            $table->integer('assessment_task_assignment_id')->comment('测评任务指定人表id');
            $table->integer('student_id')->comment('学生ID');
            $table->integer('assessment_id')->comment('测评ID');
            $table->integer('school_id')->comment('学校ID');
            $table->integer('assessment_career_question_id')->comment('生涯测评问题表的ID');
            $table->string('answer', 255)->comment('选项内容');
            // 创建复合索引
            $table->index(['school_id','assessment_id'], 'idx_school_assessment');
        });
        DB::statement("ALTER TABLE `assessment_career_answers` COMMENT '生涯测评答题原始分区数据表'");
        // 定义 LIST 分区和子分区的 SQL 语句
        $partitionCount = 32;
        $subPartitionCount = 6; // 每个父分区中的子分区数量

        // 定义 LIST 分区和子分区的 SQL 语句
        $partitionSql = "ALTER TABLE assessment_career_answers
                         PARTITION BY LIST (MOD(school_id, $partitionCount))
                         SUBPARTITION BY KEY(assessment_id)
                         SUBPARTITIONS $subPartitionCount
                         (
        ";
        for ($i = 0; $i < $partitionCount; $i++) {
            $partitionSql .= "PARTITION p$i VALUES IN ($i),\n";
        }

        // 移除最后一个多余的逗号和换行符
                $partitionSql = rtrim($partitionSql, ",\n") . ");";
        // 执行 SQL 语句
        DB::statement($partitionSql);

    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_career_answers');
    }
};
