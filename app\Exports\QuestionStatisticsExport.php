<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class QuestionStatisticsExport implements FromCollection, WithTitle, WithHeadings, WithEvents, ShouldAutoSize
{
    protected $data;
    protected $title;
    protected $headings = [];
    protected $mergeRanges = [];

    /**
     * 构造函数
     *
     * @param array $data 导出数据
     * @param string $title 表格标题
     */
    public function __construct(array $data, string $title)
    {
        $this->data = $data;
        $this->title = $title;
        $this->processData();
    }

    /**
     * 处理数据，生成表头和合并单元格范围
     */
    protected function processData()
    {
        if (empty($this->data)) {
            $this->headings = ['题目', '平均分', '有效填写量', '百分比'];
            return;
        }

        // 基本表头
        $this->headings = ['题目', '平均分'];

        // 获取第一条数据，用于确定有多少个选项
        $firstItem = $this->data[0];
        $optionCount = 0;

        // 计算选项数量
        foreach ($firstItem as $key => $value) {
            if (strpos($key, 'option_') === 0) {
                $optionCount++;
            }
        }

        // 添加选项表头
        if ($optionCount > 0) {
            $this->headings[] = '选项';
            $this->headings[] = '小计';
            $this->headings[] = '百分比(%)';

            // 记录需要合并的单元格范围
            $this->mergeRanges[] = 'C1:E1'; // 选项、小计和百分比合并
        }

        // 添加有效填写量表头
        $this->headings[] = '有效填写量';
        $this->headings[] = '百分比(%)';

        // 记录需要合并的单元格范围
        $lastColIndex = count($this->headings);
        $this->mergeRanges[] = chr(64 + $lastColIndex - 1) . '1:' . chr(64 + $lastColIndex) . '1'; // 有效填写量和百分比合并
    }

    /**
     * 获取集合数据
     *
     * @return Collection
     */
    public function collection()
    {
        $exportData = [];

        foreach ($this->data as $index => $item) {
            // 获取所有选项键
            $optionKeys = array_filter(array_keys($item), function($key) {
                return strpos($key, 'option_') === 0;
            });

            // 排序选项键，确保按字母顺序处理
            sort($optionKeys);

            // 第一行包含题目、平均分、第一个选项及有效填写量
            if (!empty($optionKeys)) {
                $firstOptionKey = reset($optionKeys);
                $optionLetter = substr($firstOptionKey, -1);

                $rowData = [
                    'content' => $item['content'],
                    'avg_score' => $item['avg_score'],
                    'option' => $item['option_' . $optionLetter],
                    'count' => $item['count_' . $optionLetter],
                    'percent' => $item['percent_' . $optionLetter],
                    'valid_count' => $item['valid_count'],
                    'valid_percent' => $item['valid_percent'],
                ];

                $exportData[] = $rowData;

                // 处理剩余选项数据（作为额外的行）
                array_shift($optionKeys); // 移除第一个选项，因为已经处理过了

                foreach ($optionKeys as $optionKey) {
                    $optionLetter = substr($optionKey, -1);
                    $extraRow = [
                        'content' => '', // 空白，因为会合并单元格
                        'avg_score' => '', // 空白，因为会合并单元格
                        'option' => $item['option_' . $optionLetter],
                        'count' => $item['count_' . $optionLetter],
                        'percent' => $item['percent_' . $optionLetter],
                        'valid_count' => '', // 空白，因为会合并单元格
                        'valid_percent' => '', // 空白，因为会合并单元格
                    ];

                    $exportData[] = $extraRow;
                }
            } else {
                // 如果没有选项，只添加基本信息
                $rowData = [
                    'content' => $item['content'],
                    'avg_score' => $item['avg_score'],
                    'valid_count' => $item['valid_count'],
                    'valid_percent' => $item['valid_percent'],
                ];

                $exportData[] = $rowData;
            }
        }

        return new Collection($exportData);
    }

    /**
     * 获取表头
     *
     * @return array
     */
    public function headings(): array
    {
        return $this->headings;
    }

    /**
     * 获取工作表标题
     *
     * @return string
     */
    public function title(): string
    {
        return $this->title;
    }

    /**
     * 注册事件
     *
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                // 设置表头样式
                $sheet->getStyle('A1:' . $this->getLastColumn() . '1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'size' => 12,
                    ],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => 'E0E0E0',
                        ],
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                        ],
                    ],
                ]);

                // 合并单元格
                foreach ($this->mergeRanges as $range) {
                    $sheet->mergeCells($range);
                }

                // 设置所有单元格边框
                $lastRow = count($this->data) * 2 + 1; // 估算最大行数
                $sheet->getStyle('A1:' . $this->getLastColumn() . $lastRow)->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                        ],
                    ],
                ]);

                // 设置内容居中
                $sheet->getStyle('A2:' . $this->getLastColumn() . $lastRow)->applyFromArray([
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                ]);

                // 设置题目列左对齐
                $sheet->getStyle('A2:A' . $lastRow)->applyFromArray([
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_LEFT,
                    ],
                ]);

                // 合并相同题目的单元格
                $this->mergeSameQuestionCells($sheet);
            },
        ];
    }

    /**
     * 获取最后一列的字母
     *
     * @return string
     */
    protected function getLastColumn(): string
    {
        return chr(64 + count($this->headings));
    }

    /**
     * 合并相同题目的单元格
     *
     * @param \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet
     */
    protected function mergeSameQuestionCells($sheet)
    {
        $currentRow = 2;
        $questionStartRow = 2;
        $currentQuestion = null;

        foreach ($this->data as $item) {
            // 计算当前题目有多少个选项
            $optionCount = 0;
            foreach (array_keys($item) as $key) {
                if (strpos($key, 'option_') === 0) {
                    $optionCount++;
                }
            }

            // 如果是新题目，且不是第一个题目，则合并前一个题目的单元格
            if ($currentQuestion !== null && $currentQuestion !== $item['content']) {
                $endRow = $currentRow - 1;
                if ($questionStartRow < $endRow) {
                    $sheet->mergeCells("A{$questionStartRow}:A{$endRow}");
                    $sheet->mergeCells("B{$questionStartRow}:B{$endRow}");
                    $sheet->mergeCells($this->getLastColumn() . $questionStartRow . ':' . $this->getLastColumn() . $endRow);
                    $sheet->mergeCells(chr(64 + count($this->headings) - 1) . $questionStartRow . ':' . chr(64 + count($this->headings) - 1) . $endRow);
                }
                $questionStartRow = $currentRow;
            }

            $currentQuestion = $item['content'];
            $currentRow += $optionCount;
        }

        // 处理最后一个题目
        $endRow = $currentRow - 1;
        if ($questionStartRow < $endRow) {
            $sheet->mergeCells("A{$questionStartRow}:A{$endRow}");
            $sheet->mergeCells("B{$questionStartRow}:B{$endRow}");
            $sheet->mergeCells($this->getLastColumn() . $questionStartRow . ':' . $this->getLastColumn() . $endRow);
            $sheet->mergeCells(chr(64 + count($this->headings) - 1) . $questionStartRow . ':' . chr(64 + count($this->headings) - 1) . $endRow);
        }
    }
}
