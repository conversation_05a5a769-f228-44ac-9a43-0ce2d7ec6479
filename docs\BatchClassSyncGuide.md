# 批量班级数据同步功能指南

## 概述

批量班级数据同步功能已实现，与单条新增逻辑完全一致。支持批量创建班级时自动同步到目标数据库。

## 功能特性

### 🎯 **核心功能**
1. **批量班级同步** - 支持一次性同步多个班级
2. **年级自动创建** - 为每个班级自动创建或获取对应年级
3. **智能去重** - 相同学校、校区、年级的年级记录会复用
4. **错误隔离** - 单个班级同步失败不影响其他班级
5. **详细统计** - 提供成功和失败的详细统计信息

### 📊 **同步流程**

#### **批量同步流程**
1. **接收批量班级数据** - 从 `ClassService::batchStore()` 获取创建的班级数组
2. **逐个处理班级** - 对每个班级执行单条同步逻辑
3. **年级处理** - 为每个班级创建或获取对应年级
4. **班级同步** - 将班级数据同步到 `ysy_class` 表
5. **统计结果** - 记录成功和失败的数量
6. **返回结果** - 返回批量同步的整体结果

#### **单个班级处理逻辑**
1. **检查班级是否存在** - 通过名字+学校+校区查询
2. **年级处理** - 根据班级年级创建或获取年级记录
3. **插入或更新** - 班级不存在则插入，存在则更新
4. **记录日志** - 记录每个班级的同步结果

## 使用方法

### **1. 批量班级创建接口**
```php
POST /school/class/batch
Content-Type: application/json

{
    "classes": [
        {
            "class_name": "高一(1)班",
            "grade": 10,
            "school_id": 1051,
            "school_campus_id": 2515
        },
        {
            "class_name": "高一(2)班", 
            "grade": 10,
            "school_id": 1051,
            "school_campus_id": 2515
        },
        {
            "class_name": "高二(1)班",
            "grade": 11,
            "school_id": 1051,
            "school_campus_id": 2515
        }
    ]
}
```

### **2. 代码调用示例**
```php
// 在 ClassController::batchStore 中自动触发
$classes = $this->classService->batchStore($request);

// 数据同步调用
if (config('datasync.enabled', false)) {
    $this->syncBatchClassData($classes, $request);
}
```

## 实现细节

### **ClassController::syncBatchClassData 方法**
```php
private function syncBatchClassData($classes, $request)
{
    try {
        $syncHelper = new DataSyncHelper();

        // 准备班级数据数组
        $classesData = collect($classes)->map(function ($class) {
            return is_array($class) ? $class : $class->toArray();
        })->toArray();

        // 调用批量班级同步
        $syncResult = $syncHelper->batchSyncClasses($classesData, $request->all());

        Log::info('批量班级数据同步结果', [
            'class_count' => count($classes),
            'success' => $syncResult['success'] ?? false,
            'message' => $syncResult['message'] ?? ''
        ]);

    } catch (\Exception $e) {
        Log::error('批量班级数据同步失败', [
            'class_count' => count($classes),
            'error' => $e->getMessage()
        ]);
    }
}
```

### **DataSyncHelper::batchSyncClasses 方法**
```php
public function batchSyncClasses(array $classesData, array $additionalData = []): array
{
    try {
        $successCount = 0;
        $failCount = 0;

        foreach ($classesData as $classData) {
            $result = $this->syncClass($classData, $additionalData);
            if ($result['success']) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        return [
            'success' => true,
            'message' => "批量班级数据同步完成",
            'data' => [
                'total' => count($classesData),
                'success_count' => $successCount,
                'fail_count' => $failCount
            ]
        ];

    } catch (\Exception $e) {
        Log::error('批量班级数据同步失败', [
            'error' => $e->getMessage()
        ]);

        return [
            'success' => false,
            'message' => '批量班级数据同步失败: ' . $e->getMessage()
        ];
    }
}
```

## 同步示例

### **原始批量班级数据**
```json
[
    {
        "id": 2001,
        "class_name": "高一(1)班",
        "grade": 10,
        "school_id": 1051,
        "school_campus_id": 2515
    },
    {
        "id": 2002,
        "class_name": "高一(2)班",
        "grade": 10,
        "school_id": 1051,
        "school_campus_id": 2515
    },
    {
        "id": 2003,
        "class_name": "高二(1)班",
        "grade": 11,
        "school_id": 1051,
        "school_campus_id": 2515
    }
]
```

### **同步后的年级数据（ysy_grade）**
```json
[
    {
        "id": 501,
        "name": "2025",
        "grade_name": "高一",
        "grade_sort": 10,
        "school_id": 1051,
        "school_district": 2515,
        "step": 0
    },
    {
        "id": 502,
        "name": "2024",
        "grade_name": "高二", 
        "grade_sort": 11,
        "school_id": 1051,
        "school_district": 2515,
        "step": 0
    }
]
```

### **同步后的班级数据（ysy_class）**
```json
[
    {
        "id": 1001,
        "name": "高一(1)班",
        "school_id": 1051,
        "school_district": 2515,
        "grade_id": 501,
        "step": 0
    },
    {
        "id": 1002,
        "name": "高一(2)班",
        "school_id": 1051,
        "school_district": 2515,
        "grade_id": 501,
        "step": 0
    },
    {
        "id": 1003,
        "name": "高二(1)班",
        "school_id": 1051,
        "school_district": 2515,
        "grade_id": 502,
        "step": 0
    }
]
```

### **批量同步结果**
```json
{
    "success": true,
    "message": "批量班级数据同步完成",
    "data": {
        "total": 3,
        "success_count": 3,
        "fail_count": 0
    }
}
```

## 日志监控

### **查看批量同步日志**
```bash
tail -f storage/logs/laravel.log | grep "批量班级数据同步"
```

### **查看单个班级同步日志**
```bash
tail -f storage/logs/laravel.log | grep "班级数据"
```

### **查看年级创建日志**
```bash
tail -f storage/logs/laravel.log | grep "年级"
```

## 优势特点

1. **🔄 复用单条逻辑** - 批量同步复用单条同步的所有逻辑
2. **📊 详细统计** - 提供成功和失败的详细统计
3. **🛡️ 错误隔离** - 单个失败不影响其他班级同步
4. **🎯 智能年级处理** - 相同年级会复用，避免重复创建
5. **📝 完整日志** - 记录每个班级和整体的同步情况
6. **⚡ 高效处理** - 批量处理但保持单条的准确性

通过这个批量班级同步功能，您可以高效地处理大量班级数据的同步需求！🚀
