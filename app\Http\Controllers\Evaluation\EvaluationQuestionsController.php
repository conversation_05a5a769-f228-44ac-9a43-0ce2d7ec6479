<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use App\Services\Evaluation\QuestionsService;
use App\Http\Requests\Evaluation\QuestionRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * 素养测评-素养题库管理控制器
 */
class EvaluationQuestionsController extends Controller
{
    protected $questionsService;

    public function __construct(QuestionsService $questionsService)
    {
        $this->questionsService = $questionsService;
    }

    /**
     * 试题查询
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function questions(Request $request): JsonResponse
    {
        try {
            $data = $this->questionsService->getQuestionsList($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取题目列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 试题添加
     * 
     * @param QuestionRequest $request
     * @return JsonResponse
     */
    public function store(QuestionRequest $request): JsonResponse
    {
        try {
            $data = $this->questionsService->createQuestion($request->validated());
            return $this->success($data, '题目添加成功');
        } catch (\Exception $e) {
            return $this->error('题目添加失败: ' . $e->getMessage());
        }
    }

    /**
     * 试题修改
     * 
     * @param QuestionRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(QuestionRequest $request, int $id): JsonResponse
    {
        try {
            $data = $this->questionsService->updateQuestion($id, $request->validated());
            return $this->success($data, '题目修改成功');
        } catch (\Exception $e) {
            return $this->error('题目修改失败: ' . $e->getMessage());
        }
    }

    /**
     * 试题删除
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->questionsService->deleteQuestion($id);
            return $this->success(null, '题目删除成功');
        } catch (\Exception $e) {
            return $this->error('题目删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 上传图片
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function uploads(Request $request): JsonResponse
    {
        try {
            $data = $this->questionsService->uploadImage($request);
            return $this->success($data, '图片上传成功');
        } catch (\Exception $e) {
            return $this->error('图片上传失败: ' . $e->getMessage());
        }
    }

    /**
     * 修改子题排序
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function sort(Request $request): JsonResponse
    {
        try {
            $data = $this->questionsService->updateSort($request->all());
            return $this->success($data, '排序修改成功');
        } catch (\Exception $e) {
            return $this->error('排序修改失败: ' . $e->getMessage());
        }
    }
}
