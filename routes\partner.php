<?php

use App\Http\Controllers\Partner\PartnerController;
use App\Http\Controllers\Partner\PartnerSchoolController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// 教育局、代理端相关模块路由
Route::group(['prefix' => 'partner', 'middleware' => ['auth.refresh']], function () {

    // 教育局详情
    Route::get('detail', [PartnerController::class, 'show'])->name('partner.detail');
    // 更新教育局信息
    Route::put('update', [PartnerController::class, 'update'])->name('partner.update');
    // 设置教育局配置信息
    Route::post('set_config', [PartnerController::class, 'setConfig'])->name('partner.set_config');
    // 获取当前机构下的学校列表
    Route::get('has_schools', [PartnerSchoolController::class, 'index'])->name('partner.has_schools');
    // 设置当前机构下的学校列表
    Route::post('set_has_schools', [PartnerSchoolController::class, 'setHasSchools'])->name('partner.set_has_schools');


});
