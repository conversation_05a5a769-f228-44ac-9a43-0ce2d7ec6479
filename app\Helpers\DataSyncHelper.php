<?php

namespace App\Helpers;

use App\Services\DataSync\DataSyncService;
use App\Events\DataSync\StudentCreated;
use App\Events\DataSync\TeacherCreated;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

/**
 * 数据同步辅助类
 * 用于在数据添加完成后调用同步接口
 */
class DataSyncHelper
{
    protected $dataSyncService;
    protected $baseUrl;

    public function __construct()
    {
        $this->dataSyncService = app(DataSyncService::class);
        $this->baseUrl = config('app.url') . '/admin/datasync';
    }

    /**
     * 同步学校数据（直接同步）
     *
     * @param array $schoolData 学校数据
     * @param array $requestData 请求数据（包含额外字段）
     * @return array
     */
    public function syncSchool(array $schoolData, array $requestData = []): array
    {
        try {
            // 处理额外数据
            $additionalData = $this->prepareSchoolAdditionalData($schoolData, $requestData);

            // 准备学校基础数据
            $syncData = array_merge($schoolData, $additionalData);

            // 直接调用同步服务（不通过HTTP接口）
            $result = $this->dataSyncService->syncSchool($syncData);

            if ($result['success']) {
                Log::info('学校数据同步成功', [
                    'school_id' => $schoolData['id'],
                    'sync_school_id' => $result['sync_school_id'] ?? null
                ]);
                return $result;
            } else {
                throw new \Exception('学校数据同步失败: ' . $result['message']);
            }
            
        } catch (\Exception $e) {
            Log::error('学校数据同步失败', [
                'school_data' => $schoolData,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '学校数据同步失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 准备学校额外数据
     *
     * @param array $schoolData 学校基础数据
     * @param array $requestData 请求数据
     * @return array
     */
    private function prepareSchoolAdditionalData(array $schoolData, array $requestData): array
    {
        return [
            'id' => $schoolData['id'] ?? null,
            'add_time' => $requestData['add_time'] ?? now()->format('Y-m-d H:i:s'),
            'name' => $requestData['name'] ?? $schoolData['name'] ?? '',
            'date_due' => $requestData['date_due'] ?? null,
            'province' => $requestData['province'] ?? '',
            'city' => $requestData['city'] ?? '',
            'district' => $requestData['district'] ?? '',
            'address' => $requestData['address'] ?? '',
            'buy_modules' => $requestData['buy_modules'] ?? '01,02,03,04,06,07,08,09,10,11,12,13,14,16,120,21,22,23,24,25,26,27,28,29,30,2802',
            'location' => $requestData['location'] ?? $requestData['province'] ?? '',
        ];
    }

    /**
     * 同步校区数据（直接同步）
     * 
     * @param array $campusData 校区数据
     * @return array
     */
    public function syncCampus(array $campusData): array
    {
        try {
            // 校区数据通常在学校同步时一起处理
            // 这里可以单独处理校区更新的情况
            Log::info('校区数据已在学校同步中处理', ['campus_data' => $campusData]);
            
            return [
                'success' => true,
                'message' => '校区数据同步成功'
            ];
            
        } catch (\Exception $e) {
            Log::error('校区数据同步失败', [
                'campus_data' => $campusData,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '校区数据同步失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 同步年级数据（直接同步）
     * 
     * @param array $gradeData 年级数据
     * @return array
     */
    public function syncGrade(array $gradeData): array
    {
        try {
            // 年级数据可以直接同步到目标数据库
            $result = $this->dataSyncService->syncGrade($gradeData);
            
            Log::info('年级数据同步成功', [
                'grade_id' => $gradeData['id'] ?? 'unknown',
                'result' => $result
            ]);
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('年级数据同步失败', [
                'grade_data' => $gradeData,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '年级数据同步失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 同步班级数据（直接同步）
     *
     * @param array $classData 班级数据
     * @param array $additionalData 额外数据
     * @param bool $isUpdate 是否为更新操作
     * @return array
     */
    public function syncClass(array $classData, array $additionalData = [], bool $isUpdate = false): array
    {
        try {
            // 准备完整的班级数据
            $syncData = array_merge($classData, $additionalData);

            // 直接调用同步服务
            $result = $this->dataSyncService->syncClass($syncData, $isUpdate);

            if ($result['success']) {
                Log::info('班级数据同步成功', [
                    'class_id' => $classData['id'],
                    'sync_class_id' => $result['sync_class_id'] ?? null
                ]);
                return $result;
            } else {
                throw new \Exception('班级数据同步失败: ' . $result['message']);
            }

        } catch (\Exception $e) {
            Log::error('班级数据同步失败', [
                'class_data' => $classData,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '班级数据同步失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 异步同步学生数据
     * 
     * @param array $studentData 学生数据
     * @return array
     */
    public function asyncSyncStudent(array $studentData): array
    {
        try {
            // 使用队列异步处理学生数据同步
            Queue::push(function ($job) use ($studentData) {
                try {
                    // 触发学生同步事件
                    event(new StudentCreated($studentData));
                    $job->delete();
                } catch (\Exception $e) {
                    Log::error('异步同步学生数据失败', [
                        'student_data' => $studentData,
                        'error' => $e->getMessage()
                    ]);
                    $job->delete();
                }
            });
            
            Log::info('学生数据已加入异步同步队列', [
                'student_id' => $studentData['id'] ?? 'unknown'
            ]);
            
            return [
                'success' => true,
                'message' => '学生数据已加入异步同步队列'
            ];
            
        } catch (\Exception $e) {
            Log::error('学生数据异步同步失败', [
                'student_data' => $studentData,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '学生数据异步同步失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 异步同步教师数据
     * 
     * @param array $teacherData 教师数据
     * @return array
     */
    public function asyncSyncTeacher(array $teacherData): array
    {
        try {
            // 使用队列异步处理教师数据同步
            Queue::push(function ($job) use ($teacherData) {
                try {
                    // 触发教师同步事件
                    event(new TeacherCreated($teacherData));
                    $job->delete();
                } catch (\Exception $e) {
                    Log::error('异步同步教师数据失败', [
                        'teacher_data' => $teacherData,
                        'error' => $e->getMessage()
                    ]);
                    $job->delete();
                }
            });
            
            Log::info('教师数据已加入异步同步队列', [
                'teacher_id' => $teacherData['id'] ?? 'unknown'
            ]);
            
            return [
                'success' => true,
                'message' => '教师数据已加入异步同步队列'
            ];
            
        } catch (\Exception $e) {
            Log::error('教师数据异步同步失败', [
                'teacher_data' => $teacherData,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '教师数据异步同步失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 批量异步同步学生数据
     * 
     * @param array $studentsData 学生数据数组
     * @return array
     */
    public function batchAsyncSyncStudents(array $studentsData): array
    {
        try {
            $successCount = 0;
            $failCount = 0;
            
            foreach ($studentsData as $studentData) {
                $result = $this->asyncSyncStudent($studentData);
                if ($result['success']) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }
            
            return [
                'success' => true,
                'message' => "批量异步同步学生数据完成",
                'data' => [
                    'total' => count($studentsData),
                    'success_count' => $successCount,
                    'fail_count' => $failCount
                ]
            ];
            
        } catch (\Exception $e) {
            Log::error('批量异步同步学生数据失败', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '批量异步同步学生数据失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 批量异步同步教师数据
     *
     * @param array $teachersData 教师数据数组
     * @return array
     */
    public function batchAsyncSyncTeachers(array $teachersData): array
    {
        try {
            $successCount = 0;
            $failCount = 0;

            foreach ($teachersData as $teacherData) {
                $result = $this->asyncSyncTeacher($teacherData);
                if ($result['success']) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }

            return [
                'success' => true,
                'message' => "批量异步同步教师数据完成",
                'data' => [
                    'total' => count($teachersData),
                    'success_count' => $successCount,
                    'fail_count' => $failCount
                ]
            ];

        } catch (\Exception $e) {
            Log::error('批量异步同步教师数据失败', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '批量异步同步教师数据失败: ' . $e->getMessage()
            ];
        }
    }



    /**
     * 批量同步班级数据
     *
     * @param array $classesData 班级数据数组
     * @param array $additionalData 额外数据
     * @return array
     */
    public function batchSyncClasses(array $classesData, array $additionalData = []): array
    {
        try {
            $successCount = 0;
            $failCount = 0;

            foreach ($classesData as $classData) {
                $result = $this->syncClass($classData, $additionalData);
                if ($result['success']) {
                    $successCount++;
                } else {
                    $failCount++;
                }
            }

            return [
                'success' => true,
                'message' => "批量班级数据同步完成",
                'data' => [
                    'total' => count($classesData),
                    'success_count' => $successCount,
                    'fail_count' => $failCount
                ]
            ];

        } catch (\Exception $e) {
            Log::error('批量班级数据同步失败', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '批量班级数据同步失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取校区数据
     * 
     * @param int $schoolId
     * @return array
     */
    private function getCampusData(int $schoolId): array
    {
        try {
            return \DB::table('school_campuses')
                ->where('school_id', $schoolId)
                ->where('status', 1)
                ->select('id', 'campus_name as name', 'school_id', 'type', 'is_main', 'status')
                ->get()
                ->map(function ($campus) {
                    return [
                        'id' => $campus->id,
                        'name' => $campus->name,
                        'code' => 'CAMPUS_' . $campus->id,
                        'school_id' => $campus->school_id,
                        'type' => $campus->type,
                        'is_main' => $campus->is_main,
                        'status' => $campus->status,
                        'address' => '',
                        'phone' => '',
                    ];
                })
                ->toArray();
        } catch (\Exception $e) {
            Log::warning('获取校区数据失败', [
                'school_id' => $schoolId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 获取默认管理员用户信息
     * 
     * @param array $schoolData
     * @return array
     */
    private function getDefaultAdminUser(array $schoolData): array
    {
        $schoolName = $schoolData['name'] ?? '学校';
        $schoolCode = $schoolData['code'] ?? '';
        
        return [
            'name' => $schoolName . '管理员',
            'username' => 'admin_' . ($schoolCode ?: 'school_' . time()),
            'password' => bcrypt('123456'), // 默认密码
            'phone' => $schoolData['phone'] ?? '',
            'email' => $schoolData['email'] ?? '',
            'role' => 'admin',
            'status' => 1
        ];
    }
}
