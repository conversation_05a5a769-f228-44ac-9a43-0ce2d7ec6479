<?php

namespace App\Models\School\Assessment;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssessmentComprehensiveRecommendMajor extends Model
{
    use SoftDeletes;

    protected $table = 'assessment_comprehensive_recommend_majors';

    protected $fillable = [
        'user_id',
        'majors',
    ];

    protected $casts = [
        'majors' => 'json',
    ];
}