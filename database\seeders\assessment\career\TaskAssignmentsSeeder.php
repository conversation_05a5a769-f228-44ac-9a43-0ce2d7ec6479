<?php

namespace Database\Seeders\assessment\career;

use Database\Seeders\BaseIncrementalSeeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

ini_set('memory_limit', '2048M');

class TaskAssignmentsSeeder extends BaseIncrementalSeeder
{
    protected string $assessment_type = 'career';

    private const SURVEYIDTOASSESSMENTID = [
        1  => 1,
        18 => 2,
        21 => 3,
        28 => 4,
        45 => 5,
        32 => 6,
        53 => 7,
        72 => 8
    ];

    public function __construct($schoolId)
    {
        parent::__construct($schoolId);
    }
    protected function getSurveyIds(): array
    {
        return [1, 18, 21, 28, 45, 32, 53, 72];
    }

    protected function getAssessmentIds(): array
    {
        return [1, 2, 3, 4, 5, 6, 7, 8];
    }

    protected function executeSeeder(): void
    {
        $lastProcessedId = $this->getLastProcessedId();

        // 构建增量查询（只处理当前学校且ID大于最后处理ID的记录）
        $assessmentIds = $this->getAssessmentIds();
        $surveyIds = $this->getSurveyIds();

        $studentSurveyList = DB::connection($this->connect)
            ->table('assessment_schedules as schedules')
            ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
            ->join('survey_user_session as session', function ($join) {
                $join->on('session.survey_id', '=', 'tasks.old_survey_id')
                    ->on('session.grade_id', '=', 'tasks.old_grade_id')
                    ->on('session.times', '=', 'tasks.old_times');
            })
            ->where('schedules.school_id', $this->school_id) // 只处理当前学校
            ->whereIn('tasks.assessment_id', $assessmentIds)
            ->whereIn('session.survey_id', $surveyIds)
            ->where('session.is_delete', 0)
            ->where('session.is_abnormal', 0)
            ->where('session.time_error', 0)
            ->where('session.result', '!=', '')
            ->where('session.id', '>', $lastProcessedId) // 只处理新记录
            ->select([
                'tasks.id as task_id',
                'tasks.assessment_id',
                'session.session_id',
                'session.member_id as student_member_id',
                'session.student_id',
                'session.create_time',
                'session.used_time',
                'session.result',
                'session.survey_id',
                'session.pdf_url',
                'session.id as survey_session_id'
            ])->get();

        // 打印 SQL 语句
        $oldStudentIds = $studentSurveyList->pluck('student_id')->unique()->values()->toArray();
        $studentClassIdArr = DB::table('student_classes')
            ->whereIn('old_student_id', $oldStudentIds)
            ->select(['id', 'old_student_id'])
            ->pluck('id', 'old_student_id')
            ->toArray();

        // 使用集合方法优化数据处理
        $surveyToAssessmentMap = self::SURVEYIDTOASSESSMENTID; // 将常量赋值给变量
        $assignmentsData = $studentSurveyList->map(function($item) use ($studentClassIdArr, $surveyToAssessmentMap) {
            return [
                'old_session_id' => $item->session_id,
                'old_student_id' => $item->student_id,
                'school_id' => $this->school_id,
                'assessment_task_id' => $item->task_id,
                'student_class_id' => $studentClassIdArr[$item->student_id] ?? 0,
                'student_id' => $item->student_member_id,
                'user_id' => $item->student_member_id,
                'duration' => $item->used_time,
                'results' => $item->result,
                'status' => 2,
                'assessment_id' => $surveyToAssessmentMap[$item->survey_id],
                'pdf_url' => $item->pdf_url,
                'created_at' => $item->create_time,
            ];
        })->toArray();
        // 使用事务和 chunk 插入大量数据
        if (isset($assignmentsData) && is_array($assignmentsData) && !empty($assignmentsData)) {
            DB::transaction(function () use ($assignmentsData) {
                collect($assignmentsData)->chunk(1000)->each(function ($chunk) {
                    DB::table('assessment_task_assignments')->insert($chunk->toArray());
                });
            });

            // 获取最大的 survey_session_id 用于更新执行日志
            $maxSurveySessionId = $studentSurveyList->max('survey_session_id');
            $this->updateExecutionLog($maxSurveySessionId, count($assignmentsData));

            // 使用更好的日志记录方式
            Log::info('career assignments seeding completed', [
                'school_id' => $this->school_id,
                'career_total_records' => count($assignmentsData),
                'career_last_task_id' => end($assignmentsData)['assessment_task_id'] ?? null,
                'last_processed_survey_session_id' => $maxSurveySessionId,
            ]);
        } else {
            Log::info('没有新的career assignments数据需要处理', [
                'school_id' => $this->school_id,
                'last_processed_id' => $lastProcessedId
            ]);
        }
    }
}
