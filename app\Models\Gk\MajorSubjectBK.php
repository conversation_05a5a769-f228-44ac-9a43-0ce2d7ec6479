<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MajorSubjectBK extends BaseModel
{
    use HasFactory;

    protected $table = 'MajorSubject_BK';

    // 指定连接
    protected $connection = 'sqlsrv_gk';

    //主键
    protected $primaryKey = 'Id';

    // 隐藏字段
    protected $hidden = [];
    // 是否使用时间戳
    public $timestamps = false;

    // 可批量赋值的字段
    protected $fillable = [
        'SubjectCode',
        'SubjectName',
    ];

    // 与 MajorBK 的一对多关系
    public function majors()
    {
        return $this->hasMany(MajorBK::class, 'MajorSubjectID', 'ID');
    }

    // 与 MajorCategoryBK 的一对多关系
    public function categories()
    {
        return $this->hasMany(MajorCategoryBK::class, 'MajorSubjectID', 'ID');
    }

    /**
     * 获取与当前学科相关的 MajorAssess 记录
     */
    public function majorAssesses()
    {
        return MajorAssess::where('MajorCode', 'like', $this->SubjectCode . '%')->get();
    }

}
