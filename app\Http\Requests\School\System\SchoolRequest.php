<?php

namespace App\Http\Requests\School\System;

use App\Http\Requests\BaseRequest;

class SchoolRequest extends BaseRequest
{

    public function rules(): array
    {
        $rules = [
            'school_id' => 'required|integer',
        ];

        return $rules;
    }

    public function messages(): array
    {
        return [
            'school_id.integer' => 'school_id 必须为整数',
            'school_campus_id.required' => 'school_id不能为空'
        ];
    }
}
