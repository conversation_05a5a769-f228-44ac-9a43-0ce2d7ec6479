<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DataSync\DataSyncService;
use App\Helpers\DataSyncHelper;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TestDataSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:datasync {--type=school}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试数据同步功能';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $type = $this->option('type');
        
        $this->info("开始测试数据同步功能 - 类型: {$type}");
        
        // 1. 测试配置
        $this->testConfig();
        
        // 2. 测试数据库连接
        $this->testDatabaseConnection();
        
        // 3. 测试学校同步
        if ($type === 'school') {
            $this->testSchoolSync();
        }
        
        return 0;
    }
    
    /**
     * 测试配置
     */
    private function testConfig()
    {
        $this->info("\n=== 测试配置 ===");
        
        $enabled = config('datasync.enabled');
        $this->info("数据同步开关: " . ($enabled ? '启用' : '禁用'));
        
        $connection = config('datasync.sync_connection');
        $this->info("同步数据库连接: {$connection}");
        
        $host = config('database.connections.sync_mysql.host');
        $database = config('database.connections.sync_mysql.database');
        $this->info("同步数据库: {$host}/{$database}");
    }
    
    /**
     * 测试数据库连接
     */
    private function testDatabaseConnection()
    {
        $this->info("\n=== 测试数据库连接 ===");
        
        try {
            // 测试主数据库连接
            $mainResult = DB::connection('mysql')->select('SELECT 1 as test');
            $this->info("✅ 主数据库连接正常");
            
            // 测试同步数据库连接
            $syncResult = DB::connection('sync_mysql')->select('SELECT 1 as test');
            $this->info("✅ 同步数据库连接正常");
            
            // 检查同步数据库表是否存在
            $schoolTables = DB::connection('sync_mysql')->select("SHOW TABLES LIKE 'ysy_school'");
            if (empty($schoolTables)) {
                $this->warn("⚠️  同步数据库中不存在 ysy_school 表");
                $this->createSyncSchoolTable();
            } else {
                $this->info("✅ ysy_school 表存在");
            }

            // 检查校区表是否存在
            $districtTables = DB::connection('sync_mysql')->select("SHOW TABLES LIKE 'ysy_school_district'");
            if (empty($districtTables)) {
                $this->warn("⚠️  同步数据库中不存在 ysy_school_district 表");
                $this->createSyncDistrictTable();
            } else {
                $this->info("✅ ysy_school_district 表存在");
            }

            // 检查角色表是否存在
            $roleTables = DB::connection('sync_mysql')->select("SHOW TABLES LIKE 'ysy_role'");
            if (empty($roleTables)) {
                $this->warn("⚠️  同步数据库中不存在 ysy_role 表");
                $this->createSyncRoleTable();
            } else {
                $this->info("✅ ysy_role 表存在");
            }

            // 检查角色权限表是否存在
            $rolePrivilegeTables = DB::connection('sync_mysql')->select("SHOW TABLES LIKE 'ysy_role_privilege'");
            if (empty($rolePrivilegeTables)) {
                $this->warn("⚠️  同步数据库中不存在 ysy_role_privilege 表");
                $this->createSyncRolePrivilegeTable();
            } else {
                $this->info("✅ ysy_role_privilege 表存在");
            }

            // 检查年级表是否存在
            $gradeTables = DB::connection('sync_mysql')->select("SHOW TABLES LIKE 'ysy_grade'");
            if (empty($gradeTables)) {
                $this->warn("⚠️  同步数据库中不存在 ysy_grade 表");
                $this->createSyncGradeTable();
            } else {
                $this->info("✅ ysy_grade 表存在");
            }

            // 检查班级表是否存在
            $classTables = DB::connection('sync_mysql')->select("SHOW TABLES LIKE 'ysy_class'");
            if (empty($classTables)) {
                $this->warn("⚠️  同步数据库中不存在 ysy_class 表");
                $this->createSyncClassTable();
            } else {
                $this->info("✅ ysy_class 表存在");

                // 检查表结构
                try {
                    $columns = DB::connection('sync_mysql')->select("DESCRIBE ysy_class");
                    $this->info("ysy_class 表结构:");
                    foreach ($columns as $column) {
                        $this->info("  {$column->Field} - {$column->Type} - {$column->Key}");
                    }
                } catch (\Exception $e) {
                    $this->warn("无法获取表结构: " . $e->getMessage());
                }
            }
            
        } catch (\Exception $e) {
            $this->error("❌ 数据库连接失败: " . $e->getMessage());
        }
    }
    
    /**
     * 创建学校同步表
     */
    private function createSyncSchoolTable()
    {
        $this->info("正在创建 ysy_school 表...");

        try {
            DB::connection('sync_mysql')->statement("
                CREATE TABLE IF NOT EXISTS `ysy_school` (
                    `id` int(11) NOT NULL,
                    `name` varchar(255) DEFAULT NULL COMMENT '学校名称',
                    `code` varchar(100) DEFAULT NULL COMMENT '学校代码',
                    `phone` varchar(20) DEFAULT NULL COMMENT '电话',
                    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
                    `address` varchar(500) DEFAULT NULL COMMENT '地址',
                    `status` tinyint(4) DEFAULT 1 COMMENT '状态',
                    `province` varchar(50) DEFAULT NULL COMMENT '省份',
                    `city` varchar(50) DEFAULT NULL COMMENT '城市',
                    `district` varchar(50) DEFAULT NULL COMMENT '区县',
                    `add_time` varchar(50) DEFAULT NULL COMMENT '添加时间',
                    `date_due` varchar(50) DEFAULT NULL COMMENT '到期时间',
                    `buy_modules` text DEFAULT NULL COMMENT '购买模块',
                    `location` varchar(100) DEFAULT NULL COMMENT '位置',
                    `original_id` int(11) DEFAULT NULL COMMENT '原始ID',
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    KEY `idx_original_id` (`original_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步学校表'
            ");

            $this->info("✅ ysy_school 表创建成功");

        } catch (\Exception $e) {
            $this->error("❌ 创建 ysy_school 表失败: " . $e->getMessage());
        }
    }

    /**
     * 创建校区同步表
     */
    private function createSyncDistrictTable()
    {
        $this->info("正在创建 ysy_school_district 表...");

        try {
            DB::connection('sync_mysql')->statement("
                CREATE TABLE IF NOT EXISTS `ysy_school_district` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `campus_name` varchar(255) DEFAULT NULL COMMENT '校区名称',
                    `school_id` int(11) DEFAULT NULL COMMENT '学校ID',
                    `school_type` tinyint(4) DEFAULT 3 COMMENT '学校类型',
                    PRIMARY KEY (`id`),
                    KEY `idx_school_id` (`school_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学校校区表'
            ");

            $this->info("✅ ysy_school_district 表创建成功");

        } catch (\Exception $e) {
            $this->error("❌ 创建 ysy_school_district 表失败: " . $e->getMessage());
        }
    }

    /**
     * 创建角色同步表
     */
    private function createSyncRoleTable()
    {
        $this->info("正在创建 ysy_role 表...");

        try {
            DB::connection('sync_mysql')->statement("
                CREATE TABLE IF NOT EXISTS `ysy_role` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) DEFAULT NULL COMMENT '角色名称',
                    `school_id` int(11) DEFAULT NULL COMMENT '学校ID',
                    `type` tinyint(4) DEFAULT NULL COMMENT '角色类型：1学生，2教务，3老师',
                    PRIMARY KEY (`id`),
                    KEY `idx_school_id` (`school_id`),
                    KEY `idx_type` (`type`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表'
            ");

            $this->info("✅ ysy_role 表创建成功");

        } catch (\Exception $e) {
            $this->error("❌ 创建 ysy_role 表失败: " . $e->getMessage());
        }
    }

    /**
     * 创建角色权限同步表
     */
    private function createSyncRolePrivilegeTable()
    {
        $this->info("正在创建 ysy_role_privilege 表...");

        try {
            DB::connection('sync_mysql')->statement("
                CREATE TABLE IF NOT EXISTS `ysy_role_privilege` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `role_id` int(11) DEFAULT NULL COMMENT '角色ID',
                    `menu_authority` text DEFAULT NULL COMMENT '菜单权限',
                    `exclusion_authority` varchar(255) DEFAULT NULL COMMENT '排除权限',
                    PRIMARY KEY (`id`),
                    KEY `idx_role_id` (`role_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限表'
            ");

            $this->info("✅ ysy_role_privilege 表创建成功");

        } catch (\Exception $e) {
            $this->error("❌ 创建 ysy_role_privilege 表失败: " . $e->getMessage());
        }
    }

    /**
     * 创建年级同步表
     */
    private function createSyncGradeTable()
    {
        $this->info("正在创建 ysy_grade 表...");

        try {
            DB::connection('sync_mysql')->statement("
                CREATE TABLE IF NOT EXISTS `ysy_grade` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) DEFAULT NULL COMMENT '年份',
                    `grade_name` varchar(255) DEFAULT NULL COMMENT '年级名称',
                    `grade_sort` int(11) DEFAULT NULL COMMENT '年级排序',
                    `school_id` int(11) DEFAULT NULL COMMENT '学校ID',
                    `school_district` int(11) DEFAULT NULL COMMENT '校区ID',
                    `step` tinyint(4) DEFAULT 0 COMMENT '步骤',
                    PRIMARY KEY (`id`),
                    KEY `idx_school_id` (`school_id`),
                    KEY `idx_school_district` (`school_district`),
                    KEY `idx_grade_sort` (`grade_sort`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='年级表'
            ");

            $this->info("✅ ysy_grade 表创建成功");

        } catch (\Exception $e) {
            $this->error("❌ 创建 ysy_grade 表失败: " . $e->getMessage());
        }
    }

    /**
     * 创建班级同步表
     */
    private function createSyncClassTable()
    {
        $this->info("正在创建 ysy_class 表...");

        try {
            DB::connection('sync_mysql')->statement("
                CREATE TABLE IF NOT EXISTS `ysy_class` (
                    `id` int(11) NOT NULL,
                    `name` varchar(255) DEFAULT NULL COMMENT '班级名称',
                    `school_id` int(11) DEFAULT NULL COMMENT '学校ID',
                    `school_district` int(11) DEFAULT NULL COMMENT '校区ID',
                    `grade_id` int(11) DEFAULT NULL COMMENT '年级ID',
                    `step` tinyint(4) DEFAULT 0 COMMENT '步骤',
                    PRIMARY KEY (`id`),
                    KEY `idx_school_id` (`school_id`),
                    KEY `idx_school_district` (`school_district`),
                    KEY `idx_grade_id` (`grade_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='班级表'
            ");

            $this->info("✅ ysy_class 表创建成功");

        } catch (\Exception $e) {
            $this->error("❌ 创建 ysy_class 表失败: " . $e->getMessage());
        }
    }
    
    /**
     * 测试学校同步
     */
    private function testSchoolSync()
    {
        $this->info("\n=== 测试学校同步 ===");
        
        try {
            $syncHelper = new DataSyncHelper();
            
            // 测试数据
            $testSchoolData = [
                'id' => 9999,
                'name' => '测试学校_' . date('YmdHis'),
                'code' => 'TEST_' . date('His'),
                'type' => 1,
                'level' => 1,
                'phone' => '010-12345678',
                'email' => '<EMAIL>',
                'address' => '测试地址',
                'status' => 1,
            ];
            
            // 额外数据
            $additionalData = [
                'add_time' => now()->format('Y-m-d H:i:s'),
                'province' => '北京市',
                'city' => '北京市',
                'district' => '朝阳区',
                'buy_modules' => '01,02,03,04,06,07,08,09,10,11,12,13,14,16,120,21,22,23,24,25,26,27,28,29,30,2802',
                'location' => '测试位置',
            ];
            
            $this->info("测试学校数据: " . json_encode($testSchoolData, JSON_UNESCAPED_UNICODE));
            
            // 调用同步
            $result = $syncHelper->syncSchool($testSchoolData, $additionalData);
            
            if ($result['success']) {
                $this->info("✅ 学校数据同步成功");
                $this->info("同步结果: " . json_encode($result, JSON_UNESCAPED_UNICODE));
                
                // 验证学校数据是否真的插入了
                $syncedSchoolData = DB::connection('sync_mysql')
                    ->table('ysy_school')
                    ->where('id', $testSchoolData['id'])
                    ->first();

                if ($syncedSchoolData) {
                    $this->info("✅ 学校数据验证成功，已在同步数据库中找到记录");
                    $this->info("同步的学校数据: " . json_encode($syncedSchoolData, JSON_UNESCAPED_UNICODE));
                } else {
                    $this->warn("⚠️  学校数据验证失败，未在同步数据库中找到记录");
                }

                // 验证校区数据是否插入了
                $syncedDistrictData = DB::connection('sync_mysql')
                    ->table('ysy_school_district')
                    ->where('school_id', $testSchoolData['id'])
                    ->first();

                if ($syncedDistrictData) {
                    $this->info("✅ 校区数据验证成功，已在同步数据库中找到记录");
                    $this->info("同步的校区数据: " . json_encode($syncedDistrictData, JSON_UNESCAPED_UNICODE));
                } else {
                    $this->warn("⚠️  校区数据验证失败，未在同步数据库中找到记录");
                }

                // 验证角色数据是否插入了
                $syncedRoleData = DB::connection('sync_mysql')
                    ->table('ysy_role')
                    ->where('school_id', $testSchoolData['id'])
                    ->get();

                if ($syncedRoleData->count() > 0) {
                    $this->info("✅ 角色数据验证成功，已在同步数据库中找到 {$syncedRoleData->count()} 条记录");
                    foreach ($syncedRoleData as $role) {
                        $this->info("角色: {$role->name} (类型: {$role->type}, ID: {$role->id})");

                        // 验证对应的权限数据
                        $privilegeData = DB::connection('sync_mysql')
                            ->table('ysy_role_privilege')
                            ->where('role_id', $role->id)
                            ->first();

                        if ($privilegeData) {
                            $this->info("  权限: {$privilegeData->menu_authority}");
                            if ($privilegeData->exclusion_authority) {
                                $this->info("  排除权限: {$privilegeData->exclusion_authority}");
                            }
                        }
                    }
                } else {
                    $this->warn("⚠️  角色数据验证失败，未在同步数据库中找到记录");
                }
                
            } else {
                $this->error("❌ 学校数据同步失败: " . $result['message']);
            }
            
        } catch (\Exception $e) {
            $this->error("❌ 测试学校同步异常: " . $e->getMessage());
            $this->error("错误详情: " . $e->getTraceAsString());
        }
    }
}
