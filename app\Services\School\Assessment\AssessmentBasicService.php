<?php

namespace App\Services\School\Assessment;

use App\Models\School\Assessment\Assessment;
use App\Models\School\Assessment\AssessmentTaskAssignment;
use App\Models\School\System\SchoolAssessment;
use App\Models\School\System\Teacher;
use App\Services\BaseService;
use App\Repositories\AssessmentRepository;

class AssessmentBasicService extends BaseService
{

    public function __construct(protected AssessmentRepository $assessmentRepository)
    {
    }

    /**
     * 获取测评分类列表
     *
     * @param int $schoolId 学校ID
     * @param string $type 用户类型，student表示学生端
     * @return array
     */
    public function getAssessmentTypes(): array
    {
        return Assessment::select(['id', 'name', 'category_code', 'official_name', 'icon', 'introduction_pc'])
            ->get()
            ->toArray();
    }

    /**
     * 获取测评分类列表
     *
     * @param int $schoolId 学校ID
     * @param int|null $type 测评类型，可为空
     * @param string $platform 平台类型，teacher表示教务端，student表示学生端
     * @return array
     */
    public function getAssessmentList(int $schoolId, ?int $type, string $platform = '', ?int $user_id, ?int $teacherId): array
    {
        // 获取学校购买的测评ID列表
        $schoolAssessmentIds = $this->getSchoolAssessmentIds($schoolId, $platform, $teacherId);
        
        // 获取所有测评数据，传入平台参数和可为空的type
        $assessments = $this->assessmentRepository->getAssessments($schoolAssessmentIds, $type, $platform);
        if (empty($assessments)) return [];
        
        //type为空说明是测评库列表
        if($type == null){
            $latest_assignments = AssessmentTaskAssignment::where('user_id', $user_id)
            ->where('school_id', $schoolId)
            ->selectRaw('MAX(id) as id, assessment_id')
            ->groupBy('assessment_id')
            ->pluck('id', 'assessment_id')
            ->toArray();
            foreach ($assessments as $assessment) {
                if (isset($latest_assignments[$assessment->id])) {
                    $assessment->assessment_task_assignment_id = $latest_assignments[$assessment->id];
                }
            }
        }
        // 按 category_code 分组并构建结果
        return $this->buildCategoryResults($assessments);
    }

    /**
     * 获取学校购买的测评ID列表
     *
     * @param int $schoolId 学校ID
     * @param string $type 用户类型
     * @return array
     */
    private function getSchoolAssessmentIds(int $schoolId, string $platform, ?int $teacherId): array
    {
        $schoolAssessmentQuery = SchoolAssessment::where('school_id', $schoolId);
        
        // 如果是学生端，则添加公开条件
        if ($platform === 'student') {
            $schoolAssessmentQuery->where('is_open_puce', 1);
        }
        
        if (!is_null($teacherId)) {
            $stage = Teacher::where('teachers.id', $teacherId)
                ->join('school_campuses', 'school_campuses.id', '=', 'teachers.school_campus_id')
                ->value('school_campuses.type');
            if ($stage == 2) {
                $assessmentIds = [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24]; //初中
            } else {
                $assessmentIds = [1, 2, 3, 4, 5, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 23, 24]; //高中
            }
            $schoolAssessmentQuery->whereIn('assessment_id', $assessmentIds);
        }
        
        $schoolAssessmentIds = $schoolAssessmentQuery->pluck('assessment_id')->toArray();

        if (empty($schoolAssessmentIds)) return [];

        return $schoolAssessmentIds;
    }

    /**
     * 构建分类结果
     *
     * @param \Illuminate\Database\Eloquent\Collection $assessments 测评集合
     * @return array
     */
    private function buildCategoryResults($assessments): array
    {
        // 按 category_code 分组
        $groupedAssessments = $assessments->groupBy('category_code');
        
        // 获取分类映射信息
        $categoryMapping = $this->getCategoryMapping();

        $result = [];
        
        // 构建结果数组，只包含学校拥有的测评类别
        foreach ($groupedAssessments as $categoryCode => $assessmentItems) {
            // 跳过未定义的分类
            if (!isset($categoryMapping[$categoryCode])) continue;
            
            $result[] = $this->buildCategoryItem($categoryCode, $assessmentItems, $categoryMapping[$categoryCode]);
        }

        return $result;
    }

    /**
     * 构建单个分类项
     *
     * @param string $categoryCode 分类代码
     * @param \Illuminate\Support\Collection $assessmentItems 测评项目集合
     * @param array $categoryInfo 分类信息
     * @return array
     */
    private function buildCategoryItem(string $categoryCode, $assessmentItems, array $categoryInfo): array
    {
        // 转换测评项目为数组格式
        $categoryItems = $assessmentItems->toArray();

        return [
            'code' => $categoryCode,
            'name' => $categoryInfo['name'],
            'description' => $categoryInfo['description'],
            'assessments' => $categoryItems
        ];
    }

    /**
     * 获取测评分类映射信息
     *
     * @return array
     */
    private function getCategoryMapping(): array
    {
        // 从配置文件获取分类信息
        $categoryConfig = config('assessment');
        
        // 分类名称映射
        return [
            'career' => [
                'name' => '生涯测评', 
                'description' => $categoryConfig['career']['description'] ?? ''
            ],
            'capability' => [
                'name' => '五力测评',
                'description' => $categoryConfig['capability']['description'] ?? ''
            ],
            'competency' => [
                'name' => '创新人才核心素养测评',
                'description' => $categoryConfig['competency']['description'] ?? ''
            ],
            'psychology' => [
                'name' => '心理评估',
                'description' => ''
            ],
            'subject' => [
                'name' => '学科兴趣测评',
                'description' => ''
            ],
        ];
    }

    public function getDimensionDetails(int $assessmentId): array
    {
        switch ($assessmentId) {
            case 3:
                //性格一题包含两个维度。内倾-外倾,判断-知觉,思维-情感,感觉-直觉
                $dimensions = ['内倾','外倾','判断','知觉','思维','情感','感觉','直觉'];
                break;
            case in_array($assessmentId, [4,8]):
                $dimensions = $this->assessmentRepository->getCareerDimensions($assessmentId);
                break;
            case in_array($assessmentId, [1,2,5,6,7]):
                $dimensions = $this->assessmentRepository->getCareerDimensions($assessmentId);
                break;
            case in_array($assessmentId, [9,10,11,12,13]):
                $dimensions = $this->assessmentRepository->getCapabilityDimensions($assessmentId);
                break;
            case in_array($assessmentId, [14,15,16,17,18,19,20]):
                $dimensions = $this->assessmentRepository->getCompetencyDimensions($assessmentId);
                break;
            case in_array($assessmentId, [21,22,23]):
                $dimensions = $this->assessmentRepository->getPsychologyDimensions($assessmentId);
                break;
            case in_array($assessmentId, [24]):
                $dimensions = $this->assessmentRepository->getSubjectDimensions($assessmentId);
                break;
        }

        return $dimensions;
    }
}