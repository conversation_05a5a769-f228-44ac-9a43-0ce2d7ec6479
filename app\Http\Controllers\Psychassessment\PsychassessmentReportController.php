<?php

namespace App\Http\Controllers\Psychassessment;

use App\Http\Controllers\Controller;
use App\Services\Psychassessment\ReportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * 心理评估-个体报告控制器 - 基于原 ThinkPHP Report 控制器重新实现
 */
class PsychassessmentReportController extends Controller
{
    protected $reportService;

    public function __construct(ReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    /**
     * 个体报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function report(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $memberId = $request->input('member_id');
            
            if (!$planId || !$surveyType) {
                return $this->error('缺少必要参数');
            }
            
            // 如果没有传member_id，使用当前登录用户的ID
            if (!$memberId && auth()->user()) {
                $memberId = auth()->user()->id;
            }
            
            $data = $this->reportService->getReport($planId, $surveyType, $memberId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取报告详情
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getReportDetail(Request $request): JsonResponse
    {
        try {
            $reportId = $request->input('report_id');
            $data = $this->reportService->getReportDetail($reportId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成报告PDF
     * 
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|JsonResponse
     */
    public function generatePdf(Request $request)
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $memberId = $request->input('member_id');
            
            if (!$planId || !$surveyType) {
                return $this->error('缺少必要参数');
            }
            
            return $this->reportService->generatePdf($planId, $surveyType, $memberId);
        } catch (\Exception $e) {
            return $this->error('生成PDF失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取报告列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getReportList(Request $request): JsonResponse
    {
        try {
            $params = $request->all();
            $data = $this->reportService->getReportList($params);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量生成报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchGenerate(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $memberIds = $request->input('member_ids');
            
            $data = $this->reportService->batchGenerate($planId, $surveyType, $memberIds);
            return $this->success($data, '批量生成成功');
        } catch (\Exception $e) {
            return $this->error('批量生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function deleteReport(Request $request): JsonResponse
    {
        try {
            $reportId = $request->input('report_id');
            $data = $this->reportService->deleteReport($reportId);
            return $this->success($data, '删除成功');
        } catch (\Exception $e) {
            return $this->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取报告统计数据
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getReportStatistics(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $data = $this->reportService->getReportStatistics($planId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 预览报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function previewReport(Request $request): JsonResponse
    {
        try {
            $planId = $request->input('plan_id');
            $surveyType = $request->input('survey_type');
            $memberId = $request->input('member_id');
            
            $data = $this->reportService->previewReport($planId, $surveyType, $memberId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 分享报告
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function shareReport(Request $request): JsonResponse
    {
        try {
            $reportId = $request->input('report_id');
            $shareType = $request->input('share_type', 'link'); // link, email, wechat
            
            $data = $this->reportService->shareReport($reportId, $shareType);
            return $this->success($data, '分享成功');
        } catch (\Exception $e) {
            return $this->error('分享失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取报告模板
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getReportTemplate(Request $request): JsonResponse
    {
        try {
            $surveyType = $request->input('survey_type');
            $data = $this->reportService->getReportTemplate($surveyType);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新报告模板
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateReportTemplate(Request $request): JsonResponse
    {
        try {
            $surveyType = $request->input('survey_type');
            $template = $request->input('template');
            
            $data = $this->reportService->updateReportTemplate($surveyType, $template);
            return $this->success($data, '更新成功');
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }
}
