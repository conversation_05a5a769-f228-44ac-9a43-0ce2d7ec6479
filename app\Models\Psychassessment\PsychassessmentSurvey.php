<?php

namespace App\Models\Psychassessment;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 心理评估测评计划模型
 */
class PsychassessmentSurvey extends Model
{
    protected $table = 'psychassessment_survey';

    protected $fillable = [
        'title',
        'description',
        'survey_type',
        'start_time',
        'end_time',
        'status',
        'creator_id',
        'school_id',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'survey_type' => 'integer',
        'status' => 'integer',
        'creator_id' => 'integer',
        'school_id' => 'integer',
        'start_time' => 'datetime',
        'end_time' => 'datetime'
    ];

    // 状态常量
    const STATUS_DRAFT = 0;      // 草稿
    const STATUS_ACTIVE = 1;     // 进行中
    const STATUS_COMPLETED = 2;  // 已完成
    const STATUS_CANCELLED = 3;  // 已取消

    /**
     * 关联创建者
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Member::class, 'creator_id');
    }

    /**
     * 关联学校
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School::class, 'school_id');
    }

    /**
     * 关联心理评估类型
     */
    public function assessment(): BelongsTo
    {
        return $this->belongsTo(PsychassessmentAssessment::class, 'survey_type');
    }

    /**
     * 关联参与学生
     */
    public function members(): HasMany
    {
        return $this->hasMany(PsychassessmentSurveyMember::class, 'survey_id');
    }

    /**
     * 关联测评结果
     */
    public function results(): HasMany
    {
        return $this->hasMany(PsychassessmentResult::class, 'survey_id');
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            self::STATUS_DRAFT => '草稿',
            self::STATUS_ACTIVE => '进行中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_CANCELLED => '已取消',
            default => '未知'
        };
    }

    /**
     * 获取参与人数
     */
    public function getMemberCountAttribute()
    {
        return $this->members()->count();
    }

    /**
     * 获取完成人数
     */
    public function getCompletedCountAttribute()
    {
        return $this->members()->where('status', 1)->count();
    }

    /**
     * 获取完成率
     */
    public function getCompletionRateAttribute()
    {
        $total = $this->member_count;
        $completed = $this->completed_count;
        
        return $total > 0 ? round($completed / $total * 100, 2) : 0;
    }

    /**
     * 检查是否可以编辑
     */
    public function canEdit(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_ACTIVE]);
    }

    /**
     * 检查是否可以删除
     */
    public function canDelete(): bool
    {
        return $this->status === self::STATUS_DRAFT;
    }

    /**
     * 检查是否已开始
     */
    public function hasStarted(): bool
    {
        return $this->start_time <= now();
    }

    /**
     * 检查是否已结束
     */
    public function hasEnded(): bool
    {
        return $this->end_time <= now();
    }

    /**
     * 作用域：按状态
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按创建者
     */
    public function scopeByCreator($query, $creatorId)
    {
        return $query->where('creator_id', $creatorId);
    }

    /**
     * 作用域：按学校
     */
    public function scopeBySchool($query, $schoolId)
    {
        return $query->where('school_id', $schoolId);
    }

    /**
     * 作用域：按评估类型
     */
    public function scopeBySurveyType($query, $surveyType)
    {
        return $query->where('survey_type', $surveyType);
    }

    /**
     * 作用域：进行中的测评
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE)
            ->where('start_time', '<=', now())
            ->where('end_time', '>=', now());
    }

    /**
     * 作用域：已完成的测评
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * 作用域：按时间范围
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 作用域：搜索
     */
    public function scopeSearch($query, $keyword)
    {
        return $query->where(function($q) use ($keyword) {
            $q->where('title', 'like', "%{$keyword}%")
              ->orWhere('description', 'like', "%{$keyword}%");
        });
    }
}
