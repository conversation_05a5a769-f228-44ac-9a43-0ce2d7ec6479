<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class GradeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            [
                'grade_name' => '一年级',
                'alisa_name' => '一年级',
            ],
            [
                'grade_name' => '二年级',
                'alisa_name' => '二年级',
            ],
            [
                'grade_name' => '三年级',
                'alisa_name' => '三年级',
            ],
            [
                'grade_name' => '四年级',
                'alisa_name' => '四年级',
            ],
            [
                'grade_name' => '五年级',
                'alisa_name' => '五年级',
            ],
            [
                'grade_name' => '六年级',
                'alisa_name' => '预初',
            ],
            [
                'grade_name' => '初一',
                'alisa_name' => '初一',
            ],
            [
                'grade_name' => '初二',
                'alisa_name' => '初二',
            ],
            [
                'grade_name' => '初三',
                'alisa_name' => '初三',
            ],
            [
                'grade_name' => '高一',
                'alisa_name' => '高一',
            ],
            [
                'grade_name' => '高二',
                'alisa_name' => '高二',
            ],
            [
                'grade_name' => '高三',
                'alisa_name' => '高三',
            ]
        ];
        DB::table("grades")->insert($data);
    }
}
