<?php
namespace App\Services\School\Assessment\IndividualReport\Career;

use App\Services\School\Assessment\IndividualReport\AbstractIndividualReportService;
use App\Models\Gk\UserTestHollandMajor;

class InterestSeniorService extends AbstractIndividualReportService
{
    /**
     * 解析高中兴趣评估结果
     */
    private const HOLLAND_SCORE_THRESHOLDS = [
        'DIFFERENTIATION' => [
            ['threshold' => 1.5, 'percentage' => 25],
            ['threshold' => 2.5, 'percentage' => 45],
            ['threshold' => 4.0, 'percentage' => 65],
            ['threshold' => PHP_FLOAT_MAX, 'percentage' => 85]
        ],
        'CONSISTENCY' => [
            ['threshold' => 3, 'index' => 0],
            ['threshold' => 6, 'index' => 1],
            ['threshold' => PHP_FLOAT_MAX, 'index' => 2]
        ]
    ];

    public function generateReport(array $params): array
    {
        $assignmentId = $params['assessment_task_assignment_id'];
        // 获取分发信息
        $assignmentInfo = $this->getAssignmentInfo($assignmentId);
        
        // 生成报告
        $results = json_decode($assignmentInfo['results'], true);
        $gender = $assignmentInfo['gender'];
        $assessmentInfo = $this->interestInfo($results,$gender);

        // 合并结果
        return array_merge($assignmentInfo, $assessmentInfo);
    }

    private function interestInfo($result, $gender): array
    {
        $hollandAnalysis = $this->analyzeHollandResult($result);
        $majorRecommendations = $this->getMajorRecommendations($hollandAnalysis['hollandCode'], $gender);

        return [
            'category' => $majorRecommendations,
            'scores' => $hollandAnalysis['survey'],
            'consistency' => $hollandAnalysis['consistency'],
            'differentiation' => $hollandAnalysis['differentiation'],
            'hollandCode' => $hollandAnalysis['hollandCode'],
            'content' => $hollandAnalysis['content'],
            'interest_fit' => $hollandAnalysis['interestFit']
        ];
    }

    private function analyzeHollandResult($result): array
    {
        // 解析基础数据
        $survey = $this->parseSurveyData($result[0]);

        // 获取前三项最高分
        $scores = array_column($survey, 'score');
        rsort($scores);
        $topThreeScores = array_slice($scores, 0, 3);

        // 计算兴趣适配度
        $interestFit = round(array_sum($topThreeScores) / 3 / 2, 1) + 23;

        // 计算分化度
        $differentiationScore = ($scores[0] - ($scores[1] + $scores[3]) / 2) / 2;
        $differentiation = $this->calculateDifferentiation($differentiationScore);

        // 计算一致性
        $consistency = $this->calculateConsistency($result[1]);

        // 获取前三项解读内容
        $content = $this->getTopThreeContent(array_slice($survey, 0, 3));

        return [
            'survey' => $survey,
            'hollandCode' => $result[1],
            'consistency' => $consistency,
            'differentiation' => $differentiation,
            'content' => $content,
            'interestFit' => $interestFit
        ];
    }

    private function parseSurveyData($data): array
    {
        $survey = [];
        foreach ($data as $key => $value) {
            $survey[$key] = [
                'name' => $value[2],
                'type' => $value[1],
                'score' => $value[0]
            ];
        }
        return $survey;
    }

    private function calculateDifferentiation($score): array
    {
        $differentiation = [];
        foreach (self::HOLLAND_SCORE_THRESHOLDS['DIFFERENTIATION'] as $level) {
            if ($score < $level['threshold']) {
                $differentiation = [
                    'content' => config('assessment.career.interest_differentiation')[array_search($level, self::HOLLAND_SCORE_THRESHOLDS['DIFFERENTIATION'])],
                    'percentage' => $level['percentage'],
                    'score' => $score
                ];
            }
        }
        return $differentiation;
    }

    private function calculateConsistency($hollandCode): array
    {
        $consistencyMap = $this->buildConsistencyMap(config('assessment.career.interest_types'));
        $score = $consistencyMap[$hollandCode] ?? 0;
        $consistency = [];
        foreach (self::HOLLAND_SCORE_THRESHOLDS['CONSISTENCY'] as $level) {
            if ($score < $level['threshold']) {
                return [
                    'content' => config('assessment.career.interest_consistency')[$level['index']],
                    'score' => $score
                ];
            }
        }
        return $consistency;
    }

    private function buildConsistencyMap($config): array
    {
        $map = [];
        foreach ($config as $score => $codes) {
            foreach (explode(',', $codes) as $code) {
                $map[$code] = $score;
            }
        }
        return $map;
    }

    private function getTopThreeContent($topThree): array
    {
        $content = [];
        foreach ($topThree as $key => $value) {
            $content[$key] = config('assessment.career.interest_dimensions')[$value['type']];
            $content[$key]['name'] = $value['name'];
            $content[$key]['type'] = $value['type'];
        }
        return $content;
    }

    private function getMajorRecommendations($hollandCode, $gender): array
    {
        $majors = $this->queryMajorsByHollandCode($hollandCode, $gender);
        return $this->organizeMajorData($majors);
    }

    private function queryMajorsByHollandCode($hollandCode, $gender): array
    {
        return UserTestHollandMajor::select(
            'UserTestHollandMajor.ID',
            'UserTestHollandMajor.HollandCode',
            'UserTestHollandMajor.MajorID',
            'UserTestHollandMajor.Gender',
            'UserTestHollandMajor.MajorName',
            'UserTestHollandMajor.MajorCode',
            'UserTestHollandMajor.SubjectID',
            'UserTestHollandMajor.CategoryID',
            'MajorSubject_BK.SubjectName',
            'MajorCategory_BK.MajorCategoryName'
        )
        ->join('MajorSubject_BK', 'UserTestHollandMajor.SubjectID', '=', 'MajorSubject_BK.ID')
        ->join('MajorCategory_BK', 'UserTestHollandMajor.CategoryID', '=', 'MajorCategory_BK.ID')
        ->where(['UserTestHollandMajor.HollandCode' => $hollandCode, 'UserTestHollandMajor.Gender' => $gender])
        ->get()
        ->toArray();
    }

    private function organizeMajorData($majors): array
    {
        $organized = [];
        foreach ($majors as $major) {
            $organized[$major['SubjectID']][$major['CategoryID']][] = $major;
        }

        return [array_values($this->formatMajorData($organized))];
    }

    private function formatMajorData($organized): array
    {
        $formatted = [];
        foreach ($organized as $subjectId => $categories) {
            $subject = reset($categories)[0];
            $formatted[$subjectId] = [
                'SubjectName' => $subject['SubjectName'],
                'SubjectID' => $subjectId,
                'Category' => array_values(array_map(function($majors) {
                    $major = reset($majors);
                    return [
                        'MajorCategoryName' => $major['MajorCategoryName'],
                        'CategoryID' => $major['CategoryID'],
                        'Major' => array_map(fn($m) => [
                            'MajorName' => $m['MajorName'],
                            'MajorCode' => $m['MajorCode']
                        ], $majors)
                    ];
                }, $categories))
            ];
        }
        return $formatted;
    }

}
