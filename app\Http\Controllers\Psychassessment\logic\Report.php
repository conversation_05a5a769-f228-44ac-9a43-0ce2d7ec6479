<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2024/01/22
 * Time: 15:16
 */
namespace app\psychassessment\logic;

use think\Config;

class Report{

    public function __construct()
    {
        $this->relation = new \app\psychassessment\model\PlanSurveyRelation();
        $this->plan = new \app\psychassessment\model\Plan();
        $this->backend_model = new \app\backend\model\Backend();
        $this->survey_user_model = new \app\survey\model\SurveyUser();
        $this->user = get_user();
    }

    public function report($plan_id,$survey_type,$member_id)
    {
        if(!$member_id) $member_id = $this->user['id'];
        $survey_id = db('survey')->where('survey_type',$survey_type)->value('id');
        $answers_arr = $this->relation->alias('a')
            ->join('survey_user_session session','a.session_id = session.session_id')
            ->join('ysy_survey survey','session.survey_id=survey.id','left')
            ->join('ysy_school school','session.school_id=school.id','left')
            ->join('ysy_student student','session.student_id=student.id','left')
            ->field('school.name as school_name,session.student_id,session.member_id,session.create_time,session.pdf_url,survey.title,session.result,student.student_no,student.gender,student.name')
            ->where(['a.plan_id'=>$plan_id,'session.survey_id'=>$survey_id,'a.member_id'=>$member_id,'a.status'=>0])
            ->find();
        $answers_arr = to_arr($answers_arr);
        if(!$answers_arr) apiReturn([],'未查到测评数据',-110);

        $result_arr = $this->relation->alias('a')
            ->join('survey_user_session session','a.session_id = session.session_id')
            ->field('session.survey_id,session.member_id,session.result')
            ->where(['a.plan_id'=>$plan_id,'session.survey_id'=>$survey_id,'a.status'=>0])
            ->select();
        $result_arr = to_arr($result_arr);

        switch ($survey_type){
            case 26:
                //此处是自信[[44,90,60,44,84,60],64],[['价值自信','品德自信','人际自信','能力自信','学业自信','外表自信'],'总分']
                $result = json_decode($answers_arr['result'],true);
                foreach ($result_arr as $kr => $vr){
                    $scores[] = json_decode($vr['result'],true)[1];
                }
                //百分比暂时注释，后面有常模数据再计算。为了做批量下载，pdf都是即时生成的，导致第一个做测评的超过0%的数据生成了pdf，它不随后面产生的数据改变，注意只是pdf有bug，页面调用接口的不会有。
//                $rank_percentage = round(( 1 - getRankDescending($result[1],$scores) / count($scores) ) * 100,2).'%';
                $result_back = [
                    ['name'=>'总分','value'=>$result[1]],
                    ['name'=>'价值自信','value'=>$result[0][0]],
                    ['name'=>'品德自信','value'=>$result[0][1]],
                    ['name'=>'人际自信','value'=>$result[0][2]],
                    ['name'=>'能力自信','value'=>$result[0][3]],
                    ['name'=>'学业自信','value'=>$result[0][4]],
                    ['name'=>'外表自信','value'=>$result[0][5]],
                ];
                $warning_level = confidence_situation($result);

//                $word = '测验结果表明，你的自信总体得分为'.$result[1].'分，结果表示你'.$warning_level.',你的自信程度比'.$rank_percentage.'的同龄人高。';
                $word = '测验结果表明，你的自信总体得分为'.$result[1].'分，结果表示你'.$warning_level.'。';
                $xinxibuzu = [];
                $yibanzixin = [];
                $bijiaozixin = [];
                $code_psych_competence = code_psych_competence(26);
                $confidence = Config::get('zixin');
                $i = 0;
                foreach ($code_psych_competence as $k => $v){
                    $competence_code = confidence_competence_level($result[0][$i]);
                    $i++;
                    switch($competence_code){
                        case 'xinxibuzu':
                            $xinxibuzu[] = $v;
                            break;
                        case 'yibanzixin':
                            $yibanzixin[] = $v;
                            break;
                        case 'bijiaozixin':
                            $bijiaozixin[] = $v;
                            break;
                    }
                    $competence_back[$k] = $confidence[$k][$competence_code];
                    $advise_back[$k] = $confidence[$k]['advise'];
                }

                $text = '具体来说，你';
                if($bijiaozixin){
                    $bijiaozixin_str = implode($bijiaozixin,'、');
                    $text .= '在'.$bijiaozixin_str.'方面有较强的信心';
                }
                if($yibanzixin){
                    $yibanzixin_str = implode($yibanzixin,'、');
                    $text .= '，在'.$yibanzixin_str.'上表现一般';
                }
                if($xinxibuzu){
                    $xinxibuzu_str = implode($xinxibuzu,'、');
                    $text .= '，但在'.$xinxibuzu_str.'方面不太自信，有很大的提升空间';
                }
                $text .= '。';

                break;
            case 27:
                //此处是自我意识$arr=[[44,77,60,44,84,60],64]，[['行为意识','学业意识','身体意识','情绪意识','人际意识','幸福意识'],'总分'];
                $result = json_decode($answers_arr['result'],true);
                foreach ($result_arr as $kr => $vr){
                    $scores[] = json_decode($vr['result'],true)[1];
                }
                //百分比暂时注释，后面有常模数据再计算。
//                $rank_percentage = round(( 1 - getRankDescending($result[1],$scores) / count($scores) ) * 100,2).'%';
                $result_back = [
                    ['name'=>'总分','value'=>$result[1]],
                    ['name'=>'行为意识','value'=>$result[0][0]],
                    ['name'=>'学业意识','value'=>$result[0][1]],
                    ['name'=>'身体意识','value'=>$result[0][2]],
                    ['name'=>'情绪意识','value'=>$result[0][3]],
                    ['name'=>'人际意识','value'=>$result[0][4]],
                    ['name'=>'幸福意识','value'=>$result[0][5]],
                ];
                $warning_level = awareness_situation($result);
//                $word = '在该评估中，你的总分为'.$result[1].'分，自我意识发展水平比'.$rank_percentage.'的同龄人高，总体自我意识处于'.$warning_level.'。';
                $word = '在该评估中，你的总分为'.$result[1].'分，总体自我意识处于'.$warning_level.'。';
                $low = [];
                $normal = [];
                $code_psych_competence = code_psych_competence(27);
                $awareness = Config::get('ziwoyishi');
                $i = 0;
                foreach ($code_psych_competence as $k => $v){
                    $competence_code = awareness_competence_level($result[0][$i]);
                    $i++;
                    switch($competence_code){
                        case 'low':
                            $low[] = $v;
                            break;
                        case 'normal':
                            $normal[] = $v;
                            break;
                    }
                    $competence_back[$k] = $awareness[$k][$competence_code];
                }

                $text = '你';
                if($normal){
                    $normal_str = implode($normal,'、');
                    $text .= '在'.$normal_str.'方面的自我意识表现较强，';
                }
                if($low){
                    $low_str = implode($low,'、');
                    $text .= '在'.$low_str.'方面的自我意识表现较弱。';
                }
                $text .= '。';
                switch($warning_level){
                    case '较低水平':
                        $advise_back = $awareness['advise']['low'];
                        break;
                    case '正常水平':
                        $advise_back = $awareness['advise']['normal'];
                        break;
                }

                break;
            case 28:
                //此处是焦虑$arr=[50,[37.5,43.75,43.75,50,50]],['焦虑程度',['学习焦虑','考试焦虑','家庭焦虑','人际焦虑','形象焦虑']];
                $result = json_decode($answers_arr['result'],true);
                $result_back = [
                    ['name'=>'焦虑程度','value'=>$result[0]],
                    ['name'=>'学习焦虑','value'=>$result[1][0]],
                    ['name'=>'考试焦虑','value'=>$result[1][1]],
                    ['name'=>'家庭焦虑','value'=>$result[1][2]],
                    ['name'=>'人际焦虑','value'=>$result[1][3]],
                    ['name'=>'形象焦虑','value'=>$result[1][4]],
                ];
                $anxiety = Config::get('jiaolv');
                $anxiety_code_and_situation = anxiety_code_and_situation($result);
                $word = '测验结果表明，你的焦虑程度总体得分为'.$result[0].'分，处于'.$anxiety_code_and_situation[0].'。'.$anxiety['jiaolvchengdu'][$anxiety_code_and_situation[1]]['desc'];
                $normal = [];
                $some = [];
                $much = [];
                $code_psych_competence = code_psych_competence(28);
                $advise_back['degree'] = $anxiety['jiaolvchengdu']['advise'][$anxiety_code_and_situation[1]]['desc'];
                $i = 0;
                foreach ($code_psych_competence as $k => $v){
                    $competence_code = anxiety_competence_level($result[1][$i]);
                    $i++;
                    switch($competence_code){
                        case 'normal':
                            $normal[] = $v;
                            break;
                        case 'some':
                            $some[] = $v;
                            break;
                        case 'much':
                            $much[] = $v;
                            break;
                    }
                    $competence_back[$k] = $anxiety[$k][$competence_code];
                    $advise_back['specific'][$k] = $anxiety[$k]['advise'];
                }

                $text = '具体来说，你';
                if($some){
                    $some_str = implode($some,'、');
                    $text .= '在'.$some_str.'上处于一些焦虑水平';
                }
                if($normal){
                    $normal_str = implode($normal,'、');
                    $text .= '，在'.$normal_str.'上处于正常水平';
                }
                if($much){
                    $much_str = implode($much,'、');
                    $text .= '，但在'.$much_str.'上处于非常焦虑水平';
                }
                $text .= '。说明你焦虑的来源可能';
                if($much) $text .= '大部分是'.$much_str;
                if($some) $text .= '一小部分来源于'.$some_str;

                break;
            default:
                //默认
        }
        $data = [
            'person'     => $answers_arr,
            'charts'     => $result_back,
            'word'       => [$word, $text],
            'competence' => $competence_back,
            'advise'     => $advise_back,
            'logo'       => $this->get_logo($member_id),
        ];
        return $data;
    }

    /**
     * 获取个人测评结果/老师/学生调用结果不同
     */
    function get_member_info($sessionnum,$survey_id)
    {
        #1.获取测评结果，如果没有studentID 说明此测评是教务或者老师测试
        $query = $this->survey_user_model->get_moreinfo(['c.session_id'=>$sessionnum,'ysy_survey.survey_type'=>$survey_id],'ysy_school.name as school_name,c.student_id,c.member_id,c.create_time,c.pdf_url,ysy_survey.survey_type,ysy_survey.title,c.result,ysy_student.student_no,ysy_student.gender,ysy_student.name');
        return $query;
    }

    public function get_logo($member_id){
        //初始化logo信息
        $data=[
            'img'         => LOGO,
            'img_white'   => LOGO_WHITE,
            'img_red'     => LOGO_RED,
            'logo_base64' => Config::get('logo_base64'),
        ];
        $school_id   = db('member')->where('id', $member_id)->value('school_id');
        $custom      = db('school_custom_made')->field('login_logo,home_logo,logo_base64')->where('school_id', $school_id)->find();//学校定制
        $supplier_id = db('school')->where('id', $school_id)->value('supplier_id');//代理商
        if(!empty($custom)){
            $data['img']         = $custom['login_logo'] ?? $data['img'];
            $data['img_white']   = $custom['home_logo'] ?? $data['img_white'];
            $data['img_red']     = $custom['home_logo'] ?? $data['img_red'];
            $data['logo_base64'] = $custom['logo_base64'] ?? $data['logo_base64'];
        }elseif($supplier_id){
            $info = $this->backend_model->get_mysql_data('agent', 'id,logo,logo_white,logo_red', FALSE, ['id' => $supplier_id, 'is_delete' => ['>=', 0]], NULL, NULL, NULL, 'find');
            if ($info) {
                $data['img']         = $info['logo'] ?? $data['img'];
                $data['img_white']   = $info['logo_white'] ?? $data['img_white'];
                $data['img_red']     = $info['logo_red'] ?? $data['img_red'];
                $data['logo_base64'] = $info['logo_base64'] ?? $data['logo_base64'];
            }
        }

        return $data;
    }
    
}