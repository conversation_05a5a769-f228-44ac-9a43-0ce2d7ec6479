<?php
namespace App\Services\School\Assessment\IndividualReport\Career;

use App\Services\School\Assessment\IndividualReport\AbstractIndividualReportService;

class DevelopmentService extends AbstractIndividualReportService
{
    public function generateReport(array $params): array
    {
        $assessment_task_assignment_id = $params['assessment_task_assignment_id'];
        $assessment_id = $params['assessment_id'];
        // 获取分发信息
        $assignment_info = $this->getAssignmentInfo($assessment_task_assignment_id);
        
        // 生成报告
        $assessment_info = $this->developmentInfo($assignment_info['standard_results'], $assessment_id);

        // 合并结果
        return array_merge($assignment_info, $assessment_info);
    }

    /**
     * 解析生涯发展评估结果
     */
    private const SCORE_THRESHOLD = 20;

    public function developmentInfo($standard_results, $assessment_id): array
    {
        if($assessment_id == 5){
            $configs = [
                'name' => config('assessment.career.development_name'),
                'dimensions' => config('assessment.career.development_dimensions'),
                'dimensionsSub' => config('assessment.career.development_dimensions_sub'),
                'levels' => config('assessment.career.development_levels'),
                'dimensionLevels' => config('assessment.career.development_dimension_levels'),
                'dimensionsSubLevels' => config('assessment.career.development_dimension_sub_levels'),
                'dimensionsSubLevelsMobile' => config('assessment.career.development_dimension_sub_levels_mobile'),
                'dimensionRange' => config('assessment.career.development_dimension_range'),
            ];
        }elseif ($assessment_id == 7){
            $configs = [
                'name' => config('assessment.career.development_name'),
                'dimensions' => config('assessment.career.development_dimensions'),
                'dimensionsSub' => config('assessment.career.development_dimensions_sub_junior'),
                'levels' => config('assessment.career.development_levels'),
                'dimensionLevels' => config('assessment.career.development_dimension_levels'),
                'dimensionsSubLevels' => config('assessment.career.development_dimension_sub_levels_junior'),
                'dimensionsSubLevelsMobile' => config('assessment.career.development_dimension_sub_levels_mobile_junior'),
                'dimensionRange' => config('assessment.career.development_dimension_range_junior'),
            ];
        }

        // 从新的标准结果格式中提取数据
        $sub_dimensions = [];
        $main_dimensions = [];
        $total_score = $standard_results['total_score'];
        
        // 处理维度数据
        foreach ($standard_results['dimensions'] as $dimension) {
            $main_dimensions[] = [
                'name' => $dimension['name'],
                'score' => $dimension['score']
            ];
            
            // 处理子维度
            foreach ($dimension['children'] as $child) {
                $sub_dimensions[$child['code']] = [
                    'name' => $child['name'],
                    'score' => $child['score']
                ];
            }
        }
        
        // 处理一级维度
        $dimensions = $this->processMainDimensions($main_dimensions, $configs);
        // 处理二级维度
        $sub_dimensions_processed = $this->processSubDimensions($sub_dimensions, $configs);
        // 生涯发展水平
        $level = [
            'name' => $configs['name'],
            'score' => $total_score,
            'title' => $configs['levels'][$total_score >= self::SCORE_THRESHOLD ? 0 : 1]
        ];
        
        return [
            'level' => $level,
            'dimensions' => $this->combineDimensions($dimensions, $sub_dimensions_processed, $configs['dimensionRange']),//一级维度和二级维度重组数组
        ];
    }

    private function processMainDimensions($dimensions, $configs): array
    {
        $result = [];
        foreach ($dimensions as $index => $dimension) {
            $score = $dimension['score'];
            $is_high_score = $score >= self::SCORE_THRESHOLD;
            $result[$index] = [
                'name' => $dimension['name'],
                'score' => $score,
                'title' => $configs['dimensionLevels'][$index][$is_high_score ? 0 : 1],
                'rank' => $configs['levels'][$is_high_score ? 0 : 1]
            ];
        }
        return $result;
    }

    private function processSubDimensions($sub_dimensions, $configs): array
    {
        $result = [];
        $i = 0;
        foreach ($sub_dimensions as $code => $dimension) {
            $score = $dimension['score'];
            $is_high_score = $score >= self::SCORE_THRESHOLD;
            $result[$i] = [
                'name' => $dimension['name'],
                'score' => $score,
                'title' => $configs['dimensionsSubLevels'][$code][$is_high_score ? 0 : 1],
                'phone_title' => $configs['dimensionsSubLevelsMobile'][$code][$is_high_score ? 0 : 1]
            ];
            $i++;
        }
        return $result;
    }

    private function combineDimensions($dimensions, $sub_dimensions, $ranges): array
    {
        $result = [];
        foreach ($dimensions as $index => $dimension) {
            $result[$index] = $dimension;

            // 获取当前维度对应的二级维度范围
            $range = $ranges[$index];
            $result[$index]['content'] = array_map(
                fn($i) => $sub_dimensions[$i],
                range($range[0], $range[1])
            );
        }
        return array_values($result);
    }
}
