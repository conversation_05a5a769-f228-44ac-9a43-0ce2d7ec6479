# 学校同步完整功能说明

## 概述

SchoolSyncService现在包含了学校同步的完整功能，包括学校基础信息、校区信息和角色信息的同步。这确保了学校数据的完整性和一致性。

## 完整同步流程

### 主要方法：syncSchool()

```php
public function syncSchool(array $schoolData): array
{
    try {
        $this->syncConnection->beginTransaction();

        // 1. 同步学校基础信息
        $syncSchoolId = $this->syncSchoolBasicInfo($schoolData);

        // 2. 同步校区信息到 ysy_school_district 表
        $this->syncSchoolDistrict($syncSchoolId, $schoolData);

        // 3. 同步角色信息到 ysy_role 表并设置权限
        $this->syncSchoolRoles($syncSchoolId, $schoolData);

        $this->syncConnection->commit();
        
        return ['success' => true, 'sync_school_id' => $syncSchoolId];
    } catch (\Exception $e) {
        $this->syncConnection->rollBack();
        return ['success' => false, 'message' => $e->getMessage()];
    }
}
```

## 三个核心步骤

### 1. 同步学校基础信息 (syncSchoolBasicInfo)

**功能：** 同步学校的基本信息到同步数据库

**目标表：** `schools`

**同步字段：**
- id (使用原表ID)
- name (学校名称)
- province (省份)
- city (城市)
- district (区县)
- address (地址)
- phone (电话)
- email (邮箱)
- status (状态)
- original_id (原始ID备份)

### 2. 同步校区信息 (syncSchoolDistrict)

**功能：** 同步学校下属的所有校区信息

**目标表：** `ysy_school_district`

**数据来源：** 查询 `school_campuses` 表中该学校的所有校区

**同步字段：**
- school_id (关联学校ID)
- name (校区名称)
- address (校区地址)
- phone (校区电话)
- status (校区状态)
- original_campus_id (原始校区ID)

### 3. 同步角色信息 (syncSchoolRoles)

**功能：** 为学校创建默认角色

**目标表：** `ysy_role`

**默认角色：**
1. **学生角色**
   - name: "学生"
   - description: "学生角色"
   - permissions: "学生基础权限"

2. **教师角色**
   - name: "老师"
   - description: "教师角色"
   - permissions: "教师基础权限"

3. **教务角色**
   - name: "教务"
   - description: "教务管理角色"
   - permissions: "教务管理权限"

## 数据库表结构

### schools 表
```sql
CREATE TABLE schools (
    id INT PRIMARY KEY,
    name VARCHAR(255) COMMENT '学校名称',
    province VARCHAR(100) COMMENT '省份',
    city VARCHAR(100) COMMENT '城市',
    district VARCHAR(100) COMMENT '区县',
    address TEXT COMMENT '地址',
    phone VARCHAR(50) COMMENT '电话',
    email VARCHAR(100) COMMENT '邮箱',
    status TINYINT DEFAULT 1 COMMENT '状态',
    original_id INT COMMENT '原始ID',
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### ysy_school_district 表
```sql
CREATE TABLE ysy_school_district (
    id INT PRIMARY KEY AUTO_INCREMENT,
    school_id INT COMMENT '学校ID',
    name VARCHAR(255) COMMENT '校区名称',
    address TEXT COMMENT '校区地址',
    phone VARCHAR(50) COMMENT '校区电话',
    status TINYINT DEFAULT 1 COMMENT '状态',
    original_campus_id INT COMMENT '原始校区ID',
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### ysy_role 表
```sql
CREATE TABLE ysy_role (
    id INT PRIMARY KEY AUTO_INCREMENT,
    school_id INT COMMENT '学校ID',
    name VARCHAR(50) COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    permissions TEXT COMMENT '权限信息',
    status TINYINT DEFAULT 1 COMMENT '状态',
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## 使用示例

### 单个学校同步
```php
$schoolSyncService = app(\App\Services\DataSync\SchoolSyncService::class);

$schoolData = [
    'id' => 1001,
    'name' => '示例中学',
    'province' => '广东省',
    'city' => '深圳市',
    'district' => '南山区',
    'address' => '深圳市南山区示例路123号',
    'phone' => '0755-12345678',
    'email' => '<EMAIL>',
    'status' => 1
];

$result = $schoolSyncService->syncSchool($schoolData);

if ($result['success']) {
    echo "学校同步成功，同步学校ID: " . $result['sync_school_id'];
} else {
    echo "学校同步失败: " . $result['message'];
}
```

### 批量学校同步
```php
$schoolSyncService = app(\App\Services\DataSync\SchoolSyncService::class);

$schoolsData = [
    ['id' => 1001, 'name' => '示例中学1', ...],
    ['id' => 1002, 'name' => '示例中学2', ...],
    ['id' => 1003, 'name' => '示例中学3', ...]
];

$result = $schoolSyncService->syncBatchSchools($schoolsData);

echo "批量同步完成: 总数 {$result['total_count']}, 成功 {$result['success_count']}, 失败 {$result['failed_count']}";
```

## 事务处理

所有同步操作都在事务中进行：
- 如果任何一个步骤失败，整个同步过程会回滚
- 确保数据的一致性和完整性
- 避免部分同步导致的数据不一致问题

## 日志记录

完整的日志记录包括：
- 同步开始和结束
- 每个步骤的执行情况
- 错误信息和堆栈跟踪
- 同步的数据统计

## 优势

1. **完整性**：一次性同步学校的所有相关数据
2. **一致性**：事务保证数据的一致性
3. **可扩展性**：易于添加新的同步步骤
4. **可维护性**：清晰的方法分离，易于维护和调试
5. **向后兼容**：保持原有接口的兼容性

## 注意事项

1. **数据依赖**：校区和角色同步依赖于学校基础信息的成功同步
2. **ID映射**：使用原表ID作为同步表ID，确保数据关联的正确性
3. **错误处理**：每个步骤都有适当的错误处理和日志记录
4. **性能考虑**：批量同步时注意内存使用和执行时间
5. **数据校验**：建议在同步前验证数据的完整性和有效性
