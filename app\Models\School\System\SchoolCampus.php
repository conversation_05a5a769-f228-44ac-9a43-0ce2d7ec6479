<?php

namespace App\Models\School\System;

use App\Models\BaseModel;
use App\Models\Partner\Partner;
use App\Models\Partner\PartnerSchool;
use Illuminate\Database\Eloquent\SoftDeletes;

class SchoolCampus extends BaseModel
{
    use SoftDeletes;
    protected $hidden = [
        'created_at',
        'updated_at',
        'creator',
        'updater',
    ];

    public function school()
    {
        return $this->belongsTo(School::class, 'school_id', 'id');
    }

    // 归属于partnerSchool
    public function partnerSchool()
    {
        return $this->belongsTo(PartnerSchool::class, 'id', 'school_campus_id')->with('partner');
    }

    // 对应某一个教育局(远程一对一)
    public function partner()
    {
        return $this->hasOneThrough(
            Partner::class, // 最终要获取数据的表
            PartnerSchool::class,// 中间表 通过中间表来 获取最终要获取数据的表
            'school_campus_id',//  中间表的字段  用于关联 当前model （默认当前表的主键   可以用下面的第三个参数来替代）
            'id',  //  当前表的字段  和中间表的字段做关联
            'id', //  这个是最终结果表 的外间去关联中间表的字段
            'partner_id' //  中间表字段  用于关联最终表的外健
        )->select('partners.id','partners.name');
    }

}
