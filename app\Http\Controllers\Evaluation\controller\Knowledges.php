<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */
namespace app\evaluation\controller;
use app\login\controller\ApiAuth;


/**
 * Class Questions
 * @package app\evaluation\controller
 */
class Knowledges extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->Knowledges = new \app\evaluation\service\Knowledges();
    }
    /**
     * 模块：素养测评-知识点管理
     * @SWG\Post(path="/evaluation/knowledges",
     *   tags={"素养测评-知识点设置:knowledges"},
     *   summary="知识点添加",
     *  @SWG\Parameter(
     *     in="formData",
     *     name="name",
     *     type="string",
     *     description="名称",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="formData",
     *     name="is_high",
     *     type="integer",
     *     description="学段（0为初中，1为高中）",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="formData",
     *     name="course_id",
     *     type="integer",
     *     description="科目ID",
     *     required=true,
     *   ),
     *  @SWG\Parameter(
     *     in="formData",
     *     name="parent_id",
     *     type="integer",
     *     description="默认为0，上一级ID",
     *     required=true,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-知识点管理
     * @SWG\Put(path="/evaluation/knowledges/{id}",
     *   tags={"素养测评-知识点设置:knowledges"},
     *   summary="知识点修改",
     *  @SWG\Parameter(
     *     in="path",
     *     name="id",
     *     type="integer",
     *     description="id",
     *     required=true,
     *  ),
     *   @SWG\Parameter(
     *     in="body",
     *     name="data",
     *     description="更新的数据",
     *     required=true,
     *     @SWG\Schema(
     *       type="object",
     *       @SWG\Property(property="name", type="string", description="名称"),
     *       @SWG\Property(property="is_high", type="integer", description="学段（0为初中，1为高中）"),
     *       @SWG\Property(property="course_id", type="integer", description="科目ID"),
     *       @SWG\Property(property="parent_id", type="integer", description="上一级ID")
     *     )
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-知识点管理
     * @SWG\Delete(path="/evaluation/knowledges/{id}",
     *   tags={"素养测评-知识点设置:knowledges"},
     *   summary="知识点删除",
     *   @SWG\Parameter(
     *     in="path",
     *     name="id",
     *     type="integer",
     *     description="题目类型ID",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    /**
     * 模块：素养测评-知识点管理
     * @SWG\Get(path="/evaluation/knowledges",
     *   tags={"素养测评-知识点设置:knowledges"},
     *   summary="知识点查询相关功能",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="course_id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="is_high",
     *     type="integer",
     *     description="初高中选择",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="name",
     *     type="string",
     *     description="知识点名称",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function knowledges(){

        $data = $this->Knowledges->hand_out();
        apiReturn($data);
    }


}