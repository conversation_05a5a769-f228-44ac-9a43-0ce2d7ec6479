<?php

namespace App\Models\Gk;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CollegeZhangCheng extends BaseModel
{
    use HasFactory;
    // 表名
    protected $table = 'CollegeZhangCheng';
    // 指定连接
    protected $connection = 'sqlsrv_gk';
    protected $primaryKey = 'ID';


    protected $hidden = [];

    // 表示字段是否自动维护时间戳（如果有 created_at 和 updated_at 字段，默认开启）
    public $timestamps = false;

    // 定义可批量赋值的字段
    protected $fillable = [
        'CollegeID',
        'CollegeName',
        'ZhangchengTitle',
        'Zhangcheng',
        'Year',
        'Remark',
        'Type',
        'CreateTime',
        'UpdateTime',
        'CreateUser',
        'UpdateUser',
        'href',
    ];

}
