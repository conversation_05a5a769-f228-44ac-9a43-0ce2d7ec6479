<?php

namespace App\Http\Controllers\School\System;

use App\Http\Controllers\Controller;
use App\Models\School\System\Claass;
use App\Models\School\System\Course;
use App\Models\School\System\Grade;
use App\Models\School\System\Teacher;
use App\Services\School\System\TeacherService;
use App\Services\DataSync\DataSyncService;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TeacherController extends Controller
{
    use CrudOperations;

    protected string $model = Teacher::class;

    protected $teacherService;
    protected $dataSyncService;

    // 构造函数注入
    public function __construct(TeacherService $teacherService, DataSyncService $dataSyncService)
    {
        $this->teacherService = $teacherService;
        $this->dataSyncService = $dataSyncService;
    }

    public function index(Request $request)
    {
        $query = $this->teacherService->listBuilder($request);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get();
        // return $this->success(compact('list', 'cnt'));
        return $this->paginateSuccess($list, $cnt);
    }

    public function show($id)
    {
        $teacher = Teacher::with(['school:id,name', 'schoolCampus:id,campus_name', 'user:id,username,real_name,gender', 'user.roles:id,name,type'])->find($id);
        if (!$teacher) {
            return $this->notFound('查询对象不存在');
        }
        return $this->success($teacher);
    }

    public function store(Request $request)
    {
        // 校验参数
        $validator = $this->validateRequest($request, [
            'school_campus_id' => 'required|integer',
            'teacher_name' => 'required|string|max:10',
            'username' => 'required|string',
            'password' => 'required|min:6',
            'roles' => 'required|array'
        ]);
        if ($validator->fails()) {
            // 输出错误信息
            return $this->error($validator->errors()->first());
        }

        // 创建教师
        $teacher = $this->teacherService->store($request);

        // 调用同步接口
        try {
            $result = $this->dataSyncService->syncSingleTeacher($request);
        } catch (\Exception $e) {
             $this->throwBusinessException('导入失败');
        }

        return $this->message('创建成功');
    }

    public function batchStore(Request $request)
    {
        $validator = $this->validateRequest($request, [
            'school_campus_id' => 'required|integer',
            'teachers' => 'required|array',
            'teachers.*.teacher_name' => 'required|string|max:10',
            'teachers.*.username' => 'required|string|distinct',
//            'teachers.*.password' => 'required|min:6',
            'teachers.*.role_name' => 'required|string',
        ]);
        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        // 批量创建教师
        $created_teachers = $this->teacherService->batchStore($request);

        // 调用批量同步接口
        try {
            $this->dataSyncService->syncBatchTeachers($request->all());
        } catch (\Exception $e) {
            Log::warning('教师批量创建后同步失败', [
                'teacher_count' => count($created_teachers),
                'error' => $e->getMessage()
            ]);
        }

        return $this->success($created_teachers, '批量创建成功');
    }

    public function update(Request $request, $id)
    {
        $validator = $this->validateRequest($request, [
            'teacher_name' => 'required|string|max:10',
            'roles' => 'required|array'
        ]);
        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        $updated_teacher = $this->teacherService->update($request, $id);

        // 调用单个教师更新同步接口
        try {
            $this->dataSyncService->syncSingleTeacherUpdate($updated_teacher);
        } catch (\Exception $e) {
            Log::warning('教师更新后同步失败', [
                'teacher_id' => $updated_teacher->id ?? null,
                'error' => $e->getMessage()
            ]);
        }

        return $this->success($updated_teacher, '更新成功');
    }

    public function batchUpdate(Request $request)
    {
        $validator = $this->validateRequest($request, [
            'teacher_ids' => 'required|array',
            'teacher_ids.*' => 'integer',
            'update_data' => 'required|array',
            'update_data.teacher_name' => 'sometimes|string|max:10',
            'update_data.roles' => 'sometimes|array'
        ]);
        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        $updated_teachers = $this->teacherService->batchUpdate($request);

        // 调用批量同步接口
        try {
            $this->dataSyncService->syncBatchTeachersUpdate($updated_teachers);
        } catch (\Exception $e) {
            Log::warning('教师批量更新后同步失败', [
                'teacher_count' => count($updated_teachers),
                'error' => $e->getMessage()
            ]);
        }

        return $this->success($updated_teachers, '批量更新成功');
    }

    public function destroy(Request $request, $id)
    {
        return $this->teacherService->destroy($request, $id);
    }



    // 当前校区拥有的年级课程列表
    public function getGradeCourseList(Request $request)
    {
        $validator = $this->validateRequest($request, [
            'school_campus_id' => 'required|integer'
        ]);
        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        $school_id = $request->user()->organization->model_id;
        $school_campus_id = $request['school_campus_id'];
        $grade_ids = Course::where('school_id', $school_id)
            ->where('school_campus_id', $school_campus_id)
            ->pluck('grade_id');
        $list = Grade::whereIn('id', $grade_ids)->with(['courses'=>fn($query)=>$query->where('school_campus_id', $school_campus_id)->where('school_id', $school_id)])->get();
        return $this->success($list);
    }

    // 当前校区拥有的年级班级列表
    public function getGradeClassList(Request $request)
    {
        $validator = $this->validateRequest($request, [
            'school_campus_id' => 'required|integer'
        ]);
        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        $school_id = $request->user()->organization->model_id;
        $school_campus_id = $request['school_campus_id'];
        $grade_ids = Claass::where('school_id', $school_id)
            ->where('school_campus_id', $school_campus_id)
            ->pluck('grade_id');
        $list = Grade::whereIn('id', $grade_ids)->with(['classes'=>fn($query)=>$query->where('school_campus_id', $school_campus_id)->where('school_id', $school_id)])->get();
        return $this->success($list);
    }




}
