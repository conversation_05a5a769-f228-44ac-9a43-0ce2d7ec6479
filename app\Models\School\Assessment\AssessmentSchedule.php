<?php

namespace App\Models\School\Assessment;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssessmentSchedule extends BaseModel
{
    use HasFactory;
    use SoftDeletes;
    
    protected $fillable = [
        'name',
        'open_time',
        'close_time',
        'school_id',
        'type',
        'is_report_visible',
        'creator',
        'school_year',
        // 其他允许批量赋值的字段
    ];

    protected $dates = [
        'open_time',
        'close_time',
    ];

    // 添加属性
    protected $appends = ['status'];
    
    /**
     * 获取计划下的所有任务
     */
    public function tasks()
    {
        return $this->hasMany(AssessmentTask::class, 'assessment_schedule_id');
    }
    
    /**
     * 获取计划的状态 0 未开始，1进行中，2已结束
     */
    public function getStatusAttribute()
    {
        $now = now();
        if ($now < $this->open_time) {
            return 0;
        } elseif ($now > $this->close_time) {
            return 2;
        } else {
            return 1;
        }
    }
}
