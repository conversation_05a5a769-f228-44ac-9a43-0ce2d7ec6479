<?php

namespace App\Services\School\Assessment\Answer;

use App\Models\School\Assessment\Answer\AssessmentCapabilityAnswer;

class CapabilityAnswerService extends BaseAnswerService
{
    /**
     * 获取职业测评的问题ID在答案表中的字段名。
     *
     * @return string 职业测评问题ID的字段名 (e.g., 'assessment_capability_question_id')
     */
    protected function getSpecificQuestionIdKey(): string
    {
        return 'assessment_capability_question_id';
    }

    /**
     * 获取职业测评的答案模型类名。
     *
     * @return string 职业测评答案模型的完整类名 (e.g., AssessmentCapabilityAnswer::class)
     */
    protected function getAnswerModelClass(): string
    {
        return AssessmentCapabilityAnswer::class;
    }
}