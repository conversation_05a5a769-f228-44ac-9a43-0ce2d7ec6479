<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('role_has_menus', function (Blueprint $table) {
            $table->id();
            $table->integer('role_id')->comment('角色ID');
            $table->integer('organization_menu_id')->comment('机构菜单organization_has_menus表id');
            $table->integer('menu_code')->comment('菜单CODE');
            $table->timestamps();
            $table->string('creator',20)->nullable()->comment('创建人');
            $table->string('updater',20)->nullable()->comment('最后更新人');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('role_has_menus');
    }
};
