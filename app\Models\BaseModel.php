<?php

namespace App\Models;

use App\Traits\PaginationTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 通用数据模型基类
 */
class BaseModel extends Model
{
    use HasFactory,PaginationTrait;

    protected $guarded = [];

    protected $casts = [
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'created_at' => 'datetime:Y-m-d H:i:s',
    ];

}
