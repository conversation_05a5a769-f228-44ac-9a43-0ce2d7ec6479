# 学校数据同步测试指南

## 测试准备

### 1. 确保配置正确

检查 `.env` 文件中的配置：
```env
# 启用数据同步
DATA_SYNC_ENABLED=true

# 同步数据库配置
SYNC_DB_HOST=yuanbo.rwlb.rds.aliyuncs.com
SYNC_DB_DATABASE=ysy_test
SYNC_DB_USERNAME=ysy_test
SYNC_DB_PASSWORD=ZYQ2rXQ1hB-m
```

### 2. 确保目标数据库表存在

确保 `ysy_test` 数据库中存在 `ysy_school` 表，包含以下字段：
- `id` (主键)
- `school_name` (学校名称)
- `name` (名称)
- `code` (学校代码)
- `type` (学校类型)
- `level` (学校等级)
- `phone` (电话)
- `email` (邮箱)
- `address` (地址)
- `status` (状态)
- `province` (省份)
- `city` (城市)
- `district` (区县)
- `add_time` (添加时间)
- `date_due` (到期时间)
- `buy_modules` (购买模块)
- `location` (位置)
- `original_id` (原始ID)
- `created_at` (创建时间)
- `updated_at` (更新时间)

## 测试方法

### 方法1：通过创建学校接口测试

```bash
POST /admin/organization/createSchool
Content-Type: application/json

{
    "name": "测试学校",
    "code": "TEST001",
    "type": 1,
    "level": 1,
    "phone": "010-12345678",
    "email": "<EMAIL>",
    "address": "北京市朝阳区测试路123号",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "location": "测试位置"
}
```

### 方法2：通过测试接口测试

```bash
POST /datasync/test-school-sync
Content-Type: application/json

{
    "id": 999,
    "name": "测试学校",
    "code": "TEST001",
    "type": 1,
    "level": 1,
    "phone": "010-12345678",
    "email": "<EMAIL>",
    "address": "北京市朝阳区测试路123号",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "location": "测试位置"
}
```

### 方法3：使用 curl 命令测试

```bash
curl -X POST http://your-domain/datasync/test-school-sync \
  -H "Content-Type: application/json" \
  -d '{
    "id": 999,
    "name": "测试学校",
    "code": "TEST001",
    "type": 1,
    "level": 1,
    "phone": "010-12345678",
    "email": "<EMAIL>",
    "address": "北京市朝阳区测试路123号",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "location": "测试位置"
  }'
```

## 预期结果

### 成功响应示例

```json
{
    "status": "success",
    "code": 200,
    "message": "学校数据同步测试完成",
    "data": {
        "test_school_data": {
            "id": 999,
            "name": "测试学校",
            "code": "TEST001",
            "type": 1,
            "level": 1,
            "phone": "010-12345678",
            "email": "<EMAIL>",
            "address": "北京市朝阳区测试路123号",
            "status": 1
        },
        "additional_data": {
            "add_time": "2024-01-01 12:00:00",
            "province": "北京市",
            "city": "北京市",
            "district": "朝阳区",
            "buy_modules": "01,02,03,04,06,07,08,09,10,11,12,13,14,16,120,21,22,23,24,25,26,27,28,29,30,2802",
            "location": "测试位置"
        },
        "sync_result": {
            "success": true,
            "sync_school_id": 999,
            "message": "学校数据同步成功"
        }
    }
}
```

### 数据库验证

同步成功后，在 `ysy_test.ysy_school` 表中应该能看到：

```sql
SELECT * FROM ysy_school WHERE id = 999;
```

预期结果：
- `id`: 999
- `school_name`: "测试学校"
- `name`: "测试学校"
- `code`: "TEST001"
- `province`: "北京市"
- `city`: "北京市"
- `district`: "朝阳区"
- `buy_modules`: "01,02,03,04,06,07,08,09,10,11,12,13,14,16,120,21,22,23,24,25,26,27,28,29,30,2802"
- `original_id`: 999

## 日志检查

查看同步日志：
```bash
tail -f storage/logs/laravel.log | grep "学校数据同步"
```

成功日志示例：
```
[2024-01-01 12:00:00] local.INFO: 学校数据同步结果 {"school_id":999,"success":true,"message":"学校数据同步成功"}
[2024-01-01 12:00:00] local.INFO: 学校数据同步成功 {"school_id":999,"sync_school_id":999}
```

## 故障排查

### 1. 同步开关未启用
检查 `.env` 文件中 `DATA_SYNC_ENABLED=true`

### 2. 数据库连接失败
检查同步数据库连接配置是否正确

### 3. 表不存在
确保 `ysy_school` 表已创建并包含所需字段

### 4. 字段映射错误
检查字段名称是否匹配

### 5. 权限问题
确保数据库用户有插入权限

## 注意事项

1. **只同步学校基础信息** - 校区、年级、班级需要单独同步
2. **使用原表ID** - 新表的 id 字段使用原表的 id 值
3. **包含学校名称字段** - 同时有 `school_name` 和 `name` 字段
4. **固定购买模块** - `buy_modules` 使用固定值
5. **错误隔离** - 同步失败不影响原有业务

## 下一步测试

学校同步测试成功后，可以继续测试：
- 校区数据同步
- 年级数据同步  
- 班级数据同步
- 学生数据异步同步
- 教师数据异步同步
