<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Psychassessment\PsychassessmentSurveyController;
use App\Http\Controllers\Psychassessment\PsychassessmentReportController;
use App\Http\Controllers\Psychassessment\PsychassessmentStatisticsController;
use App\Http\Controllers\Psychassessment\PsychassessmentFocusController;
use App\Http\Controllers\Psychassessment\PsychassessmentAppointmentRecordController;
use App\Http\Controllers\Psychassessment\PsychassessmentCounselorRecordController;
use App\Http\Controllers\Psychassessment\PsychassessmentConfigurationsController;
use App\Http\Controllers\Psychassessment\PsychassessmentPageController;
use App\Http\Controllers\Psychassessment\PsychassessmentLoadpdfController;

/*
|--------------------------------------------------------------------------
| Psychassessment Routes
|--------------------------------------------------------------------------
|
| 心理评估模块路由 - 基于原 ThinkPHP Psychassessment 模块重新实现
|
*/

// 需要认证的心理评估路由
Route::middleware(['auth:api'])->prefix('psychassessment')->group(function () {
    
    // 测评计划路由
    Route::prefix('survey')->group(function () {
        Route::match(['GET', 'POST', 'PUT', 'DELETE'], '/', [PsychassessmentSurveyController::class, 'survey'])->name('psychassessment.survey');
        Route::get('assess_list', [PsychassessmentSurveyController::class, 'assessList'])->name('psychassessment.survey.assess_list');
        Route::get('get_psychassessment_class_member', [PsychassessmentSurveyController::class, 'getPsychassessmentClassMember'])->name('psychassessment.survey.class_member');
        Route::get('get_member_complete_status', [PsychassessmentSurveyController::class, 'getMemberCompleteStatus'])->name('psychassessment.survey.member_complete_status');
        Route::get('get_student_psychassess', [PsychassessmentSurveyController::class, 'getStudentPsychassess'])->name('psychassessment.survey.student_psychassess');
        Route::get('get_detail', [PsychassessmentSurveyController::class, 'getDetail'])->name('psychassessment.survey.detail');
        Route::delete('batch_delete', [PsychassessmentSurveyController::class, 'batchDelete'])->name('psychassessment.survey.batch_delete');
        Route::put('update_status', [PsychassessmentSurveyController::class, 'updateStatus'])->name('psychassessment.survey.update_status');
        Route::post('copy', [PsychassessmentSurveyController::class, 'copy'])->name('psychassessment.survey.copy');
        Route::get('get_statistics', [PsychassessmentSurveyController::class, 'getStatistics'])->name('psychassessment.survey.statistics');
        Route::get('export', [PsychassessmentSurveyController::class, 'export'])->name('psychassessment.survey.export');
        Route::get('get_survey_types', [PsychassessmentSurveyController::class, 'getSurveyTypes'])->name('psychassessment.survey.types');
        Route::get('get_class_members', [PsychassessmentSurveyController::class, 'getClassMembers'])->name('psychassessment.survey.class_members');
    });

    // 个体报告路由
    Route::prefix('report')->group(function () {
        Route::get('/', [PsychassessmentReportController::class, 'report'])->name('psychassessment.report');
        Route::get('get_report_detail', [PsychassessmentReportController::class, 'getReportDetail'])->name('psychassessment.report.detail');
        Route::get('generate_pdf', [PsychassessmentReportController::class, 'generatePdf'])->name('psychassessment.report.generate_pdf');
        Route::get('get_report_list', [PsychassessmentReportController::class, 'getReportList'])->name('psychassessment.report.list');
        Route::post('batch_generate', [PsychassessmentReportController::class, 'batchGenerate'])->name('psychassessment.report.batch_generate');
        Route::delete('delete_report', [PsychassessmentReportController::class, 'deleteReport'])->name('psychassessment.report.delete');
        Route::get('get_report_statistics', [PsychassessmentReportController::class, 'getReportStatistics'])->name('psychassessment.report.statistics');
        Route::get('preview_report', [PsychassessmentReportController::class, 'previewReport'])->name('psychassessment.report.preview');
        Route::post('share_report', [PsychassessmentReportController::class, 'shareReport'])->name('psychassessment.report.share');
        Route::get('get_report_template', [PsychassessmentReportController::class, 'getReportTemplate'])->name('psychassessment.report.template');
        Route::put('update_report_template', [PsychassessmentReportController::class, 'updateReportTemplate'])->name('psychassessment.report.update_template');
    });

    // 统计分析路由
    Route::prefix('statistics')->group(function () {
        Route::get('get_member_score_list', [PsychassessmentStatisticsController::class, 'getMemberScoreList'])->name('psychassessment.statistics.member_score_list');
        Route::get('get_statistic_analysis', [PsychassessmentStatisticsController::class, 'getStatisticAnalysis'])->name('psychassessment.statistics.analysis');
        Route::get('get_class_statistics', [PsychassessmentStatisticsController::class, 'getClassStatistics'])->name('psychassessment.statistics.class');
        Route::get('get_grade_statistics', [PsychassessmentStatisticsController::class, 'getGradeStatistics'])->name('psychassessment.statistics.grade');
        Route::get('get_school_statistics', [PsychassessmentStatisticsController::class, 'getSchoolStatistics'])->name('psychassessment.statistics.school');
        Route::get('get_dimension_analysis', [PsychassessmentStatisticsController::class, 'getDimensionAnalysis'])->name('psychassessment.statistics.dimension');
        Route::get('get_warning_level_statistics', [PsychassessmentStatisticsController::class, 'getWarningLevelStatistics'])->name('psychassessment.statistics.warning_level');
        Route::get('get_abnormal_statistics', [PsychassessmentStatisticsController::class, 'getAbnormalStatistics'])->name('psychassessment.statistics.abnormal');
        Route::get('export_statistics', [PsychassessmentStatisticsController::class, 'exportStatistics'])->name('psychassessment.statistics.export');
        Route::get('get_trend_analysis', [PsychassessmentStatisticsController::class, 'getTrendAnalysis'])->name('psychassessment.statistics.trend');
        Route::get('get_comparison_analysis', [PsychassessmentStatisticsController::class, 'getComparisonAnalysis'])->name('psychassessment.statistics.comparison');
        Route::get('get_distribution_statistics', [PsychassessmentStatisticsController::class, 'getDistributionStatistics'])->name('psychassessment.statistics.distribution');
        Route::get('get_correlation_analysis', [PsychassessmentStatisticsController::class, 'getCorrelationAnalysis'])->name('psychassessment.statistics.correlation');
        Route::get('get_comprehensive_report', [PsychassessmentStatisticsController::class, 'getComprehensiveReport'])->name('psychassessment.statistics.comprehensive');
    });

    // 重点关注路由
    Route::prefix('focus')->group(function () {
        Route::match(['GET', 'POST', 'DELETE'], '/', [PsychassessmentFocusController::class, 'focus'])->name('psychassessment.focus');
        Route::get('get_focus_detail', [PsychassessmentFocusController::class, 'getFocusDetail'])->name('psychassessment.focus.detail');
        Route::post('batch_add', [PsychassessmentFocusController::class, 'batchAdd'])->name('psychassessment.focus.batch_add');
        Route::delete('batch_delete', [PsychassessmentFocusController::class, 'batchDelete'])->name('psychassessment.focus.batch_delete');
        Route::put('update_focus', [PsychassessmentFocusController::class, 'updateFocus'])->name('psychassessment.focus.update');
        Route::get('get_focus_statistics', [PsychassessmentFocusController::class, 'getFocusStatistics'])->name('psychassessment.focus.statistics');
        Route::get('export_focus', [PsychassessmentFocusController::class, 'exportFocus'])->name('psychassessment.focus.export');
        Route::get('get_reason_categories', [PsychassessmentFocusController::class, 'getReasonCategories'])->name('psychassessment.focus.reason_categories');
        Route::get('get_student_focus_history', [PsychassessmentFocusController::class, 'getStudentFocusHistory'])->name('psychassessment.focus.student_history');
        Route::post('add_follow_up', [PsychassessmentFocusController::class, 'addFollowUp'])->name('psychassessment.focus.add_follow_up');
        Route::get('get_follow_up_list', [PsychassessmentFocusController::class, 'getFollowUpList'])->name('psychassessment.focus.follow_up_list');
        Route::put('update_status', [PsychassessmentFocusController::class, 'updateStatus'])->name('psychassessment.focus.update_status');
    });

    // 学生预约记录路由
    Route::prefix('appointment_record')->group(function () {
        Route::match(['GET', 'POST', 'PUT', 'DELETE'], '/', [PsychassessmentAppointmentRecordController::class, 'appointmentRecord'])->name('psychassessment.appointment_record');
        Route::get('get_appointment_detail', [PsychassessmentAppointmentRecordController::class, 'getAppointmentDetail'])->name('psychassessment.appointment_record.detail');
        Route::get('get_available_time', [PsychassessmentAppointmentRecordController::class, 'getAvailableTime'])->name('psychassessment.appointment_record.available_time');
        Route::put('confirm_appointment', [PsychassessmentAppointmentRecordController::class, 'confirmAppointment'])->name('psychassessment.appointment_record.confirm');
        Route::put('cancel_appointment', [PsychassessmentAppointmentRecordController::class, 'cancelAppointment'])->name('psychassessment.appointment_record.cancel');
        Route::put('complete_consultation', [PsychassessmentAppointmentRecordController::class, 'completeConsultation'])->name('psychassessment.appointment_record.complete');
        Route::get('get_teacher_statistics', [PsychassessmentAppointmentRecordController::class, 'getTeacherStatistics'])->name('psychassessment.appointment_record.teacher_statistics');
        Route::get('get_student_history', [PsychassessmentAppointmentRecordController::class, 'getStudentHistory'])->name('psychassessment.appointment_record.student_history');
        Route::post('batch_process', [PsychassessmentAppointmentRecordController::class, 'batchProcess'])->name('psychassessment.appointment_record.batch_process');
        Route::get('export_appointments', [PsychassessmentAppointmentRecordController::class, 'exportAppointments'])->name('psychassessment.appointment_record.export');
        Route::get('get_appointment_types', [PsychassessmentAppointmentRecordController::class, 'getAppointmentTypes'])->name('psychassessment.appointment_record.types');
        Route::get('get_counselors', [PsychassessmentAppointmentRecordController::class, 'getCounselors'])->name('psychassessment.appointment_record.counselors');
        Route::post('send_reminder', [PsychassessmentAppointmentRecordController::class, 'sendReminder'])->name('psychassessment.appointment_record.send_reminder');
    });

    // 辅导记录路由
    Route::prefix('counselor_record')->group(function () {
        Route::match(['GET', 'POST', 'PUT', 'DELETE'], '/', [PsychassessmentCounselorRecordController::class, 'counselorRecord'])->name('psychassessment.counselor_record');
        Route::get('get_record_detail', [PsychassessmentCounselorRecordController::class, 'getRecordDetail'])->name('psychassessment.counselor_record.detail');
        Route::get('get_student_history', [PsychassessmentCounselorRecordController::class, 'getStudentHistory'])->name('psychassessment.counselor_record.student_history');
        Route::get('get_teacher_records', [PsychassessmentCounselorRecordController::class, 'getTeacherRecords'])->name('psychassessment.counselor_record.teacher_records');
        Route::delete('batch_delete', [PsychassessmentCounselorRecordController::class, 'batchDelete'])->name('psychassessment.counselor_record.batch_delete');
        Route::get('export_records', [PsychassessmentCounselorRecordController::class, 'exportRecords'])->name('psychassessment.counselor_record.export');
        Route::get('get_statistics', [PsychassessmentCounselorRecordController::class, 'getStatistics'])->name('psychassessment.counselor_record.statistics');
        Route::get('get_counseling_types', [PsychassessmentCounselorRecordController::class, 'getCounselingTypes'])->name('psychassessment.counselor_record.counseling_types');
        Route::get('get_effect_evaluation', [PsychassessmentCounselorRecordController::class, 'getEffectEvaluation'])->name('psychassessment.counselor_record.effect_evaluation');
        Route::post('add_effect_evaluation', [PsychassessmentCounselorRecordController::class, 'addEffectEvaluation'])->name('psychassessment.counselor_record.add_effect_evaluation');
        Route::get('get_counseling_plan', [PsychassessmentCounselorRecordController::class, 'getCounselingPlan'])->name('psychassessment.counselor_record.counseling_plan');
        Route::post('create_counseling_plan', [PsychassessmentCounselorRecordController::class, 'createCounselingPlan'])->name('psychassessment.counselor_record.create_counseling_plan');
        Route::put('update_counseling_plan', [PsychassessmentCounselorRecordController::class, 'updateCounselingPlan'])->name('psychassessment.counselor_record.update_counseling_plan');
        Route::get('get_templates', [PsychassessmentCounselorRecordController::class, 'getTemplates'])->name('psychassessment.counselor_record.templates');
        Route::post('save_template', [PsychassessmentCounselorRecordController::class, 'saveTemplate'])->name('psychassessment.counselor_record.save_template');
    });

    // 可预约时间配置路由
    Route::prefix('configurations')->group(function () {
        Route::match(['GET', 'POST', 'PUT', 'DELETE'], '/', [PsychassessmentConfigurationsController::class, 'configurations'])->name('psychassessment.configurations');
        Route::get('get_configuration_detail', [PsychassessmentConfigurationsController::class, 'getConfigurationDetail'])->name('psychassessment.configurations.detail');
        Route::delete('batch_delete', [PsychassessmentConfigurationsController::class, 'batchDelete'])->name('psychassessment.configurations.batch_delete');
        Route::get('get_available_time_slots', [PsychassessmentConfigurationsController::class, 'getAvailableTimeSlots'])->name('psychassessment.configurations.available_time_slots');
        Route::post('copy_configuration', [PsychassessmentConfigurationsController::class, 'copyConfiguration'])->name('psychassessment.configurations.copy');
        Route::get('get_repeat_types', [PsychassessmentConfigurationsController::class, 'getRepeatTypes'])->name('psychassessment.configurations.repeat_types');
        Route::post('generate_repeat_configurations', [PsychassessmentConfigurationsController::class, 'generateRepeatConfigurations'])->name('psychassessment.configurations.generate_repeat');
        Route::get('get_teacher_work_time', [PsychassessmentConfigurationsController::class, 'getTeacherWorkTime'])->name('psychassessment.configurations.teacher_work_time');
        Route::post('set_teacher_work_time', [PsychassessmentConfigurationsController::class, 'setTeacherWorkTime'])->name('psychassessment.configurations.set_teacher_work_time');
        Route::get('get_holiday_configurations', [PsychassessmentConfigurationsController::class, 'getHolidayConfigurations'])->name('psychassessment.configurations.holiday_configurations');
        Route::post('set_holiday', [PsychassessmentConfigurationsController::class, 'setHoliday'])->name('psychassessment.configurations.set_holiday');
        Route::get('get_appointment_statistics', [PsychassessmentConfigurationsController::class, 'getAppointmentStatistics'])->name('psychassessment.configurations.appointment_statistics');
        Route::get('export_configurations', [PsychassessmentConfigurationsController::class, 'exportConfigurations'])->name('psychassessment.configurations.export');
        Route::post('import_configurations', [PsychassessmentConfigurationsController::class, 'importConfigurations'])->name('psychassessment.configurations.import');
        Route::get('get_configuration_templates', [PsychassessmentConfigurationsController::class, 'getConfigurationTemplates'])->name('psychassessment.configurations.templates');
        Route::post('apply_template', [PsychassessmentConfigurationsController::class, 'applyTemplate'])->name('psychassessment.configurations.apply_template');
    });

    // 页面路由
    Route::prefix('page')->group(function () {
        Route::get('index', [PsychassessmentPageController::class, 'index'])->name('psychassessment.page.index');
        Route::get('report_detail', [PsychassessmentPageController::class, 'reportDetail'])->name('psychassessment.page.report_detail');
        Route::get('get_page_config', [PsychassessmentPageController::class, 'getPageConfig'])->name('psychassessment.page.config');
        Route::put('update_page_config', [PsychassessmentPageController::class, 'updatePageConfig'])->name('psychassessment.page.update_config');
        Route::get('get_navigation', [PsychassessmentPageController::class, 'getNavigation'])->name('psychassessment.page.navigation');
        Route::get('get_statistics_overview', [PsychassessmentPageController::class, 'getStatisticsOverview'])->name('psychassessment.page.statistics_overview');
        Route::get('get_recent_activities', [PsychassessmentPageController::class, 'getRecentActivities'])->name('psychassessment.page.recent_activities');
        Route::get('get_quick_actions', [PsychassessmentPageController::class, 'getQuickActions'])->name('psychassessment.page.quick_actions');
        Route::get('get_notifications', [PsychassessmentPageController::class, 'getNotifications'])->name('psychassessment.page.notifications');
        Route::put('mark_notification_read', [PsychassessmentPageController::class, 'markNotificationRead'])->name('psychassessment.page.mark_notification_read');
        Route::get('get_user_preferences', [PsychassessmentPageController::class, 'getUserPreferences'])->name('psychassessment.page.user_preferences');
        Route::put('update_user_preferences', [PsychassessmentPageController::class, 'updateUserPreferences'])->name('psychassessment.page.update_user_preferences');
        Route::get('get_help_documents', [PsychassessmentPageController::class, 'getHelpDocuments'])->name('psychassessment.page.help_documents');
        Route::get('search', [PsychassessmentPageController::class, 'search'])->name('psychassessment.page.search');
        Route::get('get_announcements', [PsychassessmentPageController::class, 'getAnnouncements'])->name('psychassessment.page.announcements');
        Route::get('get_version_info', [PsychassessmentPageController::class, 'getVersionInfo'])->name('psychassessment.page.version_info');
    });

    // PDF生成路由
    Route::prefix('loadpdf')->group(function () {
        Route::get('personal_pdf', [PsychassessmentLoadpdfController::class, 'personalPdf'])->name('psychassessment.loadpdf.personal_pdf');
        Route::get('individual', [PsychassessmentLoadpdfController::class, 'individual'])->name('psychassessment.loadpdf.individual');
        Route::post('batch_individual', [PsychassessmentLoadpdfController::class, 'batchIndividual'])->name('psychassessment.loadpdf.batch_individual');
        Route::get('class_report', [PsychassessmentLoadpdfController::class, 'classReport'])->name('psychassessment.loadpdf.class_report');
        Route::get('grade_report', [PsychassessmentLoadpdfController::class, 'gradeReport'])->name('psychassessment.loadpdf.grade_report');
        Route::get('school_report', [PsychassessmentLoadpdfController::class, 'schoolReport'])->name('psychassessment.loadpdf.school_report');
        Route::get('get_pdf_status', [PsychassessmentLoadpdfController::class, 'getPdfStatus'])->name('psychassessment.loadpdf.pdf_status');
        Route::post('cancel_pdf_generation', [PsychassessmentLoadpdfController::class, 'cancelPdfGeneration'])->name('psychassessment.loadpdf.cancel_pdf_generation');
        Route::get('get_pdf_templates', [PsychassessmentLoadpdfController::class, 'getPdfTemplates'])->name('psychassessment.loadpdf.pdf_templates');
        Route::put('update_pdf_template', [PsychassessmentLoadpdfController::class, 'updatePdfTemplate'])->name('psychassessment.loadpdf.update_pdf_template');
        Route::get('preview_pdf', [PsychassessmentLoadpdfController::class, 'previewPdf'])->name('psychassessment.loadpdf.preview_pdf');
    });
});

// 无认证的测试路由
Route::get('psychassessment/survey/test', function () {
    return response()->json([
        'status' => 'success',
        'code' => 200,
        'message' => '心理评估-测评计划接口测试成功',
        'data' => [
            'timestamp' => now()->toDateTimeString(),
            'test_mode' => true,
            'available_endpoints' => [
                'POST /psychassessment/survey' => '创建测评计划',
                'GET /psychassessment/survey' => '获取测评计划列表',
                'PUT /psychassessment/survey' => '更新测评计划',
                'DELETE /psychassessment/survey' => '删除测评计划',
                'GET /psychassessment/survey/assess_list' => '获取心理评估列表'
            ]
        ]
    ]);
});

Route::get('psychassessment/report/test', function () {
    return response()->json([
        'status' => 'success',
        'code' => 200,
        'message' => '心理评估-个体报告接口测试成功',
        'data' => [
            'timestamp' => now()->toDateTimeString(),
            'test_mode' => true,
            'available_endpoints' => [
                'GET /psychassessment/report' => '获取个体报告',
                'GET /psychassessment/report/get_report_detail' => '获取报告详情',
                'GET /psychassessment/report/generate_pdf' => '生成报告PDF',
                'GET /psychassessment/report/get_report_list' => '获取报告列表'
            ]
        ]
    ]);
});

Route::get('psychassessment/statistics/test', function () {
    return response()->json([
        'status' => 'success',
        'code' => 200,
        'message' => '心理评估-统计分析接口测试成功',
        'data' => [
            'timestamp' => now()->toDateTimeString(),
            'test_mode' => true,
            'available_endpoints' => [
                'GET /psychassessment/statistics/get_member_score_list' => '获取测评计划内学生列表',
                'GET /psychassessment/statistics/get_statistic_analysis' => '获取完成数据和维度与code对应关系',
                'GET /psychassessment/statistics/get_class_statistics' => '获取班级统计数据',
                'GET /psychassessment/statistics/get_grade_statistics' => '获取年级统计数据'
            ]
        ]
    ]);
});

Route::get('psychassessment/focus/test', function () {
    return response()->json([
        'status' => 'success',
        'code' => 200,
        'message' => '心理评估-重点关注接口测试成功',
        'data' => [
            'timestamp' => now()->toDateTimeString(),
            'test_mode' => true,
            'available_endpoints' => [
                'POST /psychassessment/focus' => '添加重点关注',
                'GET /psychassessment/focus' => '查询重点关注名单',
                'DELETE /psychassessment/focus' => '删除重点关注记录',
                'GET /psychassessment/focus/get_focus_detail' => '获取重点关注详情'
            ]
        ]
    ]);
});

Route::get('psychassessment/loadpdf/test', function () {
    return response()->json([
        'status' => 'success',
        'code' => 200,
        'message' => '心理评估-PDF生成接口测试成功',
        'data' => [
            'timestamp' => now()->toDateTimeString(),
            'test_mode' => true,
            'available_endpoints' => [
                'GET /psychassessment/loadpdf/individual' => '个人报告下载',
                'GET /psychassessment/loadpdf/personal_pdf' => '个人报告PDF页面',
                'POST /psychassessment/loadpdf/batch_individual' => '批量生成个人报告PDF',
                'GET /psychassessment/loadpdf/class_report' => '生成班级报告PDF'
            ]
        ]
    ]);
});
