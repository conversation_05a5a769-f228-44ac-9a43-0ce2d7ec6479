<?php

namespace App\Models\School\Assessment\Question;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssessmentCompetencyQuestion extends Model
{
    use HasFactory;
    protected $table = 'assessment_competency_questions';
    
    protected $fillable = [
        'assessment_id',
        'old_question_id',
        'content',
        'number',
        'dimension_name',
        'dimension_code',
        'options',
        'correct',
    ];

    protected $casts = [
        'options' => 'json',
    ];

    public $timestamps = false;
}
