<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// 测试路由
Route::get('/test', function () {
    return response()->json([
        'status' => 'success',
        'message' => '<PERSON><PERSON> is working!',
        'timestamp' => now()
    ]);
});

// 直接在 web.php 中测试 evaluation 路由
Route::get('/evaluation/test-direct', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'Direct evaluation route working!',
        'timestamp' => now()
    ]);
});
