<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */
namespace app\psychassessment\logic;
use app\psychassessment\model\AppointmentTimeConfig as appointmenttime_Config_model;
use app\psychassessment\model\AppointmentTime as appointmenttime_model;
use think\Config;
use think\Db;
use think\Cache;

class Configurations
{
    protected $AppModel;
    protected $user;

    public function __construct()
    {
        $this->user = get_user();
    }

    public function add()
    {
        //权限判断
        $start_date  = input('start_date'); // 开始日期
        $end_date    = input('end_date'); // 结束日期
        $start_time  = input('start_time'); // 开始时间

        $end_time    = input('end_time'); // 结束时间
        $repeat_type = input('repeat_type'); // 重复类型
        $repeat_daysofweek = input('repeat_daysofweek'); // 重复日期，周一，周二
        $teacher_id = input('teacher_id'); // 重复类型
        $school_id = $this->user['school_id'];
        if(empty($teacher_id)){
            $teacher_id = db('teacher')->where(['member_id'=>$this->user['id'], 'step'=>0])->value('id');
        }
        // 生成日期
        $dates =  $this->generateDates($start_date, $end_date, $repeat_type, $repeat_daysofweek);
        $startSegment = calculateSegment($start_time,48);
        $endSegment = calculateSegment($end_time,48);
        $get_appointmenttime_model = new appointmenttime_model();
        $overlapCount = $get_appointmenttime_model::where(function ($query) use ($startSegment, $endSegment) {
            $query->where(function ($query) use ($startSegment, $endSegment) {
                // 新时间段的开始时间在任何现有时间段内
                $query->where('start_time', '<', $endSegment)
                    ->where('start_time', '>', $startSegment);
            })->whereOr(function ($query) use ($startSegment, $endSegment) {
                // 新时间段的结束时间在任何现有时间段内
                $query->where('end_time', '>', $startSegment)
                    ->where('end_time', '<', $endSegment);
            })->whereOr(function ($query) use ($startSegment, $endSegment) {
                // 现有时间段完全包含新时间段
                $query->where('start_time', '<=', $startSegment)
                    ->where('end_time', '>=', $endSegment);
            });
        })
            ->whereIn('date', $dates)
            ->where('status', 0)
            ->where('teacher_id', $teacher_id)
            ->count();

        if ($overlapCount > 0) {
            apiReturn([],'存在冲突的时间段，不能添加！',-1);
        } else {
            $model = new appointmenttime_Config_model();
            $appointmenttime_model = new appointmenttime_model();
            $model->save([
                'start_date' => $start_date,
                'end_date' => $end_date,
                'repeat_type' => $repeat_type,
                'repeat_daysofweek' => $repeat_daysofweek,
                'school_id' => $school_id,
                'teacher_id' => $teacher_id,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' =>  date('Y-m-d H:i:s'),
            ]);
            $id = $model->id;

            foreach ($dates as $key => $date) {
                $dates[$key] = array(
                    'date' => $date,
                    'config_id' => $id,
                    "available_time_slots" =>  $startSegment.','.$endSegment,
                    "school_id" => $school_id,
                    "booked" => "0",
                    "start_time" =>$startSegment,
                    "end_time" => $endSegment,
                    'teacher_id' => $teacher_id,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' =>  date('Y-m-d H:i:s'),
                );
            }
            // $newColumn = ['config_id' => $id,'available_time_slots' => $startSegment.','.$endSegment,'school_id' =>$school_id,'booked' =>'0'];

            $appointmenttime_model->saveAll($dates);
            apiReturn($id);
        }


    }

    public function edit()
    {

        $request_body =  file_get_contents('php://input');
        $data = json_decode($request_body, true);
        $start_date  = []; // 开始日期
        $end_date    = []; // 结束日期
        $start_time  = []; // 开始时间
        $end_time    = []; // 结束时间
        $update=[];
        $teacher_id = db('teacher')->where(['member_id'=>$this->user['id'], 'step'=>0])->value('id');
        if (array_key_exists('start_date', $data)) {
            $start_date=$data['start_date'];
        }
        if (array_key_exists('end_date', $data)) {
            $end_date=$data['end_date'];
        }
        if (array_key_exists('start_time', $data)) {
            $start_time=$data['start_time'];
        }
        if (array_key_exists('end_time', $data)) {
            $end_time=$data['end_time'];
        }
        if (array_key_exists('config_id', $data)) {
            $config_id=$data['config_id'];
        }
        if (array_key_exists('update', $data)) {
            $update=$data['update'];
        }
        $id  = input('id');
        $startSegment = calculateSegment($start_time,48);
        $endSegment = calculateSegment($end_time,48);
        $get_appointmenttime_model = new appointmenttime_model();
        if($update==0){
            $where['id'] = ['not in', [$id]];;
        }else{
            $where['config_id'] =  ['not in', [input('config_id')]];
        }
        $overlapCount = $get_appointmenttime_model::where(function ($query) use ($startSegment, $endSegment) {
            $query->where(function ($query) use ($startSegment, $endSegment) {
                // 新时间段的开始时间在任何现有时间段内
                $query->where('start_time', '<', $endSegment)
                    ->where('start_time', '>', $startSegment);
            })->whereOr(function ($query) use ($startSegment, $endSegment) {
                // 新时间段的结束时间在任何现有时间段内
                $query->where('end_time', '>', $startSegment)
                    ->where('end_time', '<', $endSegment);
            })->whereOr(function ($query) use ($startSegment, $endSegment) {
                // 现有时间段完全包含新时间段
                $query->where('start_time', '<=', $startSegment)
                    ->where('end_time', '>=', $endSegment);
            });
        })
            ->where($where)
            ->where('teacher_id', $teacher_id)
            ->where('status', 0)
            ->where('date', 'between', [$start_date, $end_date])
            ->count();
        if ($overlapCount > 0) {
            apiReturn([],'存在冲突的时间段，不能添加！',-1);
        } else {
            $appointmenttime_model = new appointmenttime_model();
            if ($update == 0) {
                $appointmenttime_model->save([
                    'config_id' => '0',
                    "available_time_slots" => $startSegment . ',' . $endSegment,
                    "booked" => "0",
                    'updated_at' => date('Y-m-d H:i:s'),
                ], ['id' => input('id')]);
            } else {
                $appointmenttime_model->where('date', 'between', [$start_date, $end_date])
                    ->where('config_id', input('config_id'))
                    ->update([
                        "available_time_slots" => $startSegment . ',' . $endSegment,
                        "booked" => "0",
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]);
            }
            apiReturn($id);
        }
    }

    public function del()
    {
        $start_date  = input('start_date'); // 开始日期
        $end_date    = input('end_date'); // 结束日期
        $id  = input('id');
        $config_id = input('config_id');
        $update = input('update');
        $appointmenttime_model = new appointmenttime_model();
        if($update==0){
            $appointmenttime_model->save([
                'status' => '-1',
            ],['id' => input('id')]);
        }else{
            $appointmenttime_model->where('date', 'between', [$start_date, $end_date])
                ->where('config_id', input('config_id'))
                ->update([
                    "status" => "-1",
                ]);
        }
        apiReturn($id);
        $appointmenttime_model = new appointmenttime_model();
        $appointmenttime_model->save([
            'status' => '-1',
        ], ['id' => input('id')]);
        //软删除
        apiReturn(input('id'));
    }


    public function get_list()
    {
        $startDate  = input('start_date'); // 开始日期
        $endDate    = input('end_date'); // 结束日期

        $teacher_id= input('teacher_id'); //
        $dates= input('dates'); //
        if (!empty($teacher_id)){
            $teacher_id= input('teacher_id'); //
        }else{
            $teacher_id= db('teacher')->where(['member_id'=>$this->user['id'],'step'=>0])->value('id'); // 获取页码，默认为1
        }
        $where['t.status'] = 0;
        $where['t.teacher_id'] = $teacher_id;
        if($startDate && $endDate) $where['t.date'] = ['between',[$startDate,$endDate]];
        $model = new appointmenttime_model();
        $list = $model->alias('t')
            ->join('ysy_psychassessment_appointment_time_config a','t.config_id = a.id','LEFT')
            ->field('a.start_date,a.end_date,a.repeat_type,a.repeat_daysofweek,t.id,t.date,t.available_time_slots,t.config_id,t.booked')
            ->where($where)
            ->select();
       // print_r($model->getLastSql());die;
        $list = to_arr($list);
        $date = array_unique(array_column($list,'date'));
        $new_data = [];
        foreach ($date as $k =>$v){
            foreach ($list as $key => $value){
                $time = explode(',',$value['available_time_slots']);
                $value['start_date'] =date('Y-m-d', strtotime($value['start_date']));
                $value['end_date'] =date('Y-m-d', strtotime($value['end_date']));
                $value['start_time'] = reverseCalculateTime($time[0],48);
                $value['end_time'] = reverseCalculateTime($time[1],48);
                if($v == $value['date']){
                    $new_data[$k]['date'] = $v;
                    $new_data[$k]['phases'][] = $value;
                }
            }
        }

        foreach ($new_data as &$date) {
            usort($date['phases'], [$this, 'sortByStartTime']);
        }
        
        apiReturn(array_values($new_data));
    }

public function sortByStartTime($a, $b) {
        $timeA = strtotime($a['start_time']);
        $timeB = strtotime($b['start_time']);
        return $timeA - $timeB;
    }
    // 生成日期和时间段
    public function generateDates($start_date, $end_date, $repeat_type, $repeat_daysofweek)
    {
        $daysOfWeek = explode(',', $repeat_daysofweek);
        $dates = [];
        $start = new \DateTime($start_date);
        $end = new \DateTime($end_date);
        switch ($repeat_type) {
            case 0: // 不重复
                $dates[] = $start->format('Y-m-d');

                break;
            case 1: // 每天重复
                $current = new \DateTime($start->format('Y-m-d'));;

                while ($current <= $end) {
                    $dates[] = $current->format('Y-m-d'); // 存储日期字符串
                    $current->modify('+1 day'); // 递增日期
                }
                break;
            case 2: // 每周重复
                $repeat_daysofweek = explode(',', $repeat_daysofweek);
                $current = new \DateTime($start->format('Y-m-d'));
                while ($current <= $end) {
                    $weekday = $current->format('N'); // 获取当前星期中的某一天

                    if (in_array($weekday, $repeat_daysofweek)) {
                        $dates[] = $current->format('Y-m-d'); // 存储日期字符串
                    }
                    $current->modify('+1 day'); // 递增日期
                }
                break;
            case 3: // 每两周重复
                $repeat_daysofweek = explode(',', $repeat_daysofweek);
                $current = new \DateTime($start->format('Y-m-d'));
                $weekCount = 0;
                while ($current <= $end) {
                    $weekday = $current->format('N'); // 获取当前星期中的某一天
                    if (in_array($weekday, $repeat_daysofweek) && $weekCount % 2 == 0) {
                        $dates[] = $current->format('Y-m-d'); // 存储日期字符串
                    }
                    $current->modify('+1 day'); // 递增日期
                    if ($weekday == 7) { // 如果是周日，增加周数计数
                        $weekCount++;
                    }
                }

                break;
            case 4: // 自定义
                $repeat_daysofweek = explode(',', $repeat_daysofweek);
                $current = new \DateTime($start->format('Y-m-d'));
                while ($current <= $end) {
                    $weekday = $current->format('N'); // 获取当前星期中的某一天

                    if (in_array($weekday, $repeat_daysofweek)) {
                        $dates[] = $current->format('Y-m-d'); // 存储日期字符串
                    }
                    $current->modify('+1 day'); // 递增日期
                }
                break;

            default:
                // 处理错误或默认情况
                break;
        }

        return $dates;
    }

    public function dates()
    {
        $dates= input('dates');
        $teacher_id= db('teacher')->where(['member_id'=>$this->user['id'],'step'=>0])->value('id'); // 获取页码，默认为1
        $where['t.status'] = 0;
        $where['t.teacher_id'] = $teacher_id;
        $model = new appointmenttime_model();
        $list = $model->alias('t')
            ->join('ysy_psychassessment_appointment_time_config a','t.config_id = a.id','LEFT')
            ->field('a.start_date,a.end_date,a.repeat_type,a.repeat_daysofweek,t.id,t.date,t.available_time_slots,t.config_id,t.booked')
            ->where($where)
            ->where('t.date', 'in', $dates)
            ->select();
        $list = to_arr($list);
        $date = array_unique(array_column($list,'date'));
        $new_data = [];
        foreach ($date as $k =>$v){
            foreach ($list as $key => $value){
                $time = explode(',',$value['available_time_slots']);
                $value['start_date'] =date('Y-m-d', strtotime($value['start_date']));
                $value['end_date'] =date('Y-m-d', strtotime($value['end_date']));
                $value['start_time'] = reverseCalculateTime($time[0],48);
                $value['end_time'] = reverseCalculateTime($time[1],48);
                if($v == $value['date']){
                    $new_data[$k]['date'] = $v;
                    $new_data[$k]['phases'][] = $value;
                }
            }
        }
        foreach ($new_data as &$date) {
            usort($date['phases'], [$this, 'sortByStartTime']);
        }

        apiReturn(array_values($new_data));
    }
}