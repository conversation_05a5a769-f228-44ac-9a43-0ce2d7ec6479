<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */
namespace app\psychassessment\logic;
use app\psychassessment\model\AppointmentRecord as AppointmentRecordModel;
use app\psychassessment\model\AppointmentTime as AppointmentTimeModel;

class AppointmentRecord
{
    protected $AppModel;
    protected $TimeModel;
    protected $user;

    public function __construct()
    {
        $this->AppModel = new AppointmentRecordModel();
        $this->TimeModel = new AppointmentTimeModel();
        $this->user = get_user();
    }

    public function add()
    {
        //权限判断
        $data = input('post.');
        $data['school_id'] = $this->user['school_id'];
        $student_id = db('student')->field('id')->where(['member_id'=>$this->user['id']])->order('id desc')->find();
        $data['student_id'] = $student_id['id'];
        $data['member_id'] = $this->user['id'];
        $res = $this->AppModel->allowField(true)->save($data);
        if($res){
            $this->TimeModel->save([
                'booked' => '1',
            ], ['id' => $data['appointment_time_id']]);
        }
        apiReturn($res);
    }

    public function del()
    {
        $this->AppModel->save([
            'status' => '-1',
        ], ['id' => input('id')]);
        $appointment_time_id = $this->AppModel->where(['id' => input('id')])->value('appointment_time_id');
        if($appointment_time_id){
            $this->TimeModel->save([
                'booked' => '0',
            ], ['id' => $appointment_time_id]);
        }
        //软删除
        apiReturn(input('id'));
    }

    public function get_list()
    {
        $startDate           = input('start_date'); // 开始日期
        $endDate             = input('end_date'); // 结束日期
        $pageNumber          = input('page', 1); // 获取页码，默认为1
        $pageSize            = input('pagesize', 10); // 获取每页显示的记录数，默认为10
        $question_type       = input('question_type'); // 心理类型
        $grade_id            = input('grade_id');
        $class_id            = input('class_id');
        $name                = input('name'); // 学生姓名
        $status              = input('status'); // 预约状态0未辅导1已辅导
        $appointment_time_id = input('appointment_time_id'); // 老师设置的预约时间段id

        $where['a.status'] = 0;
        if($appointment_time_id){
            $where['a.appointment_time_id'] = $appointment_time_id;
        }else{
            //判断是老师还是学生
            switch ($this->user['role_source_id']){
                case 1:
                    $where['a.member_id']  = $this->user['id'];
                    break;
                case 3:
                    $where['a.teacher_id'] = db('teacher')->where(['member_id'=>$this->user['id'],'step'=>0])->value('id');
                    break;
            }

            if ($startDate && $endDate) $where['b.date'] = ['between', [$startDate, $endDate]];
            if ($question_type) $where['a.type'] = $question_type;
            if ($name) $where['student.name'] = ['like', '%' . $name . '%'];
            if($grade_id) $where['student.grade_id'] = ['in',$grade_id];
            if($class_id) $where['student.class_id'] = ['in',$class_id];
            if($name) $where['student.name|student.student_no'] = ['like','%'.$name.'%'];
            if($startDate && $endDate) $where['b.date'] = ['between',[$startDate,$endDate]];
            if (is_numeric($status)) $where['a.appointment_status'] = $status;
        }

        $list = $this->AppModel->alias('a')
            ->join('psychassessment_appointment_time b','a.appointment_time_id = b.id','left')
            ->join('student student','a.student_id = student.id')
            ->join('grade grade','student.grade_id = grade.id')
            ->join('class class','student.class_id = class.id')
            ->join('teacher teacher','a.teacher_id = teacher.id')
            ->field('b.date,b.available_time_slots,a.id as appointment_id,a.member_id,a.type,a.content,a.appointment_status,grade.name as grade_year,grade.grade_name,class.name as class_name,student.student_no,student.name as student_name,teacher.name as teacher_name')
            ->where($where)
            ->order('a.id desc')
            ->select();

        $list = to_arr($list);
        $type_to_psych = type_to_psych();
        foreach ($list as $ke => $val){
            $list[$ke]['psych_type'] = $type_to_psych[$val['type']] ?? '';
            if($val['available_time_slots']){
                $available_time_slots = explode(',',$val['available_time_slots']);
                $list[$ke]['appointment_time'] = reverseCalculateTime($available_time_slots[0],48) .'~'.reverseCalculateTime($available_time_slots[1],48);
            }
        }

        return pageing($list,$pageSize,$pageNumber);
    }

    public function teacher_info(){
        return db('teacher')->alias('tea')
            ->join('class cla','find_in_set(cla.id,tea.class_ids)')
            ->join('student stu','stu.class_id = cla.id')
            ->join('school sch','stu.school_id = sch.id')
            ->field('tea.id as teacher_id,tea.name as teacher_name,tea.gender as teacher_gender,sch.name as school_name')
            ->where(['stu.member_id'=>$this->user['id'],'stu.step'=>0,'tea.is_psych'=>1])
            ->group('tea.member_id')
            ->select();
    }
}