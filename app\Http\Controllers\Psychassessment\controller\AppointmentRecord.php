<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */
namespace app\psychassessment\controller;
use app\login\controller\ApiAuth;

/**
 * Class Questions
 * @package app\psychassessment\controller
 */
class AppointmentRecord extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->Record = new \app\psychassessment\service\AppointmentRecord();
    }
    /**
     * 模块：心理评估-学生预约记录
     * @SWG\Post(path="/psychassessment/appointment_record",
     *   tags={"心理评估-学生预约:appointment_record"},
     *   summary="学生预约",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="appointment_time_id",
     *     type="integer",
     *     description="老师时间段的id",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="student_id",
     *     type="integer",
     *     description="学生的student_id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="teacher_id",
     *     type="integer",
     *     description="老师的teacher_id",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="type",
     *     type="integer",
     *     description="心理类型，1情绪困扰2行为问题3学业问题4人际交往5个人发展",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="content",
     *     type="string",
     *     description="咨询内容",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="start_time",
     *     type="datetime",
     *     description="预约开始时间",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="end_time",
     *     type="datetime",
     *     description="预约结束时间",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="appointment_status",
     *     type="integer",
     *     description="预约状态",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="district_id",
     *     type="integer",
     *     description="校区ID",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="member_id",
     *     type="string",
     *     description="预约学生ID",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：心理评估-学生预约记录
     * @SWG\Delete(path="/psychassessment/appointment_record/{id}",
     *   tags={"心理评估-学生预约:appointment_record"},
     *   summary="删除记录",
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    /**
     * 模块：心理评估-学生预约记录
     * @SWG\Get(path="/psychassessment/appointment_record",
     *   tags={"心理评估-学生预约:appointment_record"},
     *   summary="记录查询",
     *   @SWG\Parameter(
     *     in="query",
     *     name="grade_id",
     *     type="string",
     *     description="年级id,多个用,拼接",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="class_id",
     *     type="string",
     *     description="班级id,多个用,拼接",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="start_date",
     *     type="string",
     *     description="预约开始时间",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="end_date",
     *     type="string",
     *     description="预约结束时间",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="question_type",
     *     type="integer",
     *     description="心理类型，1情绪困扰2行为问题3学业问题4人际交往5个人发展",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="name",
     *     type="string",
     *     description="学生姓名模糊搜索",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="status",
     *     type="integer",
     *     description="预约状态0未辅导1已辅导",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="appointment_time_id",
     *     type="integer",
     *     description="预约时间段id,此参数用来查看学生预约详情",
     *     required=false,
     *   ),
     *   description="数据说明",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    public function AppointmentRecord(){

        $data = $this->Record->hand_out();
        apiReturn($data);
    }

    /**
     * 模块：心理评估-学生预约记录
     * @SWG\Get(path="/psychassessment/teacher_info",
     *   tags={"心理评估-学生预约:appointment_record"},
     *   summary="老师信息",
     *   description="数据说明",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function teacher_info(){

        $data = $this->Record->teacher_info();
        apiReturn($data);
    }

}