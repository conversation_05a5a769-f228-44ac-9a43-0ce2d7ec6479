<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */
namespace app\psychassessment\controller;
use app\login\controller\ApiAuth;

/**
 * Class Questions
 * @package app\psychassessment\controller
 */
class Focus extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->focus = new \app\psychassessment\service\Focus();
    }
    /**
     * 模块：心理评估-重点关注
     * @SWG\Post(path="/psychassessment/focus",
     *   tags={"心理评估-重点关注名单:Focus"},
     *   summary="重点关注",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="student_ids",
     *     type="integer",
     *     description="学生的student_id,多个用逗号拼接",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="reason",
     *     type="string",
     *     description="加入重点关注原因",
     *     required=true,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：心理评估-重点关注
     * @SWG\Delete(path="/psychassessment/focus/{id}",
     *   tags={"心理评估-重点关注名单:Focus"},
     *   summary="删除记录",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="remove_reason",
     *     type="string",
     *     description="移除原因",
     *     required=true,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    /**
     * 模块：心理评估-重点关注
     * @SWG\Get(path="/psychassessment/focus",
     *   tags={"心理评估-重点关注名单:Focus"},
     *   summary="重点关注名单查询",
     *   @SWG\Parameter(
     *     in="query",
     *     name="grade_id",
     *     type="integer",
     *     description="年级id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="class_id",
     *     type="integer",
     *     description="班级id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="name",
     *     type="string",
     *     description="学生姓名模糊搜索",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="start_time",
     *     type="datetime",
     *     description="预约开始时间",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="end_time",
     *     type="datetime",
     *     description="预约结束时间",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="page",
     *     type="integer",
     *     description="页码",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="pagesize",
     *     type="integer",
     *     description="页数量",
     *     required=false,
     *   ),
     *   description="数据说明",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */

    public function focus(){

        $data = $this->focus->hand_out();
        apiReturn($data);
    }

}