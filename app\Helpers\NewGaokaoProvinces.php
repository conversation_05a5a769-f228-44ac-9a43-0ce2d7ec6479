<?php

namespace App\Helpers;

use App\Enums\ProvinceEnum;

/**
 * 新高考省份辅助类
 * 
 * 提供与新高考省份相关的辅助方法
 */
class NewGaokaoProvinces
{
    /**
     * 新高考省份列表
     * 
     * 包含已经是新高考和即将是新高考（公布了最新选科数据）的省份
     */
    private static $NEW_GAOKAO_PROVINCES = [
        ProvinceEnum::BEIJING->value, 
        ProvinceEnum::TIANJIN->value, 
        ProvinceEnum::HAINAN->value,
        ProvinceEnum::SHANGHAI->value, 
        ProvinceEnum::ZHEJIANG->value, 
        ProvinceEnum::SHANDONG->value,
        ProvinceEnum::HEBEI->value, 
        ProvinceEnum::LIAONING->value, 
        ProvinceEnum::JIANGSU->value,
        ProvinceEnum::FUJIAN->value, 
        ProvinceEnum::HUBEI->value, 
        ProvinceEnum::HUNAN->value,
        ProvinceEnum::GUANGDONG->value, 
        ProvinceEnum::CHONGQING->value, 
        ProvinceEnum::HEILONGJIANG->value,
        ProvinceEnum::JILIN->value, 
        ProvinceEnum::ANHUI->value, 
        ProvinceEnum::JIANGXI->value,
        ProvinceEnum::GUIZHOU->value, 
        ProvinceEnum::GANSU->value, 
        ProvinceEnum::SICHUAN->value,
        ProvinceEnum::SHANXI->value, 
        ProvinceEnum::YUNNAN->value, 
        ProvinceEnum::NEIMENGGU->value,
        ProvinceEnum::GUANGXI->value, 
        ProvinceEnum::HENAN->value, 
        ProvinceEnum::SHAANXI->value
    ];

    /**
     * 判断是否为新高考省份
     *
     * @param int $provinceId 省份ID
     * @return bool 如果是新高考省份则返回 true，否则返回 false
     */
    public static function isNewGaokaoProvince(int $provinceId): bool
    {
        return in_array($provinceId, self::$NEW_GAOKAO_PROVINCES);
    }
}
