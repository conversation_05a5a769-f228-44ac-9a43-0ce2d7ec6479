<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class GaokaoToYiceSeeder extends Seeder
{

    protected string $connect = 'mysql_yice';
    protected string $connect2 = 'sqlsrv_gk';

    public function run(): void
    {
//        $users =   DB::connection($this->connect)->table('users')->pluck('id','real_name')->toArray();
//        $grades =   DB::connection($this->connect)->table('grades')->pluck('id','grade')->toArray();
//        $province_names =   DB::connection($this->connect)->table('provinces')->pluck('id','province_name')->toArray();
//        $city_names =   DB::connection($this->connect)->table('cities')->pluck('id','city_name')->toArray();
//        $district_name =   DB::connection($this->connect)->table('districts')->pluck('id','district_name')->toArray();
//        DB::connection($this->connect)->table('gaokao_students')->get()->each(function ($item) use ($users, $province_names,$city_names, $district_name, $grades){
//            // 加学生
//            $student = [
//                'student_name'=>$item->StudentName,
//                'main_getter'=>$users[str_replace('-颐策', '', $item->xiaoshou)] ?? null,
//                'mobile'=>$item->Mobile,
//                'province_id'=>$province_names[$item->ProvinceName] ?? null,
//                'city_id'=>$city_names[$item->CityName] ?? null,
//                'district_id'=>$district_name[$item->DistrictName] ?? null,
//                'school'=>$item->MiddleSchool ?? '',
//                'school_area_id'=>21,
//                'grade_id'=>$grades[$this->getGradeByYear($item->GaokaoYear)] ?? null,
//                'gender'=>$item->Gender ?? null,
//                'remark'=> $item->Other ??'',
//                'created_at'=>$item->CreateTime ??null,
//                'updated_at'=>$item->CreateTime ??null,
//                'creator'=>str_replace('-颐策', '', $item->CreateUser),
//                'updater'=>str_replace('-颐策', '', $item->CreateUser),
//                'channel_category_id'=>$item->channel_category_id ??null,
//                'channel_id'=>$item->channel_id ??null,
//                'address'=>"来自第一高考数据",
//            ];
//            $student_id =  DB::connection($this->connect)->table('students')->insertGetId($student);
////            dd($student);
////             加跟进记录
//            $follows =  [];
//            DB::connection($this->connect2)
//                ->table('SxStudentFollowRecord')
//                ->where('StudentId', $item->Id)
//                ->get()
//                ->each(function($follow)use (&$follows,$student_id){
//                    $follows[] = [
//                        'student_id'=>$student_id,
//                        'follow_content'=>$follow->FollowRecord,
//                        'follow_way'=>$follow->FollowType == 3 ? 7 : ($follow->FollowType == 4 ? 8 : $follow->FollowType),
//                        'next_follow_date'=>$follow->NextVisitTime,
//                        'creator'=>str_replace('-颐策', '', $follow->CreateUser),
//                        'updater'=>str_replace('-颐策', '', $follow->CreateUser),
//                        'created_at'=>$follow->CreateTime,
//                        'updated_at'=>$follow->CreateTime,
//                        'source'=>"来自第一高考数据",
//                    ];
//
//                });
//            DB::connection($this->connect)->table('follows')->insert($follows);
//        });


////        dd($this->getGradeByYear(2040));
//        $grades = DB::connection($this->connect)->table('grades')->pluck('id','grade')->toArray();
//        DB::connection($this->connect)->table('gaokao_students')->get()->each(function ($item) use ($grades){
//            // 根据手机号更新年级
//            $student =  DB::connection($this->connect)->table('students')
//                ->where('mobile',$item->Mobile)
//                ->where('address','来自第一高考数据')
//                ->first();
//            if(!$student)
//                return;
//
//            DB::connection($this->connect)->table('students')->where('id',$student->id)
//                ->update(['grade_id'=>($grades[$this->getGradeByYear($item->GaokaoYear)] ?? null)]);
//        });


        DB::connection($this->connect)->table('students')
            ->where('address','来自第一高考数据')
            ->get()->each(function ($item) {
            // 根据手机号更新年级
            $student =  DB::connection($this->connect2)->table('SxStudent')
                ->where('EntityType', 2)
                ->where('State', 0)
                ->where('Mobile', $item->mobile)
                ->first();
            if(!$student)
                return;

            DB::connection($this->connect)->table('students')
                ->where('address','来自第一高考数据')
                ->where('mobile',$item->mobile)
                ->update(['created_at'=>$student->CreateTime, 'updated_at'=>$student->UpdateTime ?? $student->CreateTime, 'updater'=>$student->UpdateUser ?? $student->CreateUser]);
        });
    }


    // 通过高考年份获取当前年级
    public function getGradeByYear($year)
    {
        if(!$year || $year < 2000)
            return null;

        $currentYear = date('Y');
        $yearDiff = $year - $currentYear;

        // 根据高考年份与当前年份的差值确定年级
        if ($yearDiff < 0) {
            return '在读大学';
        } else if ($yearDiff <= 2) { // 高中阶段
            $grades = ['十二年级', '十一年级', '十年级'];
            return $grades[$yearDiff];
        } else if ($yearDiff <= 5) { // 初中阶段
            $grades = ['九年级', '八年级', '七年级'];
            return $grades[$yearDiff - 3];
        } else if ($yearDiff <= 11) { // 小学阶段
            $grades = ['六年级', '五年级', '四年级', '三年级', '二年级', '一年级'];
            return $grades[$yearDiff - 6];
        } else if ($yearDiff <= 14) { // 幼儿园阶段
            $grades = ['幼儿园-大班', '幼儿园-中班', '幼儿园-小班'];
            return $grades[$yearDiff - 12];
        } else {
            return '2岁内';
        }
    }

}


