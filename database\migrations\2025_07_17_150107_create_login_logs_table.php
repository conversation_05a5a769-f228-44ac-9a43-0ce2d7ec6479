<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_login_logs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->nullable()->comment('用户ID，登录失败时可能为空');
            $table->string('username', 100)->comment('登录用户名');
            $table->string('ip_address', 45)->nullable()->comment('登录IP地址');
            $table->text('user_agent')->nullable()->comment('用户代理信息');
            $table->enum('status', ['success', 'failed'])->comment('登录状态：success成功，failed失败');
            $table->string('failure_reason', 255)->nullable()->comment('登录失败原因');
            $table->timestamps();

            // 添加索引
            $table->index(['user_id', 'created_at']);
            $table->index(['username', 'created_at']);
            $table->index(['ip_address', 'created_at']);
            $table->index('status');
        });

        // 添加表注释
        DB::statement("ALTER TABLE `user_login_logs` comment '用户登录日志表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_login_logs');
    }
};
