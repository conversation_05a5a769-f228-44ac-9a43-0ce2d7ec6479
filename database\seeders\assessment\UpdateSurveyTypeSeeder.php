<?php

namespace Database\Seeders\assessment;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UpdateSurveyTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //新测评ID与老的survey_type转换
        $surveyTypeToAssessmentId = [
            1 => 1,
            4 => 2,
            5 => 3,
            2 => 4,
            3 => 5,
            6 => 6,
            7 => 7,
            8 => 8,
            9 => 9,
            10 => 10,
            11 => 11,
            12 => 12,
            13 => 13,
            16 => 14,
            17 => 15,
            18 => 16,
            19 => 17,
            20 => 18,
            21 => 19,
            22 => 20,
            23 => 21,
            25 => 22,
            26 => 23,
            27 => 24,
            28 => 25,
        ];
        foreach ($surveyTypeToAssessmentId as $k => $v){
            DB::table('assessments')->where('id', $v)->update(['survey_type' => $k]);
        }
    }
}
