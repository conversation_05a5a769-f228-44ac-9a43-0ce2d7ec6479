<?php

namespace App\Services\School\System;

use App\Exceptions\BusinessException;
use App\Http\Requests\School\System\CourseRequest;
use App\Models\School\System\Course;
use App\Services\BaseService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CourseService extends BaseService
{
    protected ClassService $classService;

    public function __construct(ClassService $classService)
    {
        $this->classService = $classService;
    }

    /**
     * 构建课程列表查询
     */
    public function listBuilder(Request $request)
    {
        $school_id = $this->classService->getSchoolId($request);
        $school_campus_id = $request['school_campus_id'];
        $grade_id = $request['grade_id'];

        return $this->buildCourseQuery($school_id, $school_campus_id, $grade_id);
    }

    /**
     * 新增课程
     */
    public function store(CourseRequest $request)
    {
        $school_id = $this->classService->getSchoolId($request);
        $school_campus_id = $request['school_campus_id'];
        $grade_id = $request['grade_id'];
        $course_name = $request['course_name'];

        // 判断课程是否已存在
        if ($this->isCourseExists($school_id, $school_campus_id, $grade_id, $course_name)) {
            $this->throwBusinessException('课程已存在');
        }

        $data = filterRequestData('courses');
        $data['school_id'] = $school_id;
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        
        $record = Course::forceCreate($data);
        return $this->success($record);
    }

    /**
     * 批量新增课程
     */
    public function batchStore(CourseRequest $request)
    {
        $school_id = $this->classService->getSchoolId($request);
        $school_campus_id = $request['school_campus_id'];
        $grade_id = $request['grade_id'];
        $course_list = $request['courses'];
        
        $course_name_list = array_filter(array_column($course_list, 'course_name'));
        
        // 判断课程是否已存在
        if ($this->isAnyCourseExists($school_id, $school_campus_id, $grade_id, $course_name_list)) {
            $this->throwBusinessException('导入数据中有课程名称在数据库中已存在');
        }

        return $this->createMultipleCourses(
            $school_id, 
            $school_campus_id, 
            $grade_id, 
            $course_list, 
            $request->user()->real_name
        );
    }

    /**
     * 更新课程
     */
    public function update(Request $request, $id)
    {
        $record = Course::find($id);
        if (!$record) {
            $this->throwBusinessException('更新对象不存在');
        }
        
        $school_id = $this->classService->getSchoolId($request);
        $school_campus_id = $request['school_campus_id'];
        $grade_id = $request['grade_id'];
        $course_name = $request['course_name'];
        
        // 判断课程是否已存在（排除当前记录）
        if ($this->isCourseExists($school_id, $school_campus_id, $grade_id, $course_name, $id)) {
            $this->throwBusinessException('课程已存在');
        }

        $data = filterRequestData('courses');
        $data['updater'] = $request->user()->real_name;
        $record->fill($data)->save();
        
        return $this->success($record);
    }
    
    /**
     * 构建课程查询
     */
    private function buildCourseQuery($school_id, $school_campus_id = null, $grade_id = null): Builder
    {
        return Course::where('school_id', $school_id)
            ->when($school_campus_id, fn($query) => $query->where('school_campus_id', $school_campus_id))
            ->when($grade_id, fn($query) => $query->where('grade_id', $grade_id));
    }
    
    /**
     * 检查课程是否已存在
     */
    private function isCourseExists($school_id, $school_campus_id, $grade_id, $course_name, $exclude_id = null): bool
    {
        $query = $this->buildCourseQuery($school_id, $school_campus_id, $grade_id)
            ->where('course_name', $course_name);
            
        if ($exclude_id) {
            $query->where('id', '!=', $exclude_id);
        }
        
        return $query->exists();
    }
    
    /**
     * 检查是否有任何课程已存在
     */
    private function isAnyCourseExists($school_id, $school_campus_id, $grade_id, array $course_names): bool
    {
        return $this->buildCourseQuery($school_id, $school_campus_id, $grade_id)
            ->whereIn('course_name', $course_names)
            ->exists();
    }
    
    /**
     * 批量创建课程
     */
    private function createMultipleCourses($school_id, $school_campus_id, $grade_id, array $course_list, $user_name)
    {
        $course_result_list = [];
        
        DB::beginTransaction();
        try {
            // 循环新增课程
            foreach ($course_list as $course) {
                $result = Course::create([
                    'school_id' => $school_id,
                    'school_campus_id' => $school_campus_id,
                    'grade_id' => $grade_id,
                    'course_name' => $course['course_name'],
                    'code' => $course['code'],
                    'type' => $course['type'],
                    'creator' => $user_name,
                    'updater' => $user_name
                ]);
                $course_result_list[] = $result;
            }
            DB::commit();
            return $course_result_list;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("批量新增课程失败逻辑错误", 500, $e->getMessage());
        }
    }
}
