<?php

namespace App\Repositories;

use App\Models\School\System\Student;

class StudentRepository
{
    /**
     * 获取学生基本信息
     * 
     * @param int $user_id 用户ID
     * @return array 学生基本信息
     */
    public function getStudentInfo(int $user_id): array
    {
        return Student::query()
            ->join('users', 'students.user_id', '=', 'users.id')
            ->join('schools', 'students.school_id', '=', 'schools.id')
            ->select(
                'user_id',
                'students.student_no',
                'users.real_name',
                'students.gender',
                'schools.name as school_name'
            )
            ->where('user_id', $user_id)
            ->first()
            ->toArray();
    }
}