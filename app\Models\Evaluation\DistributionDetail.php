<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 分发详情模型
 */
class DistributionDetail extends Model
{
    protected $table = 'evaluation_distribution_details';

    protected $fillable = [
        'distribution_id',
        'paper_id',
        'teacher_member_ids',
        'start_time',
        'exam_duration',
        'status',
        'target_type',
        'target_id',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'distribution_id' => 'integer',
        'paper_id' => 'integer',
        'exam_duration' => 'integer',
        'status' => 'integer',
        'target_type' => 'integer',
        'target_id' => 'integer',
        'start_time' => 'datetime',
    ];

    /**
     * 所属分发关联
     */
    public function distribution(): BelongsTo
    {
        return $this->belongsTo(Distribution::class, 'distribution_id');
    }

    /**
     * 所属试卷关联
     */
    public function paper(): BelongsTo
    {
        return $this->belongsTo(Papers::class, 'paper_id');
    }

    /**
     * 作用域：按分发筛选
     */
    public function scopeByDistribution($query, $distributionId)
    {
        return $query->where('distribution_id', $distributionId);
    }

    /**
     * 作用域：按试卷筛选
     */
    public function scopeByPaper($query, $paperId)
    {
        return $query->where('paper_id', $paperId);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 获取老师ID数组
     */
    public function getTeacherMemberIdsArray(): array
    {
        if (empty($this->teacher_member_ids)) {
            return [];
        }
        return explode(',', $this->teacher_member_ids);
    }

    /**
     * 设置老师ID数组
     */
    public function setTeacherMemberIdsArray(array $teacherIds): void
    {
        $this->teacher_member_ids = implode(',', $teacherIds);
    }

    /**
     * 检查是否已开始
     */
    public function isStarted(): bool
    {
        if (!$this->start_time) {
            return true; // 没有设置开始时间，认为已开始
        }
        return now() >= $this->start_time;
    }

    /**
     * 检查是否已结束
     */
    public function isEnded(): bool
    {
        if (!$this->exam_duration) {
            return false; // 没有设置考试时长，认为未结束
        }
        
        if (!$this->start_time) {
            return false;
        }

        $endTime = $this->start_time->addMinutes($this->exam_duration);
        return now() > $endTime;
    }

    /**
     * 获取结束时间
     */
    public function getEndTime()
    {
        if (!$this->start_time || !$this->exam_duration) {
            return null;
        }
        
        return $this->start_time->addMinutes($this->exam_duration);
    }

    /**
     * 获取剩余时间（分钟）
     */
    public function getRemainingMinutes(): ?int
    {
        $endTime = $this->getEndTime();
        if (!$endTime) {
            return null;
        }

        $remaining = now()->diffInMinutes($endTime, false);
        return $remaining > 0 ? $remaining : 0;
    }

    /**
     * 获取目标类型文本
     */
    public function getTargetTypeTextAttribute(): string
    {
        $typeMap = [
            1 => '班级',
            2 => '学生'
        ];

        return $typeMap[$this->target_type] ?? '未知';
    }

    /**
     * 软删除
     */
    public function softDelete(): bool
    {
        return $this->update(['status' => -1]);
    }

    /**
     * 恢复软删除
     */
    public function restore(): bool
    {
        return $this->update(['status' => 0]);
    }
}
