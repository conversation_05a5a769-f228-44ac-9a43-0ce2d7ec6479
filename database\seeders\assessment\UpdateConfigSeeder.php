<?php

namespace Database\Seeders\assessment;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UpdateConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data_pc = config('survey.survey_list_pc');
        $data_mobile = config('survey.survey_list_Mobile');
        $start_title = config('survey.start_title');
        $time_error = config('survey.time_error');
        $assessment_data = Db::table('assessments')->select('id','name','survey_type')->get()->toArray();
        $updates = [];
        foreach ($assessment_data as $v) {
            $updates[] = [
                'id' => $v->id,
                'icon' => $data_pc[$v->survey_type]['img'],
                'introduction_pc' => $data_pc[$v->survey_type]['content'],
                'introduction_mobile' => $data_mobile[$v->survey_type]['content'],
                'prompt' => json_encode($start_title[$v->survey_type]['content']),
                'note' => json_encode($start_title[$v->survey_type]['note']),
                'min_duration' => $time_error[$v->survey_type]['min'],
                'max_duration' => $time_error[$v->survey_type]['max'],
            ];
        }

        // 使用事务进行批量更新
        DB::transaction(function () use ($updates) {
            foreach ($updates as $update) {
                DB::table('assessments')
                    ->where('id', $update['id'])
                    ->update($update);
            }
        });
    }
}
