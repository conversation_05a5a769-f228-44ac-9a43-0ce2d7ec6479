<?php

namespace App\Http\Requests\Evaluation;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 题目请求验证类
 */
class QuestionRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        $rules = [
            'type_id' => 'required|integer|min:1',
            'grade' => 'required|integer|min:0|max:12',
            'course_id' => 'required|integer|min:1',
            'content' => 'required|string|max:5000',
            'score' => 'required|integer|min:1|max:100',
        ];

        // 根据请求方法添加不同的规则
        if ($this->isMethod('post')) {
            // 创建时的额外规则
            $rules = array_merge($rules, [
                'is_common' => 'sometimes|integer|in:0,1',
                'situation' => 'nullable|string|min:0',
                'knowlege_ids' => 'sometimes|string',
                'answer' => 'sometimes|string|max:2000',
                'analysis' => 'sometimes|string|max:2000',
                'parent_id' => 'sometimes|integer|min:0',
                'options' => 'sometimes|array',
                'options.*.content' => 'required_with:options|string|max:1000',
                'options.*.score' => 'required_with:options|integer|min:0',
                'options.*.title' => 'required_with:options|string|min:1',
                'categorey_percentage' => 'sometimes|array',
                'categorey_percentage.*.category_id' => 'required_with:categorey_percentage|integer|min:1',
                'categorey_percentage.*.parent_id' => 'required_with:categorey_percentage|integer|min:0',
                'categorey_percentage.*.percentage' => 'required_with:categorey_percentage|numeric|min:0|max:100',
            ]);
        } elseif ($this->isMethod('put') || $this->isMethod('patch')) {
            // 更新时的规则（大部分字段变为可选）
            $rules = [
                'is_common' => 'sometimes|integer|in:0,1',
                'type_id' => 'sometimes|integer|min:1',
                'grade' => 'sometimes|integer|min:0|max:12',
                'course_id' => 'sometimes|integer|min:1',
                'situation' => 'nullable|string|min:0',
                'content' => 'sometimes|string|max:5000',
                'score' => 'sometimes|integer|min:1|max:100',
                'knowlege_ids' => 'sometimes|string',
                'answer' => 'sometimes|string|max:2000',
                'analysis' => 'sometimes|string|max:2000',
                'parent_id' => 'sometimes|integer|min:0',
                'options' => 'sometimes|array',
                'options.*.content' => 'required_with:options|string|max:1000',
                'options.*.score' => 'required_with:options|integer|min:0',
                'options.*.title' => 'required_with:options|string|min:1',
                'categorey_percentage' => 'sometimes|array',
                'categorey_percentage.*.category_id' => 'required_with:categorey_percentage|integer|min:1',
                'categorey_percentage.*.parent_id' => 'required_with:categorey_percentage|integer|min:0',
                'categorey_percentage.*.percentage' => 'required_with:categorey_percentage|numeric|min:0|max:100',
            ];
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性名称
     */
    public function attributes(): array
    {
        return [
            'is_common' => '是否题冒题',
            'type_id' => '题目类型',
            'grade' => '适用年级',
            'course_id' => '学科',
            'situation' => '情景类型',
            'content' => '题干内容',
            'score' => '分值',
            'knowlege_ids' => '知识点',
            'answer' => '答案',
            'analysis' => '答案解析',
            'parent_id' => '父题目',
            'options' => '选项',
            'options.*.content' => '选项内容',
            'options.*.score' => '选项分值',
            'options.*.title' => '选项字母',
            'categorey_percentage' => '素养占比',
            'categorey_percentage.*.category_id' => '素养类别',
            'categorey_percentage.*.parent_id' => '素养类别父id',
            'categorey_percentage.*.percentage' => '占比百分比',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            'type_id.required' => '题目类型不能为空',
            'type_id.integer' => '题目类型必须是整数',
            'grade.required' => '适用年级不能为空',
            'grade.integer' => '适用年级必须是整数',
            'grade.min' => '适用年级不能小于0',
            'grade.max' => '适用年级不能大于12',
            'course_id.required' => '学科不能为空',
            'course_id.integer' => '学科必须是整数',
            'content.required' => '题干内容不能为空',
            'content.string' => '题干内容必须是字符串',
            'content.max' => '题干内容不能超过5000个字符',
            'score.required' => '分值不能为空',
            'score.integer' => '分值必须是整数',
            'score.min' => '分值不能小于1',
            'score.max' => '分值不能大于100',
            'is_common.integer' => '是否题冒题必须是整数',
            'is_common.in' => '是否题冒题只能是0或1',
            'situation.string' => '情景类型必须是字符串',
            'knowlege_ids.string' => '知识点必须是字符串',
            'answer.string' => '答案必须是字符串',
            'answer.max' => '答案不能超过2000个字符',
            'analysis.string' => '答案解析必须是字符串',
            'analysis.max' => '答案解析不能超过2000个字符',
            'parent_id.integer' => '父题目必须是整数',
            'options.array' => '选项必须是数组',
            'options.*.content.required_with' => '选项内容不能为空',
            'options.*.content.string' => '选项内容必须是字符串',
            'options.*.content.max' => '选项内容不能超过1000个字符',
            'options.*.score.required_with' => '选项分值不能为空',
            'options.*.score.integer' => '选项分值必须是整数',
            'options.*.title.required_with' => '选项字母不能为空',
            'options.*.title.string' => '选项字母必须是字符串',
            'categorey_percentage.array' => '素养占比必须是数组',
            'categorey_percentage.*.category_id.required_with' => '素养类别不能为空',
            'categorey_percentage.*.category_id.integer' => '素养类别必须是整数',
            'categorey_percentage.*.parent_id.required_with' => '素养类别父id不能为空',
            'categorey_percentage.*.parent_id.integer' => '素养类别父id必须是整数',
            'categorey_percentage.*.percentage.required_with' => '占比百分比不能为空',
            'categorey_percentage.*.percentage.numeric' => '占比百分比必须是数字',
            'categorey_percentage.*.percentage.min' => '占比百分比不能小于0',
            'categorey_percentage.*.percentage.max' => '占比百分比不能大于100',
        ];
    }

    /**
     * 配置验证实例
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 自定义验证逻辑
            $this->validateCategoryPercentageSum($validator);
        });
    }

    /**
     * 验证素养占比总和
     */
    private function validateCategoryPercentageSum($validator): void
    {
        $categoryPercentage = $this->input('categorey_percentage', []);
        
        if (!empty($categoryPercentage)) {
            $totalPercentage = array_sum(array_column($categoryPercentage, 'percentage'));
            
            if ($totalPercentage > 100) {
                $validator->errors()->add('categorey_percentage', '素养占比总和不能超过100%');
            }
        }
    }
}
