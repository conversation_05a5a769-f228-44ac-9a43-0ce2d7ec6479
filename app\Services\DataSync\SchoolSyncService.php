<?php

namespace App\Services\DataSync;

use App\Services\BaseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SchoolSyncService extends BaseService
{
    protected $syncConnection;

    public function __construct()
    {
        $this->syncConnection = DB::connection('sync_mysql');
    }

    /**
     * 同步学校数据（完整版本，包括基础信息、校区、角色）
     *
     * @param array $schoolData 学校数据
     * @return array
     */
    public function syncSchool(array $schoolData): array
    {
        try {
            $this->syncConnection->beginTransaction();

            // 1. 同步学校基础信息
            $syncSchoolId = $this->syncSchoolBasicInfo($schoolData);

            // 2. 同步校区信息到 ysy_school_district 表
            $this->syncSchoolDistrict($syncSchoolId, $schoolData);

            // 3. 同步角色信息到 ysy_role 表并设置权限
            $this->syncSchoolRoles($syncSchoolId, $schoolData);

            $this->syncConnection->commit();

            Log::info('学校完整数据同步成功', [
                'school_id' => $schoolData['id'] ?? 'unknown',
                'sync_school_id' => $syncSchoolId
            ]);

            return [
                'success' => true,
                'sync_school_id' => $syncSchoolId,
                'message' => '学校完整数据同步成功'
            ];

        } catch (\Exception $e) {
            $this->syncConnection->rollBack();
            $this->throwBusinessException('学校完整数据同步失败');
        }
    }

    /**
     * 获取同步数据库中的学校ID
     * 
     * @param int $originalSchoolId 原始学校ID
     * @return int|null
     */
    public function getSyncSchoolId(int $originalSchoolId): ?int
    {
        $school = $this->syncConnection->table('schools')

            ->orWhere('id', $originalSchoolId)
            ->first();

        return $school ? $school->id : null;
    }

    /**
     * 获取校区信息
     * 
     * @param int $campusId 校区ID
     * @return array
     */
    public function getCampusInfo(int $campusId): array
    {
        // 查询校区对应的学校信息
        $campus = \App\Models\School\System\SchoolCampus::with('school')->find($campusId);
        
        if (!$campus || !$campus->school) {
            throw new \Exception('未找到校区或学校信息');
        }

        // 获取同步数据库中的学校ID
        $syncSchoolId = $this->getSyncSchoolId($campus->school->id);
        
        if (!$syncSchoolId) {
            throw new \Exception('未找到同步数据库中的学校信息');
        }

        return [
            'campus_id' => $campusId,
            'school_id' => $syncSchoolId,
            'original_school_id' => $campus->school->id,
            'school_name' => $campus->school->name,
            'campus_name' => $campus->campus_name
        ];
    }

    /**
     * 映射学校字段名称（保持原有逻辑）
     * 将原始字段名转换为同步数据库的字段名
     */
    private function mapSchoolFields(array $schoolData): array
    {
        // 从配置文件获取字段映射，如果没有配置则使用默认映射
        $configMapping = config('datasync.field_mapping.school', []);

        // 默认字段映射关系
        $defaultMapping = [
            // 基础字段映射
            'school_name' => 'name',           // 学校名称
            // 扩展字段映射（保持原字段名）
            'add_time' => 'add_time',          // 添加时间
            'date_due' => 'date_due',          // 到期时间
            'province' => 'province',          // 省份
            'city' => 'city',                  // 城市
            'district' => 'district',          // 区县
            'address' => 'address',            // 详细地址
            'buy_modules' => 'buy_modules',    // 购买模块
            'location' => 'location',          // 位置信息
        ];

        // 合并配置映射和默认映射
        $fieldMapping = array_merge($defaultMapping, $configMapping);

        return $this->applyFieldMapping($schoolData, $fieldMapping);
    }

    /**
     * 应用字段映射（保持原有逻辑）
     */
    private function applyFieldMapping(array $data, array $mapping): array
    {
        $result = [];

        foreach ($mapping as $sourceField => $targetField) {
            if (isset($data[$sourceField])) {
                $result[$targetField] = $data[$sourceField];
            }
        }

        return $result;
    }

    /**
     * 同步学校基础信息（保持原有逻辑）
     *
     * @param array $schoolData 学校数据
     * @return int 同步后的学校ID
     */
    protected function syncSchoolBasicInfo(array $schoolData): int
    {
        // 应用字段映射转换
        $mappedData = $this->mapSchoolFields($schoolData);
      
        $syncSchoolData = [
            'name' => $schoolData['name'], // 学校名称
            'province' => $schoolData['province'] ?? '',
            'city' => $schoolData['city'] ?? '',
            'district' => $schoolData['district'] ?? '',
            'add_time' => $schoolData['date_start'] ?? now(),
            'date_due' => $schoolData['date_due'] ?? null,
            'buy_modules' =>'01,02,03,04,06,07,08,09,10,11,12,13,14,16,120,21,22,23,24,25,26,27,28,29,30,2802',
            'location' => $schoolData['province'] ?? '',
            'school_type' => 3,
        ];
      
        // 使用 insert 而不是 insertGetId，因为我们指定了 id
        $this->syncConnection->table('ysy_school')->insert($syncSchoolData);
        return $schoolData['id']; // 返回原表的 id
    }

    /**
     * 同步校区信息到 ysy_school_district 表（保持原有逻辑）
     *
     * @param int $schoolId 学校ID
     * @param array $schoolData 学校数据
     */
    protected function syncSchoolDistrict(int $schoolId, array $schoolData): void
    {
        try {
            // 从原数据库的 school_campuses 表查询校区数据
            $campuses = DB::connection('mysql')->table('school_campuses')
                ->where('school_id', $schoolId)
                ->where('status', 1)
                ->get();

            if ($campuses->isEmpty()) {
                // 如果没有找到校区数据，创建默认主校区
                $districtData = [
                    'campus_name' => '主校区',
                    'school_id' => $schoolId,
                    'school_type' => 3
                ];

                $this->syncConnection->table('ysy_school_district')->insert($districtData);

            } else {
                // 同步找到的校区数据
                foreach ($campuses as $campus) {
                    $districtData = [
                        'id'=> $campus->id,
                        'campus_name' => $campus->campus_name ?? '校区',
                        'school_id' => $schoolId,
                        'school_type' => 3,
                         'step' => 0
                    ];

                    $this->syncConnection->table('ysy_school_district')->insert($districtData);

                    Log::info('学校校区数据同步成功', [
                        'school_id' => $schoolId,
                        'campus_id' => $campus->id,
                        'campus_name' => $campus->campus_name,
                        'school_type' => 3
                    ]);
                }
            }

        } catch (\Exception $e) {
            $this->throwBusinessException('学校校区数据同步失败');
        }
    }

    /**
     * 同步角色信息到 ysy_role 表并设置权限（保持原有逻辑）
     *
     * @param int $schoolId 学校ID
     * @param array $schoolData 学校数据
     */
    protected function syncSchoolRoles(int $schoolId, array $schoolData): void
    {
        try {
            // 定义角色配置
            $roles = [
                [
                    'name' => '老师',
                    'type' => 3,
                    'menu_authority' => '01,02,03,06,07,08,09,10,11,12,16,22,23,25,26,29,27,28',
                    'exclusion_authority' => null
                ],
                [
                    'name' => '学生',
                    'type' => 1,
                    'menu_authority' => '01,02,03,06,07,08,10,11,13,14,16,24,25,26,27,30',
                    'exclusion_authority' => '07210201'
                ],
                [
                    'name' => '教务',
                    'type' => 2,
                    'menu_authority' => '01,02,03,06,07,08,09,10,11,16,22,23,25,26,29,27,28',
                    'exclusion_authority' => null
                ]
            ];

            foreach ($roles as $roleConfig) {
                // 插入角色数据到 ysy_role 表
                $roleData = [
                    'name' => $roleConfig['name'],
                    'school_id' => $schoolId,
                    'type' => $roleConfig['type']
                ];

                $roleId = $this->syncConnection->table('ysy_role')->insertGetId($roleData);

                // 插入权限数据到 ysy_role_privilege 表
                $privilegeData = [
                    'role_id' => $roleId,
                    'menu_authority' => $roleConfig['menu_authority']
                ];

                // 如果有排除权限，添加到数据中
                if ($roleConfig['exclusion_authority']) {
                    $privilegeData['exclusion_authority'] = $roleConfig['exclusion_authority'];
                }

                $this->syncConnection->table('ysy_role_privilege')->insert($privilegeData);

                Log::info('学校角色数据同步成功', [
                    'school_id' => $schoolId,
                    'role_name' => $roleConfig['name'],
                    'role_id' => $roleId,
                    'type' => $roleConfig['type']
                ]);
            }

        } catch (\Exception $e) {
            $this->throwBusinessException('学校角色数据同步失败');
        }
    }

    /**
     * 获取同步数据库中的校区ID（统一方法，供其他服务调用）
     *
     * @param int $school_campus_id 校区ID
     * @return int|null 学校ID
     */
    public function getSyncDistrictId(int $school_campus_id): ?int
    {
        $syncDistrict = $this->syncConnection->table('ysy_school_district')
            ->where('id', $school_campus_id)
            ->first();

        return $syncDistrict ? $syncDistrict->school_id : null;
    }
}
