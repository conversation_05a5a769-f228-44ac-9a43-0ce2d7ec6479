<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 16:01
 */
namespace app\evaluation\service;
use think\Loader;
class Questions{
    protected $QuestionsLogic;

    public function __construct()
    {
        $this->QuestionsLogic= new \app\evaluation\logic\Questions();
    }

    public function hand_out(){
        //根据不同的请求方式
        switch(choose_request()){
            case 'get'://获取
                return $this->QuestionsLogic->get_list();
                break;
            case 'post'://增加
                return $this->QuestionsLogic->add();
                break;
            case 'put'://修改

                return $this->QuestionsLogic->edit();
                break;
            case 'delete'://删除
                return $this->QuestionsLogic->del();
                break;
            default:
                return false;
        }
    }
    public function sort(){
                return $this->QuestionsLogic->edit_sort();
    }


}