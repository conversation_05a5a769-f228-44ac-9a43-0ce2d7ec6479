<?php

namespace App\Http\Requests\Evaluation;

use Illuminate\Foundation\Http\FormRequest;

/**
 * 知识点请求验证类
 */
class KnowledgeRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|string|max:100',
            'course_id' => 'required|integer|min:1',
        ];

        // 根据请求方法添加不同的规则
        if ($this->isMethod('post')) {
            // 创建时的额外规则
            $rules = array_merge($rules, [
                'parent_id' => 'sometimes|integer|min:0',
                'is_high' => 'sometimes|integer|in:0,1',
                'description' => 'sometimes|string|max:2000',
                'sort' => 'sometimes|integer|min:0',
            ]);
        } elseif ($this->isMethod('put') || $this->isMethod('patch')) {
            // 更新时的规则（大部分字段变为可选）
            $rules = [
                'name' => 'sometimes|string|max:100',
                'course_id' => 'sometimes|integer|min:1',
                'parent_id' => 'sometimes|integer|min:0',
                'is_high' => 'sometimes|integer|in:0,1',
                'description' => 'sometimes|string|max:2000',
                'sort' => 'sometimes|integer|min:0',
            ];
        }

        return $rules;
    }

    /**
     * 获取验证错误的自定义属性名称
     */
    public function attributes(): array
    {
        return [
            'name' => '知识点名称',
            'course_id' => '学科',
            'parent_id' => '父知识点',
            'is_high' => '学段',
            'description' => '知识点描述',
            'sort' => '排序',
        ];
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            'name.required' => '知识点名称不能为空',
            'name.string' => '知识点名称必须是字符串',
            'name.max' => '知识点名称不能超过100个字符',
            'course_id.required' => '学科不能为空',
            'course_id.integer' => '学科必须是整数',
            'course_id.min' => '学科ID不能小于1',
            'parent_id.integer' => '父知识点必须是整数',
            'parent_id.min' => '父知识点ID不能小于0',
            'is_high.integer' => '学段必须是整数',
            'is_high.in' => '学段只能是0或1',
            'description.string' => '知识点描述必须是字符串',
            'description.max' => '知识点描述不能超过2000个字符',
            'sort.integer' => '排序必须是整数',
            'sort.min' => '排序不能小于0',
        ];
    }

    /**
     * 配置验证实例
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 自定义验证逻辑
            $this->validateParentKnowledge($validator);
            $this->validateKnowledgeName($validator);
        });
    }

    /**
     * 验证父知识点
     */
    private function validateParentKnowledge($validator): void
    {
        $parentId = $this->input('parent_id', 0);
        
        if ($parentId > 0) {
            // 检查父知识点是否存在
            $parentExists = \App\Models\Evaluation\Knowledges::where('id', $parentId)
                ->where('status', 0)
                ->exists();
                
            if (!$parentExists) {
                $validator->errors()->add('parent_id', '指定的父知识点不存在');
            }
            
            // 更新时检查是否会形成循环引用
            if ($this->isMethod('put') || $this->isMethod('patch')) {
                $currentId = $this->route('id');
                if ($currentId && $this->wouldCreateCircularReference($currentId, $parentId)) {
                    $validator->errors()->add('parent_id', '不能将知识点移动到自己的子知识点下');
                }
            }
        }
    }

    /**
     * 验证知识点名称唯一性
     */
    private function validateKnowledgeName($validator): void
    {
        $name = $this->input('name');
        $courseId = $this->input('course_id');
        $parentId = $this->input('parent_id', 0);
        $isHigh = $this->input('is_high', 0);
        
        if ($name && $courseId !== null) {
            $query = \App\Models\Evaluation\Knowledges::where('name', $name)
                ->where('course_id', $courseId)
                ->where('parent_id', $parentId)
                ->where('is_high', $isHigh)
                ->where('status', 0);
                
            // 更新时排除当前记录
            if ($this->isMethod('put') || $this->isMethod('patch')) {
                $currentId = $this->route('id');
                if ($currentId) {
                    $query->where('id', '!=', $currentId);
                }
            }
            
            if ($query->exists()) {
                $validator->errors()->add('name', '在同一学科、学段和父知识点下，知识点名称不能重复');
            }
        }
    }

    /**
     * 检查是否会形成循环引用
     */
    private function wouldCreateCircularReference(int $knowledgeId, int $parentId): bool
    {
        if ($parentId == 0) {
            return false;
        }

        if ($parentId == $knowledgeId) {
            return true;
        }

        $parent = \App\Models\Evaluation\Knowledges::find($parentId);
        if (!$parent) {
            return false;
        }

        return $this->wouldCreateCircularReference($knowledgeId, $parent->parent_id);
    }
}
