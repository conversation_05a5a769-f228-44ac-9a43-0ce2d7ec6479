<?php

/**
 * 简单使用示例 - 在原有代码中添加数据同步调用
 */

// 1. 在控制器顶部引入 DataSyncHelper
use App\Helpers\DataSyncHelper;

class OrganizationController extends Controller
{
    /**
     * 创建学校 - 修改后的示例
     */
    public function createSchool(Request $request)
    {
        // 原有的学校创建逻辑（不修改）
        $alias = (new School())->getMorphClass();
        $school = $this->organizationService->createOrgDefaultInfo($request, $alias);
        
        // ========== 新增：数据同步调用 ==========
        if (config('datasync.enabled', false)) {
            $this->callDataSync($school, $request);
        }
        // =====================================
        
        return $this->message("学校创建成功");
    }

    /**
     * 调用数据同步（新增方法）
     */
    private function callDataSync($school, Request $request)
    {
        try {
            $syncHelper = new DataSyncHelper();
            
            // 准备学校数据
            $schoolData = $school->toArray();
            
            // 添加请求中的额外数据
            $additionalData = [
                'add_time' => $request->input('add_time', now()->format('Y-m-d H:i:s')),
                'date_due' => $request->input('date_due'),
                'province' => $request->input('province'),
                'city' => $request->input('city'),
                'district' => $request->input('district'),
                'address' => $request->input('address'),
                'buy_modules' => $request->input('buy_modules'),
                'location' => $request->input('location'),
            ];
            
            // 调用学校同步（直接同步）
            $syncResult = $syncHelper->syncSchool($schoolData, $additionalData);
            
            if ($syncResult['success']) {
                Log::info('学校数据同步成功', [
                    'school_id' => $school->id,
                    'sync_result' => $syncResult
                ]);
            } else {
                Log::error('学校数据同步失败', [
                    'school_id' => $school->id,
                    'error' => $syncResult['message']
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('数据同步调用异常', [
                'school_id' => $school->id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 创建学生 - 修改后的示例
     */
    public function createStudent(Request $request)
    {
        // 原有的学生创建逻辑（不修改）
        $student = $this->studentService->create($request->all());
        
        // ========== 新增：异步数据同步调用 ==========
        if (config('datasync.enabled', false)) {
            $this->callStudentAsyncSync($student);
        }
        // =========================================
        
        return $this->message("学生创建成功");
    }

    /**
     * 调用学生异步数据同步（新增方法）
     */
    private function callStudentAsyncSync($student)
    {
        try {
            $syncHelper = new DataSyncHelper();
            
            // 调用学生异步同步
            $syncResult = $syncHelper->asyncSyncStudent($student->toArray());
            
            Log::info('学生异步同步调用结果', [
                'student_id' => $student->id,
                'result' => $syncResult
            ]);
            
        } catch (\Exception $e) {
            Log::error('学生异步同步调用异常', [
                'student_id' => $student->id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 创建教师 - 修改后的示例
     */
    public function createTeacher(Request $request)
    {
        // 原有的教师创建逻辑（不修改）
        $teacher = $this->teacherService->create($request->all());
        
        // ========== 新增：异步数据同步调用 ==========
        if (config('datasync.enabled', false)) {
            $this->callTeacherAsyncSync($teacher);
        }
        // =========================================
        
        return $this->message("教师创建成功");
    }

    /**
     * 调用教师异步数据同步（新增方法）
     */
    private function callTeacherAsyncSync($teacher)
    {
        try {
            $syncHelper = new DataSyncHelper();
            
            // 调用教师异步同步
            $syncResult = $syncHelper->asyncSyncTeacher($teacher->toArray());
            
            Log::info('教师异步同步调用结果', [
                'teacher_id' => $teacher->id,
                'result' => $syncResult
            ]);
            
        } catch (\Exception $e) {
            Log::error('教师异步同步调用异常', [
                'teacher_id' => $teacher->id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 批量导入学生 - 修改后的示例
     */
    public function importStudents(Request $request)
    {
        // 原有的批量导入逻辑（不修改）
        $students = $this->studentService->batchImport($request->file('file'));
        
        // ========== 新增：批量异步数据同步调用 ==========
        if (config('datasync.enabled', false) && !empty($students)) {
            $this->callBatchStudentAsyncSync($students);
        }
        // ============================================
        
        return $this->message("学生批量导入成功，共导入 " . count($students) . " 名学生");
    }

    /**
     * 调用批量学生异步数据同步（新增方法）
     */
    private function callBatchStudentAsyncSync($students)
    {
        try {
            $syncHelper = new DataSyncHelper();
            
            // 准备学生数据数组
            $studentsData = collect($students)->map(function ($student) {
                return $student->toArray();
            })->toArray();
            
            // 调用批量异步同步
            $syncResult = $syncHelper->batchAsyncSyncStudents($studentsData);
            
            Log::info('批量学生异步同步调用结果', [
                'student_count' => count($students),
                'result' => $syncResult
            ]);
            
        } catch (\Exception $e) {
            Log::error('批量学生异步同步调用异常', [
                'student_count' => count($students),
                'error' => $e->getMessage()
            ]);
        }
    }
}

/**
 * 使用说明：
 * 
 * 1. 在控制器顶部添加：use App\Helpers\DataSyncHelper;
 * 
 * 2. 在原有的数据创建方法中，添加数据同步调用：
 *    - 学校、校区、年级、班级：使用直接同步
 *    - 学生、教师：使用异步同步
 * 
 * 3. 确保 .env 文件中配置了数据同步：
 *    DATA_SYNC_ENABLED=true
 * 
 * 4. 启动队列处理器处理异步任务：
 *    php artisan queue:work --queue=data-sync
 * 
 * 5. 监控同步日志：
 *    tail -f storage/logs/laravel.log | grep "数据同步"
 */
