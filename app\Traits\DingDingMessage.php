<?php

namespace App\Traits;

trait DingDingMessage
{
    /**
     * @param $content string   需要发送的信息
     * @return bool  true|false
     */
    function send_dingding_message($content, $title = '业务报警')
    {
        $url = $this->getUrl();
        $msg1 = [
            'msgtype' => 'markdown',//这是文件发送类型，可以根据需求调整
            'markdown' => [
                'title' => $title,
                'text' => $content,
            ],
        ];
        $data_string = json_encode($msg1);
        return $this->request_by_curl($url, $data_string);

    }


    function send_dingding_str_message($str)
    {
        $url = $this->getUrl();
        $msg1 = [
            'msgtype' => 'text',//这是文件发送类型，可以根据需求调整
            'text' => [
                'content' => $str,
            ],
        ];
        $data_string = json_encode($msg1);
        return $this->request_by_curl($url, $data_string);

    }


    function request_by_curl($remote_server, $post_string)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $remote_server);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json;charset=utf-8'));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_string);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // 线下环境不用开启curl证书验证, 未调通情况可尝试添加该代码
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }

    public function getUrl(): string
    {
        $url = env('DingTalk_WebHook');
        $secret = env('DingTalk_Secret');
        // 第一步，把timestamp+"\n"+密钥当做签名字符串，使用HmacSHA256算法计算签名，然后进行Base64 encode，最后再把签名参数再进行urlEncode，得到最终的签名（需要使用UTF-8字符集）。
        $time = time() * 1000;//毫秒级时间戳，我这里为了方便，直接把时间*1000了
        $sign = hash_hmac('sha256', $time . "\n" . $secret, $secret, true);
        $sign = base64_encode($sign);
        $sign = urlencode($sign);
        $url = "{$url}&timestamp={$time}&sign={$sign}";
        return $url;
    }
}
