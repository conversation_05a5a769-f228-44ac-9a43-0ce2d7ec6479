<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 题目表
        Schema::create('evaluation_questions', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('is_common')->default(0)->comment('是否题冒题 0:否 1:是');
            $table->unsignedInteger('type_id')->comment('题目类型ID');
            $table->tinyInteger('grade')->comment('适用年级 0:通用 1-12:对应年级');
            $table->unsignedInteger('course_id')->comment('学科ID');
            $table->tinyInteger('situation')->default(0)->comment('情景类型');
            $table->unsignedInteger('score')->comment('分值');
            $table->text('knowlege_ids')->nullable()->comment('知识点IDs，逗号分隔');
            $table->text('content')->comment('题干内容');
            $table->text('answer')->nullable()->comment('答案');
            $table->text('analysis')->nullable()->comment('答案解析');
            $table->unsignedInteger('parent_id')->default(0)->comment('父题目ID，0为主题');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(1)->comment('状态 0:禁用 1:启用');
            $table->unsignedInteger('creator_id')->nullable()->comment('创建者ID');
            $table->unsignedInteger('school_id')->nullable()->comment('学校ID');
            $table->timestamps();
            
            $table->index(['type_id', 'grade', 'course_id']);
            $table->index(['parent_id', 'sort']);
            $table->index(['status', 'school_id']);
        });

        // 题目选项表
        Schema::create('evaluation_question_options', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('question_id')->comment('题目ID');
            $table->text('content')->comment('选项内容');
            $table->unsignedInteger('score')->default(0)->comment('选项分值');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->boolean('is_correct')->default(false)->comment('是否正确答案');
            $table->timestamps();
            
            $table->foreign('question_id')->references('id')->on('evaluation_questions')->onDelete('cascade');
            $table->index(['question_id', 'sort']);
        });

        // 素养占比表
        Schema::create('evaluation_category_portions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('question_id')->comment('题目ID');
            $table->unsignedInteger('category_id')->comment('素养类别ID');
            $table->decimal('percentage', 5, 2)->default(0)->comment('占比百分比');
            $table->timestamps();
            
            $table->foreign('question_id')->references('id')->on('evaluation_questions')->onDelete('cascade');
            $table->index(['question_id', 'category_id']);
        });

        // 素养类别表
        Schema::create('evaluation_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('类别名称');
            $table->text('description')->nullable()->comment('类别描述');
            $table->unsignedInteger('course_id')->comment('学科ID');
            $table->unsignedInteger('parent_id')->default(0)->comment('父类别ID');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(1)->comment('状态 0:禁用 1:启用');
            $table->timestamps();
            
            $table->index(['course_id', 'parent_id', 'sort']);
        });

        // 试卷表
        Schema::create('evaluation_papers', function (Blueprint $table) {
            $table->id();
            $table->string('paper_name', 200)->comment('试卷名称');
            $table->unsignedInteger('course_id')->comment('学科ID');
            $table->tinyInteger('scenario_id')->comment('应用场景 1:素养测评 2:阶段测试 3:课时练习');
            $table->tinyInteger('grade_id')->comment('适用年级');
            $table->unsignedInteger('total_score')->default(0)->comment('总分');
            $table->unsignedInteger('question_count')->default(0)->comment('题目数量');
            $table->text('description')->nullable()->comment('试卷描述');
            $table->tinyInteger('status')->default(1)->comment('状态 0:禁用 1:启用');
            $table->unsignedInteger('creator_id')->nullable()->comment('创建者ID');
            $table->unsignedInteger('school_id')->nullable()->comment('学校ID');
            $table->timestamps();
            
            $table->index(['course_id', 'scenario_id', 'grade_id']);
            $table->index(['status', 'school_id']);
        });

        // 试卷题目关联表
        Schema::create('evaluation_paper_questions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('paper_id')->comment('试卷ID');
            $table->unsignedBigInteger('question_id')->comment('题目ID');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->unsignedInteger('score')->comment('分值');
            $table->timestamps();
            
            $table->foreign('paper_id')->references('id')->on('evaluation_papers')->onDelete('cascade');
            $table->foreign('question_id')->references('id')->on('evaluation_questions')->onDelete('cascade');
            $table->unique(['paper_id', 'question_id']);
            $table->index(['paper_id', 'sort']);
        });

        // 答题记录表
        Schema::create('evaluation_answers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('paper_id')->comment('试卷ID');
            $table->unsignedBigInteger('question_id')->comment('题目ID');
            $table->unsignedInteger('student_id')->comment('学生ID');
            $table->unsignedInteger('distribution_id')->nullable()->comment('分发ID');
            $table->text('answer_content')->nullable()->comment('答案内容');
            $table->decimal('score', 8, 2)->default(0)->comment('得分');
            $table->unsignedInteger('used_time')->default(0)->comment('用时（秒）');
            $table->tinyInteger('is_correct')->nullable()->comment('是否正确 0:错误 1:正确 null:未判断');
            $table->timestamp('answered_at')->nullable()->comment('答题时间');
            $table->timestamps();
            
            $table->foreign('paper_id')->references('id')->on('evaluation_papers');
            $table->foreign('question_id')->references('id')->on('evaluation_questions');
            $table->index(['student_id', 'paper_id']);
            $table->index(['distribution_id', 'answered_at']);
        });

        // 答题日志表
        Schema::create('evaluation_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('paper_id')->comment('试卷ID');
            $table->unsignedInteger('grade_id')->comment('年级ID');
            $table->unsignedInteger('class_id')->comment('班级ID');
            $table->unsignedInteger('student_id')->comment('学生ID');
            $table->unsignedInteger('member_id')->comment('用户ID');
            $table->unsignedBigInteger('distribution_id')->comment('分发ID');
            $table->unsignedInteger('used_time')->default(0)->comment('用时（秒）');
            $table->unsignedInteger('check_member_id')->default(0)->comment('批阅老师ID');
            $table->decimal('score', 8, 2)->default(0)->comment('总分');
            $table->tinyInteger('check_status')->default(0)->comment('批阅状态 0:待批阅 1:已批阅');
            $table->string('literacy_level', 20)->nullable()->comment('素养水平');
            $table->unsignedInteger('school_id')->comment('学校ID');
            $table->unsignedInteger('school_district_id')->default(0)->comment('校区ID');
            $table->tinyInteger('status')->default(0)->comment('状态 0:正常 -1:删除');
            $table->timestamps();

            $table->foreign('paper_id')->references('id')->on('evaluation_papers');
            $table->foreign('distribution_id')->references('id')->on('evaluation_distributions');
            $table->index(['member_id', 'paper_id', 'distribution_id']);
            $table->index(['school_id', 'grade_id', 'class_id']);
            $table->index(['check_status', 'literacy_level']);
        });

        // 知识点表
        Schema::create('evaluation_knowledges', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('知识点名称');
            $table->text('description')->nullable()->comment('知识点描述');
            $table->unsignedInteger('course_id')->comment('学科ID');
            $table->unsignedBigInteger('parent_id')->default(0)->comment('父知识点ID');
            $table->tinyInteger('is_high')->default(0)->comment('是否高中 0:初中 1:高中');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(0)->comment('状态 0:正常 -1:删除');
            $table->timestamps();

            $table->index(['course_id', 'parent_id', 'status']);
            $table->index(['is_high', 'status']);
        });

        // 题目类型表
        Schema::create('evaluation_question_types', function (Blueprint $table) {
            $table->id();
            $table->string('type_name', 50)->comment('类型名称');
            $table->text('description')->nullable()->comment('类型描述');
            $table->string('code', 20)->nullable()->comment('类型代码');
            $table->tinyInteger('is_subjective')->default(0)->comment('是否主观题 0:客观题 1:主观题');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(0)->comment('状态 0:正常 -1:删除');
            $table->timestamps();

            $table->unique(['type_name', 'status']);
            $table->unique(['code', 'status']);
            $table->index(['is_subjective', 'status']);
        });

        // 分发表
        Schema::create('evaluation_distributions', function (Blueprint $table) {
            $table->id();
            $table->string('title', 200)->comment('分发标题');
            $table->text('paper_ids')->comment('试卷IDs，逗号分隔');
            $table->unsignedInteger('grade_id')->default(0)->comment('年级ID');
            $table->text('class_ids')->nullable()->comment('班级IDs，逗号分隔');
            $table->text('member_ids')->nullable()->comment('学生IDs，逗号分隔');
            $table->unsignedInteger('distribution_by')->comment('分发人ID');
            $table->unsignedInteger('role_id')->default(0)->comment('角色ID');
            $table->unsignedInteger('from_id')->default(0)->comment('来源ID');
            $table->text('school_ids')->comment('学校IDs，逗号分隔');
            $table->text('description')->nullable()->comment('分发描述');
            $table->timestamp('start_time')->nullable()->comment('开始时间');
            $table->timestamp('end_time')->nullable()->comment('结束时间');
            $table->unsignedInteger('time_limit')->default(0)->comment('时间限制（分钟）');
            $table->tinyInteger('status')->default(0)->comment('状态 0:正常 -1:删除');
            $table->unsignedInteger('creator_id')->nullable()->comment('创建者ID');
            $table->unsignedInteger('school_id')->nullable()->comment('学校ID');
            $table->timestamps();

            $table->index(['status', 'school_id']);
            $table->index(['start_time', 'end_time']);
            $table->index(['distribution_by', 'status']);
        });

        // 分发详情表
        Schema::create('evaluation_distribution_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('distribution_id')->comment('分发ID');
            $table->unsignedBigInteger('paper_id')->comment('试卷ID');
            $table->text('teacher_member_ids')->nullable()->comment('老师IDs，逗号分隔');
            $table->timestamp('start_time')->nullable()->comment('开始时间');
            $table->unsignedInteger('exam_duration')->default(0)->comment('考试时长（分钟）');
            $table->tinyInteger('target_type')->default(1)->comment('目标类型 1:班级 2:学生');
            $table->unsignedInteger('target_id')->default(0)->comment('目标ID（班级ID或学生ID）');
            $table->tinyInteger('status')->default(0)->comment('状态 0:正常 -1:删除');
            $table->timestamps();

            $table->foreign('distribution_id')->references('id')->on('evaluation_distributions')->onDelete('cascade');
            $table->foreign('paper_id')->references('id')->on('evaluation_papers');
            $table->index(['distribution_id', 'paper_id', 'status']);
        });

        // 分发老师学生分配表
        Schema::create('evaluation_distribution_teachers_students', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('distribution_id')->comment('分发ID');
            $table->unsignedBigInteger('paper_id')->comment('试卷ID');
            $table->unsignedInteger('teacher_member_id')->comment('老师ID');
            $table->text('member_ids')->comment('负责的学生IDs，逗号分隔');
            $table->tinyInteger('status')->default(0)->comment('状态 0:正常 -1:删除');
            $table->timestamps();

            $table->foreign('distribution_id')->references('id')->on('evaluation_distributions')->onDelete('cascade');
            $table->foreign('paper_id')->references('id')->on('evaluation_papers');
            $table->index(['distribution_id', 'paper_id', 'teacher_member_id']);
            $table->index(['teacher_member_id', 'status']);
        });

        // 题目类型表
        Schema::create('evaluation_question_types', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50)->comment('类型名称');
            $table->string('code', 20)->comment('类型代码');
            $table->text('description')->nullable()->comment('类型描述');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(1)->comment('状态 0:禁用 1:启用');
            $table->timestamps();
            
            $table->unique('code');
            $table->index(['status', 'sort']);
        });

        // 知识点表
        Schema::create('evaluation_knowledges', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('知识点名称');
            $table->text('description')->nullable()->comment('知识点描述');
            $table->unsignedInteger('course_id')->comment('学科ID');
            $table->unsignedInteger('parent_id')->default(0)->comment('父知识点ID');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(1)->comment('状态 0:禁用 1:启用');
            $table->timestamps();
            
            $table->index(['course_id', 'parent_id', 'sort']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('evaluation_distribution_details');
        Schema::dropIfExists('evaluation_distributions');
        Schema::dropIfExists('evaluation_answers');
        Schema::dropIfExists('evaluation_paper_questions');
        Schema::dropIfExists('evaluation_papers');
        Schema::dropIfExists('evaluation_category_portions');
        Schema::dropIfExists('evaluation_question_options');
        Schema::dropIfExists('evaluation_questions');
        Schema::dropIfExists('evaluation_categories');
        Schema::dropIfExists('evaluation_question_types');
        Schema::dropIfExists('evaluation_knowledges');
    }
};
