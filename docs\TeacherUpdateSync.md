# 教师更新和批量更新同步功能说明

## 概述

为教师的修改（update）和批量修改功能添加了同步功能，确保与新增功能保持一致的同步条件（学校+账号）。

## 功能特点

### 1. 单个教师更新同步

**触发条件：**
- 教师信息更新后自动触发同步
- 同步失败不影响主流程，只记录日志

**同步数据格式：**
```php
$sync_request_data = [
    'teacher_name' => $teacher->teacher_name,
    'username' => $teacher->user->username,
    'gender' => $teacher->user->gender,
    'school_campus_id' => $teacher->school_campus_id,
    'roles' => $teacher->user->roles->pluck('type')->toArray()
];
```

### 2. 批量教师更新同步

**请求数据格式：**
```json
{
    "teacher_ids": [1, 2, 3],
    "update_data": {
        "teacher_name": "新名称",
        "roles": [2, 3]
    }
}
```

**同步条件：**
- 学校ID + 用户名作为唯一标识
- 与新增功能保持一致的同步逻辑

## API接口

### 1. 单个教师更新
```
PUT /api/teachers/{id}
```

**请求参数：**
```json
{
    "teacher_name": "教师姓名",
    "roles": [2, 3]
}
```

**响应：**
```json
{
    "code": 200,
    "message": "更新成功",
    "data": {
        "id": 1,
        "teacher_name": "教师姓名",
        "user_id": 123,
        // ... 其他字段
    }
}
```

### 2. 批量教师更新
```
PUT /api/teachers/batch
```

**请求参数：**
```json
{
    "teacher_ids": [1, 2, 3],
    "update_data": {
        "teacher_name": "新名称",
        "roles": [2, 3]
    }
}
```

**响应：**
```json
{
    "code": 200,
    "message": "批量更新成功",
    "data": [
        {
            "id": 1,
            "teacher_name": "新名称",
            // ... 其他字段
        },
        // ... 其他教师
    ]
}
```

## 实现细节

### 1. TeacherService::update方法

```php
public function update(Request $request, $id): ?Teacher
{
    // ... 更新逻辑 ...
    
    DB::commit();

    // 调用同步功能
    try {
        $this->syncSingleTeacherUpdate($request, $record);
    } catch (\Exception $e) {
        \Log::warning('教师更新后同步失败', [
            'teacher_id' => $record->id,
            'error' => $e->getMessage()
        ]);
    }

    return Teacher::find($record->id);
}
```

### 2. TeacherService::batchUpdate方法

```php
public function batchUpdate(Request $request): array
{
    // ... 批量更新逻辑 ...
    
    DB::commit();
    
    // 批量同步
    try {
        $this->syncBatchTeachersUpdate($updated_teachers);
    } catch (\Exception $e) {
        \Log::warning('批量教师更新后同步失败', [
            'error' => $e->getMessage(),
            'teacher_count' => count($updated_teachers)
        ]);
    }
    
    return $updated_teachers;
}
```

### 3. 同步方法实现

**TeacherService中的调用：**
```php
// 单个教师更新同步
try {
    $dataSyncService = app(\App\Services\DataSync\DataSyncService::class);
    $sync_result = $dataSyncService->syncSingleTeacherUpdate($record);
    \Log::info('教师更新同步结果', $sync_result);
} catch (\Exception $e) {
    \Log::warning('教师更新后同步失败', [
        'teacher_id' => $record->id,
        'error' => $e->getMessage()
    ]);
}

// 批量教师更新同步
try {
    $dataSyncService = app(\App\Services\DataSync\DataSyncService::class);
    $sync_result = $dataSyncService->syncBatchTeachersUpdate($updated_teachers);
    \Log::info('批量教师更新同步结果', $sync_result);
} catch (\Exception $e) {
    \Log::warning('批量教师更新后同步失败', [
        'error' => $e->getMessage(),
        'teacher_count' => count($updated_teachers)
    ]);
}
```

**DataSyncService中的实现：**
```php
// 单个教师更新同步
public function syncSingleTeacherUpdate(\App\Models\School\System\Teacher $teacher): array
{
    // 重新加载教师数据
    $teacher = \App\Models\School\System\Teacher::with(['user.roles', 'school', 'schoolCampus'])
        ->find($teacher->id);

    // 准备同步数据
    $sync_data = [
        'teacher_name' => $teacher->teacher_name,
        'username' => $teacher->user->username,
        'gender' => $teacher->user->gender,
        'school_campus_id' => $teacher->school_campus_id,
        'roles' => $teacher->user->roles->pluck('type')->toArray()
    ];

    // 创建模拟Request对象并调用同步
    $mock_request = new \Illuminate\Http\Request();
    $mock_request->merge($sync_data);
    return $this->syncSingleTeacher($mock_request);
}

// 批量教师更新同步
public function syncBatchTeachersUpdate(array $teachers): array
{
    // 准备批量同步数据
    $sync_data = [
        'school_campus_id' => $teachers[0]->school_campus_id,
        'teachers' => []
    ];

    foreach ($teachers as $teacher) {
        $teacher = \App\Models\School\System\Teacher::with(['user.roles'])->find($teacher->id);

        if ($teacher && $teacher->user) {
            $sync_data['teachers'][] = [
                'teacher_name' => $teacher->teacher_name,
                'gender' => $teacher->user->gender == 1 ? '男' : '女',
                'username' => $teacher->user->username,
                'role_name' => $this->getRoleNameFromTypes($teacher->user->roles->pluck('type')->toArray())
            ];
        }
    }

    // 调用批量同步方法
    return $this->syncBatchTeachers($sync_data);
}
```

## 数据流程

### 单个教师更新流程
```
TeacherController::update()
    ↓
TeacherService::update()
    ↓ (更新教师)
DB::commit()
    ↓ (调用同步服务)
DataSyncService::syncSingleTeacherUpdate()
    ↓
DataSyncService::syncSingleTeacher()
    ↓
更新ysy_member和ysy_teacher表
```

### 批量教师更新流程
```
TeacherController::batchUpdate()
    ↓
TeacherService::batchUpdate()
    ↓ (批量更新教师)
DB::commit()
    ↓ (调用同步服务)
DataSyncService::syncBatchTeachersUpdate()
    ↓
DataSyncService::syncBatchTeachers()
    ↓
更新ysy_member和ysy_teacher表
```

## 注意事项

1. **同步条件一致性**：更新同步使用与新增相同的条件（学校ID + 用户名）
2. **容错性**：同步失败不影响主业务流程
3. **日志记录**：详细记录同步结果，便于问题排查
4. **数据格式**：确保同步数据格式与新增时保持一致
5. **角色转换**：自动将角色类型转换为对应的角色名称（教务/老师）

## 优势

1. **功能完整性**：覆盖了教师的增删改查全生命周期同步
2. **数据一致性**：确保主数据库和同步数据库的数据一致
3. **维护性**：统一的同步逻辑，便于维护和扩展
4. **可靠性**：完善的错误处理和日志记录
