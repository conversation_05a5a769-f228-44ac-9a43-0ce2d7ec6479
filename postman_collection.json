{"info": {"name": "用户API测试集合", "description": "测试用户Token生成和获取用户详细信息的API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "item": [{"name": "1. 生成Token", "event": [{"listen": "test", "script": {"exec": ["// 检查响应状态", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// 检查响应格式和token", "pm.test(\"Response has correct format and token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData.status).to.eql('success');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('token');", "});", "", "// 保存token到环境变量", "if (pm.response.code === 200) {", "    var jsonData = pm.response.json();", "    if (jsonData.status === 'success' && jsonData.data.token) {", "        pm.collectionVariables.set(\"token\", jsonData.data.token);", "        console.log(\"Token saved: \" + jsonData.data.token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"test_user\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/user/generate-token", "host": ["{{base_url}}"], "path": ["user", "generate-token"]}, "description": "生成用户Token"}, "response": []}, {"name": "2. 获取用户详细信息 (<PERSON><PERSON>)", "event": [{"listen": "test", "script": {"exec": ["// 检查响应状态", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// 检查响应格式和用户信息", "pm.test(\"Response has correct format and user data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData.status).to.eql('success');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('id');", "    pm.expect(jsonData.data).to.have.property('username');", "});", "", "// 检查角色信息", "pm.test(\"User has role info\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.role_info) {", "        pm.expect(jsonData.data.role_info).to.have.property('role_types');", "        pm.expect(jsonData.data.role_info).to.have.property('roles');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/user/detail", "host": ["{{base_url}}"], "path": ["user", "detail"]}, "description": "使用Bearer Token获取用户详细信息"}, "response": []}, {"name": "3. 测试无效Token", "event": [{"listen": "test", "script": {"exec": ["// 检查响应状态应该是401", "pm.test(\"Status code is 401 for invalid token\", function () {", "    pm.response.to.have.status(401);", "});", "", "// 检查错误信息", "pm.test(\"Response has error message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('error');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer invalid_token_here"}], "url": {"raw": "{{base_url}}/user/detail", "host": ["{{base_url}}"], "path": ["user", "detail"]}, "description": "测试无效Token的错误处理"}, "response": []}, {"name": "4. 测试缺少Token", "event": [{"listen": "test", "script": {"exec": ["// 检查响应状态应该是401", "pm.test(\"Status code is 401 for missing token\", function () {", "    pm.response.to.have.status(401);", "});", "", "// 检查错误信息", "pm.test(\"Response has error message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('error');", "    pm.expect(jsonData.error).to.include('Token not provided');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/user/detail", "host": ["{{base_url}}"], "path": ["user", "detail"]}, "description": "测试缺少Token的错误处理"}, "response": []}, {"name": "5. 生成Token - 缺少用户名", "event": [{"listen": "test", "script": {"exec": ["// 检查响应状态应该是400", "pm.test(\"Status code is 400 for missing username\", function () {", "    pm.response.to.have.status(400);", "});", "", "// 检查错误信息", "pm.test(\"Response has error message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('error');", "    pm.expect(jsonData.error).to.include('Username not provided');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/user/generate-token", "host": ["{{base_url}}"], "path": ["user", "generate-token"]}, "description": "测试生成Token时缺少用户名的错误处理"}, "response": []}]}