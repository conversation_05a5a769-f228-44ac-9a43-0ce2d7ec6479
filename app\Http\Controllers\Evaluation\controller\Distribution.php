<?php
/**
 * Created by PhpStorm.
 * User: yangl
 * Date: 2019/11/20
 * Time: 13:52
 */
namespace app\evaluation\controller;
use app\login\controller\ApiAuth;


/**
 * Class Questions
 * @package app\evaluation\controller
 */
class Distribution extends ApiAuth
{
    public function __construct()
    {
        parent::__construct();
        $this->distribution = new \app\evaluation\service\Distribution();
    }
    /**
     * 模块：素养测评-测评管理
     * @SWG\Post(path="/evaluation/distribution",
     *   tags={"素养测评-测评管理:distribution"},
     *   summary="试卷分发",
     *   @SWG\Parameter(
     *     in="formData",
     *     name="title",
     *     type="string",
     *     description="分发主题或测评名称，(均有此项)",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="paper_ids",
     *     type="string",
     *     description="试卷id,一套试卷包含多张试卷id,用逗号拼接,(均有此项)",
     *     required=true,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="from_id",
     *     type="integer",
     *     description="admin分发的distribution_id,作为教务二次分发的来源id,(教务端)",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="grade_id",
     *     type="string",
     *     description="年级ID,允许为多个逗号拼接,(教务端)",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="class_ids",
     *     type="string",
     *     description="分发班级,(教务端)",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="member_ids",
     *     type="string",
     *     description="分发学生的member_id,多个用逗号拼接,(教务端)",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="school_ids",
     *     type="string",
     *     description="关连学校ID，多个用,拼接,(admin端)",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="formData",
     *     name="distribution_arr",
     *     type="string",
     *     description="试卷分发详细信息,json数组包含paper_id，exam_duration，start_time，teacher_ids,(教务端)",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-测评管理
     * @SWG\Put(path="/evaluation/distribution/{id}",
     *   tags={"素养测评-测评管理:distribution"},
     *   summary="试卷分发修改",
     * @SWG\Parameter(
     * in="path",
     * name="id",
     * type="integer",
     * description="分发id,(均有此项)",
     * required=true,
     * ),
     * @SWG\Parameter(
     * in="body",
     * name="data",
     * description="题目修改",
     * required=true,
     * @SWG\Schema(
     *          type="object",
     *          @SWG\Property(property="title", type="string", description="分发主题或测评名称,(均有此项)"),
     *          @SWG\Property(property="paper_ids", type="string", description="试卷id,一套试卷包含多张试卷id,用逗号拼接,(均有此项)"),
     *          @SWG\Property(property="school_ids", type="string", description="关连学校ID，多个用,拼接，(admin端)"),
     *          @SWG\Property(property="from_id", type="integer", description="admin分发的distribution_id,作为教务二次分发的来源id,(教务端)"),
     *          @SWG\Property(property="grade_id", type="string", description="年级ID,允许为多个逗号拼接,(教务端)"),
     *          @SWG\Property(property="class_ids", type="string", description="分发班级,(教务端)"),
     *          @SWG\Property(property="member_ids", type="string", description="分发学生的member_id,多个用逗号拼接,(教务端)"),
     *          @SWG\Property(property="distribution_arr", type="string", description="试卷分发详细信息,json数组包含paper_id，exam_duration，start_time，teacher_ids(老师的member_id),(教务端)"),
     *  )
     * ),
     * description="数据说明：",
     * produces={"application/json"},
     * @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-测评管理
     * @SWG\Delete(path="/evaluation/distribution/{id}",
     *   tags={"素养测评-测评管理:distribution"},
     *   summary="试卷分发删除",
     *   @SWG\Parameter(
     *     in="path",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    /**
     * 模块：素养测评-测评管理
     * @SWG\Get(path="/evaluation/distribution",
     *   tags={"素养测评-测评管理:distribution"},
     *   summary="试卷分发查询",
    @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="ID",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="papers_name",
     *     type="string",
     *     description="关键字查询",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="grade_id",
     *     type="integer",
     *     description="年级",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="start_time",
     *     type="datetime",
     *     description="测评时间左区间，格式为 'YYYY-MM-DD HH:MM:SS'",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="end_time",
     *     type="datetime",
     *     description="测评时间右区间，格式为 'YYYY-MM-DD HH:MM:SS'",
     *     required=false,
     *   ),
     @SWG\Parameter(
     *     in="query",
     *     name="status",
     *     type="integer",
     *     description="状态启用0，禁用1，删除 -1 ",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="pagesize",
     *     type="integer",
     *     description="每页显示记录数",
     *     required=false,
     *   ),
    @SWG\Parameter(
     *     in="query",
     *     name="page",
     *     type="integer",
     *     description="页码",
     *     required=false,
     *   ),
     *   @SWG\Response(
     *     response="200",
     *     description="操作成功"
     *  ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    
    public function distribution(){

        $data = $this->distribution->hand_out();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-测评管理
     * @SWG\Put(path="/evaluation/distribution/status",
     *   tags={"素养测评-测评管理:distribution"},
     *   summary="试卷分发状态修改",
     *   @SWG\Parameter(
     *     in="query",
     *     name="id",
     *     type="integer",
     *     description="id ",
     *     required=true,
     *   ),
     * @SWG\Parameter(
     * in="body",
     * name="data",
     * description="试卷分发状态修改",
     * required=true,
     * @SWG\Schema(
     * type="object",
     * @SWG\Property(property="status", type="integer", description="更改状态,禁用为1，启用为0")
     * )
     * ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function status(){

        $data = $this->distribution->set_status();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-组卷管理
     * @SWG\Get(path="/evaluation/take_district_grade_class_student_linkage",
     *   tags={"素养测评-测评管理:distribution"},
     *   summary="获取校区年级班级学生四级联动",
     *   @SWG\Parameter(
     *     in="query",
     *     name="level",
     *     type="integer",
     *     description="联动到第几级",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function take_district_grade_class_student_linkage(){

        $data = $this->distribution->take_district_grade_class_student_linkage();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-组卷管理
     * @SWG\Get(path="/evaluation/take_course_grade_paper_linkage",
     *   tags={"素养测评-测评管理:distribution"},
     *   summary="获取学科年级卷名三级联动",
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function take_course_grade_paper_linkage(){

        $data = $this->distribution->take_course_grade_paper_linkage();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-组卷管理
     * @SWG\Get(path="/evaluation/take_schools",
     *   tags={"素养测评-测评管理:distribution"},
     *   summary="admin获取学校",
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function take_schools(){
        //权限判断
        if(selfRbac() != 999){
            apiReturn([],'无权限',-5);
        }
        $data = $this->distribution->take_schools();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-组卷管理
     * @SWG\Get(path="/evaluation/take_teachers",
     *   tags={"素养测评-测评管理:distribution"},
     *   summary="教务获取所有老师",
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function take_teachers(){
        //权限判断
        if(selfRbac() != 2){
            apiReturn([],'无权限',-5);
        }
        $data = $this->distribution->take_teachers();
        apiReturn($data);
    }

    /**
     * 模块：素养测评-组卷管理
     * @SWG\Get(path="/evaluation/distribution_name_list",
     *   tags={"素养测评-测评管理:distribution"},
     *   summary="获取测评名称列表",
     *   @SWG\Parameter(
     *     in="query",
     *     name="school_district",
     *     type="integer",
     *     description="校区id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="grade_id",
     *     type="integer",
     *     description="年级id",
     *     required=false,
     *   ),
     *   @SWG\Parameter(
     *     in="query",
     *     name="class_id",
     *     type="integer",
     *     description="班级id,多个用逗号拼接",
     *     required=false,
     *   ),
     *   description="数据说明：",
     *   produces={"application/json"},
     *   @SWG\Response(response="200", description="操作成功")
     * )
     */
    public function distribution_name_list(){
        $data = $this->distribution->distribution_name_list();
        apiReturn($data);
    }

}