<?php

namespace App\Http\Controllers;

use App\Http\Requests\LoginRequest;
use App\Services\UserService;
use Illuminate\Http\Request;

class LoginController extends Controller
{

    /**
     * 用户登录
     *
     * 对于老翼生涯系统用户密码采用MD5加密和当前jwt密钥进行加密不一致问题解决方法：
     *
     * 1. 保留MD5密码
     * 在老系统中，用户的密码是使用MD5加密的。为了确保老用户能够继续使用他们的密码，你可以在新系统中暂时保留MD5密码，并在用户首次登录时将其转换为新系统的加密方式。
     *
     * 2. 在用户首次登录时迁移密码
     * 当老用户首次登录新系统时，你可以通过以下步骤迁移密码：
     *
     * 验证MD5密码：用户登录时，使用MD5加密用户输入的密码，并与数据库中存储的MD5密码进行比对。
     *
     * 如果匹配，则使用新系统的加密方式（如Laravel的bcrypt或password_hash）重新加密密码，并更新数据库中的密码字段。
     *
     * 删除MD5密码：在密码迁移完成后，删除数据库中存储的MD5密码，确保安全性。
     */
    public function login(LoginRequest $request, UserService $userService)
    {
        return $userService->login($request);
    }

}
