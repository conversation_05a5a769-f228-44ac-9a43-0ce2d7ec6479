<?php

namespace App\Services\School\Assessment\GroupReport\Capability;

use App\Services\School\Assessment\GroupReport\AbstractGroupReportService;

/**
 * 能力评估团体报告抽象服务类
 * 
 * 该类为各种能力评估团体报告提供基础功能，包括平均得分、等级比例和均衡性分析
 */
abstract class AbstractCapabilityService extends AbstractGroupReportService
{
    /**
     * 维度名称列表
     * 
     * @var array
     */
    protected array $dimension_names = [];
    
    /**
     * 维度分类
     * 
     * @var array
     */
    protected array $dimension_category = [];
    
    /**
     * 配置信息
     * 
     * @var array
     */
    protected array $config = [];

    /**
     * 构造函数
     * 
     * 初始化配置信息和维度数据
     */
    public function __construct()
    {
        $this->config = config('assessment.capability.' . $this->getConfigKey());
        $this->dimension_names = $this->config['dimension_names'];
        $this->dimension_category = $this->processDimensionCategory($this->config['category']);
    }

    /**
     * 获取配置键名
     * 
     * @return string 配置键名
     */
    abstract protected function getConfigKey(): string;

    /**
     * 生成评估报告
     * 
     * @param array $params 请求参数
     * @param int $school_id 学校ID
     * @return array 评估报告数据
     */
    public function generateReport(array $params, int $school_id): array
    {
        // 获取评估数据
        $filtered_assessments = $this->getAssessmentData($params, $school_id, $this->getFilterConditions($params));
        $filtered_assessment_with_results = array_filter($filtered_assessments, fn($v) => !empty($v['standard_results']));
        
        // 统计数据
        $class_statistics = $this->calculateClassStatistics($filtered_assessments);
        
        // 由子类实现具体的报告生成逻辑
        return $this->generateReportData($filtered_assessment_with_results, $class_statistics, $params);
    }

    /**
     * 处理维度分类
     * 
     * @param array $category 分类数据
     * @return array 处理后的分类数据
     */
    protected function processDimensionCategory(array $category): array
    {
        return $category;
    }

    /**
     * 是否移除第一个维度
     * 
     * @return bool 是否移除
     */
    protected function shouldRemoveFirstDimension(): bool
    {
        return false;
    }

    /**
     * 是否移除总分
     * 
     * @return bool 是否移除
     */
    protected function shouldRemoveTotal(): bool
    {
        return true;
    }

    /**
     * 生成报告数据
     * 
     * @param array $all_assessment_with_results 所有有结果的评估数据
     * @param array $filtered_assessment_with_results 过滤后有结果的评估数据
     * @param array $class_statistics 班级统计数据
     * @param array $params 请求参数
     * @return array 报告数据
     */
    protected function generateReportData(
        array $filtered_assessment_with_results, 
        array $class_statistics,
        array $params
    ): array {
        return [
            'member_count' => $this->getMemberCount($class_statistics),
            'avg' => $this->calculateAveragePerformance(
                $filtered_assessment_with_results,
                $class_statistics
            ),
            'level' => $this->calculateLevelDistribution(
                $filtered_assessment_with_results,
                $class_statistics['category_data']
            ),
            'balance' => $this->calculateBalanceAnalysis(
                $filtered_assessment_with_results,
                $class_statistics['category_data']
            )
        ];
    }

    /**
     * 计算平均表现
     * 
     * @param array $all_data 所有数据
     * @param array $selected_data 选中数据
     * @param array $class_stats 班级统计数据
     * @return array 平均表现数据
     */
    protected function calculateAveragePerformance(array $selected_data, array $class_stats): array
    {
        $norm_scores = $this->config['norm_score']['avg'];
        $selected_scores = $this->processScores($selected_data);

        return [
            'header'=>[
                'number' => 2,
                'name' => '平均得分',
            ],
            'avg' => [
                'axis' => $this->dimension_names,
                'legend' => ['常模', '本次'],
                'series' => [
                    '常模' => $norm_scores,
                    '本次' => $selected_scores['averages'],
                ],
                'sort' => $this->getSortedDimensions($selected_scores['averages']),
                'list' => $this->getAverageList($selected_scores['averages'])
            ],
            'avg_class' => $this->getClassAverages($class_stats['category_data'])
        ];
    }

    /**
     * 计算等级分布
     * 
     * @param array $assessments 评估数据
     * @param array $class_data 班级数据
     * @return array 等级分布数据
     */
    protected function calculateLevelDistribution(array $assessments, array $class_data): array
    {
        $level_stats = $this->calculateLevelStats($assessments);
        
        return [
            'header'=>[
                'number' => 3,
                'name' => '等级比例',
            ],
            'level' => [
                'axis' => $this->dimension_names,
                'legend' => ['优秀比例', '一般比例', '不足比例'],
                'series' => $level_stats['series'],
                'series_table' => $level_stats['series_table'],
            ],
            'level_class' => $this->getClassLevelDistribution($class_data)
        ];
    }

    /**
     * 计算均衡性分析
     * 
     * @param array $assessments 评估数据
     * @param array $class_data 班级数据
     * @return array 均衡性分析数据
     */
    protected function calculateBalanceAnalysis(array $assessments, array $class_data): array
    {
        $scores = $this->processScores($assessments);
        return [
            'header'=>[
                'number' => 4,
                'name' => '均衡性对比',
            ],
            'balance' => [
                'axis' => $this->dimension_names,
                'series' => array_map(function($scores) {
                    return $this->calculateStandardDeviation($scores);
                }, $scores['raw'])
            ],
            'balance_class' => $this->getClassBalanceAnalysis($class_data)
        ];
    }

    /**
     * 计算标准差
     * 
     * @param array $scores 分数数组
     * @return float 标准差
     */
    private function calculateStandardDeviation(array $scores): float
    {
        if (count($scores) <= 1) {
            return 0.0;
        }

        $mean = array_sum($scores) / count($scores);
        $variance = array_map(function($score) use ($mean) {
            return pow($score - $mean, 2);
        }, $scores);

        return round(sqrt(array_sum($variance) / count($scores)), 2);
    }

    /**
     * 处理分数
     * 
     * @param array $assessments 评估数据
     * @return array 处理后的分数数据
     */
    protected function processScores(array $assessments): array
    {
        $dimension_count = count($this->dimension_names);
        $dimension_scores = array_fill(0, $dimension_count, []);
        
        foreach ($assessments as $assessment) {
            $standard_results = json_decode($assessment['standard_results'], true);
            $scores = [];
            
            // 从standard_results中提取维度分数
            foreach ($standard_results['dimensions'] as $dimension) {
                $scores[] = $dimension['score'];
            }
            
            if ($this->shouldRemoveFirstDimension()) {
                array_shift($scores);
            }
            
            // 添加维度数量校验
            $this->validateDimensionCount($scores);
            
            foreach ($scores as $index => $score) {
                $dimension_scores[$index][] = $score;
            }
        }

        return [
            'raw' => $dimension_scores,
            'averages' => array_map(function($scores) {
                return empty($scores) ? 0 : round(array_sum($scores) / count($scores), 2);
            }, $dimension_scores)
        ];
    }

    /**
     * 验证维度数量
     * 
     * @param array $scores 分数数组
     * @throws \RuntimeException 当维度数量与配置不一致时抛出异常
     */
    private function validateDimensionCount(array $scores): void
    {
        if (count($scores) !== count($this->dimension_names)) {
            throw new \RuntimeException('维度数量与配置不一致');
        }
    }

    /**
     * 获取排序后的维度
     * 
     * @param array $averages 平均值数组
     * @return array 排序后的维度
     */
    private function getSortedDimensions(array $averages): array
    {
        arsort($averages);
        return array_map(function($index) {
            return $this->dimension_names[$index];
        }, array_keys($averages));
    }

    /**
     * 获取班级平均值
     * 
     * @param array $class_data 班级数据
     * @return array 班级平均值数据
     */
    private function getClassAverages(array $class_data): array
    {
        $result = ['axis' => [], 'legend' => $this->dimension_names, 'series' => []];
        
        foreach ($class_data as $class_name => $data) {
            $result['axis'][] = $class_name;
            $scores = $this->processScores($data);
            $result['series'][$class_name] = $scores['averages'];
        }

        return $result;
    }

    /**
     * 获取班级等级分布
     * 
     * @param array $class_data 班级数据
     * @return array 班级等级分布数据
     */
    private function getClassLevelDistribution(array $class_data): array
    {
        $result = [];
        foreach ($class_data as $class_name => $data) {
            $result[$class_name] = $this->calculateLevelStats($data);
        }
        return $result;
    }

    /**
     * 获取班级均衡性分析
     * 
     * @param array $class_data 班级数据
     * @return array 班级均衡性分析数据
     */
    private function getClassBalanceAnalysis(array $class_data): array
    {
        $result = ['axis' => $this->dimension_names, 'series' => []];
        
        foreach ($class_data as $class_name => $data) {
            $scores = $this->processScores($data);
            
            $result['series'][$class_name] = array_map(function($dimension_scores) {
                return $this->calculateStandardDeviation($dimension_scores);
            }, $scores['raw']);
        }

        return $result;
    }

    /**
     * 获取平均值列表
     * 
     * @param array $averages 平均值数组
     * @return array 平均值列表
     */
    private function getAverageList(array $averages): array
    {
        $list = [];
        foreach ($averages as $index => $score) {
            $list[$index] = [
                'name' => $this->dimension_names[$index],
                'score' => $score
            ];
        }
        return $list;
    }

    /**
     * 计算等级统计
     * 
     * @param array $assessments 评估数据
     * @return array 等级统计数据
     */
    private function calculateLevelStats(array $assessments): array
    {
        $dimension_count = count($this->dimension_names);
        $stats = array_fill(0, $dimension_count, [
            'high' => 0,
            'mid' => 0,
            'low' => 0
        ]);
    
        foreach ($assessments as $assessment) {
            $standard_results = json_decode($assessment['standard_results'], true);
            $scores = [];
    
            // 从standard_results中提取维度分数
            foreach ($standard_results['dimensions'] as $dimension) {
                $scores[] = $dimension['score'];
            }
    
            if ($this->shouldRemoveFirstDimension()) {
                array_shift($scores);
            }
    
            foreach ($scores as $index => $score) {
                if ($score <= $this->dimension_category[$index]['low']) {
                    $stats[$index]['low']++;
                } elseif ($score <= $this->dimension_category[$index]['high']) {
                    $stats[$index]['mid']++;
                } else {
                    $stats[$index]['high']++;
                }
            }
        }

        $total = count($assessments);
        $excellent = [];
        $medium = [];
        $poor = [];
        foreach ($stats as $index => $item) {
            $excellent[] = [
                $item['high'],
                $total > 0 ? round($item['high'] / $total * 100, 2) : 0
            ];
            $medium[] = [
                $item['mid'],
                $total > 0 ? round($item['mid'] / $total * 100, 2) : 0
            ];
            $poor[] = [
                $item['low'],
                $total > 0 ? round($item['low'] / $total * 100, 2) : 0
            ];
        }

        return [
            'axis' => $this->dimension_names,
            'legend' => ['优秀比例', '一般比例', '不足比例'],
            'series' => [
                '优秀比例' => array_column($stats, 'high'),
                '一般比例' => array_column($stats, 'mid'),
                '不足比例' => array_column($stats, 'low')
            ],
            'series_table' => [
                '优秀比例' => $excellent,
                '一般比例' => $medium,
                '不足比例' => $poor
            ],
        ];
    }
}