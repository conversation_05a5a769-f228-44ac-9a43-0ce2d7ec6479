<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * 题目模型
 */
class Question extends Model
{
    protected $table = 'evaluation_questions';

    protected $fillable = [
        'is_common',
        'type_id',
        'grade',
        'course_id',
        'situation',
        'score',
        'knowlege_ids',
        'content',
        'answer',
        'analysis',
        'parent_id',
        'sort',
        'status',
        'creator_id',
        'school_id',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'is_common' => 'integer',
        'type_id' => 'integer',
        'grade' => 'integer',
        'course_id' => 'integer',
        'situation' => 'integer',
        'score' => 'integer',
        'parent_id' => 'integer',
        'sort' => 'integer',
        'status' => 'integer',
        'creator_id' => 'integer',
        'school_id' => 'integer',
    ];

    /**
     * 题目选项关联
     */
    public function options(): HasMany
    {
        return $this->hasMany(QuestionOption::class, 'question_id');
    }

    /**
     * 素养占比关联
     */
    public function categoryPortions(): HasMany
    {
        return $this->hasMany(CategoryPortion::class, 'question_id');
    }

    /**
     * 题目类型关联
     */
    public function questionType(): BelongsTo
    {
        return $this->belongsTo(QuestionType::class, 'type_id');
    }

    /**
     * 父题目关联
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Question::class, 'parent_id');
    }

    /**
     * 子题目关联
     */
    public function children(): HasMany
    {
        return $this->hasMany(Question::class, 'parent_id');
    }

    /**
     * 试卷关联（多对多）
     */
    public function papers(): BelongsToMany
    {
        return $this->belongsToMany(Papers::class, 'evaluation_paper_questions', 'question_id', 'paper_id')
            ->withPivot(['sort', 'score'])
            ->withTimestamps();
    }

    /**
     * 知识点关联
     */
    public function knowledges(): BelongsToMany
    {
        return $this->belongsToMany(Knowledges::class, 'question_knowledges', 'question_id', 'knowledge_id');
    }

    /**
     * 答题记录关联
     */
    public function answers(): HasMany
    {
        return $this->hasMany(EvaluationAnswer::class, 'question_id');
    }

    /**
     * 获取题目的素养类别
     */
    public function getCategories()
    {
        return $this->categoryPortions()->with('category')->get()->pluck('category');
    }

    /**
     * 获取题目的知识点ID数组
     */
    public function getKnowledgeIdsArray()
    {
        if (empty($this->knowlege_ids)) {
            return [];
        }
        return explode(',', $this->knowlege_ids);
    }

    /**
     * 设置知识点ID数组
     */
    public function setKnowledgeIdsArray(array $knowledgeIds)
    {
        $this->knowlege_ids = implode(',', $knowledgeIds);
    }

    /**
     * 作用域：按题目类型筛选
     */
    public function scopeByType($query, $typeId)
    {
        return $query->where('type_id', $typeId);
    }

    /**
     * 作用域：按年级筛选
     */
    public function scopeByGrade($query, $grade)
    {
        return $query->where('grade', $grade);
    }

    /**
     * 作用域：按学科筛选
     */
    public function scopeByCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：主题目（非子题）
     */
    public function scopeMainQuestions($query)
    {
        return $query->where('parent_id', 0);
    }

    /**
     * 作用域：子题目
     */
    public function scopeSubQuestions($query, $parentId = null)
    {
        $query = $query->where('parent_id', '>', 0);
        if ($parentId) {
            $query->where('parent_id', $parentId);
        }
        return $query;
    }

    /**
     * 获取题目完整信息（包含选项、素养占比等）
     */
    public function getFullInfo()
    {
        return $this->load([
            'options' => function ($query) {
                $query->orderBy('sort');
            },
            'categoryPortions.category',
            'questionType',
            'children' => function ($query) {
                $query->orderBy('sort');
            }
        ]);
    }

    /**
     * 检查题目是否可以删除
     */
    public function canDelete(): bool
    {
        // 检查是否有答题记录
        if ($this->answers()->exists()) {
            return false;
        }

        // 检查是否在试卷中使用
        if ($this->papers()->exists()) {
            return false;
        }

        return true;
    }

    /**
     * 复制题目
     */
    public function duplicate(array $overrides = []): Question
    {
        $attributes = $this->getAttributes();
        unset($attributes['id'], $attributes['created_at'], $attributes['updated_at']);
        
        $newQuestion = static::create(array_merge($attributes, $overrides));
        
        // 复制选项
        foreach ($this->options as $option) {
            $newQuestion->options()->create($option->getAttributes());
        }
        
        // 复制素养占比
        foreach ($this->categoryPortions as $portion) {
            $newQuestion->categoryPortions()->create($portion->getAttributes());
        }
        
        return $newQuestion;
    }
}
