<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use App\Services\Admin\AssessmentTemplateService;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;

/**
 * 测评模板管理控制器
 *
 * 用于管理霍兰德职业兴趣测评和MBTI职业性格测评的模板
 */
class AssessmentTemplateController extends Controller
{
    use CrudOperations;

    /**
     * 测评模板服务实例
     *
     * @var AssessmentTemplateService
     */
    protected $templateService;

    /**
     * 构造函数
     *
     * @param AssessmentTemplateService $templateService 测评模板服务实例
     */
    public function __construct(AssessmentTemplateService $templateService)
    {
        $this->templateService = $templateService;
    }

    /**
     * 获取测评模板类型列表
     *
     * @return \Illuminate\Http\JsonResponse 返回测评类型列表，包含holland和mbti两种类型
     */
    public function getTemplateTypes()
    {
        return $this->success($this->templateService->getTemplateTypes());
    }

    /**
     * 获取测评模板列表
     *
     * @param Request $request 请求实例，包含type和可选的code参数
     * @return \Illuminate\Http\JsonResponse 返回分页后的模板列表数据
     */
    public function index(Request $request)
    {

        $validator = $this->validateRequest($request, [
            'template_type' => 'required|in:holland,mbti,mi',
        ]);

        /*if ($validator->fails()) {
            return $this->error('template_type参数值错误');
        }*/

        $result = $this->templateService->getTemplateList($request);
        return $this->paginateSuccess($result['list'], $result['cnt']);
    }

    /**
     * 创建新的测评模板
     *
     * @param Request $request 请求实例，包含type和code等必要参数
     * @return \Illuminate\Http\JsonResponse 返回创建成功的消息和页码信息
     */
    public function store(Request $request)
    {
        $validator = $this->validateRequest($request, [
            'template_type' => 'required|in:holland,mbti,mi',
            // 'body' => 'required|json'
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        $this->templateService->store($request);

        // 获取当前页码信息，用于前端返回到正确的页面
        $currentPage = $request->input('current_page', 1);
        $pageSize = $request->input('page_size', 10);

        return $this->success([
            'message' => '创建成功',
            'current_page' => $currentPage,
            'page_size' => $pageSize
        ]);
    }

    /**
     * 更新指定的测评模板
     *
     * @param Request $request 请求实例，包含type和code等必要参数
     * @param int $id 要更新的模板ID
     * @return \Illuminate\Http\JsonResponse 返回更新成功的消息和页码信息
     */
    public function update(Request $request, $id)
    {
        $validator = $this->validateRequest($request, [
            'template_type' => 'required|in:holland,mbti,mi',
            //  'body' => 'required|json'
        ]);
        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        $this->templateService->update($request, $id);

        // 获取当前页码信息，用于前端返回到正确的页面
        $currentPage = $request->input('current_page', 1);
        $pageSize = $request->input('page_size', 10);

        return $this->success([
            'message' => '更新成功',
            'current_page' => $currentPage,
            'page_size' => $pageSize
        ]);
    }

    /**
     * 删除指定的测评模板
     *
     * @param Request $request 请求实例，包含type参数
     * @param int $id 要删除的模板ID
     * @return \Illuminate\Http\JsonResponse 返回删除成功的消息和页码信息
     */
    public function destroy(Request $request, $id)
    {
        $validator = $this->validateRequest($request, [
            'template_type' => 'required|in:holland,mbti,mi',
            'grade_type' => 'nullable|in:1,2'
        ]);
        if ($validator->fails()) {
            return $this->error('template_type参数值错误');
        }

        $this->templateService->destroy($request->template_type, $id);

        // 获取当前页码信息，用于前端返回到正确的页面
        $currentPage = $request->input('current_page', 1);
        $pageSize = $request->input('page_size', 10);

        return $this->success([
            'message' => '删除成功',
            'current_page' => $currentPage,
            'page_size' => $pageSize
        ]);
    }

    /**
     * 获取测评模板详情
     *
     * @param Request $request 请求实例，包含type参数
     * @param int $id 要查看的模板ID
     * @return \Illuminate\Http\JsonResponse 返回模板详情数据
     */
    public function show(Request $request)
    {
        $validator = $this->validateRequest($request, [
            'template_type' => 'required|in:holland,mbti,mi'
        ]);
        if ($validator->fails()) {
            return $this->error('template_type参数值错误');
        }

        $template = $this->templateService->getTemplateDetail($request->template_type, $request);
        return $this->success($template);
    }

    protected function getValidationRules()
    {
        return [
            'code' => 'required|string',
            'grade_type' => 'required|in:1,2',
            'appellation' => 'required|string',
            'preface' => 'required|string',
            'interpret' => 'required|string',
            'keywords' => 'required|string',
            'img_url' => 'nullable|string'
        ];
    }
    
    public function Single(Request $request)
    {
        $validator = $this->validateRequest($request, [
            'template_type' => 'required|in:holland,mbti,mi'
        ]);
        if ($validator->fails()) {
            return $this->error('template_type参数值错误');
        }

        $template = $this->templateService->getSingleDetail($request->template_type,$request);
        return $this->success($template);
    }

    /**
     * 获取编辑页面标题信息
     *
     * @param Request $request 请求实例，包含template_type和id参数
     * @param int $id 模板ID
     * @return \Illuminate\Http\JsonResponse 返回标题信息
     */
    public function getEditTitle(Request $request, $id)
    {
        $validator = $this->validateRequest($request, [
            'template_type' => 'required|in:holland,mbti,mi'
        ]);
        if ($validator->fails()) {
            return $this->error('template_type参数值错误');
        }

        $titleInfo = $this->templateService->getEditTitle($request->template_type, $id);
        return $this->success($titleInfo);
    }
}