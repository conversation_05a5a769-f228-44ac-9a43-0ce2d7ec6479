<?php
namespace app\psychassessment\logic;

class Loadpdf{
    protected $sftp;
    public function __construct()
    {
        $this->sftp = new \app\common\controller\Sftp();
    }
    /**
     * 根据member_id生成个人心理测评报告
     * @return array
     * 注意此处pdf生成是新方式
     * 注意此处logo是base64格式，也就是说数据库直接存的base64，是文件而非链接，原插件不支持http(s),提供接口的大佬(钱潇潇)在服务器上做了下载文件转base64的操作，但是测试下来没成功
     */
    public function psychassessment_create($member_id,$plan_id,$survey_type,$logo){
        switch ($survey_type){
            case 26:
                $url= 'https://'.$_SERVER['HTTP_HOST'].'/psychassessment/loadpdf/personal_pdf?member_id='.$member_id.'&plan_id='.$plan_id.'&survey_type='.$survey_type.'#/psy_assess/psy_assess_report_self_confidence/index';
                break;
            case 27:
                $url= 'https://'.$_SERVER['HTTP_HOST'].'/psychassessment/loadpdf/personal_pdf?member_id='.$member_id.'&plan_id='.$plan_id.'&survey_type='.$survey_type.'#/psy_assess/psy_assess_report_self_awareness/index';
                break;
            case 28:
                $url= 'https://'.$_SERVER['HTTP_HOST'].'/psychassessment/loadpdf/personal_pdf?member_id='.$member_id.'&plan_id='.$plan_id.'&survey_type='.$survey_type.'#/psy_assess/psy_assess_report_anxiety/index';
                break;
        }
        //$url_post = 'http://**************:8380/htmltopdf-1.0.0/htmlToPdf/commonPy';//本地
        $url_post = 'http://*************:8380/htmltopdf-1.0.0/htmlToPdf/commonPy';//线上
        #1.url生成地址
        $saveDir = '/psychassessment/pdf/'.date('Ymd');
        $dir = '/saas_upload'.$saveDir;
        if(!$this->sftp->ssh2_dir_exits($dir)){
            $this->sftp->ssh2_sftp_mchkdir($dir);
        }
        $str = '{"path":"/data/ysy/uploads_cdn'.$saveDir.'/psychassessment_'.$member_id.'_'.$plan_id.'_'.$survey_type.'.pdf","format":"A4","printBackground":"true","displayHeaderFooter": True,"headerTemplate":"<style>#header { padding: 0;}</style><div style=\"display: flex; justify-content: space-between; align-items: center; width: 100%; height: 20.23px; padding: 39.87px 53.55px 29.75px;\"><img src = \"'.$logo.'\" style=\"width: auto; height: 20.23px;\"><div style=\"font-weight: normal; font-size: 10px; line-height: 1; color: #C2C2C2;\">自信心评估报告</div></div>","footerTemplate": "<div></div>","start_page":2,"preferCSSPageSize":True,"margin": {"top": "31.6mm","bottom": "21mm","left":"0","right":"0"}}';
        $post_data['customDict'] = base64_encode($str);//不能传json对象，只能传字符串
        $post_data['url'] = base64_encode($url);
        #2.发起请求
        $report =request_post($url_post,json_encode($post_data));
        $pdf = objectToArray(json_decode($report));
        $pdf_url='https://s.yishengya.cn'.$dir.'/psychassessment_'.$member_id.'_'.$plan_id.'_'.$survey_type.'.pdf';

        //返回结果
        return array($pdf_url,$pdf['statue']);
    }

}