<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * 专业排名资源类
 * 
 * 该类用于格式化专业排名数据的响应，将字段名转换为小写加下划线格式
 */
class MajorRankResource extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->Id,
            'major_code' => $this->MajorCode,
            'major_name' => $this->MajorName,
            'rank' => $this->Rank,
            'college_id' => $this->CollegeId,
            'college_name' => $this->CollegeName,
            'start_rank' => $this->StartRank,
            'college_level' => $this->CollegeLevel,
            'year' => $this->Year,
            'level' => $this->Level,
            'score' => $this->Score,
            
            // 关联的学校信息
            'college' => $this->when($this->relationLoaded('college'), function () {
                return [
                    'id' => $this->college->ID,
                    'college_name' => $this->college->CollegeName,
                    'is_211' => $this->college->Is211,
                    'is_985' => $this->college->Is985,
                    'yxls' => $this->college->Yxls,
                    'yxtype' => $this->college->Yxtype,
                    'small_logo' => $this->college->SmallLogo,
                ];
            }),
        ];
    }
}
