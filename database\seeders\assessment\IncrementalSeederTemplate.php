<?php

namespace Database\Seeders\assessment;

use Database\Seeders\BaseIncrementalSeeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

ini_set('memory_limit', '2048M');

/**
 * 测评增量Seeder模板
 * 复制此文件并修改相应配置即可快速创建其他测评类型的增量Seeder
 */
class IncrementalSeederTemplate extends BaseIncrementalSeeder
{
    // 修改为对应的测评类型：career, competency, capability, psychology, subject
    protected string $assessment_type = 'template';

    // 修改为对应测评的映射关系
    private const SURVEYIDTOASSESSMENTID = [
        // 示例：
        // 1  => 1,
        // 18 => 2,
        // 等等...
    ];

    public function __construct($schoolId)
    {
        parent::__construct($schoolId);
    }

    protected function getSurveyIds(): array
    {
        return array_keys(self::SURVEYIDTOASSESSMENTID);
    }

    protected function getAssessmentIds(): array
    {
        return array_values(self::SURVEYIDTOASSESSMENTID);
    }

    protected function executeSeeder(): void
    {
        $lastProcessedId = $this->getLastProcessedId();
        $surveyIds = $this->getSurveyIds();

        if (empty($surveyIds)) {
            Log::info($this->assessment_type . '测评配置为空，跳过执行', [
                'school_id' => $this->school_id
            ]);
            return;
        }

        // 使用基类提供的增量查询方法（自动包含 > $lastProcessedId 条件）
        $studentSurveyList = $this->getIncrementalAssignmentQuery()
            ->select([
                'tasks.id as task_id',
                'tasks.assessment_id',
                'session.session_id',
                'session.member_id as student_member_id',
                'session.student_id',
                'session.create_time',
                'session.used_time',
                'session.result',
                'session.survey_id',
                'session.pdf_url',
                'session.id as survey_session_id'
            ])->get();

        if ($studentSurveyList->isEmpty()) {
            Log::info('没有新的' . $this->assessment_type . '测评数据', [
                'school_id' => $this->school_id,
                'last_processed_id' => $lastProcessedId
            ]);
            return;
        }

        // 获取学生班级映射
        $oldStudentIds = $studentSurveyList->pluck('student_id')->unique()->values()->toArray();
        $studentClassIdArr = DB::table('student_classes')
            ->whereIn('old_student_id', $oldStudentIds)
            ->select(['id', 'old_student_id'])
            ->pluck('id', 'old_student_id')
            ->toArray();

        // 处理数据
        $surveyToAssessmentMap = self::SURVEYIDTOASSESSMENTID;
        $assignmentsData = $studentSurveyList->map(function($item) use ($studentClassIdArr, $surveyToAssessmentMap) {
            return [
                'old_session_id' => $item->session_id,
                'old_student_id' => $item->student_id,
                'school_id' => $this->school_id,
                'assessment_task_id' => $item->task_id,
                'student_class_id' => $studentClassIdArr[$item->student_id] ?? 0,
                'student_id' => $item->student_member_id,
                'user_id' => $item->student_member_id,
                'duration' => $item->used_time,
                'results' => $item->result,
                'status' => 2,
                'assessment_id' => $surveyToAssessmentMap[$item->survey_id],
                'pdf_url' => $item->pdf_url,
                'created_at' => $item->create_time,
            ];
        })->toArray();

        // 插入数据
        if (!empty($assignmentsData)) {
            DB::transaction(function () use ($assignmentsData) {
                collect($assignmentsData)->chunk(1000)->each(function ($chunk) {
                    DB::table('assessment_task_assignments')->insert($chunk->toArray());
                });
            });

            // 更新执行日志
            $maxSurveySessionId = $studentSurveyList->max('survey_session_id');
            $this->updateExecutionLog($maxSurveySessionId, count($assignmentsData));

            Log::info($this->assessment_type . '测评任务分配完成', [
                'school_id' => $this->school_id,
                'total_records' => count($assignmentsData),
                'last_processed_survey_session_id' => $maxSurveySessionId,
            ]);
        }
    }

    /**
     * 如果该测评类型不需要创建任务，可以重写此方法返回 false
     */
    protected function shouldCreateTasks(): bool
    {
        return true;
    }
}
