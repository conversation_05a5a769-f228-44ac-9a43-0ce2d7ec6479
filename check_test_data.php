<?php
/**
 * 检查测试数据脚本
 * 
 * 使用方法：
 * php artisan tinker
 * 然后复制粘贴以下代码
 */

// 检查用户数据
echo "=== 检查测试数据 ===\n";

// 1. 检查用户表
$userCount = App\Models\User::count();
echo "用户总数: $userCount\n";

if ($userCount > 0) {
    $firstUser = App\Models\User::first();
    echo "第一个用户:\n";
    echo "- ID: {$firstUser->id}\n";
    echo "- 用户名: {$firstUser->username}\n";
    echo "- 真实姓名: {$firstUser->real_name}\n";
    echo "- 状态: {$firstUser->status}\n";
    
    // 检查角色
    $roles = $firstUser->roles;
    echo "- 角色数量: " . $roles->count() . "\n";
    foreach ($roles as $role) {
        echo "  * {$role->name} (类型: {$role->type})\n";
    }
    
    // 检查学生信息
    if ($firstUser->student) {
        echo "- 学生信息: 存在\n";
        echo "  * 学生姓名: {$firstUser->student->student_name}\n";
        echo "  * 学号: {$firstUser->student->student_no}\n";
    } else {
        echo "- 学生信息: 不存在\n";
    }
    
    // 检查教师信息
    if ($firstUser->teacher) {
        echo "- 教师信息: 存在\n";
        echo "  * 教师姓名: {$firstUser->teacher->teacher_name}\n";
    } else {
        echo "- 教师信息: 不存在\n";
    }
} else {
    echo "❌ 没有找到用户数据，请先创建测试用户\n";
}

// 2. 检查JWT配置
echo "\n=== 检查JWT配置 ===\n";
try {
    $secret = config('jwt.secret');
    if ($secret) {
        echo "✅ JWT Secret已配置\n";
    } else {
        echo "❌ JWT Secret未配置，请运行: php artisan jwt:secret\n";
    }
    
    $ttl = config('jwt.ttl');
    echo "JWT TTL: {$ttl} 分钟\n";
    
} catch (Exception $e) {
    echo "❌ JWT配置检查失败: " . $e->getMessage() . "\n";
}

// 3. 创建测试用户（如果需要）
echo "\n=== 创建测试用户 ===\n";
echo "如果需要创建测试用户，请运行以下代码：\n\n";

echo '$user = App\Models\User::create([' . "\n";
echo '    "organization_id" => 1,' . "\n";
echo '    "username" => "test_user",' . "\n";
echo '    "real_name" => "测试用户",' . "\n";
echo '    "gender" => 1,' . "\n";
echo '    "email" => "<EMAIL>",' . "\n";
echo '    "password" => bcrypt("password123"),' . "\n";
echo '    "status" => 1,' . "\n";
echo '    "creator" => "系统"' . "\n";
echo ']);' . "\n\n";

echo '// 分配角色' . "\n";
echo '$role = App\Models\Role::where("type", 1)->first(); // 学生角色' . "\n";
echo 'if ($role) {' . "\n";
echo '    $user->roles()->sync([$role->id]);' . "\n";
echo '}' . "\n\n";

echo "然后就可以使用以下信息测试：\n";
echo "用户名: test_user\n";
echo "密码: password123\n";
?>
