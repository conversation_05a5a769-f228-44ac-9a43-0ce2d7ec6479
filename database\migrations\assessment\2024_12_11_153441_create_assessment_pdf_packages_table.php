<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assessment_pdf_packages', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('school_id')->comment('学校ID');
            $table->unsignedInteger('campus_id')->comment('校区ID');
            $table->string('name')->comment('默认以班级和报告类型命名');
            $table->json('detail')->comment('包含班级，测评类型，测评计划，测评任务等信息');
            $table->tinyInteger('is_completed')->default(0)->comment('zip包生成状态1已完成0未完成');
            $table->string('zip_url',2000)->comment('文件地址');
            $table->string('creator', 20)->comment('添加人');
            $table->softDeletes();
            $table->timestamps();
        });
        DB::statement("ALTER TABLE `assessment_pdf_packages` comment '测评留档pdf打包批量下载表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assessment_pdf_packages');
    }
};
