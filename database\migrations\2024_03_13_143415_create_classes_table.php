<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('classes', function (Blueprint $table) {
            $table->id();
            $table->string('class_name', 20)->comment('班级名称');
            $table->integer('school_id')->comment('学校ID');
            $table->integer('school_campus_id')->comment('学校校区ID');
            $table->integer('grade_id')->comment('年级ID');
//            $table->tinyInteger('status')->default(1)->comment('状态1启用2禁用');
            $table->timestamps();
            $table->string('creator',20)->nullable()->comment('创建人');
            $table->string('updater',20)->nullable()->comment('最后更新人');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('classes');
    }
};
