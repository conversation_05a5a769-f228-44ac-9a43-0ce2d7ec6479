<?php

namespace App\Services\Partner;

use App\Models\Partner\Partner;
use App\Models\Partner\PartnerSchool;
use App\Services\BaseService;
use Illuminate\Http\Request;

class PartnerService extends BaseService
{

    public function updatePartner(Request $request, $id)
    {
        $record = Partner::find($id);
        if (!$record) {
            $this->throwBusinessException('对象不存在');
        }
        $data = filterRequestData('partners');
        $data['updater'] = $request->user()->real_name;
        $record->fill($data)->save();
        return $record;
    }


    public function getPartnerCampus($partner_id)
    {
        return PartnerSchool::where('partner_id', $partner_id)->with(['school:name,id,address', 'schoolCampus'])->get();
    }

    public function setPartnerCampuses($partner_id,Request $request)
    {
        $data = $request->input('school_campus_list', []);
        //删除指定partner_id下的所有数据
        PartnerSchool::where('partner_id', $partner_id)->delete();
        $insertData = array_map(function ($item) {
            return [
                'partner_id' => $item['partner_id'],
                'school_id' => $item['school_id'],
                'school_campus_id' => $item['school_campus_id'],
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }, $data);

        // 调用模型的批量插入方法
        $insertCount = PartnerSchool::batchInsertIfNotExists($insertData);
        return $insertCount;
    }
}
