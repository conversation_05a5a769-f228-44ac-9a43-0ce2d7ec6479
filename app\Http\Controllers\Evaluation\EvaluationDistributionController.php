<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use App\Services\Evaluation\DistributionService;
use App\Http\Requests\Evaluation\DistributionRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * 分发管理控制器
 */
class EvaluationDistributionController extends Controller
{
    protected $distributionService;

    public function __construct(DistributionService $distributionService)
    {
        $this->distributionService = $distributionService;
    }

    /**
     * 分发查询
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function distribution(Request $request): JsonResponse
    {
        try {
            $data = $this->distributionService->getDistributionList($request->all());
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取分发列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 分发添加
     * 
     * @param DistributionRequest $request
     * @return JsonResponse
     */
    public function store(DistributionRequest $request): JsonResponse
    {
        try {
            $data = $this->distributionService->createDistribution($request->validated());
            return $this->success($data, '分发创建成功');
        } catch (\Exception $e) {
            return $this->error('分发创建失败: ' . $e->getMessage());
        }
    }

    /**
     * 分发修改
     * 
     * @param DistributionRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(DistributionRequest $request, int $id): JsonResponse
    {
        try {
            $data = $this->distributionService->updateDistribution($id, $request->validated());
            return $this->success($data, '分发修改成功');
        } catch (\Exception $e) {
            return $this->error('分发修改失败: ' . $e->getMessage());
        }
    }

    /**
     * 分发删除
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->distributionService->deleteDistribution($id);
            return $this->success(null, '分发删除成功');
        } catch (\Exception $e) {
            return $this->error('分发删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取分发详情
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function getDistributionDetail(int $id): JsonResponse
    {
        try {
            $data = $this->distributionService->getDistributionDetail($id);
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取分发详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取分发的学生列表
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function getDistributionStudents(int $id): JsonResponse
    {
        try {
            $data = $this->distributionService->getDistributionStudents($id);
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取学生列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取分发统计
     * 
     * @param int $id
     * @return JsonResponse
     */
    public function getDistributionStatistics(int $id): JsonResponse
    {
        try {
            $data = $this->distributionService->getDistributionStatistics($id);
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取分发统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新分发状态
     * 
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateStatus(Request $request, int $id): JsonResponse
    {
        try {
            $status = $request->input('status', 0);
            $this->distributionService->updateStatus($id, $status);
            return $this->success(null, '状态更新成功');
        } catch (\Exception $e) {
            return $this->error('状态更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 分配老师
     * 
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function assignTeachers(Request $request, int $id): JsonResponse
    {
        try {
            $assignments = $request->input('assignments', []);
            $this->distributionService->assignTeachers($id, $assignments);
            return $this->success(null, '老师分配成功');
        } catch (\Exception $e) {
            return $this->error('老师分配失败: ' . $e->getMessage());
        }
    }

    /**
     * 复制分发
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function copy(Request $request): JsonResponse
    {
        try {
            $distributionId = $request->input('distribution_id');
            $newTitle = $request->input('title');
            
            // 获取原分发数据
            $originalData = $this->distributionService->getDistributionDetail($distributionId);
            
            // 修改标题
            $originalData['title'] = $newTitle ?: $originalData['title'] . '_副本';
            
            // 创建新分发
            $data = $this->distributionService->createDistribution($originalData);
            return $this->success($data, '分发复制成功');
        } catch (\Exception $e) {
            return $this->error('分发复制失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量操作
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchOperation(Request $request): JsonResponse
    {
        try {
            $action = $request->input('action');
            $ids = $request->input('ids', []);
            
            if (empty($ids)) {
                return $this->error('请选择要操作的分发');
            }

            $successCount = 0;
            $errors = [];

            foreach ($ids as $id) {
                try {
                    switch ($action) {
                        case 'delete':
                            $this->distributionService->deleteDistribution($id);
                            break;
                        case 'activate':
                            $this->distributionService->updateStatus($id, 0);
                            break;
                        case 'deactivate':
                            $this->distributionService->updateStatus($id, 1);
                            break;
                        default:
                            throw new \Exception('不支持的操作类型');
                    }
                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "ID {$id}: " . $e->getMessage();
                }
            }

            $message = "成功处理 {$successCount} 个分发";
            if (!empty($errors)) {
                $message .= "，失败: " . implode('; ', $errors);
            }

            return $this->success([
                'success_count' => $successCount,
                'error_count' => count($errors),
                'errors' => $errors
            ], $message);
        } catch (\Exception $e) {
            return $this->error('批量操作失败: ' . $e->getMessage());
        }
    }
}
