<?php

namespace App\Http\Controllers\Psychassessment;

use App\Http\Controllers\Controller;
use App\Services\Psychassessment\AppointmentRecordService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * 心理评估-学生预约记录控制器 - 基于原 ThinkPHP AppointmentRecord 控制器重新实现
 */
class PsychassessmentAppointmentRecordController extends Controller
{
    protected $appointmentRecordService;

    public function __construct(AppointmentRecordService $appointmentRecordService)
    {
        $this->appointmentRecordService = $appointmentRecordService;
    }

    /**
     * 学生预约记录操作（添加/查询/删除/修改）
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function appointmentRecord(Request $request): JsonResponse
    {
        try {
            $method = $request->method();
            
            switch ($method) {
                case 'POST':
                    // 学生预约
                    $data = $this->appointmentRecordService->createAppointment($request->all(), auth()->user());
                    return $this->success($data, '预约成功');
                    
                case 'GET':
                    // 查询预约记录
                    $params = $request->all();
                    $data = $this->appointmentRecordService->getAppointmentList($params);
                    return $this->success($data, '获取成功');
                    
                case 'PUT':
                    // 修改预约记录
                    $id = $request->route('id') ?: $request->input('id');
                    $data = $this->appointmentRecordService->updateAppointment($id, $request->all(), auth()->user());
                    return $this->success($data, '修改成功');
                    
                case 'DELETE':
                    // 删除预约记录
                    $id = $request->route('id') ?: $request->input('id');
                    $data = $this->appointmentRecordService->deleteAppointment($id, auth()->user());
                    return $this->success($data, '删除成功');
                    
                default:
                    return $this->error('不支持的请求方法');
            }
        } catch (\Exception $e) {
            return $this->error('操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取预约详情
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getAppointmentDetail(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $data = $this->appointmentRecordService->getAppointmentDetail($id);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取可预约时间段
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getAvailableTime(Request $request): JsonResponse
    {
        try {
            $teacherId = $request->input('teacher_id');
            $date = $request->input('date');
            
            $data = $this->appointmentRecordService->getAvailableTime($teacherId, $date);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 确认预约
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function confirmAppointment(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $status = $request->input('status', 2); // 2-已确认
            
            $data = $this->appointmentRecordService->confirmAppointment($id, $status, auth()->user());
            return $this->success($data, '确认成功');
        } catch (\Exception $e) {
            return $this->error('确认失败: ' . $e->getMessage());
        }
    }

    /**
     * 取消预约
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function cancelAppointment(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $cancelReason = $request->input('cancel_reason');
            
            $data = $this->appointmentRecordService->cancelAppointment($id, $cancelReason, auth()->user());
            return $this->success($data, '取消成功');
        } catch (\Exception $e) {
            return $this->error('取消失败: ' . $e->getMessage());
        }
    }

    /**
     * 完成咨询
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function completeConsultation(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $consultationRecord = $request->input('consultation_record');
            
            $data = $this->appointmentRecordService->completeConsultation($id, $consultationRecord, auth()->user());
            return $this->success($data, '完成咨询');
        } catch (\Exception $e) {
            return $this->error('操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取教师预约统计
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getTeacherStatistics(Request $request): JsonResponse
    {
        try {
            $teacherId = $request->input('teacher_id');
            $startTime = $request->input('start_time');
            $endTime = $request->input('end_time');
            
            $data = $this->appointmentRecordService->getTeacherStatistics($teacherId, $startTime, $endTime);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取学生预约历史
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getStudentHistory(Request $request): JsonResponse
    {
        try {
            $studentId = $request->input('student_id');
            $data = $this->appointmentRecordService->getStudentHistory($studentId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量处理预约
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function batchProcess(Request $request): JsonResponse
    {
        try {
            $ids = $request->input('ids');
            $action = $request->input('action'); // confirm, cancel, complete
            $data = $request->input('data', []);
            
            $result = $this->appointmentRecordService->batchProcess($ids, $action, $data, auth()->user());
            return $this->success($result, '批量处理成功');
        } catch (\Exception $e) {
            return $this->error('批量处理失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出预约记录
     * 
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|JsonResponse
     */
    public function exportAppointments(Request $request)
    {
        try {
            $params = $request->all();
            return $this->appointmentRecordService->exportAppointments($params);
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取预约类型列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getAppointmentTypes(Request $request): JsonResponse
    {
        try {
            $data = $this->appointmentRecordService->getAppointmentTypes();
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取咨询师列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getCounselors(Request $request): JsonResponse
    {
        try {
            $districtId = $request->input('district_id');
            $data = $this->appointmentRecordService->getCounselors($districtId);
            return $this->success($data, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 发送预约提醒
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function sendReminder(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            $reminderType = $request->input('reminder_type', 'sms'); // sms, email, wechat
            
            $data = $this->appointmentRecordService->sendReminder($id, $reminderType);
            return $this->success($data, '提醒发送成功');
        } catch (\Exception $e) {
            return $this->error('提醒发送失败: ' . $e->getMessage());
        }
    }
}
