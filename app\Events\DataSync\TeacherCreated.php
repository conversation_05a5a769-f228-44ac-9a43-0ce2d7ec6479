<?php

namespace App\Events\DataSync;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 教师创建事件
 */
class TeacherCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $teacherData;

    /**
     * Create a new event instance.
     */
    public function __construct(array $teacherData)
    {
        $this->teacherData = $teacherData;
    }
}
