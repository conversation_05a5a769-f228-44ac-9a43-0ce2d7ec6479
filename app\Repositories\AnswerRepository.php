<?php

namespace App\Repositories;

use App\Models\School\Assessment\Answer\AssessmentCareerAnswer;
use App\Models\School\Assessment\Answer\AssessmentCapabilityAnswer;
use App\Models\School\Assessment\Answer\AssessmentCompetencyAnswer;
use App\Models\School\Assessment\Answer\AssessmentPsychologyAnswer;
use App\Models\School\Assessment\Answer\AssessmentSubjectAnswer;
use Illuminate\Support\Facades\DB;

class AnswerRepository
{
    /**
     * 获取生涯测评维度分数数据
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 以dimension_code，dimension_name分组的维度分数数据
     */
    public function getCareerDimensionScores($params): array
    {
        return AssessmentCareerAnswer::query()
        ->select([
            'acq.dimension_code as code',
            'acq.dimension_name as name',
            DB::raw('SUM(CASE 
                WHEN aca.score IS NOT NULL THEN aca.score 
                ELSE JSON_EXTRACT(acq.options, CONCAT(\'$."\', aca.answer, \'".score\')) 
            END) as score'),
        ])
        ->from('assessment_career_answers as aca')
        ->join('assessment_career_questions as acq', 'aca.assessment_career_question_id', '=', 'acq.id')
        ->where([
            'aca.student_id' => $params['student_id'],
            'aca.assessment_task_assignment_id' => $params['assessment_task_assignment_id'],
            'aca.assessment_id' => $params['assessment_id'],
            'aca.school_id' => $params['school_id'],
            'acq.is_normal' => 1
        ])
        ->groupBy(['acq.dimension_code', 'acq.dimension_name'])
        ->orderBy('acq.dimension_code')
        ->get()
        ->toArray();
    }
    
    /**
     * 获取生涯性格测评维度分数数据
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 以dimension_code，dimension_name分组的维度分数数据
     */
    public function getCareerPersonalityDimensionScores($params): array
    {
        $data = DB::select("
        WITH dimension_data AS (
            SELECT 
                JSON_UNQUOTE(JSON_EXTRACT(acq.options, CONCAT('$.\"', aca.answer, '\".code'))) as code,
                CASE 
                    WHEN JSON_UNQUOTE(JSON_EXTRACT(acq.options, CONCAT('$.\"', aca.answer, '\".code'))) = 'E' THEN '外倾'
                    WHEN JSON_UNQUOTE(JSON_EXTRACT(acq.options, CONCAT('$.\"', aca.answer, '\".code'))) = 'I' THEN '内倾'
                    WHEN JSON_UNQUOTE(JSON_EXTRACT(acq.options, CONCAT('$.\"', aca.answer, '\".code'))) = 'S' THEN '感觉'
                    WHEN JSON_UNQUOTE(JSON_EXTRACT(acq.options, CONCAT('$.\"', aca.answer, '\".code'))) = 'N' THEN '直觉'
                    WHEN JSON_UNQUOTE(JSON_EXTRACT(acq.options, CONCAT('$.\"', aca.answer, '\".code'))) = 'T' THEN '思维'
                    WHEN JSON_UNQUOTE(JSON_EXTRACT(acq.options, CONCAT('$.\"', aca.answer, '\".code'))) = 'F' THEN '情感'
                    WHEN JSON_UNQUOTE(JSON_EXTRACT(acq.options, CONCAT('$.\"', aca.answer, '\".code'))) = 'J' THEN '判断'
                    WHEN JSON_UNQUOTE(JSON_EXTRACT(acq.options, CONCAT('$.\"', aca.answer, '\".code'))) = 'P' THEN '知觉'
                END as name
            FROM assessment_career_answers aca
            JOIN assessment_career_questions acq ON aca.assessment_career_question_id = acq.id
            WHERE aca.student_id = ?
                AND aca.assessment_task_assignment_id = ?
                AND aca.assessment_id = ?
                AND aca.school_id = ?
                AND acq.is_normal = 1
                AND JSON_UNQUOTE(JSON_EXTRACT(acq.options, CONCAT('$.\"', aca.answer, '\".code'))) IS NOT NULL
        )
        SELECT 
            code,
            name,
            SUM(1) as score
        FROM dimension_data
        GROUP BY code, name
        ORDER BY code", [
            $params['student_id'],
            $params['assessment_task_assignment_id'],
            $params['assessment_id'],
            $params['school_id']
        ]);
        return collect($data)->toArray();
    }
    
    /**
     * 获取五力测评维度分数数据
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 以dimension_code，dimension_name分组的维度分数数据
     */
    public function getCapabilityDimensionScores($params): array
    {
        return AssessmentCapabilityAnswer::query()
        ->select([
            'acq.dimension_code as code',
            'acq.dimension_name as name',
            DB::raw('SUM(CASE 
                WHEN acq.correct IS NOT NULL THEN 
                    CASE WHEN aca.answer = acq.correct THEN 1 ELSE 0 END
                ELSE JSON_EXTRACT(acq.options, CONCAT(\'$."\', aca.answer, \'".score\')) 
            END) as score'),
            DB::raw('COUNT(*) as question_count')
        ])
        ->from('assessment_capability_answers as aca')
        ->join('assessment_capability_questions as acq', 'aca.assessment_capability_question_id', '=', 'acq.id')
        ->where([
            'aca.student_id' => $params['student_id'],
            'aca.assessment_task_assignment_id' => $params['assessment_task_assignment_id'],
            'aca.assessment_id' => $params['assessment_id'],
            'aca.school_id' => $params['school_id'],
            'acq.is_normal' => 1
        ])
        ->groupBy(['acq.dimension_code', 'acq.dimension_name'])
        ->orderBy('acq.dimension_code')
        ->get()
        ->toArray();
    }
    
    /**
     * 获取创新人才核心素养测评维度分数数据
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 以dimension_code，dimension_name分组的维度分数数据
     */
    public function getCompetencyDimensionScores($params): array
    {
        return AssessmentCompetencyAnswer::query()
            ->select([
                'acq.dimension_code as code',
                'acq.dimension_name as name',
                DB::raw('SUM(CASE 
                    WHEN acq.correct IS NOT NULL THEN 
                        CASE WHEN aca.answer = acq.correct THEN 1 ELSE 0 END
                    ELSE JSON_EXTRACT(acq.options, CONCAT(\'$."\', aca.answer, \'".score\')) 
                END) as score'),
                DB::raw('COUNT(*) as question_count')
            ])
            ->from('assessment_competency_answers as aca')
            ->join('assessment_competency_questions as acq', 'aca.assessment_competency_question_id', '=', 'acq.id')
            ->where([
                'aca.student_id' => $params['student_id'],
                'aca.assessment_task_assignment_id' => $params['assessment_task_assignment_id'],
                'aca.assessment_id' => $params['assessment_id'],
                'aca.school_id' => $params['school_id']
            ])
            ->groupBy(['acq.dimension_code', 'acq.dimension_name'])
            ->orderBy('acq.dimension_code')
            ->get()
            ->toArray();
    }
    
    /**
     * 获取创新人才核心素养测评作答数据
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 以assessment_competency_question_id为键的答案数据数组
     */
    public function getCompetencyAnswers($params): array
    {
        return AssessmentCompetencyAnswer::query()
            ->from('assessment_competency_answers as aca')
            ->join('assessment_competency_questions as acq', 'aca.assessment_competency_question_id', '=', 'acq.id')
            ->where([
                'aca.student_id' => $params['student_id'],
                'aca.assessment_task_assignment_id' => $params['assessment_task_assignment_id'],
                'aca.assessment_id' => $params['assessment_id'],
                'aca.school_id' => $params['school_id']
            ])
            ->select(
                'acq.id', 
                'acq.dimension_name', 
                'acq.dimension_code',
                'acq.options', 
                'acq.correct',
                'aca.answer',
                'aca.assessment_competency_question_id'
            )
            ->get()
            ->keyBy('assessment_competency_question_id')
            ->toArray();
            
    }

    /**
     * 获取心理健康测评维度分数数据
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 以dimension_code，dimension_name分组的维度分数数据
     */
    public function getPsychologyDimensionScores($params): array
    {
        return AssessmentPsychologyAnswer::query()
        ->select([
            'acq.dimension_code as code',
            'acq.dimension_name as name',
            DB::raw('SUM(JSON_EXTRACT(acq.options, CONCAT(\'$."\', aca.answer, \'".score\'))) as score'),
            DB::raw('COUNT(*) as question_count')
        ])
        ->from('assessment_psychology_answers as aca')
        ->join('assessment_psychology_questions as acq', 'aca.assessment_psychology_question_id', '=', 'acq.id')
        ->where([
            'aca.student_id' => $params['student_id'],
            'aca.assessment_task_assignment_id' => $params['assessment_task_assignment_id'],
            'aca.assessment_id' => $params['assessment_id'],
            'aca.school_id' => $params['school_id'],
            'acq.is_normal' => 1
        ])
        ->groupBy(['acq.dimension_code', 'acq.dimension_name'])
        ->orderBy('acq.dimension_code')
        ->get()
        ->toArray();
    }
    
    /**
     * 获取学科兴趣测评维度分数数据
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 以dimension_code，dimension_name分组的维度分数数据
     */
    public function getSubjectDimensionScores($params): array
    {
        return AssessmentSubjectAnswer::query()
        ->select([
            'acq.dimension_code as code',
            'acq.dimension_name as name',
            DB::raw('SUM(JSON_EXTRACT(acq.options, CONCAT(\'$."\', aca.answer, \'".score\'))) as score'),
            DB::raw('COUNT(*) as question_count')
        ])
        ->from('assessment_subject_answers as aca')
        ->join('assessment_subject_questions as acq', 'aca.assessment_subject_question_id', '=', 'acq.id')
        ->where([
            'aca.student_id' => $params['student_id'],
            'aca.assessment_task_assignment_id' => $params['assessment_task_assignment_id'],
            'aca.assessment_id' => $params['assessment_id'],
            'aca.school_id' => $params['school_id'],
            'acq.is_normal' => 1
        ])
        ->groupBy(['acq.dimension_code', 'acq.dimension_name'])
        ->orderBy('acq.dimension_code')
        ->get()
        ->toArray();
    }
}