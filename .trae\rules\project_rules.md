# 项目规范指南

## 1. 代码规范

### 1.1 命名规范

#### 类命名
- 类名必须以类似 StudlyCaps 形式的大写驼峰命名方式声明
- 例如：`UserController`, `StudentService`, `BaseModel`

#### 常量命名
- 类的常量中所有字母都必须大写，词间以下划线分隔
- 例如：`MAX_COUNT`, `DEFAULT_VALUE`, `ERROR_CODE`

#### 变量和属性命名
- 普通变量和类属性所有字母都必须小写，词间以下划线分隔
- 例如：`user_name`, `student_count`, `total_score`
- 用于接收实例化类或依赖注入的变量应使用小驼峰命名法
- 例如：`protected AssessmentBasicService $assessmentBasicService`

#### 方法命名
- 方法名称必须符合 camelCase() 式的小写驼峰开头命名规范
- 例如：`getUserInfo()`, `calculateScore()`, `validateInput()`

### 1.2 代码格式规范

#### 花括号和空格
- 类的左花括号必须另起一行，且不得在其上一行或下一行存在空行
- 类的右花括号必须跟在类主体的下一行
- 任何注释和语句不得跟在其右花括号后的同一行

#### 函数参数
- 方法和函数中带有默认值的参数必须放在参数列表的最后面

## 2. 项目结构规范

### 2.1 目录结构
- app/
  - Console/：命令行相关
  - Constants/：常量定义
  - Enums/：枚举类型
  - Exceptions/：异常处理
  - Helpers/：辅助函数
  - Http/：HTTP相关
  - Jobs/：队列任务
  - Models/：数据模型
  - Notifications/：通知相关
  - Providers/：服务提供者
  - Repositories/：数据仓库
  - Services/：业务服务
  - Traits/：复用特性

### 2.2 文件命名
- 控制器文件：以Controller结尾（如：UserController.php）
- 模型文件：单数形式（如：User.php, Student.php）
- 数据库迁移文件：使用下划线分隔（如：create_users_table.php）
- 配置文件：全小写（如：app.php, database.php）

## 3. 开发规范

### 3.1 常量定义
- 业务相关的常量必须定义在对应的Constants类中
- 常量必须有清晰的注释说明其用途
- 避免使用魔术数字，所有固定值都应该定义为常量

### 3.2 注释规范
- 类必须有类注释，说明类的用途
- 方法必须有方法注释，包含参数说明和返回值说明
- 复杂的业务逻辑必须有行级注释
- 注释要简洁明了，避免冗余

## 4. 数据库规范

### 4.1 数据表命名
- 表名使用小写字母，单词间用下划线分隔
- 表名应该使用复数形式（如：users, students）
- 关联表名使用单数形式（如：user_role）

### 4.2 字段命名
- 字段名使用小写字母，单词间用下划线分隔
- 主键统一使用 id
- 外键格式：表名单数_id（如：user_id, role_id）
- 创建和更新时间字段：created_at, updated_at
- 软删除字段：deleted_at

### 4.3 字段设计
- 所有表必须包含 id, created_at, updated_at 字段
- 需要软删除的表必须包含 deleted_at 字段
- 字段要设置合适的类型和长度
- 必须设置默认值的字段要明确指定默认值

## 5. API规范

### 5.1 接口命名
- 使用 RESTful 风格
- URL使用小写字母，单词间用中划线分隔
- 资源采用复数形式

### 5.2 响应格式
- 统一使用JSON格式
- 必须包含状态码和消息
- 数据放在data字段中
- 分页数据需要包含总数和分页信息

### 5.3 状态码使用
- 200：成功
- 400：请求参数错误
- 401：未授权
- 403：禁止访问
- 404：资源不存在
- 500：服务器错误

## 6. 安全规范

### 6.1 数据验证
- 所有用户输入必须经过验证
- 使用表单请求类进行数据验证
- 特殊字符必须进行转义
- 防止SQL注入和XSS攻击

### 6.2 权限控制
- 使用角色权限管理
- 接口必须进行权限验证
- 敏感操作需要二次确认
- 记录关键操作日志