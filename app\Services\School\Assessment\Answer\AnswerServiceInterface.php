<?php

namespace App\Services\School\Assessment\Answer;

interface AnswerServiceInterface
{
    /**
     * 处理评估任务
     *
     * @param array $data
     * @param object $user
     * @return mixed
     */
    public function submitAnswer(array $data, object $user);

    /**
     * 直接从答案生成报告（不保存数据）
     *
     * @param array $data
     * @param object $user
     * @return array
     */
    public function generateReportFromAnswers(array $data, object $user): array;
}