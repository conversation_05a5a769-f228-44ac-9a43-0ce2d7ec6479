<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 答题记录模型
 */
class EvaluationAnswer extends Model
{
    protected $table = 'evaluation_answers';

    protected $fillable = [
        'paper_id',
        'distribution_id',
        'student_id',
        'question_id',
        'answer',
        'score',
        'grading_method',
        'school_id',
        'school_district_id',
        'member_id',
        'status',
        'used_time',
        'is_correct',
        'answered_at',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'paper_id' => 'integer',
        'distribution_id' => 'integer',
        'student_id' => 'integer',
        'question_id' => 'integer',
        'score' => 'decimal:2',
        'grading_method' => 'integer',
        'school_id' => 'integer',
        'school_district_id' => 'integer',
        'member_id' => 'integer',
        'status' => 'integer',
        'used_time' => 'integer',
        'is_correct' => 'integer',
        'answered_at' => 'datetime',
    ];

    /**
     * 所属试卷关联
     */
    public function paper(): BelongsTo
    {
        return $this->belongsTo(Papers::class, 'paper_id');
    }

    /**
     * 所属题目关联
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class, 'question_id');
    }

    /**
     * 所属分发关联
     */
    public function distribution(): BelongsTo
    {
        return $this->belongsTo(Distribution::class, 'distribution_id');
    }

    /**
     * 所属学生关联
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(\App\Models\School\System\Student::class, 'student_id');
    }

    /**
     * 作用域：按试卷筛选
     */
    public function scopeByPaper($query, $paperId)
    {
        return $query->where('paper_id', $paperId);
    }

    /**
     * 作用域：按学生筛选
     */
    public function scopeByStudent($query, $studentId)
    {
        return $query->where('student_id', $studentId);
    }

    /**
     * 作用域：按分发筛选
     */
    public function scopeByDistribution($query, $distributionId)
    {
        return $query->where('distribution_id', $distributionId);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 获取答案是否正确
     */
    public function getIsCorrectAttribute($value)
    {
        if ($value === null) {
            return null;
        }
        return $value == 1;
    }

    /**
     * 设置答案是否正确
     */
    public function setIsCorrectAttribute($value)
    {
        if ($value === null) {
            $this->attributes['is_correct'] = null;
        } else {
            $this->attributes['is_correct'] = $value ? 1 : 0;
        }
    }
}
