<?php

namespace App\Http\Middleware;

use App\Traits\ApiResponse;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use Ty<PERSON>\JWTAuth\Exceptions\TokenBlacklistedException;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use Tymon\JWTAuth\Http\Middleware\BaseMiddleware;

class RefreshToken extends BaseMiddleware
{
    use ApiResponse;

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            // 检查此次请求中是否带有 token，如果没有 token，则抛出异常。
            $this->checkForToken($request);
            // 检测用户的登录状态，如果正常则通过
            if ($this->auth->parseToken()->authenticate()) {
                if (!$request->user()->status) {
                    return $this->error('账号已被禁用', Response::HTTP_LOCKED);
                }

                return $next($request);
            }

            return $this->error('未登录', Response::HTTP_UNAUTHORIZED);
        } catch (TokenExpiredException $e) {
            // 此处捕获到了 token 过期所抛出的 TokenExpiredException 异常，我们在这里需要做的是刷新该用户的 token 并将它添加到响应头中
            try{
                // 刷新 token
                $token = $this->auth->refresh();
                // 使用一次性登录保证此次请求的成功
                Auth::guard('api')->onceUsingId($this->auth->manager()->getPayloadFactory()->buildClaimsCollection()->toPlainArray()['sub']);
            } catch (JWTException $e){
                // 如果捕获到异常，即代表refresh也过期了，无法刷新令牌，需要重新登录。
                return $this->error('token_absent', Response::HTTP_UNAUTHORIZED);
            }
        } catch (TokenBlacklistedException $e){
            return $this->error('用户已退出登录，当前token已列入黑名单', Response::HTTP_UNAUTHORIZED);
        } catch (\Exception $e){
            return $this->error('token逻辑验证失败', Response::HTTP_UNAUTHORIZED);
        }
        // 在响应头中返回新的 token
        return $this->setAuthenticationHeader($next($request),$token);
    }
}
