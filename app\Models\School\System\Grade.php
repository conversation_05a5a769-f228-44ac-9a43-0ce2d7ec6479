<?php

namespace App\Models\School\System;

use App\Models\BaseModel;

class Grade extends BaseModel
{

    protected $hidden = ['created_at', 'updated_at'];

    // 拥有多个科目
    public function courses()
    {
        return $this->hasMany(Course::class, 'grade_id', 'id')
            ->select('id', 'grade_id', 'course_name', 'code', 'type');
    }

    // 拥有多个班级
    public function classes()
    {
        return $this->hasMany(Claass::class, 'grade_id', 'id')
            ->select('id', 'grade_id', 'class_name');
    }

}
