<?php

namespace App\Http\Requests;

use App\Enums\ProvinceEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class BaseRequest extends FormRequest
{
    /**
     * 确定用户是否有权提出此请求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 获取应用于请求的验证规则
     */
    public function rules(): array
    {
        return [];
    }

    /**
     * 获取已定义验证规则的错误消息
     */
    public function messages(): array
    {
        return [];
    }

    /**
     * 重写验证失败的处理方法
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'code' => 400,
            'message' => $validator->errors()->first(),
//            'data' => null
        ], 200));
    }


     /**
     * 配置验证器实例
     *
     * @param \Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->addExtension('exists_province', function ($attribute, $value, $parameters, $validator) {
            return $this->checkProvinceExists($value);
        });

        $validator->addReplacer('exists_province', function ($message, $attribute, $rule, $parameters) {
            return '省份id错误';
        });
    }

    /**
     * 检查省份是否存在
     *
     * @param int $provinceId 省份 ID
     * @return bool 如果省份存在则返回 true，否则返回 false
     */
    protected function checkProvinceExists(int $provinceId): bool
    {
        return ProvinceEnum::tryFrom($provinceId) !== null;
    }

}