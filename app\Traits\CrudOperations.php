<?php

namespace App\Traits;

use Illuminate\Http\Request;

trait CrudOperations
{

    public function store(Request $request)
    {
        $data = filterRequestData($this->getTableName());
        $data['creator'] = $request->user()->real_name;
        $data['updater'] = $request->user()->real_name;
        $this->model::forceCreate($data);
        return $this->message('新增成功');
    }

    public function show($id)
    {
        $record = $this->model::find($id);
        if (!$record) {
            return $this->notFound('查询对象不存在');
        }
        return $this->success($record);
    }

    public function update(Request $request, $id)
    {
        $record = $this->model::find($id);
        if (!$record) {
            return $this->notFound('更新对象不存在');
        }
        $data = filterRequestData($this->getTableName());
        $data['updater'] = $request->user()->real_name;
        $record->fill($data)->save();
        return $this->message('更新成功');
    }

    public function destroy($id)
    {
        $record = $this->model::find($id);
        if (!$record) {
            return $this->notFound('删除对象不存在');
        }
        $record->delete();
        return $this->message("删除成功，Id为 {$id}");
    }


    protected function getTableName()
    {
        return (new $this->model)->getTable();
    }
}
