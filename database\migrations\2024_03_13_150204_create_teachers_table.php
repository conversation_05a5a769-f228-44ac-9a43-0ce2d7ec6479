<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('teachers', function (Blueprint $table) {
            $table->id();
            $table->integer('teacher_id')->comment('原teacher_id冗余临时字段');
            $table->string('teacher_name',20)->comment('教师姓名');
            $table->integer('user_id')->comment('用户ID');
            $table->integer('school_id')->comment('学校ID');
            $table->integer('school_campus_id')->comment('学校校区ID');
            $table->tinyInteger('is_psychology_teacher')->default(0)->comment('是否心理教师1是0否');
            $table->softDeletes();
            $table->timestamps();
            $table->string('creator',20)->nullable()->comment('创建人');
            $table->string('updater',20)->nullable()->comment('最后更新人');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('teachers');
    }
};
