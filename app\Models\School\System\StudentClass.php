<?php

namespace App\Models\School\System;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class StudentClass extends BaseModel
{
    use HasFactory;

    // 归属于学生
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id', 'id');
    }

    // 归属于班级
    public function claass()
    {
        return $this->belongsTo(Claass::class, 'class_id', 'id');
    }


}
