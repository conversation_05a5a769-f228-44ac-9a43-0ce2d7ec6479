<?php

namespace App\Services;

use Faker\Provider\Uuid;
use Illuminate\Support\Facades\Log;

class CreatePdfFileService extends BaseService
{
    const API_URL = 'http://172.16.15.126:8380/htmltopdf-1.0.0/htmlToPdf/commonPy';
    const API_URL_DEV = 'http://112.124.200.64:8380/htmltopdf-1.0.0/htmlToPdf/commonPy';
    const BASIC_URL = 'https://s.yishengya.cn/saas_upload';

    private $html_url;

    public function __construct($html_url)
    {
        $this->html_url = $html_url;//显示数据的网页地址
    }

    public function createPdfFile($pdfFileName = '', $customDictConfig = [])
    {
        if (empty($customDictConfig)) {
            $customDictConfig = [
                'path' => '/data/ysy/uploads_cdn' . '/assessment/pdf/' . date('Ymd') . '/' . Uuid::uuid() . '.pdf',
                'format' => 'A4',
                'printBackground' => 'true',
            ];
        }

        $customDict = json_encode($customDictConfig, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        $pdf_path = $this->overallCreate($pdfFileName, $customDict) ?? '';

        return $pdf_path;
    }

    public function overallCreate($pdfFileName, $customDict)
    {
        #1.参数
        $post_data['customDict'] = base64_encode($customDict);//不能传json对象，只能传字符串
        $post_data['url'] = base64_encode($this->html_url);

        #2.发起请求
        $apiUrl = app()->environment('production') ? self::API_URL : self::API_URL_DEV;

        Log::info('请求apiUrl',['apiUrl'=>$apiUrl, 'post_data'=>$post_data, 'html_url'=>$this->html_url, 'customDict'=>$customDict]);

        $report = $this->request_post($apiUrl, json_encode($post_data));
        $response = json_decode($report, true);

        #3.返回结果
        if ($response['msg'] == 'success' || $response['statue'] == 1) {
            $pdf_url = self::BASIC_URL . $pdfFileName;
            return $pdf_url;
        } else {
            throw new \Exception(json_encode($report, 256));
        }
    }

}
