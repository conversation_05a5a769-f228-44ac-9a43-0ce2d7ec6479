<?php

namespace Database\Seeders\assessment\capability;

use Database\Seeders\BaseIncrementalSeeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
ini_set('memory_limit', '1024M');

class AssignmentsSeeder extends BaseIncrementalSeeder
{
    protected string $assessment_type = 'capability';
    private const SURVEYIDTOASSESSMENTID = [
        234 => 9,
        258 => 10,
        257 => 11,
        256 => 12,
        255 => 13
    ];
    
    public function __construct($schoolId)
    {
        parent::__construct($schoolId);
    }

    protected function getSurveyIds(): array
    {
        return [234, 258, 257, 256, 255];
    }

    protected function getAssessmentIds(): array
    {
        return [9, 10, 11, 12, 13];
    }

    /**
     * Run the database seeds.
     */
    protected function executeSeeder(): void
    {
        $lastProcessedId = $this->getLastProcessedId();
        
        // 限制核心素养测评类型
        $assessmentIds = $this->getAssessmentIds();
        $surveyIds = $this->getSurveyIds();

        // 查询学生测评记录，添加增量查询条件
        $studentSurveyList = DB::connection($this->connect)
        ->table('assessment_schedules as schedules')
        ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
        ->join('survey_user_session as session', function($join) {
            $join->on('session.survey_id', '=', 'tasks.old_survey_id')
                ->on('session.grade_id', '=', 'tasks.old_grade_id')
                ->on('session.times', '=', 'tasks.old_times');
        })
        ->whereIn('tasks.assessment_id', $assessmentIds)
        ->where('schedules.school_id', $this->school_id)
        ->where('session.school_id', $this->school_id)
        ->whereIn('session.survey_id', $surveyIds)
        ->where('session.is_delete', 0)
        ->where('session.is_abnormal', 0)
        ->where('session.time_error', 0)
        ->where('session.result', '!=', '')
        ->whereNotNull('session.result')
        ->where('session.id', '>', $lastProcessedId) // 只处理新记录
        ->select([
            'tasks.id',
            'tasks.assessment_id',
            'session.session_id',
            'session.member_id',
            'session.student_id',
            'session.create_time',
            'session.used_time',
            'session.result',
            'session.survey_id',
            'session.pdf_url',
            'session.school_id',
            'session.id as survey_session_id' // 添加survey_session_id用于更新执行日志
        ])
        ->get();

        // 优化查询效率
        $oldStudentIds = $studentSurveyList->pluck('student_id')->unique()->values()->toArray();
        $studentClassIdArr = DB::table('student_classes')
        ->whereIn('old_student_id', $oldStudentIds)
        ->select(['id', 'old_student_id'])
        ->pluck('id', 'old_student_id')
        ->toArray();

        // 使用集合方法优化数据处理
        $surveyToAssessmentMap = self::SURVEYIDTOASSESSMENTID;
        $assignmentsData = $studentSurveyList->map(function($item) use ($studentClassIdArr,$surveyToAssessmentMap) {
        return [
            'old_session_id' => $item->session_id,
            'school_id' => $item->school_id,
            'assessment_task_id' => $item->id,
            'student_class_id' => $studentClassIdArr[$item->student_id] ?? 0,
            'student_id' => $item->member_id,
            'user_id' => $item->member_id,
            'duration' => $item->used_time,
            'results' => $item->result,
            'status' => 2,
            'assessment_id' => $surveyToAssessmentMap[$item->survey_id],
            'pdf_url' => $item->pdf_url,
            'created_at' => $item->create_time,
        ];
        })->toArray();

        // 使用事务和 chunk 插入大量数据
        if (!empty($assignmentsData)) {
            DB::transaction(function () use ($assignmentsData) {
                collect($assignmentsData)->chunk(1000)->each(function($chunk) {
                    DB::table('assessment_task_assignments')->insert($chunk->toArray());
                });
            });

            // 获取最大的 survey_session_id 用于更新执行日志
            $maxSurveySessionId = $studentSurveyList->max('survey_session_id');
            $this->updateExecutionLog($maxSurveySessionId, count($assignmentsData));

            // 使用更好的日志记录方式
            Log::info('Capability assignments seeding completed', [
                'school_id' => $this->school_id,
                'total_records' => count($assignmentsData),
                'last_task_id' => end($assignmentsData)['assessment_task_id'] ?? null,
                'last_processed_survey_session_id' => $maxSurveySessionId,
            ]);
        } else {
            Log::info('没有新的capability assignments数据需要处理', [
                'school_id' => $this->school_id,
                'last_processed_id' => $lastProcessedId
            ]);
        }
    }
}
