<?php

namespace App\Services\Evaluation;

use App\Models\Evaluation\EvaluationAnswer;
use App\Models\Evaluation\EvaluationLog;
use App\Models\Evaluation\Question;
use App\Models\Evaluation\Distribution;
use App\Models\Evaluation\Papers;
use App\Models\Evaluation\DistributionTeachersStudents;
use App\Models\School\System\Student;
use App\Models\School\System\Teacher;
use App\Models\School\System\Grade;
use App\Models\School\System\Claass;
use App\Models\School\System\School;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

/**
 * 答题管理服务类 - 基于原 ThinkPHP Answer 控制器重新实现
 */
class AnswerService
{
    protected $evaluationAnswerModel;
    protected $evaluationLogModel;
    protected $questionModel;
    protected $distributionModel;
    protected $paperModel;
    protected $distributionTeachersStudents;
    protected $studentModel;
    protected $teacherModel;
    protected $gradeModel;
    protected $classModel;
    protected $schoolModel;
    protected $user;

    public function __construct(
        EvaluationAnswer $evaluationAnswerModel,
        EvaluationLog $evaluationLogModel,
        Question $questionModel,
        Distribution $distributionModel,
        Papers $paperModel,
        DistributionTeachersStudents $distributionTeachersStudents,
        Student $studentModel,
        Teacher $teacherModel,
        Grade $gradeModel,
        Claass $classModel,
        School $schoolModel
    ) {
        $this->evaluationAnswerModel = $evaluationAnswerModel;
        $this->evaluationLogModel = $evaluationLogModel;
        $this->questionModel = $questionModel;
        $this->distributionModel = $distributionModel;
        $this->paperModel = $paperModel;
        $this->distributionTeachersStudents = $distributionTeachersStudents;
        $this->studentModel = $studentModel;
        $this->teacherModel = $teacherModel;
        $this->gradeModel = $gradeModel;
        $this->classModel = $classModel;
        $this->schoolModel = $schoolModel;
        $this->user = Auth::user();
    }

    /**
     * 获取阅卷管理列表
     *
     * @param array $params
     * @return array
     */
    public function getAnswerList(array $params): array
    {
        $page = $params['page'] ?? 1;
        $pageSize = $params['pagesize'] ?? 15;

        // 构建查询
        $query = $this->evaluationLogModel
            ->with([
                'paper:id,paper_name,course_id,grade_id,is_high',
                'student:id,name,student_no,class_id,grade_id,school_id',
                'distribution:id,title,start_time,end_time,status',
                'student.class:id,name,grade_id,school_id',
                'student.grade:id,grade_name',
                'student.school:id,name'
            ])
            ->where('status', 0);

        // 应用筛选条件
        if (!empty($params['distribution_id'])) {
            $query->where('distribution_id', $params['distribution_id']);
        }

        if (!empty($params['paper_id'])) {
            $query->where('paper_id', $params['paper_id']);
        }

        if (isset($params['check_status']) && $params['check_status'] !== '') {
            $query->where('check_status', $params['check_status']);
        }

        if (!empty($params['member_id'])) {
            $query->where('member_id', $params['member_id']);
        }

        if (!empty($params['school_id'])) {
            $query->where('school_id', $params['school_id']);
        }

        if (!empty($params['grade_id'])) {
            $query->where('grade_id', $params['grade_id']);
        }

        if (!empty($params['class_id'])) {
            $query->where('class_id', $params['class_id']);
        }

        if (!empty($params['student_id'])) {
            $query->where('student_id', $params['student_id']);
        }

        if (!empty($params['keyword'])) {
            $keyword = $params['keyword'];
            $query->whereHas('student', function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('student_no', 'like', "%{$keyword}%");
            });
        }

        // 根据用户角色限制查询范围
        if ($this->user && !$this->isAdmin()) {
            $query->where('school_id', $this->user->school_id);
        }

        // 分页查询
        $total = $query->count();
        $data = $query->orderBy('id', 'desc')
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get();

        // 处理数据
        $result = [];
        foreach ($data as $item) {
            $result[] = [
                'id' => $item->id,
                'paper_id' => $item->paper_id,
                'paper_name' => $item->paper->paper_name ?? '',
                'student_id' => $item->student_id,
                'student_name' => $item->student->name ?? '',
                'student_no' => $item->student->student_no ?? '',
                'class_id' => $item->class_id,
                'class_name' => $item->student->class->name ?? '',
                'grade_id' => $item->grade_id,
                'grade_name' => $item->student->grade->grade_name ?? '',
                'school_id' => $item->school_id,
                'school_name' => $item->student->school->name ?? '',
                'distribution_id' => $item->distribution_id,
                'distribution_title' => $item->distribution->title ?? '',
                'score' => $item->score,
                'check_status' => $item->check_status,
                'check_status_text' => $this->getCheckStatusText($item->check_status),
                'literacy_level' => $item->literacy_level,
                'created_at' => $item->create_at,
                'updated_at' => $item->update_at,
            ];
        }

        return [
            'data' => $result,
            'total' => $total,
            'page' => (int)$page,
            'pagesize' => (int)$pageSize,
            'total_pages' => ceil($total / $pageSize)
        ];
    }

    /**
     * 获取批阅状态文本
     *
     * @param int $status
     * @return string
     */
    private function getCheckStatusText(int $status): string
    {
        $statusMap = [
            0 => '待批阅',
            1 => '已批阅',
            2 => '批阅中',
        ];

        return $statusMap[$status] ?? '未知状态';
    }

    /**
     * 检查是否为管理员
     *
     * @return bool
     */
    private function isAdmin(): bool
    {
        return $this->user && ($this->user->role_id == 999 || $this->user->role_source_id == 1);
    }

    /**
     * 提交答案
     *
     * @param array $data
     * @return mixed
     */
    public function submitAnswer(array $data)
    {
        // 检查权限
        $memberIds = $this->distributionModel->where('id', $data['distribution_id'])->value('member_ids');
        if (!in_array($this->user->id, explode(',', $memberIds))) {
            throw new \Exception('你没有权限', -5);
        }

        // 获取学生信息
        $student = Student::select('id', 'grade_id', 'class_id', 'school_district')
            ->where('member_id', $this->user->id)
            ->orderBy('id', 'desc')
            ->first();

        if (!$student) {
            throw new \Exception('学生信息不存在');
        }

        $submitArr = json_decode($data['answer_arr'], true);

        // 提交拦截，不能二次提交
        $where = [
            'distribution_id' => $data['distribution_id'],
            'paper_id' => $data['paper_id'],
            'member_id' => $this->user->id,
            'status' => 0,
        ];

        $answer = $this->evaluationAnswerModel->where($where)->first();
        $log = $this->evaluationLogModel->where($where)->first();
        
        if (!empty($answer) || !empty($log)) {
            throw new \Exception('请勿重复提交答案', -8);
        }

        return DB::transaction(function () use ($data, $student, $submitArr) {
            // 获取问题详情和计算分数
            $getScoreArr = $this->calculateScores($submitArr);

            // 保存答案记录
            $this->saveAnswerRecords($data, $student, $submitArr, $getScoreArr);

            // 保存日志记录
            $this->saveLogRecord($data, $student, $getScoreArr);

            // 计算排名
            $memberIds = $this->distributionModel->where('id', $data['distribution_id'])->value('member_ids');
            $this->calculateRanking($data['distribution_id'], $data['paper_id'], $this->user->id, $memberIds);

            return true;
        });
    }

    /**
     * 获取答题记录列表
     * 
     * @param array $params
     * @return array
     */
    public function getAnswerRecord(array $params): array
    {
        if (!empty($params['id'])) {
            $data = $this->evaluationLogModel->where('id', $params['id'])->first();
            return $data ? $data->toArray() : [];
        }

        $query = $this->evaluationLogModel->alias('a')
            ->select([
                'a.*',
                'p.paper_name',
                'p.has_subjective',
                'f.name as teacher_name'
            ])
            ->join('evaluation_papers as p', 'a.paper_id', '=', 'p.id')
            ->leftJoin('member as f', 'a.check_member_id', '=', 'f.id');

        // 应用筛选条件
        $this->applyAnswerFilters($query, $params);

        $page = $params['page'] ?? 1;
        $pagesize = $params['pagesize'] ?? 10;

        $data = $query->orderBy('a.id', 'desc')->get();

        // 获取分组数据
        $evaluationLogArr = $this->getGroupedAnswerRecords($params);

        return $this->paginateResults($evaluationLogArr, $pagesize, $page);
    }

    /**
     * 获取学生答题详情
     * 
     * @param array $params
     * @return array
     */
    public function getStudentAnswerDetail(array $params): array
    {
        $where = [
            'distribution_id' => $params['distribution_id'],
            'paper_id' => $params['paper_id'],
            'member_id' => $params['member_id'],
            'status' => 0
        ];

        $data = $this->evaluationAnswerModel
            ->select('id', 'question_id', 'answer', 'score')
            ->where($where)
            ->get();

        foreach ($data as $item) {
            if ($item->score !== null) {
                $item->score = floatval($item->score);
            }
        }

        return $data->toArray();
    }

    /**
     * 提交主观题批改
     * 
     * @param array $data
     * @return mixed
     */
    public function submitSubjective(array $data)
    {
        $where = [
            'distribution_id' => $data['distribution_id'],
            'paper_id' => $data['paper_id'],
            'member_id' => $data['member_id'],
            'status' => 0
        ];

        return DB::transaction(function () use ($data, $where) {
            // 获取答案ID映射
            $questionIdToAnswer = $this->evaluationAnswerModel
                ->where($where)
                ->pluck('id', 'question_id')
                ->toArray();

            $checkStatus = 1;
            $updateAnswerData = [];

            foreach ($data['answer_arr'] as $item) {
                if (isset($questionIdToAnswer[$item[0]]) && $questionIdToAnswer[$item[0]] > 0) {
                    $updateAnswerData[] = [
                        'id' => $questionIdToAnswer[$item[0]],
                        'score' => $item[1]
                    ];
                    if ($checkStatus == 1 && $item[1] === null) {
                        $checkStatus = 0; // 只要有一个null即可判定为中途提交
                    }
                }
            }

            // 批量更新答案分数
            foreach ($updateAnswerData as $updateData) {
                $this->evaluationAnswerModel->where('id', $updateData['id'])
                    ->update(['score' => $updateData['score']]);
            }

            // 如果批改完成，更新日志记录
            if ($checkStatus == 1) {
                $score = $this->evaluationAnswerModel->where($where)->sum('score');
                
                $updateLogData = [
                    'score' => $score,
                    'check_status' => 1
                ];

                $this->evaluationLogModel->where($where)->update($updateLogData);
                $this->calculateRanking($data['distribution_id'], $data['paper_id'], $data['member_id'], '');
            }

            return true;
        });
    }

    /**
     * 修改批阅老师
     * 
     * @param array $params
     * @return bool
     */
    public function changeReviewTeacher(array $params): bool
    {
        return $this->evaluationLogModel
            ->where(['id' => $params['id'], 'check_status' => 0])
            ->update(['check_member_id' => $params['check_member_id']]) > 0;
    }

    /**
     * 修正分数
     * 
     * @param array $params
     * @return bool
     */
    public function correctionScore(array $params): bool
    {
        return $this->evaluationLogModel
            ->where('id', $params['record_id'])
            ->update(['score' => $params['score']]) > 0;
    }

    /**
     * 获取试卷列表
     * 
     * @return array
     */
    public function getLogPapers(): array
    {
        return $this->evaluationLogModel->alias('a')
            ->join('evaluation_papers as b', 'a.paper_id', '=', 'b.id')
            ->select('a.paper_id', 'b.paper_name')
            ->where([
                'a.school_id' => $this->user->school_id,
                'a.status' => 0,
                'b.status' => 0
            ])
            ->groupBy('paper_id')
            ->get()
            ->toArray();
    }

    /**
     * 删除答题记录
     * 
     * @param array $params
     * @return bool
     */
    public function deleteAnswerRecord(array $params): bool
    {
        return DB::transaction(function () use ($params) {
            $where = [
                'distribution_id' => $params['distribution_id'],
                'paper_id' => $params['paper_id'],
                'member_id' => $params['member_id']
            ];

            $this->evaluationAnswerModel->where($where)->delete();
            $this->evaluationLogModel->where($where)->delete();

            return true;
        });
    }

    /**
     * 获取答题统计
     * 
     * @param array $params
     * @return array
     */
    public function getAnswerStatistics(array $params): array
    {
        // 实现统计逻辑
        return [];
    }

    /**
     * 获取批量答题记录
     *
     * @param array $params
     * @return array
     */
    public function getBatchAnswerRecord(array $params): array
    {
        $query = $this->evaluationLogModel
            ->with(['paper:id,paper_name', 'student:id,name,student_no'])
            ->where('status', 0);

        // 应用筛选条件
        if (!empty($params['distribution_id'])) {
            $query->where('distribution_id', $params['distribution_id']);
        }

        if (!empty($params['paper_id'])) {
            $query->where('paper_id', $params['paper_id']);
        }

        if (!empty($params['member_ids'])) {
            $memberIds = is_array($params['member_ids']) ? $params['member_ids'] : explode(',', $params['member_ids']);
            $query->whereIn('member_id', $memberIds);
        }

        return $query->orderBy('id', 'desc')->get()->toArray();
    }

    /**
     * 获取题目分数
     *
     * @param array $params
     * @return array
     */
    public function getQuestionScore(array $params): array
    {
        $where = [
            'distribution_id' => $params['distribution_id'],
            'paper_id' => $params['paper_id'],
            'member_id' => $params['member_id'],
            'status' => 0
        ];

        $answers = $this->evaluationAnswerModel
            ->select('question_id', 'answer', 'score')
            ->where($where)
            ->get();

        $result = [];
        foreach ($answers as $answer) {
            $result[$answer->question_id] = [
                'answer' => $answer->answer,
                'score' => $answer->score
            ];
        }

        return $result;
    }

    /**
     * 获取题目分数和素养分数
     *
     * @param array $params
     * @return array
     */
    public function getQuestionScoreWithCompetence(array $params): array
    {
        $questionScores = $this->getQuestionScore($params);

        // 这里可以添加素养分数的计算逻辑
        // 暂时返回题目分数
        return $questionScores;
    }

    /**
     * 获取学生考试信息
     *
     * @param array $params
     * @return array
     */
    public function getStudentExam(array $params): array
    {
        $where = [
            'distribution_id' => $params['distribution_id'],
            'paper_id' => $params['paper_id'],
            'member_id' => $params['member_id'],
            'status' => 0
        ];

        $log = $this->evaluationLogModel
            ->with(['paper:id,paper_name', 'student:id,name,student_no'])
            ->where($where)
            ->first();

        if (!$log) {
            return [];
        }

        $answers = $this->evaluationAnswerModel
            ->select('question_id', 'answer', 'score')
            ->where('log_id', $log->id)
            ->get();

        return [
            'log' => $log->toArray(),
            'answers' => $answers->toArray()
        ];
    }

    /**
     * 获取学生Excel数据
     *
     * @param array $params
     * @return array
     */
    public function getStudentExcel(array $params): array
    {
        // 实现Excel数据导出逻辑
        return [];
    }

    /**
     * 获取十年级学生Excel数据
     *
     * @param array $params
     * @return array
     */
    public function getStudentExcelTenth(array $params): array
    {
        // 实现十年级Excel数据导出逻辑
        return [];
    }

    /**
     * 获取十年级学生等级Excel数据
     *
     * @param array $params
     * @return array
     */
    public function getStudentExcelLevelTenth(array $params): array
    {
        // 实现十年级等级Excel数据导出逻辑
        return [];
    }

    /**
     * 插入单选题答案
     *
     * @param array $data
     * @return mixed
     */
    public function insertAnswerSingle(array $data)
    {
        return $this->insertAnswer($data, 'single');
    }

    /**
     * 插入多选题答案
     *
     * @param array $data
     * @return mixed
     */
    public function insertAnswerMult(array $data)
    {
        return $this->insertAnswer($data, 'multiple');
    }

    /**
     * 插入主观题答案
     *
     * @param array $data
     * @return mixed
     */
    public function insertAnswerSub(array $data)
    {
        return $this->insertAnswer($data, 'subjective');
    }

    /**
     * 插入答案的通用方法
     *
     * @param array $data
     * @param string $type
     * @return mixed
     */
    private function insertAnswer(array $data, string $type)
    {
        $answerData = [
            'log_id' => $data['log_id'],
            'question_id' => $data['question_id'],
            'answer' => $data['answer'],
            'score' => $data['score'] ?? 0,
            'is_correct' => ($data['score'] ?? 0) > 0 ? 1 : 0,
            'status' => 0
        ];

        return $this->evaluationAnswerModel->create($answerData);
    }

    /**
     * 插入日志
     *
     * @param array $data
     * @return mixed
     */
    public function insertLog(array $data)
    {
        $logData = [
            'paper_id' => $data['paper_id'],
            'distribution_id' => $data['distribution_id'],
            'member_id' => $data['member_id'],
            'student_id' => $data['student_id'] ?? 0,
            'grade_id' => $data['grade_id'] ?? 0,
            'class_id' => $data['class_id'] ?? 0,
            'school_id' => $data['school_id'] ?? 0,
            'used_time' => $data['used_time'] ?? 0,
            'score' => $data['score'] ?? 0,
            'check_status' => $data['check_status'] ?? 0,
            'literacy_level' => $data['literacy_level'] ?? '',
            'status' => 0
        ];

        return $this->evaluationLogModel->create($logData);
    }

    /**
     * 更新日志素养分数
     *
     * @param array $data
     * @return bool
     */
    public function updateLogCompetence(array $data): bool
    {
        return $this->evaluationLogModel
            ->where('id', $data['log_id'])
            ->update([
                'literacy_level' => $data['literacy_level'] ?? '',
                'score' => $data['score'] ?? 0
            ]) > 0;
    }

    /**
     * 更新答案素养分数
     *
     * @param array $data
     * @return bool
     */
    public function updateAnswerCompetence(array $data): bool
    {
        return $this->evaluationAnswerModel
            ->where('id', $data['answer_id'])
            ->update([
                'score' => $data['score'] ?? 0,
                'is_correct' => ($data['score'] ?? 0) > 0 ? 1 : 0
            ]) > 0;
    }

    /**
     * 计算素养等级
     *
     * @param float $score
     * @param int $paperId
     * @return string
     */
    private function calculateLiteracyLevel(float $score, int $paperId): string
    {
        // 获取试卷总分
        $paper = $this->paperModel->find($paperId);
        if (!$paper) {
            return '';
        }

        $totalScore = $paper->total_score ?? 100;
        $percentage = ($totalScore > 0) ? ($score / $totalScore) * 100 : 0;

        // 根据百分比确定素养等级
        if ($percentage >= 85) {
            return 'excellent'; // 优秀
        } elseif ($percentage >= 70) {
            return 'good'; // 良好
        } elseif ($percentage >= 60) {
            return 'pass'; // 及格
        } else {
            return 'need_improve'; // 需要提高
        }
    }

    /**
     * 计算题目分数
     *
     * @param int $questionId
     * @param string $answer
     * @return float
     */
    private function calculateScore(int $questionId, string $answer): float
    {
        $question = $this->questionModel->find($questionId);
        if (!$question) {
            return 0;
        }

        // 根据题目类型计算分数
        switch ($question->type_id) {
            case 1: // 单选题
                return $question->answer === $answer ? $question->score : 0;

            case 2: // 多选题
                $correctAnswers = explode(',', $question->answer);
                $userAnswers = explode(',', $answer);
                sort($correctAnswers);
                sort($userAnswers);
                return (implode(',', $correctAnswers) === implode(',', $userAnswers)) ? $question->score : 0;

            case 3: // 判断题
                return $question->answer === $answer ? $question->score : 0;

            case 4: // 填空题
                $correctAnswers = explode('|', $question->answer);
                foreach ($correctAnswers as $correctAnswer) {
                    if (trim($correctAnswer) === trim($answer)) {
                        return $question->score;
                    }
                }
                return 0;

            case 5: // 主观题
                // 主观题需要人工评分，先给0分
                return 0;

            default:
                return 0;
        }
    }

    /**
     * 应用答题记录筛选条件
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param array $params
     * @return void
     */
    private function applyAnswerFilters($query, array $params): void
    {
        if (!empty($params['distribution_id'])) {
            $query->where('a.distribution_id', $params['distribution_id']);
        }

        if (!empty($params['paper_id'])) {
            $query->where('a.paper_id', $params['paper_id']);
        }

        if (isset($params['check_status']) && $params['check_status'] !== '') {
            $query->where('a.check_status', $params['check_status']);
        }

        if (!empty($params['member_id'])) {
            $query->where('a.member_id', $params['member_id']);
        }

        if (!empty($params['school_id'])) {
            $query->where('a.school_id', $params['school_id']);
        }

        if (!empty($params['grade_id'])) {
            $query->where('a.grade_id', $params['grade_id']);
        }

        if (!empty($params['class_id'])) {
            $query->where('a.class_id', $params['class_id']);
        }

        if (!empty($params['student_id'])) {
            $query->where('a.student_id', $params['student_id']);
        }

        if (!empty($params['keyword'])) {
            $keyword = $params['keyword'];
            $query->whereHas('student', function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('student_no', 'like', "%{$keyword}%");
            });
        }

        // 根据用户角色限制查询范围
        if ($this->user && !$this->isAdmin()) {
            $query->where('a.school_id', $this->user->school_id);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 计算题目分数
     *
     * @param array $submitArr
     * @return array
     */
    private function calculateScores(array $submitArr): array
    {
        // 获取问题ID字符串
        $questionIds = '';
        $inputData = [];

        foreach ($submitArr as $v) {
            $questionIds .= ',' . $v[0];
            $inputData[$v[0]] = $v[1];
        }
        $questionIds = ltrim($questionIds, ',');

        // 查询题目详情
        $questionOptionDetail = $this->questionModel
            ->with(['typeStr', 'optionList', 'children' => function ($query) {
                $query->with(['typeStr', 'optionList']);
            }])
            ->where('status', 0)
            ->whereIn('id', explode(',', $questionIds))
            ->get()
            ->toArray();

        $getScoreArr = [];

        foreach ($questionOptionDetail as $question) {
            $score = $this->calculateQuestionScore($question, $inputData);
            if ($score !== null) {
                $getScoreArr[$question['id']] = $score;
            }
        }

        return $getScoreArr;
    }

    /**
     * 计算单个题目分数
     *
     * @param array $question
     * @param array $inputData
     * @return float|null
     */
    private function calculateQuestionScore(array $question, array $inputData): ?float
    {
        $questionId = $question['id'];
        $answer = $inputData[$questionId] ?? '';

        switch ($question['type_id']) {
            case 1: // 选择题
                if ($question['answer'] == $answer) {
                    return $question['score'];
                } else {
                    foreach ($question['option_list'] as $option) {
                        if ($option['title'] == $answer) {
                            return $option['score'];
                        }
                    }
                }
                break;

            case 2: // 多选题
                return $this->getMultipleChoiceScore(
                    explode(',', $answer),
                    explode(',', $question['answer'])
                );

            case 6: // 判断题
                if ($question['is_common'] == 1 && $question['parent_id'] == 0) {
                    return $question['answer'] == $answer ? $question['score'] : 0;
                } elseif ($question['is_common'] == 1 && $question['parent_id'] != 0) {
                    $answerArr = explode(',', $answer);
                    $wrongCount = 0;

                    foreach ($question['option_list'] as $k => $option) {
                        if (isset($answerArr[$k]) && $option['answer'] != $answerArr[$k]) {
                            $wrongCount++;
                        }
                    }

                    if ($wrongCount == count($question['option_list'])) {
                        return 0;
                    } else {
                        return max(0, $question['score'] - $wrongCount * 2);
                    }
                }
                break;
        }

        return null;
    }

    /**
     * 计算多选题分数
     *
     * @param array $studentAnswers
     * @param array $correctAnswers
     * @return float
     */
    private function getMultipleChoiceScore(array $studentAnswers, array $correctAnswers): float
    {
        $n = count($correctAnswers);
        $score = 0;

        foreach ($studentAnswers as $answer) {
            if (in_array($answer, $correctAnswers)) {
                $score += 8 / $n;
            } else {
                $score -= 10 / $n;
            }
        }

        return max(0, ceil($score));
    }

    /**
     * 保存答案记录
     *
     * @param array $data
     * @param Student $student
     * @param array $submitArr
     * @param array $getScoreArr
     */
    private function saveAnswerRecords(array $data, Student $student, array $submitArr, array $getScoreArr): void
    {
        $rows = [];

        foreach ($submitArr as $v) {
            $rows[] = [
                'paper_id' => $data['paper_id'],
                'distribution_id' => $data['distribution_id'],
                'student_id' => $student->id,
                'question_id' => $v[0],
                'answer' => $v[1],
                'score' => $getScoreArr[$v[0]] ?? null,
                'grading_method' => $v[2],
                'school_id' => $this->user->school_id,
                'school_district_id' => $student->school_district,
                'member_id' => $this->user->id,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $this->evaluationAnswerModel->insert($rows);
    }

    /**
     * 保存日志记录
     *
     * @param array $data
     * @param Student $student
     * @param array $getScoreArr
     */
    private function saveLogRecord(array $data, Student $student, array $getScoreArr): void
    {
        $hasSubjective = $this->paperModel->where('id', $data['paper_id'])->value('has_subjective');

        $teacherMemberId = $this->distributionTeachersStudents
            ->where('distribution_id', $data['distribution_id'])
            ->where('paper_id', $data['paper_id'])
            ->whereRaw('FIND_IN_SET(?, member_ids)', [$this->user->id])
            ->value('teacher_member_id');

        $info = [
            'paper_id' => $data['paper_id'],
            'grade_id' => $student->grade_id,
            'class_id' => $student->class_id,
            'student_id' => $student->id,
            'member_id' => $this->user->id,
            'distribution_id' => $data['distribution_id'],
            'used_time' => $data['used_time'] ?? '',
            'check_member_id' => $teacherMemberId ?? 0,
            'score' => array_sum(array_values($getScoreArr)),
            'check_status' => $hasSubjective == 1 ? 0 : 1, // 有主观题置为待批阅，无主观题置为已批阅
            'school_id' => $this->user->school_id,
            'school_district_id' => $student->school_district,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        $this->evaluationLogModel->create($info);
    }

    /**
     * 计算排名
     *
     * @param int $distributionId
     * @param int $paperId
     * @param int $currentId
     * @param string $memberIds
     */
    private function calculateRanking(int $distributionId, int $paperId, int $currentId, string $memberIds): void
    {
        if (!$memberIds) {
            $memberIds = $this->distributionModel->where('id', $distributionId)->value('member_ids');
        }

        $evaluationLog = $this->evaluationLogModel
            ->where([
                'distribution_id' => $distributionId,
                'paper_id' => $paperId,
                'status' => 0
            ])
            ->whereIn('member_id', explode(',', $memberIds))
            ->select('id', 'score', 'check_status')
            ->groupBy('member_id')
            ->get()
            ->toArray();

        $num = count($evaluationLog); // 做了测评的学生
        $total = count(explode(',', $memberIds)); // 所有指定的学生

        if ($num < $total) return; // 有学生没做测评，就不计算优良

        $checkArr = array_column($evaluationLog, 'check_status');
        if (in_array(0, $checkArr)) return; // 有测评没有批阅，就不计算优良

        $scoreArr = array_column($evaluationLog, 'score');

        // 根据分数倒序排序
        array_multisort($scoreArr, SORT_DESC, $evaluationLog);

        $normal = ceil(0.25 * $total);
        $youArr = array_slice($evaluationLog, 0, $normal); // 前25%
        $liangArr = array_slice($evaluationLog, $normal, $normal); // 中25%
        $youdaitigaoArr = array_slice($evaluationLog, 2 * $normal); // 50%到最后

        // 组装更新数据
        $youData = $this->makeUpdateData($youArr, '优');
        $liangData = $this->makeUpdateData($liangArr, '良');
        $youdaitigaoData = $this->makeUpdateData($youdaitigaoArr, '有待提高');
        $updateData = array_merge($youData, $liangData, $youdaitigaoData);

        // 批量更新
        foreach ($updateData as $data) {
            $this->evaluationLogModel->where('id', $data['id'])
                ->update(['literacy_level' => $data['literacy_level']]);
        }
    }

    /**
     * 组装更新数据
     *
     * @param array $arr
     * @param string $level
     * @return array
     */
    private function makeUpdateData(array $arr, string $level = '优'): array
    {
        $updateData = [];
        foreach ($arr as $item) {
            $updateData[] = [
                'id' => $item['id'],
                'literacy_level' => $level
            ];
        }
        return $updateData;
    }

    /**
     * 应用筛选条件
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param array $params
     * @return void
     */
    private function applyFilters($query, array $params): void
    {
        if (!empty($params['grade_id'])) {
            $query->whereIn('grade_id', is_array($params['grade_id']) ? $params['grade_id'] : [$params['grade_id']]);
        }

        if (!empty($params['class_id'])) {
            $query->whereIn('class_id', is_array($params['class_id']) ? $params['class_id'] : [$params['class_id']]);
        }

        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $query->whereBetween('created_at', [$params['start_time'], $params['end_time']]);
        }

        if (is_numeric($params['check_status'] ?? null)) {
            $query->where('check_status', $params['check_status']);
        }
    }
}

