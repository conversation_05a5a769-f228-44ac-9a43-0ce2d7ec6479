<?php

namespace App\Constants;

use App\Enums\QueryTypeEnum;

/**
 * 查询相关常量
 *
 * 定义与数据查询相关的常量和辅助方法
 */
class QueryConstants
{
    /**
     * 默认年份限制
     *
     * 用于限制查询返回的年份数量
     */
    public const DEFAULT_YEARS_LIMIT = 4;

    /**
     * 最新年份限制
     *
     * 用于限制查询返回的最新年份数量
     */
    public const LATEST_YEARS_LIMIT = 4;

    /**
     * 默认排序
     *
     * 当没有指定排序方式时使用的默认排序值
     */
    public const DEFAULT_SORT = 0;

    /**
     * 获取查询类型表名
     *
     * 根据查询类型和省份拼音生成完整的表名
     *
     * @param int $queryType 查询类型索引
     * @param string $provinceSpell 省份拼音
     * @return string 表名
     */
    public static function getQueryTable(int $queryType, string $provinceSpell): string
    {
        return QueryTypeEnum::fromInt($queryType)->getTableName($provinceSpell);
    }
}
