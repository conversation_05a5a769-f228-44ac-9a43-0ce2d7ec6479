<?php

namespace App\Http\Resources\School\Assessment;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * 问题资源类
 *
 * 用于将问题数据转换为API响应格式，主要处理选项格式的转换
 * 将选项从键值对格式 {"A": {...}, "B": {...}} 转换为数组格式 [{"level": "A", ...}, {"level": "B", ...}]
 */
class QuestionResource extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param Request $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // 获取原始数据
        $data = parent::toArray($request);

        // 将选项从键值对格式转换为数组格式，并添加level字段
        if (isset($data['options']) && is_array($data['options'])) {
            $options = [];

            // 遍历所有选项
            foreach ($data['options'] as $key => $value) {
                if (is_array($value)) {
                    // 情况1: 选项是数组 (如 {"A": {"name": "选项A", "score": 5}, ...})
                    // 复制选项的所有属性
                    $option = $value;
                    // 添加level字段，保存原始的键名
                    $option['level'] = $key;
                } else {
                    // 情况2: 选项是字符串 (如 {"A": "选项A", ...})
                    // 创建一个包含name属性的数组
                    $option = ['name' => $value, 'level' => $key];
                }

                // 将处理后的选项添加到新数组中
                $options[] = $option;
            }

            // 用新的数组格式替换原来的键值对格式
            $data['options'] = $options;
        }

        return $data;
    }
}
