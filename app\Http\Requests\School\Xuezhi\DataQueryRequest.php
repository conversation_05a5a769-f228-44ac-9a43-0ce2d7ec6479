<?php

namespace App\Http\Requests\School\Xuezhi;

use App\Http\Requests\BaseRequest;

class DataQueryRequest extends BaseRequest
{
    protected array $commonRules = [
        'province_id' => 'required|integer|exists_province',
    ];

    public function rules(): array
    {
        $method = $this->route()->getActionMethod();
        
        return match ($method) {
            'yearList' => $this->yearListRules(),
            'liberalSciences' => $this->liberalSciencesRules(),
            'collegeScore' => $this->collegeScoreRules(),
            'majorScore' => $this->majorScoreRules(),
            'majorPlan' => $this->majorPlanRules(),
            default => [],
        };
    }

    protected function yearListRules(): array
    {
        return array_merge($this->commonRules, [
            'query_type' => 'required|integer|in:1,2,3',
        ]);
    }

    protected function liberalSciencesRules(): array
    {
        return array_merge($this->commonRules, [
            'query_type' => 'required|integer|in:1,2,3',
            'year' => 'nullable|integer|min:2015|max:' . (date('Y') + 1),
        ]);
    }

    protected function collegeScoreRules(): array
    {
        return array_merge($this->commonRules, [
            'college_id' => 'required|integer',
            'liberalScience' => 'required|integer|in:0,1,2',
        ]);
    }

    protected function majorScoreRules(): array
    {
        return array_merge($this->commonRules, [
            'college_id' => 'required|integer',
            'year' => 'required|integer|min:2015|max:' . (date('Y') + 1),
            'liberalScience' => 'required|integer|in:0,1,2',
            'phase' => 'nullable|integer',
        ]);
    }

    protected function majorPlanRules(): array
    {
        return array_merge($this->commonRules, [
            'college_id' => 'required|integer',
            'year' => 'required|integer|min:2015|max:' . (date('Y') + 1),
            'liberalScience' => 'required|integer|in:0,1,2',
        ]);
    }

    public function messages(): array
    {
        return [
            'province_id.required' => '省份ID不能为空',
            'province_id.integer' => '省份ID必须为整数',
            'province_id.exists' => '省份不存在',
            
            'college_id.required' => '院校ID不能为空',
            'college_id.integer' => '院校ID必须为整数',
            
            'query_type.required' => '查询类型不能为空',
            'query_type.integer' => '查询类型必须为整数',
            'query_type.in' => '查询类型值无效',
            
            'year.required' => '年份不能为空',
            'year.integer' => '年份必须为整数',
            'year.min' => '年份不能小于2015年',
            'year.max' => '年份不能大于当前年的下一年',
            
            'liberalScience.required' => '文理科类型不能为空',
            'liberalScience.integer' => '文理科类型必须为整数',
            'liberalScience.in' => '文理科类型值无效',
            
            'phase.integer' => '批次必须为整数',
        ];
    }
}