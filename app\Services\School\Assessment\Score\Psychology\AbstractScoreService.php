<?php

namespace App\Services\School\Assessment\Score\Psychology;

use App\Repositories\AnswerRepository;
use App\Services\BaseService;
use App\Services\School\Assessment\Score\ScoreServiceInterface;

/**
 * 心理健康测评抽象评分服务
 * 
 * 提供心理健康测评相关的基础评分功能，包括分数计算和结果分析
 */
abstract class AbstractScoreService extends BaseService implements ScoreServiceInterface
{
    public function __construct(
        protected AnswerRepository $answerRepository
    ) {
        
    }

    /**
     * 查询心理健康测评分数，维度分由sql完成计算
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 维度分数数组
     */
    public function calculateScores(array $params): array
    {
        $data = $this->answerRepository->getPsychologyDimensionScores($params);

        return $data;
    }
}
