<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class MajorCategoryResource extends JsonResource
{


    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->ID,
            'subject_code' => $this->SubjectCode,
            'subject_name' => $this->SubjectName,
            'categories' => $this->categories->map(function ($category) {
                return [
                    'id' => $category->ID,
                    'major_subject_id' => $category->MajorSubjectID,
                    'major_category_code' => $category->MajorCategoryCode,
                    'major_category_name' => $category->MajorCategoryName,
                    'majors' => $category->majors->map(function ($major) {
                        $majorData = [
                            'id' => $major->ID,
                            'major_code' => $major->MajorCode,
                            'major_name' => $major->MajorName,
                            'major_subject_id' => $major->MajorSubjectID,
                            'subject_code' => $major->SubjectCode,
                            'subject_name' => $major->SubjectName,
                            'major_category_id' => $major->MajorCategoryID,
                            'category_code' => $major->CategoryCode,
                            'category_name' => $major->CategoryName,
                            'remark' => $major->Remark,
                        ];

                        // Check if AcademicGroupId exists and is not null, only then add it
                        if (isset($major->AcademicGroupId)) {
                            $majorData['academic_group_id'] = $major->AcademicGroupId;
                            $majorData['phase'] = 1;
                        }else {
                            $majorData['phase'] = 4;
                        }

                        return $majorData;
                    })
                    ];
                }),
        ];
    }


}
