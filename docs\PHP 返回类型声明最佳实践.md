# PHP 返回类型声明最佳实践

返回类型声明是 PHP 7.0 引入的一项重要特性，它可以提高代码的可读性、可维护性和安全性。以下是使用返回类型声明的最佳实践：

## 1. 基本使用规范

### 基本语法
```php
function functionName(参数): 返回类型 {
    // 函数体
}
```

### 基本类型示例
```php
function add(int $a, int $b): int {
    return $a + $b;
}

function getUserName(User $user): string {
    return $user->name;
}

function isActive(User $user): bool {
    return $user->status === 'active';
}
```

## 2. 所有函数方法都应声明返回类型

### 最佳实践
```php
// 好
function calculateTotal(float $price, int $quantity): float {
    return $price * $quantity;
}

// 不好（缺少返回类型声明）
function calculateTotal(float $price, int $quantity) {
    return $price * $quantity;
}
```

## 3. 使用适当的返回类型

### 常见返回类型
- `: void` - 不返回任何值
- `: ?type` - 可空类型（PHP 7.1+）
- `: type` - 必须返回指定类型
- `: mixed` - 任何类型（PHP 8.0+）
- `: static` - 返回当前类的实例（PHP 8.0+）
- `: self` - 返回当前类的实例

## 4. 可空返回类型（Nullable Types）

### 正确使用
```php
function findUserById(int $id): ?User {
    // 可能返回 User 对象或 null
    return $this->users[$id] ?? null;
}
```

## 5. Void 返回类型

### 明确表示不返回值
```php
function logMessage(string $message): void {
    $this->logger->info($message);
    // 不需要 return 语句
}
```

## 6. 严格类型模式

### 启用严格类型检查
```php
declare(strict_types=1);

function divide(int $a, int $b): float {
    return $a / $b;
}

// 在严格模式下会抛出 TypeError
divide("10", "2");
```

## 7. 联合类型（PHP 8.0+）

### 多类型返回
```php
function getConfigValue(string $key): string|array|null {
    $config = $this->loadConfig();
    return $config[$key] ?? null;
}
```

## 8. 返回类型与继承

### 子类方法返回类型必须兼容
```php
class ParentClass {
    public function factory(): ParentClass {
        return new ParentClass();
    }
}

class ChildClass extends ParentClass {
    // 合法 - 返回更具体的类型
    public function factory(): ChildClass {
        return new ChildClass();
    }
}
```

## 9. 返回类型与接口实现

### 实现必须匹配接口定义
```php
interface UserRepository {
    public function find(int $id): ?User;
}

class DatabaseUserRepository implements UserRepository {
    // 必须与接口定义一致
    public function find(int $id): ?User {
        // 实现...
    }
}
```

## 10. 返回类型与异常

### 返回类型不匹配会抛出 TypeError
```php
function parseInt(string $input): int {
    $value = filter_var($input, FILTER_VALIDATE_INT);
    if ($value === false) {
        throw new InvalidArgumentException("Invalid integer input");
    }
    return $value;
}
```

## 11. 文档注释补充

### 使用 PHPDoc 补充信息
```php
/**
 * 计算订单总价
 * 
 * @param float $unitPrice 单价
 * @param int $quantity 数量
 * @param float $discount 折扣 (0-1)
 * @return float 最终价格
 * @throws InvalidArgumentException 如果折扣不在0-1范围内
 */
function calculateTotalPrice(float $unitPrice, int $quantity, float $discount): float {
    if ($discount < 0 || $discount 1) {
        throw new InvalidArgumentException("Discount must be between 0 and 1");
    }
    return $unitPrice * $quantity * (1 - $discount);
}
```

## 12. 返回类型重构建议

### 重构无返回类型的老代码
1. 先添加 `@return` PHPDoc 注释
2. 然后逐步添加实际的返回类型声明
3. 使用静态分析工具检查类型一致性

## 13. 静态分析工具

### 使用工具验证类型
- PHPStan
- Psalm
- Phan

这些工具可以帮助你在开发早期发现类型不匹配的问题。

## 14. 返回类型性能

虽然返回类型声明会带来极小的运行时开销（类型检查），但这点开销通常可以忽略不计，而带来的代码质量提升是显著的。

## 总结

1. **始终声明返回类型** - 提高代码清晰度
2. **使用最具体的类型** - 尽可能精确
3. **启用严格模式** - `declare(strict_types=1)`
4. **文档补充** - 使用 PHPDoc 补充复杂情况
5. **异常处理** - 返回类型不匹配应抛出 TypeError
6. **工具辅助** - 使用静态分析工具验证
7. **渐进式采用** - 逐步为老代码添加类型声明

遵循这些最佳实践将使你的 PHP 代码更加健壮、可维护，并减少运行时错误。