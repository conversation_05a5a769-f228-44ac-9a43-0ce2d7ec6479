<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TeacherViewCourseSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 查询新的教师基础表
        $teacherIds = DB::table('teachers')->where('school_id', $this->school_id)
            ->pluck('teacher_id','id')->toArray();
        // 查询老表老师对应的查看科目数据
        $teacherViewCourseList = DB::connection($this->connect)->table('teacher_viewcourse_relation')
            ->whereIn('teacher_id', array_values($teacherIds))
            ->select('teacher_id','viewcourse_id')
            ->get()->toArray();
        //老表viewcourse_id数组
        $viewCourseIdArr = array_column($teacherViewCourseList,'viewcourse_id');
        // 转化科目数据，找到新的科目id，数组结构为老表course_id为键,新表course_id和year为值
        $teacherCourseSeeder = new TeacherCourseSeeder($this->school_id);
        $viewCourseInfo = $teacherCourseSeeder->transformCourse($viewCourseIdArr);
        if(!$viewCourseInfo) dump('查询新表course_id为空');//没查到新表course_id打印空
        $teacherViewCourseData = [];
        foreach ($teacherViewCourseList as $item){
            $teacherId = $item->teacher_id;
            // 查找教师ID对应的新ID
            //$new_teacher_id = array_search($item->teacher_id, $teacherIds);
            //查询到新表course_id的才加进数组
            if(isset($viewCourseInfo[$item->viewcourse_id])){
                $teacherViewCourseData[] = [
                    'teacher_id' => $teacherId,
                    'course_id' => $viewCourseInfo[$item->viewcourse_id]['course_id'],
                    'school_year' => $viewCourseInfo[$item->viewcourse_id]['school_year'],
                    'created_at' => date('Y-m-d H:i:s',time()),
                    'updated_at' => date('Y-m-d H:i:s',time()),
                ];
            }
        }
        // 存储老师和查看科目关系数据
        DB::table('teacher_view_courses')->insert($teacherViewCourseData);
        dump('教师查科目...end...');
    }
}
