<?php

namespace App\Models\Evaluation;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 题目类型模型
 */
class QuestionType extends Model
{
    protected $table = 'evaluation_question_types';

    protected $fillable = [
        'type_name',
        'description',
        'code',
        'is_subjective',
        'sort',
        'status',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'is_subjective' => 'integer',
        'sort' => 'integer',
        'status' => 'integer',
    ];

    /**
     * 题目关联
     */
    public function questions(): HasMany
    {
        return $this->hasMany(Question::class, 'type_id');
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeActive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * 作用域：主观题
     */
    public function scopeSubjective($query)
    {
        return $query->where('is_subjective', 1);
    }

    /**
     * 作用域：客观题
     */
    public function scopeObjective($query)
    {
        return $query->where('is_subjective', 0);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort')->orderBy('id');
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttribute(): string
    {
        return $this->is_subjective == 1 ? '主观题' : '客观题';
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        $statusMap = [
            0 => '正常',
            -1 => '已删除'
        ];

        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 检查是否为主观题
     */
    public function isSubjective(): bool
    {
        return $this->is_subjective == 1;
    }

    /**
     * 检查是否为客观题
     */
    public function isObjective(): bool
    {
        return $this->is_subjective == 0;
    }

    /**
     * 检查是否可以删除
     */
    public function canDelete(): bool
    {
        // 检查是否被题目使用
        if ($this->questions()->where('status', 0)->exists()) {
            return false;
        }

        return true;
    }

    /**
     * 获取使用该类型的题目数量
     */
    public function getQuestionCount(): int
    {
        return $this->questions()->where('status', 0)->count();
    }

    /**
     * 软删除
     */
    public function softDelete(): bool
    {
        return $this->update(['status' => -1]);
    }

    /**
     * 恢复软删除
     */
    public function restore(): bool
    {
        return $this->update(['status' => 0]);
    }

    /**
     * 批量软删除
     */
    public static function batchSoftDelete(array $ids): bool
    {
        return static::whereIn('id', $ids)->update(['status' => -1]) > 0;
    }

    /**
     * 获取下一个排序值
     */
    public static function getNextSort(): int
    {
        $maxSort = static::where('status', 0)->max('sort');
        return ($maxSort ?? 0) + 1;
    }

    /**
     * 重新排序
     */
    public static function reorder(): bool
    {
        $questionTypes = static::where('status', 0)
            ->orderBy('sort')
            ->orderBy('id')
            ->get();

        foreach ($questionTypes as $index => $questionType) {
            $questionType->update(['sort' => $index + 1]);
        }

        return true;
    }

    /**
     * 复制题目类型
     */
    public function duplicate(string $nameSuffix = '_副本'): QuestionType
    {
        $newQuestionType = static::create([
            'type_name' => $this->type_name . $nameSuffix,
            'description' => $this->description,
            'code' => $this->code ? $this->code . '_copy' : '',
            'is_subjective' => $this->is_subjective,
            'sort' => static::getNextSort(),
            'status' => 0
        ]);

        return $newQuestionType;
    }

    /**
     * 获取题目类型选项
     */
    public static function getOptions(): array
    {
        return static::where('status', 0)
            ->orderBy('sort')
            ->orderBy('id')
            ->pluck('type_name', 'id')
            ->toArray();
    }

    /**
     * 获取主观题类型选项
     */
    public static function getSubjectiveOptions(): array
    {
        return static::where('status', 0)
            ->where('is_subjective', 1)
            ->orderBy('sort')
            ->orderBy('id')
            ->pluck('type_name', 'id')
            ->toArray();
    }

    /**
     * 获取客观题类型选项
     */
    public static function getObjectiveOptions(): array
    {
        return static::where('status', 0)
            ->where('is_subjective', 0)
            ->orderBy('sort')
            ->orderBy('id')
            ->pluck('type_name', 'id')
            ->toArray();
    }

    /**
     * 根据代码获取题目类型
     */
    public static function getByCode(string $code): ?QuestionType
    {
        return static::where('code', $code)->where('status', 0)->first();
    }

    /**
     * 检查代码是否唯一
     */
    public static function isCodeUnique(string $code, ?int $excludeId = null): bool
    {
        $query = static::where('code', $code)->where('status', 0);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return !$query->exists();
    }

    /**
     * 获取统计信息
     */
    public static function getStatistics(): array
    {
        $total = static::where('status', 0)->count();
        $subjective = static::where('status', 0)->where('is_subjective', 1)->count();
        $objective = static::where('status', 0)->where('is_subjective', 0)->count();

        return [
            'total' => $total,
            'subjective' => $subjective,
            'objective' => $objective,
            'subjective_rate' => $total > 0 ? round(($subjective / $total) * 100, 2) : 0,
            'objective_rate' => $total > 0 ? round(($objective / $total) * 100, 2) : 0
        ];
    }
}
