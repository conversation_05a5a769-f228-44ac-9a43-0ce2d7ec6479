<?php

use App\Http\Controllers\School\Xuezhi\ForeignCollegeController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/


// 学校系统管理相关接口放在学校路由组中
Route::group(['prefix' => 'school'], function () {
    // 系统设置
    require base_path('routes/school/system.php');
});

// 学校端业务相关模块路由
Route::group([], function () {
    // 测评
    require base_path('routes/school/assessment.php');
    // 生涯教育
    require base_path('routes/school/xuezhi.php');

    // 学校端 海外院校库 相关模块路由
    Route::group(['prefix' => 'foreignCollege'], function () {
        Route::get('list', [ForeignCollegeController::class, 'index'])->name('foreignCollege.list');
        Route::get('/{id}', [ForeignCollegeController::class, 'show'])->name('foreignCollege.show')->where('id', '[0-9]+');
    });
});

