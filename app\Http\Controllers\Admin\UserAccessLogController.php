<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Permission;
use App\Services\Admin\UserAccessLogService;
use Illuminate\Http\Request;

class UserAccessLogController extends Controller
{

    // 构造函数注入 SchoolService
    public function __construct(protected UserAccessLogService $userAccessLogService)
    {
    }

    public function list(Request $request)
    {
        $builder = $this->userAccessLogService->listBuilder($request);
        $cnt = $builder->count();
        $list = $builder->pagination()->orderBy("id", "desc")->get();
        // return  $this->success(compact('cnt', 'list'));
        return $this->paginateSuccess($list, $cnt);
    }

    public function store(Request $request, Permission $permission): void
    {
        $this->userAccessLogService->store($request, $permission);
    }



}
