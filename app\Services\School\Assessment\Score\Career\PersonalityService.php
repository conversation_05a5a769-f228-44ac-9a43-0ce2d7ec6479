<?php

namespace App\Services\School\Assessment\Score\Career;

use App\Services\School\Assessment\Score\Career\AbstractScoreService;

/**
 * 性格评估服务
 * 
 * 用于计算学生的性格评估结果
 */
class PersonalityService extends AbstractScoreService
{
    /**
     * 计算性格评估结果
     * 
     * @param array $params 包含评估所需参数的数组
     * @return array 性格评估结果数组
     */
    public function calculate($params): array
    {
        $result = $this->calculatePersonalityScores($params);
        $scores = array_column($result,'score','code');
        // 初始化结果
        $code = '';

        // E/I维度
        $code .= $scores['E'] > $scores['I']? 'E' : 'I';

        // S/N维度
        $code .= $scores['S'] > $scores['N']? 'S' : 'N';

        // T/F维度
        $code .= $scores['T'] > $scores['F']? 'T' : 'F';

        // J/P维度
        $code .= $scores['J'] > $scores['P']? 'J' : 'P';

        return [
            'dimensions' => $result, 
            'code' => $code
        ];
    }
}