<?php
/**
 * Created by PhpStorm.
 * User: zhaoyongyuan
 * Date: 2023/7/4
 * Time: 15:16
 */
namespace app\evaluation\logic;
use app\evaluation\model\Question as question_model;
use app\evaluation\model\Papers as paper_model;
use app\evaluation\model\QuestionOption as QuestionOption_model;
use app\evaluation\model\CategoryPortion as CategoryPortion_model;
use think\Cache;

class Questions
{
    protected $question_model;
    protected $user;

    public function __construct()
    {
        $this->user = get_user();
    }

    public function add()
    {
        set_time_limit(0);
        $model = new question_model();
        $QuestionOption_model = new QuestionOption_model();
        $CategoryPortion_model = new CategoryPortion_model();
        $request_body =  file_get_contents('php://input');
        $data = json_decode($request_body, true);
        $is_common=$data['is_common'];
        $category_ids=[];
        $type_id=[];
        $grade=[];
        $course_id=[];
        $situation=[];
        $score=[];
        $knowlege_ids=[];
        $content=[];
        $answer=[];
        $analysis=[];
        if (array_key_exists('category_ids', $data)) {
            $category_ids=$data['category_ids'];
        }
        if (array_key_exists('type_id', $data)) {
            $type_id=$data['type_id'];
        }
        if (array_key_exists('grade', $data)) {
            $grade=$data['grade'];
        }
        if (array_key_exists('course_id', $data)) {
            $course_id=$data['course_id'];
        }
        if (array_key_exists('situation', $data)) {
            $situation=$data['situation'];
        }
        if (array_key_exists('score', $data)) {
            $score=$data['score'];
        }
        if (array_key_exists('knowlege_ids', $data)) {
            $knowlege_ids=$data['knowlege_ids'];
        }
        if (array_key_exists('content', $data)) {
            $content=$data['content'];
        }
        if (array_key_exists('answer', $data)) {
            $answer=$data['answer'];
        }
        if (array_key_exists('analysis', $data)) {
            $analysis=$data['analysis'];
        }

        $parent_id=$data['parent_id'];
        $model->save([
            'is_common' => $is_common,
            'category_ids' => $category_ids,
            'type_id' => $type_id,
            'grade' => $grade,
            'course_id' => $course_id,
            'situation' => $situation,
            'score' => $score,
            'knowlege_ids' => $knowlege_ids,
            'content' => $content,
            'answer' => $answer,
            'analysis' => $analysis,
            'parent_id' => $parent_id,
            'update_at' =>  date('Y-m-d H:i:s'),
        ]);

        //$data = json_decode(input('post.options'),true);

        if (array_key_exists('options', $data)) {
            $option_name = $data['options'];
        }
        if (array_key_exists('categorey_percentage', $data)) {
            $categorey_percentage = $data['categorey_percentage'];
        }

        $model->save();
        $id = $model->id;
        $newColumn = ['question_id' => $id];
        if(!empty($option_name)){
            foreach ($option_name as &$item) {
                $item = array_merge($item,  $newColumn);
            }
            $QuestionOption_model->saveAll($option_name);
        }
        if(!empty($categorey_percentage)){
            foreach ($categorey_percentage as &$categoreyitem) {
                $categoreyitem = array_merge($categoreyitem,  $newColumn);
            }
            $CategoryPortion_model->saveAll($categorey_percentage);
        }

        apiReturn($id);
    }

    public function edit()
    {
        set_time_limit(0);
        $model = new question_model();
        $QuestionOption_model = new QuestionOption_model();
        $request_body =  file_get_contents('php://input');
        $data = json_decode($request_body, true);
        $is_common=$data['is_common'];
        $category_ids=[];
        $type_id=[];
        $grade=[];
        $course_id=[];
        $situation=[];
        $score=[];
        $knowlege_ids=[];
        $content=[];
        $answer=[];
        $analysis=[];
        if (array_key_exists('category_ids', $data)) {
            $category_ids=$data['category_ids'];
        }
        if (array_key_exists('type_id', $data)) {
            $type_id=$data['type_id'];
        }
        if (array_key_exists('grade', $data)) {
            $grade=$data['grade'];
        }
        if (array_key_exists('course_id', $data)) {
            $course_id=$data['course_id'];
        }
        if (array_key_exists('situation', $data)) {
            $situation=$data['situation'];
        }
        if (array_key_exists('score', $data)) {
            $score=$data['score'];
        }
        if (array_key_exists('knowlege_ids', $data)) {
            $knowlege_ids=$data['knowlege_ids'];
        }
        if (array_key_exists('content', $data)) {
            $content=$data['content'];
        }
        if (array_key_exists('answer', $data)) {
            $answer=$data['answer'];
        }
        if (array_key_exists('analysis', $data)) {
            $analysis=$data['analysis'];
        }
        $parent_id=$data['parent_id'];
        $model->save([
            'is_common' => $is_common,
            'category_ids' => $category_ids,
            'type_id' => $type_id,
            'grade' => $grade,
            'course_id' => $course_id,
            'situation' => $situation,
            'score' => $score,
            'knowlege_ids' => $knowlege_ids,
            'content' => $content,
            'answer' => $answer,
            'analysis' => $analysis,
            'parent_id' => $parent_id,
            'update_at' =>  date('Y-m-d H:i:s'),
        ], ['id' => input('id')]);

        //更新题目后，更新缓存
        if($data['parent_id']){
            $input_question_id = $data['parent_id'];
        }else{
            $input_question_id = input('id');
        }
        $paper_ids_arr = db('evaluation_papers')->where('find_in_set('.$input_question_id.',question_ids)')->where('status',0)->column('id');
        if(!empty($paper_ids_arr)){
            $prefix = get_redis_key();
            foreach ($paper_ids_arr as $v){
                $questions_key = "evaluation:{$prefix}:questions:" . $v;
                $store = Cache::get($questions_key);
                $store = json_decode($store,true);
                if($store)  Cache::rm($questions_key);//如果题目有修改就清除包含此题目的试卷缓存
            }
        }

        $newColumn = ['question_id' => input('id')];
        $QuestionOption_model = new QuestionOption_model();
        $QuestionOption = $QuestionOption_model->field('title, content, score, answer')->where('question_id', input('id'))->where('status', 0)->select();

        $QstingOptions = [];
        $existingOptions = [];
        $existing = [];
        foreach ($QuestionOption as $option) {
            $existingOptions[] = $option->toArray();
        }

        if (array_key_exists('options', $data)) {
            $option_name = $data['options'];
            foreach ($existingOptions as $option) {
                $existing[] = array_map('strval', $option);
            }
            $diff = false;
            if (count($existing) === count($option_name)) {
                foreach ($existing as $key => $value) {
                    if ($value !== $option_name[$key]) {
                        $diff = true;
                        break;
                    }
                }
            } else {
                $diff = true;
            }

            if (!empty($diff)) {
                $model = new QuestionOption_model();
                $model->save([
                    'status' => '-1',
                ], ['question_id' => input('id')]);
                if (!empty($option_name)) {
                    foreach ($option_name as &$item) {
                        $item = array_merge($item, $newColumn);
                    }
                    $model = new QuestionOption_model();
                    $QuestionOption_model->saveAll($option_name);
                }
            }
        }

        if (array_key_exists('categorey_percentage', $data)) {
            $categorey_percentage = $data['categorey_percentage'];
        }

        if (!empty($categorey_percentage)) {

            $CategoryPortion_model = new CategoryPortion_model();
            $CategoryPortion_model->save([
                'status' => '-1',
            ], ['question_id' => input('id')]);

            foreach ($categorey_percentage as &$categoreyitem) {
                $categoreyitem = array_merge($categoreyitem, $newColumn);
            }

            $CategoryPortion_model = new CategoryPortion_model();
            $CategoryPortion_model->saveAll($categorey_percentage);
        }

        if (!empty($diff)) {
            $model = new QuestionOption_model();
            $model->save([
                'status' => '-1',
            ], ['question_id' => input('id')]);
            if (!empty($option_name)) {
                foreach ($option_name as &$item) {
                    $item = array_merge($item, $newColumn);
                }
                $model = new QuestionOption_model();
                $model->saveAll($option_name);
            }
        }

        apiReturn(input('id'));
    }

    public function del()
    {
        $model = new question_model();
        $model->save([
            'status' => '-1',
        ], ['id' => input('id')]);
        //软删除
        apiReturn(input('id'));
    }

    public function get_list()
    {
        $id = input('id');
        $paper_id = input('paper_id');
        $pageNumber = input('page', 1); // 获取页码，默认为1
        $pageSize = input('pagesize', 10); // 获取每页显示的记录数，默认为10
        $question_model = new question_model();
        $content=input('content');
        $is_common=input('is_common');
        //传paper_id的时候可以调用redis缓存
        if(!empty($paper_id)){
            $prefix = get_redis_key();
            $questions_key = "evaluation:{$prefix}:questions:" . $paper_id;
            $data = Cache::get($questions_key);
            $data = json_decode($data,true);
            if(!$data) {
                $paper_model = new paper_model();
                $question_ids = $paper_model->where('id',$paper_id)->value('question_ids');
                $query  = $question_model->with(['typeStr', 'optionList', 'categoryPortion'])
                    ->where(function ($query) use($question_ids){
                        $query->where('status', 0);
                        $query->where(['id'=>['in',$question_ids]]);
                    });
                $query->with(['children'=>function($query) use ($content){
                    $query->with(['typeStr', 'optionList', 'categoryPortion']);
                }]);
                $data = $query->order(\think\Db::raw("FIELD(id,".$question_ids.")"))->select();
                $data = to_arr($data);
                $grade = array_flip(grade());
                $course = array_flip(course());
                foreach ($data as $key => $value){
                    $data[$key]['grade_name'] = $grade[$value['grade']] ?? '';
                    $data[$key]['course_name'] = $course[$value['course_id']] ?? '';
                }
                Cache::set($questions_key,json_encode($data) , 24 * 60 * 60 * 7);
            }
            apiReturn($data);
        }elseif (!empty($id)) {
            $query  = $question_model->with(['typeStr', 'optionList', 'categoryPortion'])
                ->where(function ($query) {
                    $query->where('status', 0);
                    !empty(input('id')) && $query->where(['id'=>['in',input('id')]]);
                });
            $query->with(['children'=>function($query) use ($content){
                $query->with(['typeStr', 'optionList', 'categoryPortion']);
            }]);
            $data = $query->order(\think\Db::raw("FIELD(id,".$id.")"))->select();
            $data = to_arr($data);
            $grade = array_flip(grade());
            $course = array_flip(course());
            foreach ($data as $key => $value){
                $data[$key]['grade_name'] = $grade[$value['grade']] ?? '';
                $data[$key]['course_name'] = $course[$value['course_id']] ?? '';
                $data[$key]['score'] = floatval($value['score']);
                if($value['is_common'] == 2){
                    foreach ($value['children'] as $k => $v){
                        $data[$key]['children'][$k]['score'] = floatval($v['score']);
                    }
                }
            }
            apiReturn($data);
        } else {
            $query  = $question_model->with(['typeStr', 'optionList', 'categoryPortion'])
                ->where(function ($query) {
                    $query->where('status', 0)->where('parent_id', 0);
                    !empty(input('type_id')) && $query->where('type_id', input('type_id'));
                    !empty(input('is_common')) && $query->where('is_common', input('is_common'));
                    !empty(input('course_id')) && $query->where('course_id', input('course_id'));

                    if (!empty(input('category_ids'))) {
                        $category_ids_arr = explode(',', input('category_ids'));
                        if (count($category_ids_arr) == 1) {
                            $p_ids = question_model::where("FIND_IN_SET('" . input('category_ids') . "', category_ids)")->column('id,parent_id');
                        } elseif (count($category_ids_arr) > 1) {
                            $str = '';
                            foreach ($category_ids_arr as $k => $v) {
                                if ($k == 0) {
                                    $str = "FIND_IN_SET('" . $v . "', category_ids)";
                                } else {
                                    $str .= " or FIND_IN_SET('" . $v . "', category_ids)";
                                }
                            }
                            $p_ids = question_model::where($str)->column('id,parent_id');
                        }
                        $id    = array_filter(array_keys($p_ids));
                        $p_id  = array_unique(array_filter(array_values($p_ids)));
                        $p_arr = array_merge($id, $p_id);
                        $p_arr = implode($p_arr, ',');
                        $query->whereIn('id', $p_arr);
                    }

                    !empty(input('grade')) && $query->where('grade', input('grade'));
                    if (!empty(input('content'))) {

                        $parent_ids = question_model::where('content', 'like', '%' . input('content') . '%')->column('id,parent_id');
                        $id=array_filter(array_keys($parent_ids));
                        $parent_id=array_filter(array_values($parent_ids));
                        $parent_arr=array_merge($id,$parent_id);
                        $parent_arr=implode($parent_arr,',');
                        $query->whereIn('id',$parent_arr);
                    }
                    !empty(input('start_score')) && !empty(input('end_score')) && $query->whereBetween('score', [input('start_score'), input('end_score')]);
                });
            $query->with(['children'=>function($query) use ($content){
                $query->with(['typeStr', 'optionList', 'categoryPortion']);
                if (!empty($content)) {
                    $query->where('content', 'like', '%' .$content . '%');
                }
            }]);
            //  ->with(['children.typeStr', 'children.optionList']); // 在这里预加载 children 关联的 typeStr 和 optionList
            $data = $query->order('id desc')->paginate($pageSize, false, ['page' => $pageNumber]);
            //   echo $query->getLastSql();exit;
            $data = to_arr($data);
            $grade = array_flip(grade());
            $course = array_flip(course());
            foreach ($data['data'] as $key => $value){
                $data['data'][$key]['grade_name'] = $grade[$value['grade']] ?? '';
                $data['data'][$key]['course_name'] = $course[$value['course_id']] ?? '';
            }
            apiReturn(['total' => $data['total'], 'data' => $data['data']]);
        }
    }

    public function edit_sort()
    {
        $request_body =  file_get_contents('php://input');
        $question_model = new question_model();
        $data = json_decode($request_body, true);
        if (array_key_exists('questions', $data)) {
            $questions_data = $data['questions'];
            $items = [];
            foreach ($questions_data as $item) {
                $question_model->isUpdate(true)->save(['sort' => $item['sort']], ['id' => $item['id']]);
            }

            apiReturn($data,'操作成功！',0);
        }else{
            apiReturn([],'操作失败！',-1);
        }
    }
}