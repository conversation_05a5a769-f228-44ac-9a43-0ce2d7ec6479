# 数据同步使用示例

## 概述

本文档展示如何在原有数据添加完成后，调用数据同步功能将数据同步到另一个数据库。

## 使用方式

### 1. 引入 DataSyncHelper

```php
use App\Helpers\DataSyncHelper;
```

### 2. 在数据添加完成后调用同步

#### 学校创建完成后同步（直接同步）

```php
/**
 * 创建学校示例
 */
public function createSchool(Request $request)
{
    try {
        // 原有的学校创建逻辑
        $alias = (new School())->getMorphClass();
        $school = $this->organizationService->createOrgDefaultInfo($request, $alias);
        
        // 数据添加完成后，调用同步
        if (config('datasync.enabled', false)) {
            $syncHelper = new DataSyncHelper();
            
            // 准备学校数据
            $schoolData = $school->toArray();
            
            // 添加额外数据
            $additionalData = [
                'add_time' => $request->input('add_time', now()->format('Y-m-d H:i:s')),
                'date_due' => $request->input('date_due'),
                'province' => $request->input('province'),
                'city' => $request->input('city'),
                'district' => $request->input('district'),
                'address' => $request->input('address'),
                'buy_modules' => $request->input('buy_modules'),
                'location' => $request->input('location'),
            ];
            
            // 调用同步（直接同步）
            $syncResult = $syncHelper->syncSchool($schoolData, $additionalData);
            
            if ($syncResult['success']) {
                Log::info('学校数据同步成功', $syncResult);
            } else {
                Log::error('学校数据同步失败', $syncResult);
            }
        }
        
        return $this->message("学校创建成功");
        
    } catch (\Exception $e) {
        Log::error('创建学校失败', ['error' => $e->getMessage()]);
        return $this->error('创建学校失败: ' . $e->getMessage());
    }
}
```

#### 校区创建完成后同步（直接同步）

```php
/**
 * 创建校区示例
 */
public function createCampus(Request $request)
{
    try {
        // 原有的校区创建逻辑
        $campus = $this->campusService->create($request->all());
        
        // 数据添加完成后，调用同步
        if (config('datasync.enabled', false)) {
            $syncHelper = new DataSyncHelper();
            
            // 校区数据通常在学校同步时一起处理
            // 如果需要单独同步校区，可以调用
            $syncResult = $syncHelper->syncCampus($campus->toArray());
            
            Log::info('校区数据同步结果', $syncResult);
        }
        
        return $this->message("校区创建成功");
        
    } catch (\Exception $e) {
        return $this->error('创建校区失败: ' . $e->getMessage());
    }
}
```

#### 年级创建完成后同步（直接同步）

```php
/**
 * 创建年级示例
 */
public function createGrade(Request $request)
{
    try {
        // 原有的年级创建逻辑
        $grade = $this->gradeService->create($request->all());
        
        // 数据添加完成后，调用同步
        if (config('datasync.enabled', false)) {
            $syncHelper = new DataSyncHelper();
            
            // 调用年级同步（直接同步）
            $syncResult = $syncHelper->syncGrade($grade->toArray());
            
            if ($syncResult['success']) {
                Log::info('年级数据同步成功', $syncResult);
            } else {
                Log::error('年级数据同步失败', $syncResult);
            }
        }
        
        return $this->message("年级创建成功");
        
    } catch (\Exception $e) {
        return $this->error('创建年级失败: ' . $e->getMessage());
    }
}
```

#### 班级创建完成后同步（直接同步）

```php
/**
 * 创建班级示例
 */
public function createClass(Request $request)
{
    try {
        // 原有的班级创建逻辑
        $class = $this->classService->create($request->all());
        
        // 数据添加完成后，调用同步
        if (config('datasync.enabled', false)) {
            $syncHelper = new DataSyncHelper();
            
            // 调用班级同步（直接同步）
            $syncResult = $syncHelper->syncClass($class->toArray());
            
            if ($syncResult['success']) {
                Log::info('班级数据同步成功', $syncResult);
            } else {
                Log::error('班级数据同步失败', $syncResult);
            }
        }
        
        return $this->message("班级创建成功");
        
    } catch (\Exception $e) {
        return $this->error('创建班级失败: ' . $e->getMessage());
    }
}
```

#### 学生创建完成后同步（异步同步）

```php
/**
 * 创建学生示例
 */
public function createStudent(Request $request)
{
    try {
        // 原有的学生创建逻辑
        $student = $this->studentService->create($request->all());
        
        // 数据添加完成后，调用异步同步
        if (config('datasync.enabled', false)) {
            $syncHelper = new DataSyncHelper();
            
            // 调用学生异步同步
            $syncResult = $syncHelper->asyncSyncStudent($student->toArray());
            
            Log::info('学生数据异步同步结果', $syncResult);
        }
        
        return $this->message("学生创建成功");
        
    } catch (\Exception $e) {
        return $this->error('创建学生失败: ' . $e->getMessage());
    }
}
```

#### 教师创建完成后同步（异步同步）

```php
/**
 * 创建教师示例
 */
public function createTeacher(Request $request)
{
    try {
        // 原有的教师创建逻辑
        $teacher = $this->teacherService->create($request->all());
        
        // 数据添加完成后，调用异步同步
        if (config('datasync.enabled', false)) {
            $syncHelper = new DataSyncHelper();
            
            // 调用教师异步同步
            $syncResult = $syncHelper->asyncSyncTeacher($teacher->toArray());
            
            Log::info('教师数据异步同步结果', $syncResult);
        }
        
        return $this->message("教师创建成功");
        
    } catch (\Exception $e) {
        return $this->error('创建教师失败: ' . $e->getMessage());
    }
}
```

#### 批量导入学生后同步（异步同步）

```php
/**
 * 批量导入学生示例
 */
public function importStudents(Request $request)
{
    try {
        // 原有的批量导入逻辑
        $students = $this->studentService->batchImport($request->file('file'));
        
        // 数据添加完成后，调用批量异步同步
        if (config('datasync.enabled', false) && !empty($students)) {
            $syncHelper = new DataSyncHelper();
            
            // 准备学生数据数组
            $studentsData = collect($students)->map(function ($student) {
                return $student->toArray();
            })->toArray();
            
            // 调用批量异步同步
            $syncResult = $syncHelper->batchAsyncSyncStudents($studentsData);
            
            Log::info('批量学生数据异步同步结果', $syncResult);
        }
        
        return $this->message("学生批量导入成功，共导入 " . count($students) . " 名学生");
        
    } catch (\Exception $e) {
        return $this->error('批量导入学生失败: ' . $e->getMessage());
    }
}
```

#### 批量导入教师后同步（异步同步）

```php
/**
 * 批量导入教师示例
 */
public function importTeachers(Request $request)
{
    try {
        // 原有的批量导入逻辑
        $teachers = $this->teacherService->batchImport($request->file('file'));
        
        // 数据添加完成后，调用批量异步同步
        if (config('datasync.enabled', false) && !empty($teachers)) {
            $syncHelper = new DataSyncHelper();
            
            // 准备教师数据数组
            $teachersData = collect($teachers)->map(function ($teacher) {
                return $teacher->toArray();
            })->toArray();
            
            // 调用批量异步同步
            $syncResult = $syncHelper->batchAsyncSyncTeachers($teachersData);
            
            Log::info('批量教师数据异步同步结果', $syncResult);
        }
        
        return $this->message("教师批量导入成功，共导入 " . count($teachers) . " 名教师");
        
    } catch (\Exception $e) {
        return $this->error('批量导入教师失败: ' . $e->getMessage());
    }
}
```

## 同步策略说明

### 直接同步（学校、校区、年级、班级）
- **特点**: 立即执行，阻塞当前请求
- **适用**: 数据量小，重要性高的基础数据
- **优点**: 实时性好，可以立即获得同步结果
- **缺点**: 可能影响用户体验

### 异步同步（学生、教师）
- **特点**: 加入队列异步处理，不阻塞当前请求
- **适用**: 数据量大的用户数据
- **优点**: 不影响用户体验，可以处理大量数据
- **缺点**: 无法立即获得同步结果

## 队列配置

确保队列处理器正在运行：

```bash
# 启动队列处理器
php artisan queue:work --queue=data-sync

# 或者使用 Supervisor 管理队列进程
```

## 监控和日志

### 查看同步日志

```bash
# 查看同步日志
tail -f storage/logs/laravel.log | grep "数据同步"

# 查看队列状态
php artisan queue:work --queue=data-sync --verbose
```

### 监控同步状态

```php
// 检查同步状态
$syncHelper = new DataSyncHelper();
$status = $syncHelper->checkSyncStatus('school', $schoolId);
```

## 错误处理

### 同步失败处理

```php
$syncResult = $syncHelper->syncSchool($schoolData, $additionalData);

if (!$syncResult['success']) {
    // 记录错误日志
    Log::error('学校数据同步失败', [
        'school_id' => $schoolData['id'],
        'error' => $syncResult['message']
    ]);
    
    // 可以选择重试或者发送告警
    // $this->retrySync($schoolData);
    // $this->sendAlert($syncResult['message']);
}
```

### 队列失败处理

```bash
# 查看失败的队列任务
php artisan queue:failed

# 重试失败的任务
php artisan queue:retry all

# 清除失败的任务
php artisan queue:flush
```

## 性能优化建议

1. **批量处理**: 对于大量数据，使用批量同步方法
2. **队列分离**: 将同步任务放在专门的队列中处理
3. **错误重试**: 配置合理的重试机制
4. **监控告警**: 设置同步失败的告警机制
5. **数据验证**: 在同步前验证数据完整性

通过这种方式，您可以在不修改原有代码逻辑的情况下，轻松地在数据添加完成后调用数据同步功能。
