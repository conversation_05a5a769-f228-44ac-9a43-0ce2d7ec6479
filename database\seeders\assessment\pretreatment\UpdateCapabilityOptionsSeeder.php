<?php

namespace Database\Seeders\assessment\pretreatment;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UpdateCapabilityOptionsSeeder extends Seeder
{
    protected string $connect = 'mysql_prod';
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 学习力测评
        $this->processSurveyQuestions('234', 9);
        // 批判性思维能力
        $this->processSurveyQuestions('258', 10);
        // 问题解决能力
        $this->processSurveyQuestions('257', 11);
        // 创造思维倾向
        $this->processSurveyQuestions('256', 12);
        // 沟通与合作
        $this->processSurveyQuestions('255', 13);
    }

    public function processSurveyQuestions($surveyId,$assessmentId)
    {
        $surveyQuestionData = DB::connection($this->connect)
        ->table('survey_question')
        ->where('survey_id', $surveyId)
        ->pluck('id')->toArray();
        // dd($surveyQuestionData);
        // 遍历数据，将数据插入到测评表中
        foreach ($surveyQuestionData as $item) {
            // 获取每个问题的选项
            $answersData = DB::connection($this->connect)
                ->table('survey_question_answer')
                ->where('question_id', $item)
                ->orderBy('id') // 使用适当的列来排序选项
                ->get()
                ->toArray();
            
            foreach ($answersData as $k => $v){
                $letter = chr(65 + ($v->sort - 1));//sort=1非常不符合，分配A
                $updates[] = [
                    'id'=>$v->id,
                    'option'=>$letter
                ];
            }
        }
        // dd($updates);
        // 批量更新
        if (!empty($updates)) {
            $connection = DB::connection($this->connect);
            foreach ($updates as $update) {
                $connection->table('survey_question_answer')
                    ->where('id', $update['id'])
                    ->update(['option' => $update['option']]);
            }
        }
        dump('question_answer表中,测评'.$surveyId.'对应option字段更新完毕');
    }

}
