<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

abstract class BaseIncrementalSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';
    protected string $assessment_type;
    
    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }

    /**
     * 获取最后处理的记录ID
     */
    protected function getLastProcessedId(): int
    {
        $log = DB::table('seeder_execution_logs')
            ->where('seeder_class', static::class)
            ->where('assessment_type', $this->assessment_type)
            ->where('school_id', $this->school_id)
            ->first();

        return $log ? $log->last_processed_id : 0;
    }

    /**
     * 更新执行日志
     */
    protected function updateExecutionLog(int $lastProcessedId, int $totalProcessed): void
    {
        DB::table('seeder_execution_logs')->updateOrInsert(
            [
                'seeder_class' => static::class,
                'assessment_type' => $this->assessment_type,
                'school_id' => $this->school_id,
            ],
            [
                'last_processed_id' => $lastProcessedId,
                'total_processed' => $totalProcessed,
                'last_executed_at' => now(),
                'updated_at' => now(),
            ]
        );
    }

    /**
     * 检查是否有新数据需要处理
     */
    protected function hasNewData(): bool
    {
        $lastProcessedId = $this->getLastProcessedId();
        $maxId = $this->getMaxIdForNewDataCheck();

        Log::info("检查新数据", [
            'seeder_class' => static::class,
            'assessment_type' => $this->assessment_type,
            'school_id' => $this->school_id,
            'last_processed_id' => $lastProcessedId,
            'max_id' => $maxId,
            'has_new_data' => $maxId > $lastProcessedId
        ]);

        return $maxId > $lastProcessedId;
    }

    /**
     * 获取 ysy_survey_user_session 表中的最大ID
     */
    protected function getMaxSurveySessionId(): int
    {
        $surveyIds = $this->getSurveyIds();

        if (empty($surveyIds)) {
            return 0;
        }

        $maxId = DB::connection($this->connect)
            ->table('survey_user_session')
            ->where('school_id', $this->school_id)
            ->whereIn('survey_id', $surveyIds)
            ->where('is_delete', 0)
            ->where('is_abnormal', 0)
            ->where('time_error', 0)
            ->where('result', '!=', '')
            ->max('id');

        return $maxId ?: 0;
    }

    /**
     * 获取增量查询的基础Query Builder
     * 子类可以使用这个方法来构建带有增量条件的查询
     */
    protected function getIncrementalSurveyQuery()
    {
        $lastProcessedId = $this->getLastProcessedId();
        $surveyIds = $this->getSurveyIds();

        return DB::connection($this->connect)
            ->table('survey_user_session')
            ->where('school_id', $this->school_id)
            ->whereIn('survey_id', $surveyIds)
            ->where('is_delete', 0)
            ->where('is_abnormal', 0)
            ->where('time_error', 0)
            ->where('result', '!=', '')
            ->where('id', '>', $lastProcessedId); // 关键：只查询大于最后处理ID的记录
    }

    /**
     * 获取增量查询的完整Query Builder（包含任务关联）
     * 子类可以直接使用这个方法来获取带有任务关联的增量数据
     */
    protected function getIncrementalAssignmentQuery()
    {
        $lastProcessedId = $this->getLastProcessedId();
        $assessmentIds = $this->getAssessmentIds();
        $surveyIds = $this->getSurveyIds();

        return DB::connection($this->connect)
            ->table('assessment_schedules as schedules')
            ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
            ->join('survey_user_session as session', function ($join) {
                $join->on('session.survey_id', '=', 'tasks.old_survey_id')
                    ->on('session.grade_id', '=', 'tasks.old_grade_id')
                    ->on('session.times', '=', 'tasks.old_times');
            })
            ->whereIn('tasks.assessment_id', $assessmentIds)
            ->where('schedules.school_id', $this->school_id)
            ->where('session.school_id', $this->school_id)
            ->whereIn('session.survey_id', $surveyIds)
            ->where('session.is_delete', 0)
            ->where('session.is_abnormal', 0)
            ->where('session.time_error', 0)
            ->where('session.result', '!=', '')
            ->where('session.id', '>', $lastProcessedId); // 关键：只查询大于最后处理ID的记录
    }

    /**
     * 检查测评任务是否已存在
     */
    protected function checkAssessmentTasksExist(): bool
    {
        $assessmentIds = $this->getAssessmentIds();
        
        if (empty($assessmentIds)) {
            return false;
        }

        $existingTasks = DB::table('assessment_tasks')
            ->join('assessment_schedules', 'assessment_tasks.assessment_schedule_id', '=', 'assessment_schedules.id')
            ->where('assessment_schedules.school_id', $this->school_id)
            ->whereIn('assessment_tasks.assessment_id', $assessmentIds)
            ->count();

        Log::info("检查测评任务", [
            'seeder_class' => static::class,
            'assessment_type' => $this->assessment_type,
            'school_id' => $this->school_id,
            'existing_tasks_count' => $existingTasks,
            'tasks_exist' => $existingTasks > 0
        ]);

        return $existingTasks > 0;
    }

    /**
     * 主执行方法
     */
    public function run(): void
    {
        Log::info("开始执行增量Seeder", [
            'seeder_class' => static::class,
            'assessment_type' => $this->assessment_type,
            'school_id' => $this->school_id
        ]);

        // 检查是否有新数据
        if (!$this->hasNewData()) {
            Log::info("没有新数据需要处理，跳过执行", [
                'seeder_class' => static::class,
                'assessment_type' => $this->assessment_type,
                'school_id' => $this->school_id
            ]);
            return;
        }

        // 检查测评任务是否已存在（如果需要创建任务的话）
        if ($this->shouldCreateTasks() && $this->checkAssessmentTasksExist()) {
            Log::info("测评任务已存在，跳过任务创建", [
                'seeder_class' => static::class,
                'assessment_type' => $this->assessment_type,
                'school_id' => $this->school_id
            ]);
        }

        // 执行具体的seeder逻辑
        $this->executeSeeder();

        Log::info("增量Seeder执行完成", [
            'seeder_class' => static::class,
            'assessment_type' => $this->assessment_type,
            'school_id' => $this->school_id
        ]);
    }

    /**
     * 子类需要实现的方法
     */
    abstract protected function getSurveyIds(): array;
    abstract protected function getAssessmentIds(): array;
    abstract protected function executeSeeder(): void;
    
    /**
     * 是否需要创建测评任务（子类可重写）
     */
    protected function shouldCreateTasks(): bool
    {
        return true;
    }

    /**
     * 获取用于检查新数据的最大ID（子类可重写）
     * 默认使用 survey_user_session 表的最大ID
     */
    protected function getMaxIdForNewDataCheck(): int
    {
        return $this->getMaxSurveySessionId();
    }
}
