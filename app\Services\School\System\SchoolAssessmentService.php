<?php

namespace App\Services\School\System;

use App\Exceptions\BusinessException;
use App\Models\School\Assessment\Assessment;
use App\Models\School\System\SchoolAssessment;
use App\Services\BaseService;
use Illuminate\Http\Request;

class SchoolAssessmentService extends BaseService
{

    //getSchoolAssessmentBySchoolId
    public function getSchoolAssessmentBySchoolId($school_id)
    {
        $schoolAssessment = SchoolAssessment::with(['assessment','school'])->where('school_id', $school_id)->get();
        return $schoolAssessment;
    }

    //getAssessments - 返回树结构数据用于Element UI树形穿梭框
    public function getAssessments(Request $request)
    {
        // 获取所有测评数据
        $assessments = Assessment::select([
            'id',
            'name',
            'category_code',
            'category_name',
            'official_name',
            'stage',
            'status'
        ])
        ->where('status', 0) // 只获取正常状态的测评
        ->orderBy('assessment_sort', 'asc')
        ->orderBy('id', 'asc')
        ->get();

        // 构建树结构数据
        return $this->buildTreeStructure($assessments);
    }

    /**
     * 构建树结构数据
     *
     * @param \Illuminate\Database\Eloquent\Collection $assessments
     * @return array
     */
    private function buildTreeStructure($assessments)
    {
        // 测评分类映射
        $categoryMapping = $this->getCategoryMapping();

        // 按分类分组
        $groupedAssessments = $assessments->groupBy('category_code');

        $treeData = [];

        foreach ($categoryMapping as $categoryCode => $categoryInfo) {
            // 获取该分类下的测评
            $categoryAssessments = $groupedAssessments->get($categoryCode, collect());

            if ($categoryAssessments->isEmpty()) {
                continue; // 如果该分类下没有测评，跳过
            }

            // 构建分类节点
            $categoryNode = [
                'id' => 'category_' . $categoryCode, // 分类节点使用特殊ID
                'label' => $categoryInfo['name'],
                'full_name' => $categoryInfo['name'], // Element UI Transfer组件需要的label字段
                'is_category' => true, // 标识这是分类节点
                'category_code' => $categoryCode,
                'children' => []
            ];

            // 添加该分类下的测评
            foreach ($categoryAssessments as $assessment) {
                $categoryNode['children'][] = [
                    'id' => $assessment->id,
                    'label' => $assessment->name,
                    'full_name' => $assessment->name, // Element UI Transfer组件需要的label字段
                    'official_name' => $assessment->official_name,
                    'category_code' => $assessment->category_code,
                    'category_name' => $assessment->category_name,
                    'stage' => $assessment->stage,
                    'is_category' => false // 标识这是测评节点
                ];
            }

            $treeData[] = $categoryNode;
        }

        return $treeData;
    }

    /**
     * 获取测评分类映射信息
     *
     * @return array
     */
    private function getCategoryMapping()
    {
        return [
            'career' => [
                'name' => '生涯测评',
                'description' => '生涯测评板块帮助你了解自己的兴趣所在、性格特征、能力优劣势等'
            ],
            'capability' => [
                'name' => '五力测评',
                'description' => '五力测评帮助评估学习能力、思维能力等核心能力'
            ],
            'competency' => [
                'name' => '核心素养',
                'description' => '创新人才核心素养测评'
            ],
            'psychology' => [
                'name' => '心理健康',
                'description' => '心理健康评估'
            ],
            'subject' => [
                'name' => '学科兴趣',
                'description' => '学科兴趣测评'
            ],
        ];
    }

    //setSchoolAssessments - 增量更新，保留is_open_puce等字段设置
    public function setSchoolAssessments($school_id, Request $request)
    {
        $data = $request->input('school_assessment_list', []);

        // 提取新的测评ID列表
        $newAssessmentIds = array_column($data, 'assessment_id');

        // 获取当前学校已有的测评记录
        $existingAssessments = SchoolAssessment::where('school_id', $school_id)
            ->pluck('assessment_id')
            ->toArray();

        // 需要新增的测评ID（在新列表中但不在现有记录中）
        $toInsertIds = array_diff($newAssessmentIds, $existingAssessments);

        // 需要删除的测评ID（在现有记录中但不在新列表中）
        $toDeleteIds = array_diff($existingAssessments, $newAssessmentIds);

        $insertCount = 0;
        $deleteCount = 0;

        // 删除不再需要的测评记录
        if (!empty($toDeleteIds)) {
            $deleteCount = SchoolAssessment::where('school_id', $school_id)
                ->whereIn('assessment_id', $toDeleteIds)
                ->delete();
        }

        // 新增测评记录
        if (!empty($toInsertIds)) {
            $insertData = [];
            foreach ($toInsertIds as $assessmentId) {
                $recordData = [
                    'school_id' => $school_id,
                    'assessment_id' => $assessmentId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                // 检查表中是否有is_open_puce字段，如果有则添加默认值
                if ($this->hasIsOpenPuceColumn()) {
                    $recordData['is_open_puce'] = 0; // 新增记录默认不开放普测
                }

                $insertData[] = $recordData;
            }

            // 批量插入新记录
            SchoolAssessment::insert($insertData);
            $insertCount = count($insertData);
        }

        return [
            'inserted' => $insertCount,
            'deleted' => $deleteCount,
            'message' => "成功新增 {$insertCount} 个测评，删除 {$deleteCount} 个测评"
        ];
    }

    /**
     * 检查school_assessments表是否有is_open_puce字段
     *
     * @return bool
     */
    private function hasIsOpenPuceColumn()
    {
        try {
            // 使用Schema检查字段是否存在
            return \Schema::hasColumn('school_assessments', 'is_open_puce');
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 设置学校某个测评是否开放普测
     * @param int $school_id 学校ID
     * @param Request $request 请求对象
     */
    public function setSchoolAssessmentOpenPuce($school_id, $request)
    {
        // 检查表中是否有is_open_puce字段
        if (!$this->hasIsOpenPuceColumn()) {
            throw new BusinessException('当前数据库版本不支持普测设置功能，请联系管理员升级数据库');
        }

        $assessment_id = $request->input('assessment_id');
        $is_open_puce = $request->input('is_open_puce');

        // 查找学校测评记录
        $schoolAssessment = SchoolAssessment::where('school_id', $school_id)
            ->where('assessment_id', $assessment_id)
            ->first();

        if (!$schoolAssessment) {
            throw new BusinessException('学校测评记录不存在');
        }

        // 更新是否开放普测状态
        $schoolAssessment->is_open_puce = $is_open_puce;
        $schoolAssessment->updated_at = now();
        $schoolAssessment->updater = $request->user()->real_name;

        $schoolAssessment->save();
    }
}
