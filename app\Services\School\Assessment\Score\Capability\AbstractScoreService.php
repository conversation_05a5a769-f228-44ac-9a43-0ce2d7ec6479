<?php

namespace App\Services\School\Assessment\Score\Capability;

use App\Repositories\AnswerRepository;
use App\Services\BaseService;
use App\Services\School\Assessment\Score\ScoreServiceInterface;

/**
 * 能力测评抽象评分服务
 * 
 * 提供能力测评相关的基础评分功能，包括分数计算和结果分析
 */
abstract class AbstractScoreService extends BaseService implements ScoreServiceInterface
{
    public function __construct(
        protected AnswerRepository $answerRepository
    ) {
        
    }

    /**
     * 计算能力测评分数
     * 
     * @param array $params 包含学生ID、任务分配ID、测评ID和学校ID的参数数组
     * @return array 维度分数数组
     */
    public function calculateScores(array $params): array
    {
        $data = $this->answerRepository->getCapabilityDimensionScores($params);

        return $data;
    }

    /**
     * 计算维度得分的倍数
     * @return int
     */
    protected function getScoreMultiplier(): int
    {
        return 2;
    }

    /**
     * 是否包含维度在总分计算中
     * @param array $dimension
     * @return bool
     */
    protected function shouldIncludeDimensionInTotal(array $dimension): bool
    {
        return true;
    }

    /**
     * 是否使用维度平均分作为总分
     * @return bool
     */
    protected function useAverageDimensionScore(): bool
    {
        return false;
    }

    /**
     * 计算总分
     * 
     * @param array $scores 维度分数数组
     * @return float 计算后的总分
     */
    protected function calculateTotalScore(array $scores): float
    {
        if ($this->useAverageDimensionScore()) {
            $total_score = 0;
            $dimension_count = 0;
            
            foreach ($scores as $score) {
                if ($this->shouldIncludeDimensionInTotal($score)) {
                    $total_score += round($score['score'] * $this->getScoreMultiplier() / $score['question_count']);
                    $dimension_count++;
                }
            }
            
            return $dimension_count > 0 ? round($total_score / $dimension_count) : 0;
        }
        
        $total_score = 0;
        $question_count = 0;
        
        foreach ($scores as $score) {
            if ($this->shouldIncludeDimensionInTotal($score)) {
                $total_score += $score['score'];
                $question_count += $score['question_count'];
            }
        }
        
        return $question_count > 0 ? round($total_score * $this->getScoreMultiplier() / $question_count) : 0;
    }

    /**
     * 通用计算实现
     * 
     * @param array $params 包含评估所需参数的数组
     * @return array 评估结果数组
     */
    public function calculate(array $params): array
    {
        $dimension_scores = $this->calculateScores($params);
        $multiplier = $this->getScoreMultiplier();
        
        // 先计算总分
        $total_score = $this->calculateTotalScore($dimension_scores);
        
        // 再处理各维度分数并移除 question_count
        foreach ($dimension_scores as $key => $dimension) {
            if ($this->shouldIncludeDimensionInTotal($dimension)) {
                $dimension_scores[$key]['score'] = round($dimension['score'] * $multiplier / $dimension['question_count']);
            }
            unset($dimension_scores[$key]['question_count']);
        }

        return [
            'dimensions' => $dimension_scores,
            'total_score' => $total_score
        ];
    }
}