<?php

namespace App\Http\Requests\School\Xuezhi;

use App\Http\Requests\BaseRequest;

class OccupationRequest extends BaseRequest
{
    /**
     * 获取应用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            'occupation_name' => 'nullable|string|max:50',
        ];
    }

    /**
     * 获取已定义验证规则的错误消息
     */
    public function messages(): array
    {
        return [
            'occupation_name.string' => '职业名称必须是字符串',
            'occupation_name.max' => '职业名称不能超过50个字符',
        ];
    }
}
