<?php

namespace App\Http\Controllers;


use App\Exceptions\BusinessException;
use App\Models\Role;
use App\Models\RoleHasMenu;
use App\Services\RoleService;
use App\Services\Admin\MenuService;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;
use App\Http\Requests\RoleRequest;

class RoleController extends Controller
{
    use CrudOperations;

    protected string $model = Role::class;

        // 构造函数注入 SchoolService
    public function __construct(protected RoleService $roleService, protected MenuService $menuService)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $list = $this->roleService->listBuilder($request);

        return $this->success($list);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $data = filterRequestData("roles");
        $organization_id = $this->roleService->getOrganizationId($request);

        // 判断角色名称是否已存在
        if (Role::where('name', $data['name'])->where('organization_id', $organization_id)->exists()) {
            return $this->error("角色名称已存在");
        }

        return $this->success($this->roleService->createRole($data, $organization_id, $request->user()->real_name));
    }

    // 获取机构角色类型
    public function getOrgRoleTypes(Request $request)
    {
        return $this->success($this->roleService->getOrgRoleTypes($request));
    }

    // 获取机构购买拥有的菜单列表
    public function getOrgHasMenus(Request $request)
    {
        return $this->success($this->roleService->getOrgHasMenus2($request));
    }

    // 获取角色拥有的菜单
    public function getRoleHasMenus(Request $request, $role_id)
    {
        $role = Role::find($role_id);
        $organization_id = $this->roleService->getOrganizationId($request);
        if ($role && $role->organization_id != $organization_id) {
            return $this->error("角色所属机构与当前用户所属机构不一致");
        }

        $roleHasMenuIds = RoleHasMenu::where('role_id', $role_id)->pluck('organization_menu_id')->toArray();
        return $this->success($roleHasMenuIds);
    }

    // 设置角色拥有的菜单
    public function setRoleHasMenus(Request $request, $role_id)
    {
        $role = Role::find($role_id);
        $organization_id = $this->roleService->getOrganizationId($request);
        if ($role && $role->organization_id != $organization_id) {
            return $this->error("角色所属机构与当前用户所属机构不一致");
        }
        return $this->success($this->roleService->setRoleHasMenus($request, $role_id));
    }

    /**
     * 获取适用人群的默认菜单
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function crowdDefaultMenus(RoleRequest $request)
    {
        $crowd = $request->input('crowd');
        $organization_id = $this->roleService->getOrganizationId($request);
        return $this->success($this->menuService->crowdMenus($crowd, $organization_id));
    }

    // 禁用启用角色
    public function updateRoleStatus(Request $request, $id)
    {
        try {
            $result = $this->roleService->updateRoleStatus($request,$id);
            if($result) {
                return $this->message('修改成功');
            } else {
                return $this->error('修改失败');
            }
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }
}
