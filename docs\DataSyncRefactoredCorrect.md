# 数据同步服务正确拆分说明

## 概述

已经将DataSyncService正确拆分为多个专门的服务类，**保持了原有的逻辑不变**，只是将代码组织到不同的服务中。

## 拆分后的服务

### 1. SchoolSyncService (学校同步服务)
**功能：** 完整的学校同步，包括基础信息、校区、角色
**保持原有逻辑：**
- syncSchoolBasicInfo() - 同步学校基础信息
- syncSchoolDistrict() - 同步校区信息到ysy_school_district表
- syncSchoolRoles() - 同步角色信息到ysy_role表

### 2. TeacherSyncService (教师同步服务)
**功能：** 教师数据同步
**保持原有逻辑：**
```php
// 原有的教师同步逻辑完全保持不变
public function syncSingleTeacher($request): array
{
    $name = $request->input('teacher_name');
    $username = $request->input('username');
    $password = '827ccb0eea8a706c4c34a16891f84e7b';
    $gender = $request->input('gender', 1);
    $school_district = $request->input('school_campus_id');
    $roles = $request->input('roles');
    
    // 如果包含2就是教务，如果只有3就是老师
    if (in_array(2, $roles)) {
        // 教务角色逻辑
    } elseif (in_array(3, $roles) && !in_array(2, $roles)) {
        // 教师角色逻辑
    }
    
    // 调用syncTeacher方法
    return $this->syncTeacher($teacherData);
}
```

### 3. StudentSyncService (学生同步服务)
**功能：** 学生数据同步
**保持原有逻辑：**
```php
// 原有的学生同步逻辑完全保持不变
public function syncSingleStudent($request): array
{
    $name = $request->input('student_name');
    $username = $request->input('username');
    $gender = $request->input('gender', 1);
    $class_id = $request->input('class_id');
    $grade_year = $request->input('grade_year');
    $init_grade_id = $request->input('init_grade_id');
    $school_campus_id = $request->input('school_campus_id');
    $school_no = $request->input('school_no');
    
    // 通过校区ID、grade_year和init_grade_id查询ysy_grade表
    $ysy_grade = $this->syncConnection->table('ysy_grade')
        ->where('school_id', $syncSchoolId)
        ->where('name', $grade_year)
        ->where('grade_sort', $init_grade_id)
        ->first();
    
    // 通过学生的class_id查询班级名称
    $class = \DB::table('classes')->where('id', $class_id)->first();
    $class_name = $class->class_name;
    
    // 通过班级名称查询同步数据库中的班级ID
    $ysy_class = $this->syncConnection->table('ysy_class')
        ->where('school_id', $syncSchoolId)
        ->where('grade_id', $sync_grade_id)
        ->where('name', $class_name)
        ->first();
    
    // 调用syncStudent方法
    return $this->syncStudent($student_data);
}
```

### 4. ClassSyncService (班级同步服务)
**功能：** 简化的班级同步服务，只保留必要功能

## 关键原则

### ✅ 保持不变的内容
1. **所有的同步逻辑** - 完全按照原来的方式
2. **数据库查询** - 保持原有的查询方式
3. **字段映射** - 保持原有的字段对应关系
4. **错误处理** - 保持原有的错误处理逻辑
5. **返回格式** - 保持原有的返回数据格式

### ✅ 只改变的内容
1. **代码位置** - 将代码从DataSyncService移动到专门的服务类
2. **依赖注入** - 通过构造函数注入需要的服务
3. **方法调用** - 通过服务实例调用方法

## 使用方式

### 在现有代码中的调用
```php
// 教师同步 - 保持原有调用方式
$teacherSyncService = app(\App\Services\DataSync\TeacherSyncService::class);
$result = $teacherSyncService->syncSingleTeacher($request);

// 学生同步 - 保持原有调用方式  
$studentSyncService = app(\App\Services\DataSync\StudentSyncService::class);
$result = $studentSyncService->syncSingleStudent($request);
$result = $studentSyncService->syncBatchStudents($request);

// 学校同步 - 保持原有调用方式
$schoolSyncService = app(\App\Services\DataSync\SchoolSyncService::class);
$result = $schoolSyncService->syncSchool($schoolData);
```

### 或者使用统一入口（向后兼容）
```php
$dataSyncService = app(\App\Services\DataSync\DataSyncServiceNew::class);
$result = $dataSyncService->syncSingleTeacher($request);
$result = $dataSyncService->syncSingleStudent($request);
```

## 数据流程保持不变

### 教师同步流程
```
请求参数 → 角色判断 → ysy_member表 → ysy_teacher表（如果是教师角色）
```

### 学生同步流程  
```
请求参数 → 年级查询 → 班级查询 → ysy_member表 → ysy_student表
```

### 学校同步流程
```
学校数据 → 基础信息 → 校区信息 → 角色信息
```

## 优势

1. **代码组织更清晰** - 每个服务只负责一种数据类型
2. **易于维护** - 修改某种数据的同步逻辑不影响其他数据
3. **保持兼容性** - 原有的调用方式和逻辑完全不变
4. **可扩展性** - 容易添加新的数据类型同步
5. **测试友好** - 可以独立测试每种数据的同步功能

## 注意事项

1. **不要修改原有逻辑** - 只是代码位置的移动，不改变任何业务逻辑
2. **保持接口一致** - 方法签名和返回格式保持不变
3. **依赖关系** - 确保服务之间的依赖关系正确配置
4. **测试验证** - 确保拆分后的功能与原来完全一致

## 迁移建议

1. **逐步迁移** - 可以逐步将调用改为使用新的服务类
2. **保持原有调用** - 在确认新服务工作正常前，保持原有调用方式
3. **充分测试** - 确保所有同步功能正常工作
4. **监控日志** - 观察同步结果是否与之前一致
