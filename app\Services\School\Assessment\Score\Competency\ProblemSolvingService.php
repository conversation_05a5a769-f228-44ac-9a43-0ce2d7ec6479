<?php

namespace App\Services\School\Assessment\Score\Competency;

use App\Services\School\Assessment\Score\Competency\AbstractScoreService;

/**
 * 问题解决能力评估服务
 * 
 * 用于计算学生的问题解决能力评估结果
 */
class ProblemSolvingService extends AbstractScoreService
{
    /**
     * 主观维度数量
     */
    private const SUBJECTIVE_DIMENSIONS_COUNT = 6;
    
    /**
     * 分数乘数
     */
    private const SCORE_MULTIPLIER = [
        'subjective' => 2,
        'objective' => 2
    ];

    /**
     * 计算问题解决能力评估结果
     * 
     * @param array $params 包含评估所需参数的数组
     * @return array 问题解决能力评估结果数组
     */
    public function calculate(array $params): array
    {
        // 计算维度分数
        $dimension_scores = $this->calculateDimensionScores($params);
        
        return $this->calculateProblemSolvingDimensionScores($dimension_scores);
    }

    /**
     * 计算问题解决能力维度分数
     * 
     * @param array $dimension_scores 维度分数数组
     * @return array 问题解决能力维度分数结果数组
     */
    private function calculateProblemSolvingDimensionScores(array $dimension_scores): array
    {
        // 处理主观题
        $subjective_total = 0;
        $subjective_count = 0;
        $dimension_subjective_scores = array_slice($dimension_scores, 0, self::SUBJECTIVE_DIMENSIONS_COUNT);
        foreach ($dimension_subjective_scores as $key => $value){
            $dimension_subjective_scores[$key]['score'] = round($value['score'] * self::SCORE_MULTIPLIER['subjective'] / $value['question_count'], 1); // 展示每个维度的分数=每个维度总分*2/每个维度的题数
            $subjective_total += $value['score'];
            $subjective_count += $value['question_count'];
            unset($dimension_subjective_scores[$key]['question_count']);
        }
        $subjective_show = round($subjective_total * self::SCORE_MULTIPLIER['subjective'] / $subjective_count, 1); // 展示总分=所有题目总分*2/所有题数

        // 处理客观题
        $dimension_objective_scores = array_slice($dimension_scores, self::SUBJECTIVE_DIMENSIONS_COUNT, 1);
        $dimension_objective_scores = $dimension_objective_scores[0];
        $dimension_objective_scores['score'] = $dimension_objective_scores['score'] * self::SCORE_MULTIPLIER['objective']; // 实际问题解决能力得分*10/5
        unset($dimension_objective_scores['question_count']);

        return [
            'dimension_tendency' => $dimension_subjective_scores,
            'total_score' => $subjective_show,
            'dimension_ability' => $dimension_objective_scores
        ];
    }
}